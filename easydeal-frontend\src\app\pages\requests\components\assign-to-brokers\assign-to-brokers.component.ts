import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { RequestService } from '../../services/request.service';

@Component({
  selector: 'app-assign-to-brokers',
  templateUrl: './assign-to-brokers.component.html',
  styleUrl: './assign-to-brokers.component.scss',
})
export class AssignToBrokersComponent implements OnInit {
  requestId: number;
  AssignBroker: any[] = [];
  isLoading = false;

  trackById(index: number, broker: any): number {
    return broker.id;
  }

  selectAll = false;

  selectAllBrokers() {
    this.AssignBroker.forEach((broker) => {
      broker.selected = this.selectAll;
    });
    this.printSelectedIds();
  }

  updateSelectAllStatus() {
    this.selectAll = this.AssignBroker.every((broker) => broker.selected);
    this.printSelectedIds();
  }

  printSelectedIds() {
    const selectedIds = this.AssignBroker.filter(
      (broker) => broker.selected
    ).map((broker) => broker.id);

    console.log('Selected IDs:', selectedIds);
  }

  get selectedCount(): number {
    return this.AssignBroker.filter((broker) => broker.selected).length;
  }

  constructor(
    protected cd: ChangeDetectorRef,
    protected requestService: RequestService,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    // Get requestId from route parameters
    this.route.paramMap.subscribe((params) => {
      this.requestId = Number(params.get('id'));
      if (this.requestId) {
        this.loadRequestHistory();
      } else {
        console.error('No requestId provided in route parameters');
      }
    });
  }

  loadRequestHistory(): void {
    this.isLoading = true;

    this.requestService.getRecommendedBrokers(this.requestId).subscribe({
      next: (response: any) => {
        this.AssignBroker = response?.data || [];
        this.isLoading = false;
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading broker history:', error);
        this.isLoading = false;
      },
    });
  }

  assignSelectedBrokers(): void {
    const selectedBrokerIds = this.AssignBroker.filter(
      (broker) => broker.selected
    ).map((broker) => broker.id);

    this.requestService
      .assignBrokersToRequest(this.requestId, selectedBrokerIds)
      .subscribe({
        next: (res) => {
          console.log('Assigned successfully:', res);
        },
        error: (err) => {
          console.error('Assignment error:', err);
        },
      });
  }
}
