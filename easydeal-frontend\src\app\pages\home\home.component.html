<!-- Header Section -->
<header class="home-header">
  <!-- Navigation Bar -->
  <nav class="navbar navbar-expand-lg">
    <div class="container-fluid px-4">
      <!-- Logo -->
      <div class="navbar-brand">
        <img alt="Logo" src="./assets/media/easydeallogos/loading-logo.png" class="h-40px app-sidebar-logo-default" />
      </div>

      <!-- Navigation Menu -->
      <div class="navbar-nav mx-auto">
        <ul class="nav-list d-flex align-items-center mb-0">
          <li class="nav-item">
            <a href="#" class="nav-link"> Home </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link"> About EasyDeal </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link"> New Projects </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link"> Advertisements </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link"> Contact Us </a>
          </li>
        </ul>
      </div>

      <!-- User Registration Link / User Profile -->
      <div class="navbar-nav position-relative">
        <!-- If user is logged in, show user profile -->
        <div *ngIf="isLoggedIn" class="nav-link user-profile" (click)="toggleUserDropdown()">
          <img [src]="getUserProfileImage()" [alt]="getUserDisplayName()" class="user-avatar me-2">
          <span class="user-name">{{ getUserDisplayName() }}</span>
          <i class="fas fa-chevron-down ms-2"></i>
        </div>

        <!-- User Dropdown Menu -->
        <div *ngIf="isLoggedIn && showUserDropdown" class="user-dropdown">
          <div class="dropdown-item" (click)="closeUserDropdown()">
            <i class="fas fa-file-alt text-danger me-2"></i>
            <span>Requests</span>
          </div>
          <div class="dropdown-item" (click)="closeUserDropdown()">
            <i class="fas fa-user text-primary me-2"></i>
            <span> My Profile </span>
          </div>
          <div class="dropdown-item" (click)="closeUserDropdown()">
            <i class="fas fa-paper-plane text-info me-2"></i>
            <span> Messages </span>
          </div>
          <div class="dropdown-item" (click)="closeUserDropdown()">
            <i class="fas fa-question-circle text-warning me-2"></i>
            <span> Help </span>
          </div>
          <div class="dropdown-item" (click)="closeUserDropdown()">
            <i class="fas fa-bell text-secondary me-2"></i>
            <span> Notifications </span>
          </div>
          <div class="dropdown-divider"></div>
          <div class="dropdown-item logout-item" (click)="logout()">
            <i class="fas fa-sign-out-alt text-danger me-2"></i>
            <span> Logout </span>
          </div>
          <div class="dropdown-divider"></div>
          <div class="dropdown-item new-request-item" (click)="closeUserDropdown()">
            <span class="text-success"> New Request </span>
          </div>
        </div>

        <!-- If user is not logged in, show register button -->
        <a *ngIf="!isLoggedIn" href="#" class="nav-link user-link">
          <i class="fas fa-user me-2"></i>
          Register Guest
        </a>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <div class="hero-section">
    <div class="hero-background">
      <img
        src="./assets/media/home/<USER>"
        alt="Hero Background" class="hero-bg-image">
      <div class="hero-overlay"></div>
    </div>

    <div class="hero-content">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-12">
            <div class="hero-text-container">
              <div class="hero-text-item">
                <h2 class="hero-text">سهولة</h2>
              </div>
              <div class="hero-text-item">
                <h2 class="hero-text">سرعة</h2>
              </div>
              <div class="hero-text-item">
                <h2 class="hero-text">ثقة</h2>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>