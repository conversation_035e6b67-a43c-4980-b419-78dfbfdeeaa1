<!-- Header Section -->
<header class="home-header">
  <!-- Navigation Bar -->
  <nav class="navbar navbar-expand-lg">
    <div class="container-fluid px-4">
      <!-- Logo -->
      <div class="navbar-brand">
        <img alt="Logo" src="./assets/media/easydeallogos/loading-logo.png" class="h-40px app-sidebar-logo-default" />
      </div>

      <!-- Mobile Menu Toggle Button -->
      <button class="navbar-toggler d-lg-none" type="button" (click)="toggleMobileMenu()"
        [attr.aria-expanded]="showMobileMenu" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon">
          <i class="fas" [class.fa-bars]="!showMobileMenu" [class.fa-times]="showMobileMenu"></i>
        </span>
      </button>

      <!-- Navigation Menu -->
      <div class="navbar-nav mx-auto d-none d-lg-flex">
        <ul class="nav-list d-flex align-items-center mb-0">
          <li class="nav-item">
            <a href="#" class="nav-link"> Home </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link"> About EasyDeal </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link"> New Projects </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link"> Advertisements </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link"> Contact Us </a>
          </li>
        </ul>
      </div>

      <!-- Mobile Navigation Dropdown -->
      <div *ngIf="showMobileMenu" class="mobile-nav-dropdown d-lg-none">
        <div class="dropdown-item">
          <span class="menu-icon me-2">
            <i class="fas fa-home fs-6 text-primary"></i>
          </span>
          <span>Home</span>
        </div>
        <div class="dropdown-item">
          <span class="menu-icon me-2">
            <i class="fas fa-info-circle fs-6 text-info"></i>
          </span>
          <span>About EasyDeal</span>
        </div>
        <div class="dropdown-item">
          <span class="menu-icon me-2">
            <i class="fas fa-building fs-6 text-success"></i>
          </span>
          <span>New Projects</span>
        </div>
        <div class="dropdown-item">
          <span class="menu-icon me-2">
            <i class="fas fa-bullhorn fs-6 text-warning"></i>
          </span>
          <span>Advertisements</span>
        </div>
        <div class="dropdown-item">
          <span class="menu-icon me-2">
            <i class="fas fa-phone fs-6 text-secondary"></i>
          </span>
          <span>Contact Us</span>
        </div>
      </div>

      <!-- User Registration Link / User Profile -->
      <div class="navbar-nav position-relative">
        <!-- If user is logged in, show user profile -->
        <div *ngIf="isLoggedIn" class="nav-link user-profile" (click)="toggleUserDropdown()">
          <img [src]="getUserProfileImage()" [alt]="getUserDisplayName()" class="user-avatar me-2">
          <span class="user-name">{{ getUserDisplayName() }}</span>
          <i class="fas fa-chevron-down ms-2"></i>
        </div>

        <!-- User Dropdown Menu -->
        <div *ngIf="isLoggedIn && showUserDropdown" class="user-dropdown">
          <div class="dropdown-item" (click)="closeUserDropdown()">
            <span class="menu-icon me-2">
              <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_24_2533)">
                  <path stroke="#e74c3c" stroke-width="1"
                    d="M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z" />
                  <path stroke="#e74c3c" stroke-width="1"
                    d="M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z" />
                  <path stroke="#e74c3c" stroke-width="1"
                    d="M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z" />
                </g>
                <defs>
                  <clipPath id="clip0_24_2533">
                    <rect width="19" height="19" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </span>
            <span>Requests</span>
          </div>
          <div class="dropdown-item" (click)="closeUserDropdown()">
            <span class="menu-icon me-2">
              <app-keenicon name="user" class="fs-5 text-primary" type="outline"></app-keenicon>
            </span>
            <span> My Profile </span>
          </div>
          <div class="dropdown-item" (click)="closeUserDropdown()">
            <span class="menu-icon me-2">
              <app-keenicon name="messages" class="fs-5 text-info" type="outline"></app-keenicon>
            </span>
            <span> Messages </span>
          </div>
          <div class="dropdown-item" (click)="closeUserDropdown()">
            <span class="menu-icon me-2">
              <i class="fa-regular fa-circle-question fs-6 text-warning"></i>
            </span>
            <span> Help </span>
          </div>
          <div class="dropdown-item" (click)="closeUserDropdown()">
            <span class="menu-icon me-2">
              <app-keenicon name="notification-on" class="fs-5 text-gray-600" type="outline"></app-keenicon>
            </span>
            <span> Notifications </span>
          </div>
          <div class="dropdown-divider"></div>
          <div class="dropdown-item logout-item" (click)="logout()">
            <span class="menu-icon me-2">
              <i class="fas fa-sign-out-alt fs-6 text-danger"></i>
            </span>
            <span> Logout </span>
          </div>
          <div class="dropdown-divider"></div>
          <div class="dropdown-item new-request-item" (click)="closeUserDropdown()">
            <span class="text-success"> New Request </span>
          </div>
        </div>

        <!-- If user is not logged in, show register button -->
        <a *ngIf="!isLoggedIn" href="#" class="nav-link user-link">
          <i class="fas fa-user me-2"></i>
          Register Guest
        </a>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <div class="hero-section">
    <div class="hero-background">
      <img
        src="./assets/media/home/<USER>"
        alt="Hero Background" class="hero-bg-image">
      <div class="hero-overlay"></div>
    </div>

    <div class="hero-content">
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-12">
            <div class="hero-text-container">
              <div class="hero-text-item">
                <h2 class="hero-text"> Easy</h2>
              </div>
              <div class="hero-text-item">
                <h2 class="hero-text"> Speed </h2>
              </div>
              <div class="hero-text-item">
                <h2 class="hero-text"> Reliability </h2>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>