{"ast": null, "code": "// Step configuration constants\nexport const STEPPER_CONFIG = {\n  TOTAL_STEPS: 5,\n  INITIAL_STEP: 1,\n  STEP_NAMES: {\n    1: 'Request Settings',\n    2: 'Location Information',\n    3: 'Unit Information',\n    4: 'Project Documents',\n    5: 'Financial Information'\n  }\n};\n// Main option arrays\nexport const SPECIALIZATION_SCOPE_OPTIONS = [{\n  key: 'Purchase/ Sell Outside Compound',\n  value: 'purchase_sell_outside_compound'\n}, {\n  key: 'Primary Inside Compound',\n  value: 'primary_inside_compound'\n}, {\n  key: 'Resale Inside Compound',\n  value: 'resale_inside_compound'\n}, {\n  key: 'Rentals Outside Compound',\n  value: 'rentals_outside_compound'\n}, {\n  key: 'Rentals Inside Compound',\n  value: 'rentals_inside_compound'\n}];\nexport const TYPE_OPTIONS = [{\n  key: 'Purchasing',\n  value: 'purchasing'\n}, {\n  key: 'Sell',\n  value: 'sell'\n}, {\n  key: 'Rent Out',\n  value: 'rent_out'\n}, {\n  key: 'Rent In',\n  value: 'rent_in'\n}];\n// Property type options\nexport const FLOOR_TYPES_OPTIONS = [{\n  key: 'Ground Floor',\n  value: 'ground'\n}, {\n  key: 'Last Floor',\n  value: 'last_floor'\n}, {\n  key: 'Repeated',\n  value: 'repeated'\n}, {\n  key: 'All The Above Are Suitable',\n  value: 'all_the_above_are_suitable'\n}];\nexport const BUILDING_LICENSE_TYPES_OPTIONS = [{\n  key: 'Permit Available',\n  value: 'Permit_Available'\n}, {\n  key: 'No Permit',\n  value: 'No_Permit'\n}];\nexport const UNIT_LAYOUT_STATUS_TYPES_OPTIONS = [{\n  key: 'Partial Roof',\n  value: 'partial_roof'\n}, {\n  key: 'Full Roof',\n  value: 'full_roof'\n}, {\n  key: 'Open Space',\n  value: 'open_space'\n}, {\n  key: 'Single Apartment',\n  value: 'single_apartment'\n}, {\n  key: 'Two Apartments',\n  value: 'two_apartments'\n}, {\n  key: 'All Acceptable',\n  value: 'all_acceptable'\n}];\nexport const BUILDING_LAYOUT_STATUS_TYPES_OPTIONS = [{\n  key: 'Open Space',\n  value: 'open_space'\n}, {\n  key: 'Single Apartment',\n  value: 'single_apartment'\n}, {\n  key: 'Two Apartments',\n  value: 'two_apartments'\n}, {\n  key: 'All Acceptable',\n  value: 'all_acceptable'\n}];\nexport const GROUND_LAYOUT_STATUS_TYPES_OPTIONS = [{\n  key: 'Vacant Land',\n  value: 'vacant_land'\n}, {\n  key: 'Under Construction',\n  value: 'under_construction'\n}, {\n  key: 'Fully Built',\n  value: 'fully_built'\n}, {\n  key: 'All Acceptable',\n  value: 'all_acceptable'\n}];\nexport const UNIT_DESIGN_TYPES_OPTIONS = [{\n  key: 'Custom Design',\n  value: 'custom_design'\n}, {\n  key: 'One Apartment Per Floor',\n  value: 'one_apartment_per_floor'\n}, {\n  key: 'Two Apartments Per Floor',\n  value: 'two_apartments_per_floor'\n}, {\n  key: 'More Than Two Apartments Per Floor',\n  value: 'more_than_two_apartments_per_floor'\n}, {\n  key: 'All Acceptable',\n  value: 'all_acceptable'\n}];\nexport const ACTIVITY_TYPES_OPTIONS = [{\n  key: 'Administrative Only',\n  value: 'administrative_only'\n}, {\n  key: 'Commercial Only',\n  value: 'commercial_only'\n}, {\n  key: 'Medical Only',\n  value: 'medical_only'\n}, {\n  key: 'Administrative and Commercial',\n  value: 'administrative_and_commercial'\n}, {\n  key: 'Administrative, Commercial and Medical',\n  value: 'administrative_commercial_and_medical'\n}];\nexport const UNIT_DESCRIPTION_TYPES_OPTIONS = [{\n  key: 'Single Front',\n  value: 'single_front'\n}, {\n  key: 'Corner',\n  value: 'corner'\n}, {\n  key: 'Double Front',\n  value: 'double_front'\n}, {\n  key: 'Triple Corner',\n  value: 'triple_corner'\n}, {\n  key: 'Quad Corner',\n  value: 'quad_corner'\n}, {\n  key: 'All Acceptable',\n  value: 'all_acceptable'\n}];\nexport const SUB_UNIT_TYPE_OPTIONS = [{\n  key: 'Apartments',\n  value: 'apartments'\n}, {\n  key: 'I Villa',\n  value: 'i_villa'\n}, {\n  key: 'Twin Houses',\n  value: 'town_houses'\n}, {\n  key: 'Administrative Units',\n  value: 'administrative_units'\n}, {\n  key: 'Medical Clinics',\n  value: 'medical_clinics'\n}, {\n  key: 'Pharmacies',\n  value: 'pharmacies'\n}, {\n  key: 'Commercial Stores',\n  value: 'commercial_stores'\n}, {\n  key: 'Warehouses',\n  value: 'warehouses'\n}, {\n  key: 'Factory Lands',\n  value: 'factory_lands'\n}, {\n  key: 'Warehouses Land',\n  value: 'warehouses_land'\n}, {\n  key: 'Standalone Villas',\n  value: 'standalone_villas'\n}, {\n  key: 'Commercial Administrative Buildings',\n  value: 'commercial_administrative_buildings'\n}, {\n  key: 'Commercial Administrative Lands',\n  value: 'commercial_administrative_lands'\n}, {\n  key: 'Residential Buildings',\n  value: 'residential_buildings'\n}, {\n  key: 'Residential Lands',\n  value: 'residential_lands'\n}, {\n  key: 'Chalets',\n  value: 'chalets'\n}, {\n  key: 'Hotels',\n  value: 'hotels'\n}, {\n  key: 'Factories',\n  value: 'factories'\n}, {\n  key: 'Basements',\n  value: 'basements'\n}, {\n  key: 'Full Buildings',\n  value: 'full_buildings'\n}, {\n  key: 'Commercial Units',\n  value: 'commercial_units'\n}, {\n  key: 'Shops',\n  value: 'shops'\n}, {\n  key: 'Mixed Housings',\n  value: 'mixed_housings'\n}, {\n  key: 'Cooperatives',\n  value: 'cooperatives'\n}, {\n  key: 'Youth Units',\n  value: 'youth_units'\n}, {\n  key: 'Ganat Misr',\n  value: 'ganat_misr'\n}, {\n  key: 'Dar Misr',\n  value: 'dar_misr'\n}, {\n  key: 'Sakan Misr',\n  value: 'sakan_misr'\n}, {\n  key: 'Industrial Lands',\n  value: 'industrial_lands'\n}, {\n  key: 'Cabin',\n  value: 'cabin'\n}, {\n  key: 'Vacation Villa',\n  value: 'vacation_villa'\n}, {\n  key: 'Hotel Units',\n  value: 'hotel_units'\n}];\nexport const RENT_RECURRENCE_OPTIONS = [{\n  key: 'Monthly',\n  value: 'monthly'\n}, {\n  key: 'Quarterly',\n  value: 'quarterly'\n}, {\n  key: 'Semi Annually',\n  value: 'semi_annually'\n}, {\n  key: 'Annually',\n  value: 'annually'\n}];\nexport const PAYMENT_METHOD_OPTIONS = [{\n  key: 'Cash',\n  value: 'cash'\n}, {\n  key: 'Installment',\n  value: 'installment'\n}, {\n  key: 'Both',\n  value: 'all_of_the_above_are_suitable'\n}];\nexport const REQUIRED_INSURANCE_TYPES_OPTIONS = [{\n  key: 'One Month',\n  value: 'one_month'\n}, {\n  key: 'Two Months',\n  value: 'two_months'\n}, {\n  key: 'Fixed Amount',\n  value: 'fixed_amount'\n}];\nexport const OTHER_EXPENSES_OPTIONS = [{\n  key: 'Other',\n  value: 'other'\n}, {\n  key: 'Electricity',\n  value: 'electricity'\n}, {\n  key: 'Gas',\n  value: 'gas'\n}, {\n  key: 'Water',\n  value: 'water'\n}, {\n  key: 'Security Maintenance',\n  value: 'security_maintenance'\n}];\nexport const FURNISHING_STATUS_OPTIONS = [{\n  key: 'Unfurnished',\n  value: 'unfurnished'\n}, {\n  key: 'Furnished with Air Conditioners',\n  value: 'furnished_with_air_conditioners'\n}, {\n  key: 'Furnished without Air Conditioners',\n  value: 'furnished_without_air_conditioners'\n}];\nexport const DELIVERY_STATUS_TYPES_OPTIONS = [{\n  key: 'Immediate Delivery',\n  value: 'immediate_delivery'\n}, {\n  key: 'Under Construction',\n  value: 'under_construction'\n}];\nexport const FINANCIAL_STATUS_TYPES_OPTIONS = [{\n  key: 'Paid in Full',\n  value: 'paid_in_full'\n}, {\n  key: 'Partially Paid with Remaining Installments',\n  value: 'partially_paid_with_remaining_installments'\n}];\nexport const LEGAL_STATUS_TYPES_OPTIONS = [{\n  key: 'Licensed',\n  value: 'licensed'\n}, {\n  key: 'Reconciled',\n  value: 'reconciled'\n}, {\n  key: 'Reconciliation Required',\n  value: 'reconciliation_required'\n}];\nexport const FIT_OUT_CONDITION_TYPES_OPTIONS = [{\n  key: 'Unfitted',\n  value: 'unfitted'\n}, {\n  key: 'Fully Fitted',\n  value: 'fully_fitted'\n}, {\n  key: 'All The Above Are Suitable',\n  value: 'all_the_above_are_suitable'\n}];\nexport const FINISHING_STATUS_TYPES_OPTIONS = [{\n  key: 'On Brick',\n  value: 'on_brick'\n}, {\n  key: 'Semi Finished',\n  value: 'semi_finished'\n}, {\n  key: 'Company Finished',\n  value: 'company_finished'\n}, {\n  key: 'Super Lux',\n  value: 'super_lux'\n}, {\n  key: 'Ultra Super Lux',\n  value: 'ultra_super_lux'\n}];\nexport const UNIT_VIEW_TYPES_OPTIONS = [{\n  key: 'Water View',\n  value: 'water_view'\n}, {\n  key: 'Gardens and Landscape',\n  value: 'gardens_and_landscape'\n}, {\n  key: 'Street',\n  value: 'street'\n}, {\n  key: 'Entertainment Area',\n  value: 'entertainment_area'\n}, {\n  key: 'Garden',\n  value: 'garden'\n}, {\n  key: 'Main Street',\n  value: 'main_street'\n}, {\n  key: 'Square',\n  value: 'square'\n}, {\n  key: 'Side Street',\n  value: 'side_street'\n}, {\n  key: 'Rear View',\n  value: 'rear_view'\n}];\nexport const UNIT_FACING_TYPES_OPTIONS = [{\n  key: 'Right of Facade',\n  value: 'right_of_facade'\n}, {\n  key: 'Left of Facade',\n  value: 'left_of_facade'\n}, {\n  key: 'Side View',\n  value: 'side_view'\n}, {\n  key: 'Rear View',\n  value: 'rear_view'\n}];\nexport const OTHER_ACCESSORIES_OPTIONS = [{\n  key: 'Garage',\n  value: 'garage'\n}, {\n  key: 'Clubhouse',\n  value: 'clubhouse'\n}, {\n  key: 'Club',\n  value: 'club'\n}, {\n  key: 'Storage',\n  value: 'storage'\n}, {\n  key: 'Elevator',\n  value: 'elevator'\n}, {\n  key: 'Swimming Pool',\n  value: 'swimming_pool'\n}];\nexport const BUILDING_DEADLINE_TYPES_OPTIONS = [{\n  key: 'Grace Period Allowed',\n  value: 'grace_period_allowed'\n}, {\n  key: 'No Grace Period',\n  value: 'no_grace_period'\n}];\n// File validation constants\nexport const FILE_VALIDATION_CONFIG = {\n  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif'],\n  MAX_SIZE: 10 * 1024 * 1024 // 10MB\n};", "map": {"version": 3, "names": ["STEPPER_CONFIG", "TOTAL_STEPS", "INITIAL_STEP", "STEP_NAMES", "SPECIALIZATION_SCOPE_OPTIONS", "key", "value", "TYPE_OPTIONS", "FLOOR_TYPES_OPTIONS", "BUILDING_LICENSE_TYPES_OPTIONS", "UNIT_LAYOUT_STATUS_TYPES_OPTIONS", "BUILDING_LAYOUT_STATUS_TYPES_OPTIONS", "GROUND_LAYOUT_STATUS_TYPES_OPTIONS", "UNIT_DESIGN_TYPES_OPTIONS", "ACTIVITY_TYPES_OPTIONS", "UNIT_DESCRIPTION_TYPES_OPTIONS", "SUB_UNIT_TYPE_OPTIONS", "RENT_RECURRENCE_OPTIONS", "PAYMENT_METHOD_OPTIONS", "REQUIRED_INSURANCE_TYPES_OPTIONS", "OTHER_EXPENSES_OPTIONS", "FURNISHING_STATUS_OPTIONS", "DELIVERY_STATUS_TYPES_OPTIONS", "FINANCIAL_STATUS_TYPES_OPTIONS", "LEGAL_STATUS_TYPES_OPTIONS", "FIT_OUT_CONDITION_TYPES_OPTIONS", "FINISHING_STATUS_TYPES_OPTIONS", "UNIT_VIEW_TYPES_OPTIONS", "UNIT_FACING_TYPES_OPTIONS", "OTHER_ACCESSORIES_OPTIONS", "BUILDING_DEADLINE_TYPES_OPTIONS", "FILE_VALIDATION_CONFIG", "ALLOWED_TYPES", "MAX_SIZE"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\shared\\stepper-modal\\stepper-modal.constants.ts"], "sourcesContent": ["import { Validators } from '@angular/forms';\r\n\r\n// Option types interface\r\nexport interface OptionItem {\r\n  key: string;\r\n  value: string;\r\n}\r\n\r\n// Step configuration constants\r\nexport const STEPPER_CONFIG = {\r\n  TOTAL_STEPS: 5,\r\n  INITIAL_STEP: 1,\r\n  STEP_NAMES: {\r\n    1: 'Request Settings',\r\n    2: 'Location Information',\r\n    3: 'Unit Information',\r\n    4: 'Project Documents',\r\n    5: 'Financial Information',\r\n  } as { [key: number]: string }\r\n};\r\n\r\n// Main option arrays\r\nexport const SPECIALIZATION_SCOPE_OPTIONS: OptionItem[] = [\r\n  { key: 'Purchase/ Sell Outside Compound', value: 'purchase_sell_outside_compound' },\r\n  { key: 'Primary Inside Compound', value: 'primary_inside_compound' },\r\n  { key: 'Resale Inside Compound', value: 'resale_inside_compound' },\r\n  { key: 'Rentals Outside Compound', value: 'rentals_outside_compound' },\r\n  { key: 'Rentals Inside Compound', value: 'rentals_inside_compound' },\r\n];\r\n\r\nexport const TYPE_OPTIONS: OptionItem[] = [\r\n  { key: 'Purchasing', value: 'purchasing' },\r\n  { key: 'Sell', value: 'sell' },\r\n  { key: 'Rent Out', value: 'rent_out' },\r\n  { key: 'Rent In', value: 'rent_in' },\r\n];\r\n\r\n// Property type options\r\nexport const FLOOR_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Ground Floor', value: 'ground' },\r\n  { key: 'Last Floor', value: 'last_floor' },\r\n  { key: 'Repeated', value: 'repeated' },\r\n  { key: 'All The Above Are Suitable', value: 'all_the_above_are_suitable' },\r\n];\r\n\r\nexport const BUILDING_LICENSE_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Permit Available', value: 'Permit_Available' },\r\n  { key: 'No Permit', value: 'No_Permit' },\r\n];\r\n\r\nexport const UNIT_LAYOUT_STATUS_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Partial Roof', value: 'partial_roof' },\r\n  { key: 'Full Roof', value: 'full_roof' },\r\n  { key: 'Open Space', value: 'open_space' },\r\n  { key: 'Single Apartment', value: 'single_apartment' },\r\n  { key: 'Two Apartments', value: 'two_apartments' },\r\n  { key: 'All Acceptable', value: 'all_acceptable' },\r\n];\r\n\r\nexport const BUILDING_LAYOUT_STATUS_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Open Space', value: 'open_space' },\r\n  { key: 'Single Apartment', value: 'single_apartment' },\r\n  { key: 'Two Apartments', value: 'two_apartments' },\r\n  { key: 'All Acceptable', value: 'all_acceptable' },\r\n];\r\n\r\nexport const GROUND_LAYOUT_STATUS_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Vacant Land', value: 'vacant_land' },\r\n  { key: 'Under Construction', value: 'under_construction' },\r\n  { key: 'Fully Built', value: 'fully_built' },\r\n  { key: 'All Acceptable', value: 'all_acceptable' },\r\n];\r\n\r\nexport const UNIT_DESIGN_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Custom Design', value: 'custom_design' },\r\n  { key: 'One Apartment Per Floor', value: 'one_apartment_per_floor' },\r\n  { key: 'Two Apartments Per Floor', value: 'two_apartments_per_floor' },\r\n  { key: 'More Than Two Apartments Per Floor', value: 'more_than_two_apartments_per_floor' },\r\n  { key: 'All Acceptable', value: 'all_acceptable' },\r\n];\r\n\r\nexport const ACTIVITY_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Administrative Only', value: 'administrative_only' },\r\n  { key: 'Commercial Only', value: 'commercial_only' },\r\n  { key: 'Medical Only', value: 'medical_only' },\r\n  { key: 'Administrative and Commercial', value: 'administrative_and_commercial' },\r\n  { key: 'Administrative, Commercial and Medical', value: 'administrative_commercial_and_medical' },\r\n];\r\n\r\nexport const UNIT_DESCRIPTION_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Single Front', value: 'single_front' },\r\n  { key: 'Corner', value: 'corner' },\r\n  { key: 'Double Front', value: 'double_front' },\r\n  { key: 'Triple Corner', value: 'triple_corner' },\r\n  { key: 'Quad Corner', value: 'quad_corner' },\r\n  { key: 'All Acceptable', value: 'all_acceptable' },\r\n];\r\n\r\nexport const SUB_UNIT_TYPE_OPTIONS: OptionItem[] = [\r\n  { key: 'Apartments', value: 'apartments' },\r\n  { key: 'I Villa', value: 'i_villa' },\r\n  { key: 'Twin Houses', value: 'town_houses' },\r\n  { key: 'Administrative Units', value: 'administrative_units' },\r\n  { key: 'Medical Clinics', value: 'medical_clinics' },\r\n  { key: 'Pharmacies', value: 'pharmacies' },\r\n  { key: 'Commercial Stores', value: 'commercial_stores' },\r\n  { key: 'Warehouses', value: 'warehouses' },\r\n  { key: 'Factory Lands', value: 'factory_lands' },\r\n  { key: 'Warehouses Land', value: 'warehouses_land' },\r\n  { key: 'Standalone Villas', value: 'standalone_villas' },\r\n  { key: 'Commercial Administrative Buildings', value: 'commercial_administrative_buildings' },\r\n  { key: 'Commercial Administrative Lands', value: 'commercial_administrative_lands' },\r\n  { key: 'Residential Buildings', value: 'residential_buildings' },\r\n  { key: 'Residential Lands', value: 'residential_lands' },\r\n  { key: 'Chalets', value: 'chalets' },\r\n  { key: 'Hotels', value: 'hotels' },\r\n  { key: 'Factories', value: 'factories' },\r\n  { key: 'Basements', value: 'basements' },\r\n  { key: 'Full Buildings', value: 'full_buildings' },\r\n  { key: 'Commercial Units', value: 'commercial_units' },\r\n  { key: 'Shops', value: 'shops' },\r\n  { key: 'Mixed Housings', value: 'mixed_housings' },\r\n  { key: 'Cooperatives', value: 'cooperatives' },\r\n  { key: 'Youth Units', value: 'youth_units' },\r\n  { key: 'Ganat Misr', value: 'ganat_misr' },\r\n  { key: 'Dar Misr', value: 'dar_misr' },\r\n  { key: 'Sakan Misr', value: 'sakan_misr' },\r\n  { key: 'Industrial Lands', value: 'industrial_lands' },\r\n  { key: 'Cabin', value: 'cabin' },\r\n  { key: 'Vacation Villa', value: 'vacation_villa' },\r\n  { key: 'Hotel Units', value: 'hotel_units' },\r\n];\r\n\r\nexport const RENT_RECURRENCE_OPTIONS: OptionItem[] = [\r\n  { key: 'Monthly', value: 'monthly' },\r\n  { key: 'Quarterly', value: 'quarterly' },\r\n  { key: 'Semi Annually', value: 'semi_annually' },\r\n  { key: 'Annually', value: 'annually' },\r\n];\r\n\r\nexport const PAYMENT_METHOD_OPTIONS: OptionItem[] = [\r\n  { key: 'Cash', value: 'cash' },\r\n  { key: 'Installment', value: 'installment' },\r\n  { key: 'Both', value: 'all_of_the_above_are_suitable' },\r\n];\r\n\r\nexport const REQUIRED_INSURANCE_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'One Month', value: 'one_month' },\r\n  { key: 'Two Months', value: 'two_months' },\r\n  { key: 'Fixed Amount', value: 'fixed_amount' },\r\n];\r\n\r\nexport const OTHER_EXPENSES_OPTIONS: OptionItem[] = [\r\n  { key: 'Other', value: 'other' },\r\n  { key: 'Electricity', value: 'electricity' },\r\n  { key: 'Gas', value: 'gas' },\r\n  { key: 'Water', value: 'water' },\r\n  { key: 'Security Maintenance', value: 'security_maintenance' },\r\n];\r\n\r\nexport const FURNISHING_STATUS_OPTIONS: OptionItem[] = [\r\n  { key: 'Unfurnished', value: 'unfurnished' },\r\n  { key: 'Furnished with Air Conditioners', value: 'furnished_with_air_conditioners' },\r\n  { key: 'Furnished without Air Conditioners', value: 'furnished_without_air_conditioners' },\r\n];\r\n\r\nexport const DELIVERY_STATUS_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Immediate Delivery', value: 'immediate_delivery' },\r\n  { key: 'Under Construction', value: 'under_construction' },\r\n];\r\n\r\nexport const FINANCIAL_STATUS_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Paid in Full', value: 'paid_in_full' },\r\n  { key: 'Partially Paid with Remaining Installments', value: 'partially_paid_with_remaining_installments' },\r\n];\r\n\r\nexport const LEGAL_STATUS_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Licensed', value: 'licensed' },\r\n  { key: 'Reconciled', value: 'reconciled' },\r\n  { key: 'Reconciliation Required', value: 'reconciliation_required' },\r\n];\r\n\r\nexport const FIT_OUT_CONDITION_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Unfitted', value: 'unfitted' },\r\n  { key: 'Fully Fitted', value: 'fully_fitted' },\r\n  { key: 'All The Above Are Suitable', value: 'all_the_above_are_suitable' },\r\n];\r\n\r\nexport const FINISHING_STATUS_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'On Brick', value: 'on_brick' },\r\n  { key: 'Semi Finished', value: 'semi_finished' },\r\n  { key: 'Company Finished', value: 'company_finished' },\r\n  { key: 'Super Lux', value: 'super_lux' },\r\n  { key: 'Ultra Super Lux', value: 'ultra_super_lux' },\r\n];\r\n\r\nexport const UNIT_VIEW_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Water View', value: 'water_view' },\r\n  { key: 'Gardens and Landscape', value: 'gardens_and_landscape' },\r\n  { key: 'Street', value: 'street' },\r\n  { key: 'Entertainment Area', value: 'entertainment_area' },\r\n  { key: 'Garden', value: 'garden' },\r\n  { key: 'Main Street', value: 'main_street' },\r\n  { key: 'Square', value: 'square' },\r\n  { key: 'Side Street', value: 'side_street' },\r\n  { key: 'Rear View', value: 'rear_view' },\r\n];\r\n\r\nexport const UNIT_FACING_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Right of Facade', value: 'right_of_facade' },\r\n  { key: 'Left of Facade', value: 'left_of_facade' },\r\n  { key: 'Side View', value: 'side_view' },\r\n  { key: 'Rear View', value: 'rear_view' },\r\n];\r\n\r\nexport const OTHER_ACCESSORIES_OPTIONS: OptionItem[] = [\r\n  { key: 'Garage', value: 'garage' },\r\n  { key: 'Clubhouse', value: 'clubhouse' },\r\n  { key: 'Club', value: 'club' },\r\n  { key: 'Storage', value: 'storage' },\r\n  { key: 'Elevator', value: 'elevator' },\r\n  { key: 'Swimming Pool', value: 'swimming_pool' },\r\n];\r\n\r\nexport const BUILDING_DEADLINE_TYPES_OPTIONS: OptionItem[] = [\r\n  { key: 'Grace Period Allowed', value: 'grace_period_allowed' },\r\n  { key: 'No Grace Period', value: 'no_grace_period' },\r\n];\r\n\r\n// File validation constants\r\nexport const FILE_VALIDATION_CONFIG = {\r\n  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif'],\r\n  MAX_SIZE: 10 * 1024 * 1024 // 10MB\r\n};\r\n"], "mappings": "AAQA;AACA,OAAO,MAAMA,cAAc,GAAG;EAC5BC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfC,UAAU,EAAE;IACV,CAAC,EAAE,kBAAkB;IACrB,CAAC,EAAE,sBAAsB;IACzB,CAAC,EAAE,kBAAkB;IACrB,CAAC,EAAE,mBAAmB;IACtB,CAAC,EAAE;;CAEN;AAED;AACA,OAAO,MAAMC,4BAA4B,GAAiB,CACxD;EAAEC,GAAG,EAAE,iCAAiC;EAAEC,KAAK,EAAE;AAAgC,CAAE,EACnF;EAAED,GAAG,EAAE,yBAAyB;EAAEC,KAAK,EAAE;AAAyB,CAAE,EACpE;EAAED,GAAG,EAAE,wBAAwB;EAAEC,KAAK,EAAE;AAAwB,CAAE,EAClE;EAAED,GAAG,EAAE,0BAA0B;EAAEC,KAAK,EAAE;AAA0B,CAAE,EACtE;EAAED,GAAG,EAAE,yBAAyB;EAAEC,KAAK,EAAE;AAAyB,CAAE,CACrE;AAED,OAAO,MAAMC,YAAY,GAAiB,CACxC;EAAEF,GAAG,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAY,CAAE,EAC1C;EAAED,GAAG,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAM,CAAE,EAC9B;EAAED,GAAG,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAU,CAAE,EACtC;EAAED,GAAG,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAS,CAAE,CACrC;AAED;AACA,OAAO,MAAME,mBAAmB,GAAiB,CAC/C;EAAEH,GAAG,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAQ,CAAE,EACxC;EAAED,GAAG,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAY,CAAE,EAC1C;EAAED,GAAG,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAU,CAAE,EACtC;EAAED,GAAG,EAAE,4BAA4B;EAAEC,KAAK,EAAE;AAA4B,CAAE,CAC3E;AAED,OAAO,MAAMG,8BAA8B,GAAiB,CAC1D;EAAEJ,GAAG,EAAE,kBAAkB;EAAEC,KAAK,EAAE;AAAkB,CAAE,EACtD;EAAED,GAAG,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAW,CAAE,CACzC;AAED,OAAO,MAAMI,gCAAgC,GAAiB,CAC5D;EAAEL,GAAG,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAc,CAAE,EAC9C;EAAED,GAAG,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAW,CAAE,EACxC;EAAED,GAAG,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAY,CAAE,EAC1C;EAAED,GAAG,EAAE,kBAAkB;EAAEC,KAAK,EAAE;AAAkB,CAAE,EACtD;EAAED,GAAG,EAAE,gBAAgB;EAAEC,KAAK,EAAE;AAAgB,CAAE,EAClD;EAAED,GAAG,EAAE,gBAAgB;EAAEC,KAAK,EAAE;AAAgB,CAAE,CACnD;AAED,OAAO,MAAMK,oCAAoC,GAAiB,CAChE;EAAEN,GAAG,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAY,CAAE,EAC1C;EAAED,GAAG,EAAE,kBAAkB;EAAEC,KAAK,EAAE;AAAkB,CAAE,EACtD;EAAED,GAAG,EAAE,gBAAgB;EAAEC,KAAK,EAAE;AAAgB,CAAE,EAClD;EAAED,GAAG,EAAE,gBAAgB;EAAEC,KAAK,EAAE;AAAgB,CAAE,CACnD;AAED,OAAO,MAAMM,kCAAkC,GAAiB,CAC9D;EAAEP,GAAG,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAa,CAAE,EAC5C;EAAED,GAAG,EAAE,oBAAoB;EAAEC,KAAK,EAAE;AAAoB,CAAE,EAC1D;EAAED,GAAG,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAa,CAAE,EAC5C;EAAED,GAAG,EAAE,gBAAgB;EAAEC,KAAK,EAAE;AAAgB,CAAE,CACnD;AAED,OAAO,MAAMO,yBAAyB,GAAiB,CACrD;EAAER,GAAG,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAe,CAAE,EAChD;EAAED,GAAG,EAAE,yBAAyB;EAAEC,KAAK,EAAE;AAAyB,CAAE,EACpE;EAAED,GAAG,EAAE,0BAA0B;EAAEC,KAAK,EAAE;AAA0B,CAAE,EACtE;EAAED,GAAG,EAAE,oCAAoC;EAAEC,KAAK,EAAE;AAAoC,CAAE,EAC1F;EAAED,GAAG,EAAE,gBAAgB;EAAEC,KAAK,EAAE;AAAgB,CAAE,CACnD;AAED,OAAO,MAAMQ,sBAAsB,GAAiB,CAClD;EAAET,GAAG,EAAE,qBAAqB;EAAEC,KAAK,EAAE;AAAqB,CAAE,EAC5D;EAAED,GAAG,EAAE,iBAAiB;EAAEC,KAAK,EAAE;AAAiB,CAAE,EACpD;EAAED,GAAG,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAc,CAAE,EAC9C;EAAED,GAAG,EAAE,+BAA+B;EAAEC,KAAK,EAAE;AAA+B,CAAE,EAChF;EAAED,GAAG,EAAE,wCAAwC;EAAEC,KAAK,EAAE;AAAuC,CAAE,CAClG;AAED,OAAO,MAAMS,8BAA8B,GAAiB,CAC1D;EAAEV,GAAG,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAc,CAAE,EAC9C;EAAED,GAAG,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAQ,CAAE,EAClC;EAAED,GAAG,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAc,CAAE,EAC9C;EAAED,GAAG,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAe,CAAE,EAChD;EAAED,GAAG,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAa,CAAE,EAC5C;EAAED,GAAG,EAAE,gBAAgB;EAAEC,KAAK,EAAE;AAAgB,CAAE,CACnD;AAED,OAAO,MAAMU,qBAAqB,GAAiB,CACjD;EAAEX,GAAG,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAY,CAAE,EAC1C;EAAED,GAAG,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAS,CAAE,EACpC;EAAED,GAAG,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAa,CAAE,EAC5C;EAAED,GAAG,EAAE,sBAAsB;EAAEC,KAAK,EAAE;AAAsB,CAAE,EAC9D;EAAED,GAAG,EAAE,iBAAiB;EAAEC,KAAK,EAAE;AAAiB,CAAE,EACpD;EAAED,GAAG,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAY,CAAE,EAC1C;EAAED,GAAG,EAAE,mBAAmB;EAAEC,KAAK,EAAE;AAAmB,CAAE,EACxD;EAAED,GAAG,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAY,CAAE,EAC1C;EAAED,GAAG,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAe,CAAE,EAChD;EAAED,GAAG,EAAE,iBAAiB;EAAEC,KAAK,EAAE;AAAiB,CAAE,EACpD;EAAED,GAAG,EAAE,mBAAmB;EAAEC,KAAK,EAAE;AAAmB,CAAE,EACxD;EAAED,GAAG,EAAE,qCAAqC;EAAEC,KAAK,EAAE;AAAqC,CAAE,EAC5F;EAAED,GAAG,EAAE,iCAAiC;EAAEC,KAAK,EAAE;AAAiC,CAAE,EACpF;EAAED,GAAG,EAAE,uBAAuB;EAAEC,KAAK,EAAE;AAAuB,CAAE,EAChE;EAAED,GAAG,EAAE,mBAAmB;EAAEC,KAAK,EAAE;AAAmB,CAAE,EACxD;EAAED,GAAG,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAS,CAAE,EACpC;EAAED,GAAG,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAQ,CAAE,EAClC;EAAED,GAAG,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAW,CAAE,EACxC;EAAED,GAAG,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAW,CAAE,EACxC;EAAED,GAAG,EAAE,gBAAgB;EAAEC,KAAK,EAAE;AAAgB,CAAE,EAClD;EAAED,GAAG,EAAE,kBAAkB;EAAEC,KAAK,EAAE;AAAkB,CAAE,EACtD;EAAED,GAAG,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAO,CAAE,EAChC;EAAED,GAAG,EAAE,gBAAgB;EAAEC,KAAK,EAAE;AAAgB,CAAE,EAClD;EAAED,GAAG,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAc,CAAE,EAC9C;EAAED,GAAG,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAa,CAAE,EAC5C;EAAED,GAAG,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAY,CAAE,EAC1C;EAAED,GAAG,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAU,CAAE,EACtC;EAAED,GAAG,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAY,CAAE,EAC1C;EAAED,GAAG,EAAE,kBAAkB;EAAEC,KAAK,EAAE;AAAkB,CAAE,EACtD;EAAED,GAAG,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAO,CAAE,EAChC;EAAED,GAAG,EAAE,gBAAgB;EAAEC,KAAK,EAAE;AAAgB,CAAE,EAClD;EAAED,GAAG,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAa,CAAE,CAC7C;AAED,OAAO,MAAMW,uBAAuB,GAAiB,CACnD;EAAEZ,GAAG,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAS,CAAE,EACpC;EAAED,GAAG,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAW,CAAE,EACxC;EAAED,GAAG,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAe,CAAE,EAChD;EAAED,GAAG,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAU,CAAE,CACvC;AAED,OAAO,MAAMY,sBAAsB,GAAiB,CAClD;EAAEb,GAAG,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAM,CAAE,EAC9B;EAAED,GAAG,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAa,CAAE,EAC5C;EAAED,GAAG,EAAE,MAAM;EAAEC,KAAK,EAAE;AAA+B,CAAE,CACxD;AAED,OAAO,MAAMa,gCAAgC,GAAiB,CAC5D;EAAEd,GAAG,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAW,CAAE,EACxC;EAAED,GAAG,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAY,CAAE,EAC1C;EAAED,GAAG,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAc,CAAE,CAC/C;AAED,OAAO,MAAMc,sBAAsB,GAAiB,CAClD;EAAEf,GAAG,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAO,CAAE,EAChC;EAAED,GAAG,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAa,CAAE,EAC5C;EAAED,GAAG,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAK,CAAE,EAC5B;EAAED,GAAG,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAO,CAAE,EAChC;EAAED,GAAG,EAAE,sBAAsB;EAAEC,KAAK,EAAE;AAAsB,CAAE,CAC/D;AAED,OAAO,MAAMe,yBAAyB,GAAiB,CACrD;EAAEhB,GAAG,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAa,CAAE,EAC5C;EAAED,GAAG,EAAE,iCAAiC;EAAEC,KAAK,EAAE;AAAiC,CAAE,EACpF;EAAED,GAAG,EAAE,oCAAoC;EAAEC,KAAK,EAAE;AAAoC,CAAE,CAC3F;AAED,OAAO,MAAMgB,6BAA6B,GAAiB,CACzD;EAAEjB,GAAG,EAAE,oBAAoB;EAAEC,KAAK,EAAE;AAAoB,CAAE,EAC1D;EAAED,GAAG,EAAE,oBAAoB;EAAEC,KAAK,EAAE;AAAoB,CAAE,CAC3D;AAED,OAAO,MAAMiB,8BAA8B,GAAiB,CAC1D;EAAElB,GAAG,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAc,CAAE,EAC9C;EAAED,GAAG,EAAE,4CAA4C;EAAEC,KAAK,EAAE;AAA4C,CAAE,CAC3G;AAED,OAAO,MAAMkB,0BAA0B,GAAiB,CACtD;EAAEnB,GAAG,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAU,CAAE,EACtC;EAAED,GAAG,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAY,CAAE,EAC1C;EAAED,GAAG,EAAE,yBAAyB;EAAEC,KAAK,EAAE;AAAyB,CAAE,CACrE;AAED,OAAO,MAAMmB,+BAA+B,GAAiB,CAC3D;EAAEpB,GAAG,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAU,CAAE,EACtC;EAAED,GAAG,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAc,CAAE,EAC9C;EAAED,GAAG,EAAE,4BAA4B;EAAEC,KAAK,EAAE;AAA4B,CAAE,CAC3E;AAED,OAAO,MAAMoB,8BAA8B,GAAiB,CAC1D;EAAErB,GAAG,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAU,CAAE,EACtC;EAAED,GAAG,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAe,CAAE,EAChD;EAAED,GAAG,EAAE,kBAAkB;EAAEC,KAAK,EAAE;AAAkB,CAAE,EACtD;EAAED,GAAG,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAW,CAAE,EACxC;EAAED,GAAG,EAAE,iBAAiB;EAAEC,KAAK,EAAE;AAAiB,CAAE,CACrD;AAED,OAAO,MAAMqB,uBAAuB,GAAiB,CACnD;EAAEtB,GAAG,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAY,CAAE,EAC1C;EAAED,GAAG,EAAE,uBAAuB;EAAEC,KAAK,EAAE;AAAuB,CAAE,EAChE;EAAED,GAAG,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAQ,CAAE,EAClC;EAAED,GAAG,EAAE,oBAAoB;EAAEC,KAAK,EAAE;AAAoB,CAAE,EAC1D;EAAED,GAAG,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAQ,CAAE,EAClC;EAAED,GAAG,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAa,CAAE,EAC5C;EAAED,GAAG,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAQ,CAAE,EAClC;EAAED,GAAG,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAa,CAAE,EAC5C;EAAED,GAAG,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAW,CAAE,CACzC;AAED,OAAO,MAAMsB,yBAAyB,GAAiB,CACrD;EAAEvB,GAAG,EAAE,iBAAiB;EAAEC,KAAK,EAAE;AAAiB,CAAE,EACpD;EAAED,GAAG,EAAE,gBAAgB;EAAEC,KAAK,EAAE;AAAgB,CAAE,EAClD;EAAED,GAAG,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAW,CAAE,EACxC;EAAED,GAAG,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAW,CAAE,CACzC;AAED,OAAO,MAAMuB,yBAAyB,GAAiB,CACrD;EAAExB,GAAG,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAQ,CAAE,EAClC;EAAED,GAAG,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAW,CAAE,EACxC;EAAED,GAAG,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAM,CAAE,EAC9B;EAAED,GAAG,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAS,CAAE,EACpC;EAAED,GAAG,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAU,CAAE,EACtC;EAAED,GAAG,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAe,CAAE,CACjD;AAED,OAAO,MAAMwB,+BAA+B,GAAiB,CAC3D;EAAEzB,GAAG,EAAE,sBAAsB;EAAEC,KAAK,EAAE;AAAsB,CAAE,EAC9D;EAAED,GAAG,EAAE,iBAAiB;EAAEC,KAAK,EAAE;AAAiB,CAAE,CACrD;AAED;AACA,OAAO,MAAMyB,sBAAsB,GAAG;EACpCC,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC;EACvDC,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;CAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}