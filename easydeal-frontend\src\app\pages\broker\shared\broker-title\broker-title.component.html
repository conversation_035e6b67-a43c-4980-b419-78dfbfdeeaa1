<div class="card mb-1 mb-xl-1" *ngIf="user?.role === 'broker'">
  <div class="card-body pt-5 pb-0">
    <div class="row align-items-center mb-3">
      <div class="col-12 col-sm-auto mb-4 mb-sm-0 text-center text-sm-start">
        <div class="symbol symbol-65px symbol-fixed position-relative mx-auto mx-sm-0">
          <img [src]="user.image" alt="User" />
        </div>
      </div>

      <div class="col">
        <div class="row justify-content-between">
          <div class="col-12 col-lg-9">
            <div class="d-flex align-items-center flex-wrap mb-2" *ngIf="user?.fullName">
              <span class="text-gray-800 fs-2 fw-bolder me-1">Hello -</span>
              <a [routerLink]="['/broker/profile']" class="text-gray-800 text-hover-dark-blue fs-2 fw-semibold me-1">
                {{ capitalizeWords(user?.fullName) }}
              </a>
            </div>

            <div class="row">
              <div class="col-12 d-flex flex-wrap">
                <span class="me-2 mb-2 fw-bolder fs-6 py-2 px-3 badge"
                  [ngClass]="getAccountTypeBadge(user.accountType)">
                  {{ capitalizeWords(user?.accountType) }}
                </span>

                <ng-container *ngFor="let specialization of user?.specializationScopes">
                  <span class="me-2 mb-2 fw-bolder fs-6 py-2 px-3 badge badge-mid-blue">
                    {{ specialization?.specialization }}
                  </span>
                </ng-container>

                <ng-container *ngFor="let area of user?.areas">
                  <span class="me-2 mb-2 fw-bolder fs-6 py-2 px-3 badge badge-light-dark-blue text-dark-blue">
                    {{ area?.name_en }}
                  </span>
                </ng-container>
              </div>
            </div>
          </div>

          <div class="col-12 col-lg-auto mt-3 mt-lg-0 text-lg-end" *ngIf="showCreateButton">
            <a [routerLink]="['/broker/stepper-modal']"
              class="btn btn-sm btn-dark-blue btn-active-light-dark-blue mt-9">
              <i class="fa-solid fa-plus me-1"></i>
              Create New Request
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="card mb-1 mb-xl-1" *ngIf="user?.role === 'client'">
  <div class="card-body pt-5 pb-0">
    <div class="row align-items-center mb-3">
      <div class="col-12 col-sm-auto mb-4 mb-sm-0 text-center text-sm-start">
        <div class="symbol symbol-65px symbol-fixed position-relative mx-auto mx-sm-0">
          <img [src]="user.image" alt="Profile Image" *ngIf="user.image" />
        </div>
      </div>

      <div class="col">
        <div class="row justify-content-between">
          <div class="col-12 col-lg-9">
            <div class="d-flex align-items-center flex-wrap mb-2" *ngIf="user?.fullName">
              <span class="text-gray-800 fs-2 fw-bolder me-1">Hello -</span>
              <a [routerLink]="['/broker/profile']" class="text-gray-800 text-hover-dark-blue fs-2 fw-semibold me-1">
                {{ capitalizeWords(user?.fullName) }}
              </a>
            </div>
          </div>

          <div class="col-12 col-lg-auto" *ngIf="showCreateButton">
            <a [routerLink]="['/broker/stepper-modal']"
              class="btn btn-sm btn-dark-blue btn-active-light-dark-blue mt-0">
              <i class="fa-solid fa-plus me-1"></i>
              Create New Request
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
