{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { BaseConfigService } from '../base-config.service';\nimport { UNIT_VIEW_TYPES_OPTIONS, UNIT_FACING_TYPES_OPTIONS, FINISHING_STATUS_TYPES_OPTIONS, FURNISHING_STATUS_OPTIONS, OTHER_ACCESSORIES_OPTIONS, RENT_RECURRENCE_OPTIONS, REQUIRED_INSURANCE_TYPES_OPTIONS, OTHER_EXPENSES_OPTIONS, FLOOR_TYPES_OPTIONS, UNIT_LAYOUT_STATUS_TYPES_OPTIONS, BUILDING_LAYOUT_STATUS_TYPES_OPTIONS, UNIT_DESIGN_TYPES_OPTIONS, FIT_OUT_CONDITION_TYPES_OPTIONS, ACTIVITY_TYPES_OPTIONS } from '../../stepper-modal.constants';\nimport * as i0 from \"@angular/core\";\nexport class RentalOutsideCompoundConfigService extends BaseConfigService {\n  // ============================================================================\n  // RENT-OUT CONFIGURATIONS (Property owner looking to rent out)\n  // ============================================================================\n  /**\n   * Create rental-specific location inputs for rent-out outside compound scenarios\n   */\n  createRentOutOutsideCompoundLocationInputs(stepperModal) {\n    return [{\n      step: 2,\n      name: 'locationSuggestions',\n      type: 'checkbox',\n      label: 'Location Suggestion',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 2,\n      name: 'cityId',\n      type: 'select',\n      label: 'City',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'areaId',\n      type: 'select',\n      label: 'Area',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'subAreaId',\n      type: 'select',\n      label: 'Sub Area',\n      options: [],\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'detailedAddress',\n      type: 'text',\n      label: 'Detailed Address',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'addressLink',\n      type: 'url',\n      label: 'Address Link',\n      validators: [Validators.pattern(/^https?:\\/\\/.+/)],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rental-specific location inputs for rent-out outside compound administrative units (includes mallName)\n   */\n  createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal) {\n    return [{\n      step: 2,\n      name: 'cityId',\n      type: 'select',\n      label: 'City',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'areaId',\n      type: 'select',\n      label: 'Area',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'subAreaId',\n      type: 'select',\n      label: 'Sub Area',\n      options: [],\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'detailedAddress',\n      type: 'text',\n      label: 'Detailed Address',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'addressLink',\n      type: 'url',\n      label: 'Address Link',\n      validators: [Validators.pattern(/^https?:\\/\\/.+/)],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'mallName',\n      type: 'text',\n      label: 'Mall Name',\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out outside compound unit information inputs for apartments and duplexes\n   */\n  createRentOutOutsideCompoundUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor Number',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitFacing',\n      type: 'select',\n      label: 'Unit Facing',\n      options: UNIT_FACING_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out outside compound unit information inputs for studios (without rooms)\n   */\n  createRentOutOutsideCompoundStudiosUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'text',\n      label: 'Floor',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitFacing',\n      type: 'select',\n      label: 'Unit Facing',\n      options: UNIT_FACING_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out outside compound unit information inputs for penthouses (without floor)\n   */\n  createRentOutOutsideCompoundPenthousesUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitFacing',\n      type: 'select',\n      label: 'Unit Facing',\n      options: UNIT_FACING_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out outside compound unit information inputs for basements\n   */\n  createRentOutOutsideCompoundBasementsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitLayoutStatus',\n      type: 'select',\n      label: 'Unit Layout Status',\n      options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out outside compound unit information inputs for roofs\n   */\n  createRentOutOutsideCompoundRoofsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitLayoutStatus',\n      type: 'select',\n      label: 'Unit Layout Status',\n      options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingLayoutStatus',\n      type: 'select',\n      label: 'Building Layout Status',\n      options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out outside compound unit information inputs for standalone villas\n   */\n  createRentOutOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitDesign',\n      type: 'select',\n      label: 'Unit Design',\n      options: UNIT_DESIGN_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out outside compound unit information inputs for administrative units\n   */\n  createRentOutOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out outside compound unit information inputs for medical clinics\n   */\n  createRentOutOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out outside compound unit information inputs for pharmacies\n   */\n  createRentOutOutsideCompoundPharmaciesUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Fit Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out outside compound unit information inputs for commercial stores\n   */\n  createRentOutOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Fit Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity Type',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out outside compound unit information inputs for industrial properties\n   * (factory_lands, factories, warehouses, warehouse_lands)\n   */\n  createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingLayoutStatus',\n      type: 'select',\n      label: 'Building Layout Status',\n      options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Fit Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity Type',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out outside compound unit information inputs for commercial administrative buildings\n   */\n  createRentOutOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingLayoutStatus',\n      type: 'select',\n      label: 'Building Layout Status',\n      options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity Type',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out outside compound financial inputs\n   */\n  createRentOutOutsideCompoundFinancialInputs(stepperModal) {\n    return [{\n      step: 5,\n      name: 'unitPrice',\n      type: 'number',\n      label: 'Unit Price',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'unitPriceSuggestions',\n      type: 'checkbox',\n      label: 'Unit Price Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'rentRecurrence',\n      type: 'select',\n      label: 'Rent Recurrence',\n      options: RENT_RECURRENCE_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'requiredInsurance',\n      type: 'select',\n      label: 'Required Insurance',\n      options: REQUIRED_INSURANCE_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'otherExpenses',\n      type: 'multiSelect',\n      label: 'Other Expenses (Tenant Responsibility)',\n      options: OTHER_EXPENSES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create media inputs for step 4\n   */\n  createMediaInputs() {\n    return [{\n      step: 4,\n      name: 'mainImage',\n      type: 'file',\n      label: 'Main Image',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 4,\n      name: 'galleryImages',\n      type: 'file',\n      label: 'Gallery Images',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 4,\n      name: 'video',\n      type: 'file',\n      label: 'Video',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  // ============================================================================\n  // SPECIFIC CONFIGURATIONS\n  // ============================================================================\n  createRentOutOutsideCompoundApartmentsConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundDuplexesConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundStudiosConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundStudiosUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundPenthousesConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundPenthousesUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundBasementsConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundBasementsUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundRoofsConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundRoofsUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundStandaloneVillasConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundAdministrativeUnitsConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundMedicalClinicsConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundPharmaciesConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundPharmaciesUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundCommercialStoresConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundFactoryLandsConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundFactoriesConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundWarehousesConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundWarehouseLandsConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal) {\n    const config = [...this.createRentOutOutsideCompoundLocationInputs(stepperModal), ...this.createRentOutOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  // ============================================================================\n  // RENT-IN CONFIGURATIONS (Property seeker looking to rent)\n  // ============================================================================\n  /**\n   * Create rental-specific location inputs for rent-in outside compound scenarios\n   */\n  createRentInOutsideCompoundLocationInputs(stepperModal) {\n    return [{\n      step: 2,\n      name: 'cityId',\n      type: 'select',\n      label: 'City',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'areaId',\n      type: 'select',\n      label: 'Area',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'subAreaId',\n      type: 'select',\n      label: 'Sub Area',\n      options: [],\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'locationSuggestions',\n      type: 'checkbox',\n      label: 'Location Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }];\n  }\n  /**\n   * Create rent-in outside compound unit information inputs for apartments and duplexes\n   */\n  createRentInOutsideCompoundUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor Number',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-in outside compound financial inputs\n   */\n  createRentInOutsideCompoundFinancialInputs(stepperModal) {\n    return [{\n      step: 5,\n      name: 'averageUnitPriceMin',\n      type: 'number',\n      label: 'Minimum Average Unit Price',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'averageUnitPriceMax',\n      type: 'number',\n      label: 'Maximum Average Unit Price',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'unitPriceSuggestions',\n      type: 'checkbox',\n      label: 'Unit Price Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'rentRecurrence',\n      type: 'select',\n      label: 'Rent Recurrence',\n      options: RENT_RECURRENCE_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Configuration for rent-in outside compound apartments\n   */\n  createRentInOutsideCompoundApartmentsConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Configuration for rent-in outside compound duplexes\n   */\n  createRentInOutsideCompoundDuplexesConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Create rent-in outside compound unit information inputs for studios\n   */\n  createRentInOutsideCompoundStudiosUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor Number',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Configuration for rent-in outside compound studios\n   */\n  createRentInOutsideCompoundStudiosConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundStudiosUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Create rent-in outside compound unit information inputs for penthouses\n   */\n  createRentInOutsideCompoundPenthousesUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0.01)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Configuration for rent-in outside compound penthouses\n   */\n  createRentInOutsideCompoundPenthousesConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundPenthousesUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Create rent-in outside compound unit information inputs for basements\n   */\n  createRentInOutsideCompoundBasementsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0.01)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitLayoutStatus',\n      type: 'select',\n      label: 'Unit Layout Status',\n      options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Configuration for rent-in outside compound basements\n   */\n  createRentInOutsideCompoundBasementsConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundBasementsUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Create rent-in outside compound unit information inputs for roofs\n   */\n  createRentInOutsideCompoundRoofsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0.01)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitLayoutStatus',\n      type: 'select',\n      label: 'Unit Layout Status',\n      options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingLayoutStatus',\n      type: 'select',\n      label: 'Building Layout Status',\n      options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Configuration for rent-in outside compound roofs\n   */\n  createRentInOutsideCompoundRoofsConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundRoofsUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Create rent-in outside compound unit information inputs for standalone villas\n   */\n  createRentInOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'groundAreaMin',\n      type: 'number',\n      label: 'Minimum Ground Area (m²)',\n      validators: [Validators.required, Validators.min(0.01)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundAreaMax',\n      type: 'number',\n      label: 'Maximum Ground Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingAreaMin',\n      type: 'number',\n      label: 'Minimum Building Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingAreaMax',\n      type: 'number',\n      label: 'Maximum Building Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitDesign',\n      type: 'select',\n      label: 'Unit Design',\n      options: UNIT_DESIGN_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Configuration for rent-in outside compound standalone villas\n   */\n  createRentInOutsideCompoundStandaloneVillasConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Create rent-in outside compound unit information inputs for administrative units\n   */\n  createRentInOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0.01)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-in outside compound unit information inputs for medical clinics\n   */\n  createRentInOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0.01)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Configuration for rent-in outside compound administrative units\n   */\n  createRentInOutsideCompoundAdministrativeUnitsConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Configuration for rent-in outside compound medical clinics\n   */\n  createRentInOutsideCompoundMedicalClinicsConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Create rent-in outside compound unit information inputs for pharmacies\n   */\n  createRentInOutsideCompoundPharmaciesUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0.01)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Fit Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Configuration for rent-in outside compound pharmacies\n   */\n  createRentInOutsideCompoundPharmaciesConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundPharmaciesUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Create rent-in outside compound unit information inputs for commercial stores\n   */\n  createRentInOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0.01)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Fit Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Configuration for rent-in outside compound commercial stores\n   */\n  createRentInOutsideCompoundCommercialStoresConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Create rent-in outside compound unit information inputs for industrial properties\n   * (factory_lands, factories, warehouses, warehouse_lands, commercial_administrative_buildings)\n   */\n  createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'groundAreaMin',\n      type: 'number',\n      label: 'Minimum Ground Area (m²)',\n      validators: [Validators.required, Validators.min(0.01)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundAreaMax',\n      type: 'number',\n      label: 'Maximum Ground Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingAreaMin',\n      type: 'number',\n      label: 'Minimum Building Area (m²)',\n      validators: [Validators.required, Validators.min(0.01)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingAreaMax',\n      type: 'number',\n      label: 'Maximum Building Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingLayoutStatus',\n      type: 'select',\n      label: 'Building Layout Status',\n      options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Fit Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Configuration for rent-in outside compound factory lands\n   */\n  createRentInOutsideCompoundFactoryLandsConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Configuration for rent-in outside compound factories\n   */\n  createRentInOutsideCompoundFactoriesConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Configuration for rent-in outside compound warehouses\n   */\n  createRentInOutsideCompoundWarehousesConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Configuration for rent-in outside compound warehouse lands\n   */\n  createRentInOutsideCompoundWarehouseLandsConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Configuration for rent-in outside compound commercial administrative buildings\n   */\n  createRentInOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal) {\n    const config = [...this.createRentInOutsideCompoundLocationInputs(stepperModal), ...this.createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal)];\n    // No media inputs for step 4 in rent-in scenarios\n    // Add financial inputs for step 5\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\n    return config;\n  }\n  // ============================================================================\n  // PUBLIC API\n  // ============================================================================\n  /**\n   * Get input configurations for rental outside compound cases\n   */\n  getInputConfigs(stepperModal) {\n    return [\n    // RENT-OUT OUTSIDE COMPOUND CONFIGURATIONS\n    {\n      key: 'rentals_outside_compound_rent_out_apartments',\n      value: this.createRentOutOutsideCompoundApartmentsConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_duplexes',\n      value: this.createRentOutOutsideCompoundDuplexesConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_studios',\n      value: this.createRentOutOutsideCompoundStudiosConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_penthouses',\n      value: this.createRentOutOutsideCompoundPenthousesConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_basements',\n      value: this.createRentOutOutsideCompoundBasementsConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_roofs',\n      value: this.createRentOutOutsideCompoundRoofsConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_standalone_villas',\n      value: this.createRentOutOutsideCompoundStandaloneVillasConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_administrative_units',\n      value: this.createRentOutOutsideCompoundAdministrativeUnitsConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_medical_clinics',\n      value: this.createRentOutOutsideCompoundMedicalClinicsConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_pharmacies',\n      value: this.createRentOutOutsideCompoundPharmaciesConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_commercial_stores',\n      value: this.createRentOutOutsideCompoundCommercialStoresConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_factory_lands',\n      value: this.createRentOutOutsideCompoundFactoryLandsConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_factories',\n      value: this.createRentOutOutsideCompoundFactoriesConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_warehouses',\n      value: this.createRentOutOutsideCompoundWarehousesConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_warehouse_lands',\n      value: this.createRentOutOutsideCompoundWarehouseLandsConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_out_commercial_administrative_buildings',\n      value: this.createRentOutOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal)\n    },\n    // RENT-IN OUTSIDE COMPOUND CONFIGURATIONS\n    {\n      key: 'rentals_outside_compound_rent_in_apartments',\n      value: this.createRentInOutsideCompoundApartmentsConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_duplexes',\n      value: this.createRentInOutsideCompoundDuplexesConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_studios',\n      value: this.createRentInOutsideCompoundStudiosConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_penthouses',\n      value: this.createRentInOutsideCompoundPenthousesConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_basements',\n      value: this.createRentInOutsideCompoundBasementsConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_roofs',\n      value: this.createRentInOutsideCompoundRoofsConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_standalone_villas',\n      value: this.createRentInOutsideCompoundStandaloneVillasConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_administrative_units',\n      value: this.createRentInOutsideCompoundAdministrativeUnitsConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_medical_clinics',\n      value: this.createRentInOutsideCompoundMedicalClinicsConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_pharmacies',\n      value: this.createRentInOutsideCompoundPharmaciesConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_commercial_stores',\n      value: this.createRentInOutsideCompoundCommercialStoresConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_factory_lands',\n      value: this.createRentInOutsideCompoundFactoryLandsConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_factories',\n      value: this.createRentInOutsideCompoundFactoriesConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_warehouses',\n      value: this.createRentInOutsideCompoundWarehousesConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_warehouse_lands',\n      value: this.createRentInOutsideCompoundWarehouseLandsConfig(stepperModal)\n    }, {\n      key: 'rentals_outside_compound_rent_in_commercial_administrative_buildings',\n      value: this.createRentInOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal)\n    }];\n  }\n  /**\n   * Get all available rental outside compound configuration keys\n   */\n  getRentalOutsideCompoundConfigurationKeys() {\n    return [\n    // RENT-OUT OUTSIDE COMPOUND KEYS\n    'rentals_outside_compound_rent_out_apartments', 'rentals_outside_compound_rent_out_duplexes', 'rentals_outside_compound_rent_out_studios', 'rentals_outside_compound_rent_out_penthouses', 'rentals_outside_compound_rent_out_basements', 'rentals_outside_compound_rent_out_roofs', 'rentals_outside_compound_rent_out_standalone_villas', 'rentals_outside_compound_rent_out_administrative_units', 'rentals_outside_compound_rent_out_medical_clinics', 'rentals_outside_compound_rent_out_pharmacies', 'rentals_outside_compound_rent_out_commercial_stores', 'rentals_outside_compound_rent_out_factory_lands', 'rentals_outside_compound_rent_out_factories', 'rentals_outside_compound_rent_out_warehouses', 'rentals_outside_compound_rent_out_warehouse_lands', 'rentals_outside_compound_rent_out_commercial_administrative_buildings',\n    // RENT-IN OUTSIDE COMPOUND KEYS\n    'rentals_outside_compound_rent_in_apartments', 'rentals_outside_compound_rent_in_duplexes', 'rentals_outside_compound_rent_in_studios', 'rentals_outside_compound_rent_in_penthouses', 'rentals_outside_compound_rent_in_basements', 'rentals_outside_compound_rent_in_roofs', 'rentals_outside_compound_rent_in_standalone_villas', 'rentals_outside_compound_rent_in_administrative_units', 'rentals_outside_compound_rent_in_medical_clinics', 'rentals_outside_compound_rent_in_pharmacies', 'rentals_outside_compound_rent_in_commercial_stores', 'rentals_outside_compound_rent_in_factory_lands', 'rentals_outside_compound_rent_in_factories', 'rentals_outside_compound_rent_in_warehouses', 'rentals_outside_compound_rent_in_warehouse_lands', 'rentals_outside_compound_rent_in_commercial_administrative_buildings'];\n  }\n  /**\n   * Check if a key is rent-out outside compound configuration\n   */\n  isRentOutOutsideCompoundConfiguration(key) {\n    return key.includes('rentals_outside_compound_rent_out_');\n  }\n  /**\n   * Check if a key is rent-in outside compound configuration\n   */\n  isRentInOutsideCompoundConfiguration(key) {\n    return key.includes('rentals_outside_compound_rent_in_');\n  }\n  static ɵfac = /*@__PURE__*/(() => {\n    let ɵRentalOutsideCompoundConfigService_BaseFactory;\n    return function RentalOutsideCompoundConfigService_Factory(__ngFactoryType__) {\n      return (ɵRentalOutsideCompoundConfigService_BaseFactory || (ɵRentalOutsideCompoundConfigService_BaseFactory = i0.ɵɵgetInheritedFactory(RentalOutsideCompoundConfigService)))(__ngFactoryType__ || RentalOutsideCompoundConfigService);\n    };\n  })();\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: RentalOutsideCompoundConfigService,\n    factory: RentalOutsideCompoundConfigService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["Validators", "BaseConfigService", "UNIT_VIEW_TYPES_OPTIONS", "UNIT_FACING_TYPES_OPTIONS", "FINISHING_STATUS_TYPES_OPTIONS", "FURNISHING_STATUS_OPTIONS", "OTHER_ACCESSORIES_OPTIONS", "RENT_RECURRENCE_OPTIONS", "REQUIRED_INSURANCE_TYPES_OPTIONS", "OTHER_EXPENSES_OPTIONS", "FLOOR_TYPES_OPTIONS", "UNIT_LAYOUT_STATUS_TYPES_OPTIONS", "BUILDING_LAYOUT_STATUS_TYPES_OPTIONS", "UNIT_DESIGN_TYPES_OPTIONS", "FIT_OUT_CONDITION_TYPES_OPTIONS", "ACTIVITY_TYPES_OPTIONS", "RentalOutsideCompoundConfigService", "createRentOutOutsideCompoundLocationInputs", "stepperModal", "step", "name", "type", "label", "validators", "visibility", "isClient", "options", "required", "pattern", "createRentOutOutsideCompoundAdministrativeUnitsLocationInputs", "createRentOutOutsideCompoundUnitInformationInputs", "min", "createRentOutOutsideCompoundStudiosUnitInformationInputs", "createRentOutOutsideCompoundPenthousesUnitInformationInputs", "createRentOutOutsideCompoundBasementsUnitInformationInputs", "createRentOutOutsideCompoundRoofsUnitInformationInputs", "createRentOutOutsideCompoundStandaloneVillasUnitInformationInputs", "createRentOutOutsideCompoundAdministrativeUnitsUnitInformationInputs", "createRentOutOutsideCompoundMedicalClinicsUnitInformationInputs", "createRentOutOutsideCompoundPharmaciesUnitInformationInputs", "createRentOutOutsideCompoundCommercialStoresUnitInformationInputs", "createRentOutOutsideCompoundIndustrialUnitInformationInputs", "createRentOutOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs", "createRentOutOutsideCompoundFinancialInputs", "createMediaInputs", "createRentOutOutsideCompoundApartmentsConfig", "config", "push", "createRentOutOutsideCompoundDuplexesConfig", "createRentOutOutsideCompoundStudiosConfig", "createRentOutOutsideCompoundPenthousesConfig", "createRentOutOutsideCompoundBasementsConfig", "createRentOutOutsideCompoundRoofsConfig", "createRentOutOutsideCompoundStandaloneVillasConfig", "createRentOutOutsideCompoundAdministrativeUnitsConfig", "createRentOutOutsideCompoundMedicalClinicsConfig", "createRentOutOutsideCompoundPharmaciesConfig", "createRentOutOutsideCompoundCommercialStoresConfig", "createRentOutOutsideCompoundFactoryLandsConfig", "createRentOutOutsideCompoundFactoriesConfig", "createRentOutOutsideCompoundWarehousesConfig", "createRentOutOutsideCompoundWarehouseLandsConfig", "createRentOutOutsideCompoundCommercialAdministrativeBuildingsConfig", "createRentInOutsideCompoundLocationInputs", "createRentInOutsideCompoundUnitInformationInputs", "createRentInOutsideCompoundFinancialInputs", "createRentInOutsideCompoundApartmentsConfig", "createRentInOutsideCompoundDuplexesConfig", "createRentInOutsideCompoundStudiosUnitInformationInputs", "createRentInOutsideCompoundStudiosConfig", "createRentInOutsideCompoundPenthousesUnitInformationInputs", "createRentInOutsideCompoundPenthousesConfig", "createRentInOutsideCompoundBasementsUnitInformationInputs", "createRentInOutsideCompoundBasementsConfig", "createRentInOutsideCompoundRoofsUnitInformationInputs", "createRentInOutsideCompoundRoofsConfig", "createRentInOutsideCompoundStandaloneVillasUnitInformationInputs", "createRentInOutsideCompoundStandaloneVillasConfig", "createRentInOutsideCompoundAdministrativeUnitsUnitInformationInputs", "createRentInOutsideCompoundMedicalClinicsUnitInformationInputs", "createRentInOutsideCompoundAdministrativeUnitsConfig", "createRentInOutsideCompoundMedicalClinicsConfig", "createRentInOutsideCompoundPharmaciesUnitInformationInputs", "createRentInOutsideCompoundPharmaciesConfig", "createRentInOutsideCompoundCommercialStoresUnitInformationInputs", "createRentInOutsideCompoundCommercialStoresConfig", "createRentInOutsideCompoundIndustrialUnitInformationInputs", "createRentInOutsideCompoundFactoryLandsConfig", "createRentInOutsideCompoundFactoriesConfig", "createRentInOutsideCompoundWarehousesConfig", "createRentInOutsideCompoundWarehouseLandsConfig", "createRentInOutsideCompoundCommercialAdministrativeBuildingsConfig", "getInputConfigs", "key", "value", "getRentalOutsideCompoundConfigurationKeys", "isRentOutOutsideCompoundConfiguration", "includes", "isRentInOutsideCompoundConfiguration", "__ngFactoryType__", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\shared\\stepper-modal\\services\\outside-compound\\rental-outside-compound-config.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Validators } from '@angular/forms';\r\nimport { BaseConfigService, InputConfig, StepperConfiguration } from '../base-config.service';\r\nimport {\r\n  UNIT_VIEW_TYPES_OPTIONS,\r\n  UNIT_FACING_TYPES_OPTIONS,\r\n  FINISHING_STATUS_TYPES_OPTIONS,\r\n  FURNISHING_STATUS_OPTIONS,\r\n  OTHER_ACCESSORIES_OPTIONS,\r\n  RENT_RECURRENCE_OPTIONS,\r\n  REQUIRED_INSURANCE_TYPES_OPTIONS,\r\n  OTHER_EXPENSES_OPTIONS,\r\n  FLOOR_TYPES_OPTIONS,\r\n  UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n  BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n  UNIT_DESIGN_TYPES_OPTIONS,\r\n  FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n  ACTIVITY_TYPES_OPTIONS,\r\n} from '../../stepper-modal.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class RentalOutsideCompoundConfigService extends BaseConfigService {\r\n\r\n  // ============================================================================\r\n  // RENT-OUT CONFIGURATIONS (Property owner looking to rent out)\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Create rental-specific location inputs for rent-out outside compound scenarios\r\n   */\r\n  private createRentOutOutsideCompoundLocationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 2,\r\n        name: 'locationSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Location Suggestion',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'cityId',\r\n        type: 'select',\r\n        label: 'City',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'areaId',\r\n        type: 'select',\r\n        label: 'Area',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'subAreaId',\r\n        type: 'select',\r\n        label: 'Sub Area',\r\n        options: [],\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'detailedAddress',\r\n        type: 'text',\r\n        label: 'Detailed Address',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'addressLink',\r\n        type: 'url',\r\n        label: 'Address Link',\r\n        validators: [Validators.pattern(/^https?:\\/\\/.+/)],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rental-specific location inputs for rent-out outside compound administrative units (includes mallName)\r\n   */\r\n  private createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 2,\r\n        name: 'cityId',\r\n        type: 'select',\r\n        label: 'City',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'areaId',\r\n        type: 'select',\r\n        label: 'Area',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'subAreaId',\r\n        type: 'select',\r\n        label: 'Sub Area',\r\n        options: [],\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'detailedAddress',\r\n        type: 'text',\r\n        label: 'Detailed Address',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'addressLink',\r\n        type: 'url',\r\n        label: 'Address Link',\r\n        validators: [Validators.pattern(/^https?:\\/\\/.+/)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'mallName',\r\n        type: 'text',\r\n        label: 'Mall Name',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out outside compound unit information inputs for apartments and duplexes\r\n   */\r\n  private createRentOutOutsideCompoundUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor Number',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitFacing',\r\n        type: 'select',\r\n        label: 'Unit Facing',\r\n        options: UNIT_FACING_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out outside compound unit information inputs for studios (without rooms)\r\n   */\r\n  private createRentOutOutsideCompoundStudiosUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'text',\r\n        label: 'Floor',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitFacing',\r\n        type: 'select',\r\n        label: 'Unit Facing',\r\n        options: UNIT_FACING_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out outside compound unit information inputs for penthouses (without floor)\r\n   */\r\n  private createRentOutOutsideCompoundPenthousesUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitFacing',\r\n        type: 'select',\r\n        label: 'Unit Facing',\r\n        options: UNIT_FACING_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out outside compound unit information inputs for basements\r\n   */\r\n  private createRentOutOutsideCompoundBasementsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitLayoutStatus',\r\n        type: 'select',\r\n        label: 'Unit Layout Status',\r\n        options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out outside compound unit information inputs for roofs\r\n   */\r\n  private createRentOutOutsideCompoundRoofsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitLayoutStatus',\r\n        type: 'select',\r\n        label: 'Unit Layout Status',\r\n        options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingLayoutStatus',\r\n        type: 'select',\r\n        label: 'Building Layout Status',\r\n        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out outside compound unit information inputs for standalone villas\r\n   */\r\n  private createRentOutOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitDesign',\r\n        type: 'select',\r\n        label: 'Unit Design',\r\n        options: UNIT_DESIGN_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out outside compound unit information inputs for administrative units\r\n   */\r\n  private createRentOutOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out outside compound unit information inputs for medical clinics\r\n   */\r\n  private createRentOutOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out outside compound unit information inputs for pharmacies\r\n   */\r\n  private createRentOutOutsideCompoundPharmaciesUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fit Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out outside compound unit information inputs for commercial stores\r\n   */\r\n  private createRentOutOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fit Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity Type',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out outside compound unit information inputs for industrial properties\r\n   * (factory_lands, factories, warehouses, warehouse_lands)\r\n   */\r\n  private createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingLayoutStatus',\r\n        type: 'select',\r\n        label: 'Building Layout Status',\r\n        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fit Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity Type',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out outside compound unit information inputs for commercial administrative buildings\r\n   */\r\n  private createRentOutOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingLayoutStatus',\r\n        type: 'select',\r\n        label: 'Building Layout Status',\r\n        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity Type',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out outside compound financial inputs\r\n   */\r\n  private createRentOutOutsideCompoundFinancialInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 5,\r\n        name: 'unitPrice',\r\n        type: 'number',\r\n        label: 'Unit Price',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'unitPriceSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Unit Price Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'rentRecurrence',\r\n        type: 'select',\r\n        label: 'Rent Recurrence',\r\n        options: RENT_RECURRENCE_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'requiredInsurance',\r\n        type: 'select',\r\n        label: 'Required Insurance',\r\n        options: REQUIRED_INSURANCE_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'otherExpenses',\r\n        type: 'multiSelect',\r\n        label: 'Other Expenses (Tenant Responsibility)',\r\n        options: OTHER_EXPENSES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create media inputs for step 4\r\n   */\r\n  private createMediaInputs(): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 4,\r\n        name: 'mainImage',\r\n        type: 'file',\r\n        label: 'Main Image',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 4,\r\n        name: 'galleryImages',\r\n        type: 'file',\r\n        label: 'Gallery Images',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 4,\r\n        name: 'video',\r\n        type: 'file',\r\n        label: 'Video',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  // ============================================================================\r\n  // SPECIFIC CONFIGURATIONS\r\n  // ============================================================================\r\n\r\n  private createRentOutOutsideCompoundApartmentsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundDuplexesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundStudiosConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundStudiosUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundPenthousesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundPenthousesUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundBasementsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundBasementsUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundRoofsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundRoofsUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundStandaloneVillasConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundMedicalClinicsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundPharmaciesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundPharmaciesUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundCommercialStoresConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundAdministrativeUnitsLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundFactoryLandsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundFactoriesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundWarehousesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundWarehouseLandsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundIndustrialUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentOutOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  // ============================================================================\r\n  // RENT-IN CONFIGURATIONS (Property seeker looking to rent)\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Create rental-specific location inputs for rent-in outside compound scenarios\r\n   */\r\n  private createRentInOutsideCompoundLocationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 2,\r\n        name: 'cityId',\r\n        type: 'select',\r\n        label: 'City',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'areaId',\r\n        type: 'select',\r\n        label: 'Area',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'subAreaId',\r\n        type: 'select',\r\n        label: 'Sub Area',\r\n        options: [],\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'locationSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Location Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-in outside compound unit information inputs for apartments and duplexes\r\n   */\r\n  private createRentInOutsideCompoundUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor Number',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-in outside compound financial inputs\r\n   */\r\n  private createRentInOutsideCompoundFinancialInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 5,\r\n        name: 'averageUnitPriceMin',\r\n        type: 'number',\r\n        label: 'Minimum Average Unit Price',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'averageUnitPriceMax',\r\n        type: 'number',\r\n        label: 'Maximum Average Unit Price',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'unitPriceSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Unit Price Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'rentRecurrence',\r\n        type: 'select',\r\n        label: 'Rent Recurrence',\r\n        options: RENT_RECURRENCE_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound apartments\r\n   */\r\n  private createRentInOutsideCompoundApartmentsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound duplexes\r\n   */\r\n  private createRentInOutsideCompoundDuplexesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Create rent-in outside compound unit information inputs for studios\r\n   */\r\n  private createRentInOutsideCompoundStudiosUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor Number',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound studios\r\n   */\r\n  private createRentInOutsideCompoundStudiosConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundStudiosUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Create rent-in outside compound unit information inputs for penthouses\r\n   */\r\n  private createRentInOutsideCompoundPenthousesUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0.01)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound penthouses\r\n   */\r\n  private createRentInOutsideCompoundPenthousesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundPenthousesUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Create rent-in outside compound unit information inputs for basements\r\n   */\r\n  private createRentInOutsideCompoundBasementsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0.01)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitLayoutStatus',\r\n        type: 'select',\r\n        label: 'Unit Layout Status',\r\n        options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound basements\r\n   */\r\n  private createRentInOutsideCompoundBasementsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundBasementsUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Create rent-in outside compound unit information inputs for roofs\r\n   */\r\n  private createRentInOutsideCompoundRoofsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0.01)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitLayoutStatus',\r\n        type: 'select',\r\n        label: 'Unit Layout Status',\r\n        options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingLayoutStatus',\r\n        type: 'select',\r\n        label: 'Building Layout Status',\r\n        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound roofs\r\n   */\r\n  private createRentInOutsideCompoundRoofsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundRoofsUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Create rent-in outside compound unit information inputs for standalone villas\r\n   */\r\n  private createRentInOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'groundAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(0.01)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Ground Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Building Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Building Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitDesign',\r\n        type: 'select',\r\n        label: 'Unit Design',\r\n        options: UNIT_DESIGN_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound standalone villas\r\n   */\r\n  private createRentInOutsideCompoundStandaloneVillasConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Create rent-in outside compound unit information inputs for administrative units\r\n   */\r\n  private createRentInOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0.01)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-in outside compound unit information inputs for medical clinics\r\n   */\r\n  private createRentInOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0.01)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound administrative units\r\n   */\r\n  private createRentInOutsideCompoundAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound medical clinics\r\n   */\r\n  private createRentInOutsideCompoundMedicalClinicsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Create rent-in outside compound unit information inputs for pharmacies\r\n   */\r\n  private createRentInOutsideCompoundPharmaciesUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0.01)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fit Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound pharmacies\r\n   */\r\n  private createRentInOutsideCompoundPharmaciesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundPharmaciesUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Create rent-in outside compound unit information inputs for commercial stores\r\n   */\r\n  private createRentInOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0.01)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fit Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound commercial stores\r\n   */\r\n  private createRentInOutsideCompoundCommercialStoresConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Create rent-in outside compound unit information inputs for industrial properties\r\n   * (factory_lands, factories, warehouses, warehouse_lands, commercial_administrative_buildings)\r\n   */\r\n  private createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'groundAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(0.01)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Ground Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(0.01)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Building Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingLayoutStatus',\r\n        type: 'select',\r\n        label: 'Building Layout Status',\r\n        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fit Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound factory lands\r\n   */\r\n  private createRentInOutsideCompoundFactoryLandsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound factories\r\n   */\r\n  private createRentInOutsideCompoundFactoriesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound warehouses\r\n   */\r\n  private createRentInOutsideCompoundWarehousesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound warehouse lands\r\n   */\r\n  private createRentInOutsideCompoundWarehouseLandsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for rent-in outside compound commercial administrative buildings\r\n   */\r\n  private createRentInOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createRentInOutsideCompoundIndustrialUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // No media inputs for step 4 in rent-in scenarios\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInOutsideCompoundFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  // ============================================================================\r\n  // PUBLIC API\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Get input configurations for rental outside compound cases\r\n   */\r\n  getInputConfigs(stepperModal: any): StepperConfiguration[] {\r\n    return [\r\n      // RENT-OUT OUTSIDE COMPOUND CONFIGURATIONS\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_apartments',\r\n        value: this.createRentOutOutsideCompoundApartmentsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_duplexes',\r\n        value: this.createRentOutOutsideCompoundDuplexesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_studios',\r\n        value: this.createRentOutOutsideCompoundStudiosConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_penthouses',\r\n        value: this.createRentOutOutsideCompoundPenthousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_basements',\r\n        value: this.createRentOutOutsideCompoundBasementsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_roofs',\r\n        value: this.createRentOutOutsideCompoundRoofsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_standalone_villas',\r\n        value: this.createRentOutOutsideCompoundStandaloneVillasConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_administrative_units',\r\n        value: this.createRentOutOutsideCompoundAdministrativeUnitsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_medical_clinics',\r\n        value: this.createRentOutOutsideCompoundMedicalClinicsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_pharmacies',\r\n        value: this.createRentOutOutsideCompoundPharmaciesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_commercial_stores',\r\n        value: this.createRentOutOutsideCompoundCommercialStoresConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_factory_lands',\r\n        value: this.createRentOutOutsideCompoundFactoryLandsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_factories',\r\n        value: this.createRentOutOutsideCompoundFactoriesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_warehouses',\r\n        value: this.createRentOutOutsideCompoundWarehousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_warehouse_lands',\r\n        value: this.createRentOutOutsideCompoundWarehouseLandsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_out_commercial_administrative_buildings',\r\n        value: this.createRentOutOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal),\r\n      },\r\n      // RENT-IN OUTSIDE COMPOUND CONFIGURATIONS\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_apartments',\r\n        value: this.createRentInOutsideCompoundApartmentsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_duplexes',\r\n        value: this.createRentInOutsideCompoundDuplexesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_studios',\r\n        value: this.createRentInOutsideCompoundStudiosConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_penthouses',\r\n        value: this.createRentInOutsideCompoundPenthousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_basements',\r\n        value: this.createRentInOutsideCompoundBasementsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_roofs',\r\n        value: this.createRentInOutsideCompoundRoofsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_standalone_villas',\r\n        value: this.createRentInOutsideCompoundStandaloneVillasConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_administrative_units',\r\n        value: this.createRentInOutsideCompoundAdministrativeUnitsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_medical_clinics',\r\n        value: this.createRentInOutsideCompoundMedicalClinicsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_pharmacies',\r\n        value: this.createRentInOutsideCompoundPharmaciesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_commercial_stores',\r\n        value: this.createRentInOutsideCompoundCommercialStoresConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_factory_lands',\r\n        value: this.createRentInOutsideCompoundFactoryLandsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_factories',\r\n        value: this.createRentInOutsideCompoundFactoriesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_warehouses',\r\n        value: this.createRentInOutsideCompoundWarehousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_warehouse_lands',\r\n        value: this.createRentInOutsideCompoundWarehouseLandsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_outside_compound_rent_in_commercial_administrative_buildings',\r\n        value: this.createRentInOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal),\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get all available rental outside compound configuration keys\r\n   */\r\n  getRentalOutsideCompoundConfigurationKeys(): string[] {\r\n    return [\r\n      // RENT-OUT OUTSIDE COMPOUND KEYS\r\n      'rentals_outside_compound_rent_out_apartments',\r\n      'rentals_outside_compound_rent_out_duplexes',\r\n      'rentals_outside_compound_rent_out_studios',\r\n      'rentals_outside_compound_rent_out_penthouses',\r\n      'rentals_outside_compound_rent_out_basements',\r\n      'rentals_outside_compound_rent_out_roofs',\r\n      'rentals_outside_compound_rent_out_standalone_villas',\r\n      'rentals_outside_compound_rent_out_administrative_units',\r\n      'rentals_outside_compound_rent_out_medical_clinics',\r\n      'rentals_outside_compound_rent_out_pharmacies',\r\n      'rentals_outside_compound_rent_out_commercial_stores',\r\n      'rentals_outside_compound_rent_out_factory_lands',\r\n      'rentals_outside_compound_rent_out_factories',\r\n      'rentals_outside_compound_rent_out_warehouses',\r\n      'rentals_outside_compound_rent_out_warehouse_lands',\r\n      'rentals_outside_compound_rent_out_commercial_administrative_buildings',\r\n      // RENT-IN OUTSIDE COMPOUND KEYS\r\n      'rentals_outside_compound_rent_in_apartments',\r\n      'rentals_outside_compound_rent_in_duplexes',\r\n      'rentals_outside_compound_rent_in_studios',\r\n      'rentals_outside_compound_rent_in_penthouses',\r\n      'rentals_outside_compound_rent_in_basements',\r\n      'rentals_outside_compound_rent_in_roofs',\r\n      'rentals_outside_compound_rent_in_standalone_villas',\r\n      'rentals_outside_compound_rent_in_administrative_units',\r\n      'rentals_outside_compound_rent_in_medical_clinics',\r\n      'rentals_outside_compound_rent_in_pharmacies',\r\n      'rentals_outside_compound_rent_in_commercial_stores',\r\n      'rentals_outside_compound_rent_in_factory_lands',\r\n      'rentals_outside_compound_rent_in_factories',\r\n      'rentals_outside_compound_rent_in_warehouses',\r\n      'rentals_outside_compound_rent_in_warehouse_lands',\r\n      'rentals_outside_compound_rent_in_commercial_administrative_buildings',\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Check if a key is rent-out outside compound configuration\r\n   */\r\n  isRentOutOutsideCompoundConfiguration(key: string): boolean {\r\n    return key.includes('rentals_outside_compound_rent_out_');\r\n  }\r\n\r\n  /**\r\n   * Check if a key is rent-in outside compound configuration\r\n   */\r\n  isRentInOutsideCompoundConfiguration(key: string): boolean {\r\n    return key.includes('rentals_outside_compound_rent_in_');\r\n  }\r\n\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,iBAAiB,QAA2C,wBAAwB;AAC7F,SACEC,uBAAuB,EACvBC,yBAAyB,EACzBC,8BAA8B,EAC9BC,yBAAyB,EACzBC,yBAAyB,EACzBC,uBAAuB,EACvBC,gCAAgC,EAChCC,sBAAsB,EACtBC,mBAAmB,EACnBC,gCAAgC,EAChCC,oCAAoC,EACpCC,yBAAyB,EACzBC,+BAA+B,EAC/BC,sBAAsB,QACjB,+BAA+B;;AAKtC,OAAM,MAAOC,kCAAmC,SAAQf,iBAAiB;EAEvE;EACA;EACA;EAEA;;;EAGQgB,0CAA0CA,CAACC,YAAiB;IAClE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbI,OAAO,EAAE,EAAE;MACXH,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbI,OAAO,EAAE,EAAE;MACXH,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBI,OAAO,EAAE,EAAE;MACXH,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,cAAc;MACrBC,UAAU,EAAE,CAACvB,UAAU,CAAC4B,OAAO,CAAC,gBAAgB,CAAC,CAAC;MAClDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQK,6DAA6DA,CAACX,YAAiB;IACrF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbI,OAAO,EAAE,EAAE;MACXH,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbI,OAAO,EAAE,EAAE;MACXH,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBI,OAAO,EAAE,EAAE;MACXH,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,cAAc;MACrBC,UAAU,EAAE,CAACvB,UAAU,CAAC4B,OAAO,CAAC,gBAAgB,CAAC,CAAC;MAClDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,WAAW;MAClBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQM,iDAAiDA,CAACZ,YAAiB;IACzE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,cAAc;MACrBI,OAAO,EAAEhB,mBAAmB;MAC5Ba,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBI,OAAO,EAAEvB,yBAAyB;MAClCoB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQQ,wDAAwDA,CAACd,YAAiB;IAChF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBI,OAAO,EAAEvB,yBAAyB;MAClCoB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQS,2DAA2DA,CAACf,YAAiB;IACnF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBI,OAAO,EAAEvB,yBAAyB;MAClCoB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQU,0DAA0DA,CAAChB,YAAiB;IAClF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BI,OAAO,EAAEf,gCAAgC;MACzCY,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQW,sDAAsDA,CAACjB,YAAiB;IAC9E,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BI,OAAO,EAAEf,gCAAgC;MACzCY,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BI,OAAO,EAAEd,oCAAoC;MAC7CW,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQY,iEAAiEA,CAAClB,YAAiB;IACzF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBI,OAAO,EAAEb,yBAAyB;MAClCU,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQa,oEAAoEA,CAACnB,YAAiB;IAC5F,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdI,OAAO,EAAEhB,mBAAmB;MAC5Ba,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQc,+DAA+DA,CAACpB,YAAiB;IACvF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdI,OAAO,EAAEhB,mBAAmB;MAC5Ba,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQe,2DAA2DA,CAACrB,YAAiB;IACnF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdI,OAAO,EAAEhB,mBAAmB;MAC5Ba,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEZ,+BAA+B;MACxCS,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQgB,iEAAiEA,CAACtB,YAAiB;IACzF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdI,OAAO,EAAEhB,mBAAmB;MAC5Ba,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEZ,+BAA+B;MACxCS,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,eAAe;MACtBI,OAAO,EAAEX,sBAAsB;MAC/BQ,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;;EAIQiB,2DAA2DA,CAACvB,YAAiB;IACnF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BI,OAAO,EAAEd,oCAAoC;MAC7CW,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEZ,+BAA+B;MACxCS,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,eAAe;MACtBI,OAAO,EAAEX,sBAAsB;MAC/BQ,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQkB,kFAAkFA,CAACxB,YAAiB;IAC1G,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BI,OAAO,EAAEd,oCAAoC;MAC7CW,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,eAAe;MACtBI,OAAO,EAAEX,sBAAsB;MAC/BQ,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQmB,2CAA2CA,CAACzB,YAAiB;IACnE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,YAAY;MACnBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBI,OAAO,EAAEnB,uBAAuB;MAChCgB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,mBAAmB;MACzBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BI,OAAO,EAAElB,gCAAgC;MACzCe,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,wCAAwC;MAC/CI,OAAO,EAAEjB,sBAAsB;MAC/Bc,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQoB,iBAAiBA,CAAA;IACvB,OAAO,CACL;MACEzB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,YAAY;MACnBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;EACA;EACA;EAEQqB,4CAA4CA,CAAC3B,YAAiB;IACpE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,0CAA0C,CAACC,YAAY,CAAC,EAChE,GAAG,IAAI,CAACY,iDAAiD,CAACZ,YAAY,CAAC,CACxE;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQE,0CAA0CA,CAAC9B,YAAiB;IAClE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,0CAA0C,CAACC,YAAY,CAAC,EAChE,GAAG,IAAI,CAACY,iDAAiD,CAACZ,YAAY,CAAC,CACxE;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQG,yCAAyCA,CAAC/B,YAAiB;IACjE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,0CAA0C,CAACC,YAAY,CAAC,EAChE,GAAG,IAAI,CAACc,wDAAwD,CAACd,YAAY,CAAC,CAC/E;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQI,4CAA4CA,CAAChC,YAAiB;IACpE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,0CAA0C,CAACC,YAAY,CAAC,EAChE,GAAG,IAAI,CAACe,2DAA2D,CAACf,YAAY,CAAC,CAClF;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQK,2CAA2CA,CAACjC,YAAiB;IACnE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,0CAA0C,CAACC,YAAY,CAAC,EAChE,GAAG,IAAI,CAACgB,0DAA0D,CAAChB,YAAY,CAAC,CACjF;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQM,uCAAuCA,CAAClC,YAAiB;IAC/D,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,0CAA0C,CAACC,YAAY,CAAC,EAChE,GAAG,IAAI,CAACiB,sDAAsD,CAACjB,YAAY,CAAC,CAC7E;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQO,kDAAkDA,CAACnC,YAAiB;IAC1E,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,0CAA0C,CAACC,YAAY,CAAC,EAChE,GAAG,IAAI,CAACkB,iEAAiE,CAAClB,YAAY,CAAC,CACxF;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQQ,qDAAqDA,CAACpC,YAAiB;IAC7E,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjB,6DAA6D,CAACX,YAAY,CAAC,EACnF,GAAG,IAAI,CAACmB,oEAAoE,CAACnB,YAAY,CAAC,CAC3F;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQS,gDAAgDA,CAACrC,YAAiB;IACxE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjB,6DAA6D,CAACX,YAAY,CAAC,EACnF,GAAG,IAAI,CAACoB,+DAA+D,CAACpB,YAAY,CAAC,CACtF;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQU,4CAA4CA,CAACtC,YAAiB;IACpE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjB,6DAA6D,CAACX,YAAY,CAAC,EACnF,GAAG,IAAI,CAACqB,2DAA2D,CAACrB,YAAY,CAAC,CAClF;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQW,kDAAkDA,CAACvC,YAAiB;IAC1E,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjB,6DAA6D,CAACX,YAAY,CAAC,EACnF,GAAG,IAAI,CAACsB,iEAAiE,CAACtB,YAAY,CAAC,CACxF;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQY,8CAA8CA,CAACxC,YAAiB;IACtE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,0CAA0C,CAACC,YAAY,CAAC,EAChE,GAAG,IAAI,CAACuB,2DAA2D,CAACvB,YAAY,CAAC,CAClF;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQa,2CAA2CA,CAACzC,YAAiB;IACnE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,0CAA0C,CAACC,YAAY,CAAC,EAChE,GAAG,IAAI,CAACuB,2DAA2D,CAACvB,YAAY,CAAC,CAClF;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQc,4CAA4CA,CAAC1C,YAAiB;IACpE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,0CAA0C,CAACC,YAAY,CAAC,EAChE,GAAG,IAAI,CAACuB,2DAA2D,CAACvB,YAAY,CAAC,CAClF;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQe,gDAAgDA,CAAC3C,YAAiB;IACxE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,0CAA0C,CAACC,YAAY,CAAC,EAChE,GAAG,IAAI,CAACuB,2DAA2D,CAACvB,YAAY,CAAC,CAClF;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEQgB,mEAAmEA,CAAC5C,YAAiB;IAC3F,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,0CAA0C,CAACC,YAAY,CAAC,EAChE,GAAG,IAAI,CAACwB,kFAAkF,CAACxB,YAAY,CAAC,CACzG;IAED;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACH,iBAAiB,EAAE,CAAC;IAExC;IACAE,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACJ,2CAA2C,CAACzB,YAAY,CAAC,CAAC;IAE9E,OAAO4B,MAAM;EACf;EAEA;EACA;EACA;EAEA;;;EAGQiB,yCAAyCA,CAAC7C,YAAiB;IACjE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbI,OAAO,EAAE,EAAE;MACXH,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbI,OAAO,EAAE,EAAE;MACXH,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBI,OAAO,EAAE,EAAE;MACXH,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,CACF;EACH;EAEA;;;EAGQuC,gDAAgDA,CAAC9C,YAAiB;IACxE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,cAAc;MACrBI,OAAO,EAAEhB,mBAAmB;MAC5Ba,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQyC,0CAA0CA,CAAC/C,YAAiB;IAClE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBI,OAAO,EAAEnB,uBAAuB;MAChCgB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQ0C,2CAA2CA,CAAChD,YAAiB;IACnE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAAC8C,gDAAgD,CAAC9C,YAAY,CAAC,CACvE;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;EAGQqB,yCAAyCA,CAACjD,YAAiB;IACjE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAAC8C,gDAAgD,CAAC9C,YAAY,CAAC,CACvE;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;EAGQsB,uDAAuDA,CAAClD,YAAiB;IAC/E,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,cAAc;MACrBI,OAAO,EAAEhB,mBAAmB;MAC5Ba,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQ6C,wCAAwCA,CAACnD,YAAiB;IAChE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAACkD,uDAAuD,CAAClD,YAAY,CAAC,CAC9E;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;EAGQwB,0DAA0DA,CAACpD,YAAiB;IAClF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,IAAI,CAAC,CAAC;MACvDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQ+C,2CAA2CA,CAACrD,YAAiB;IACnE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAACoD,0DAA0D,CAACpD,YAAY,CAAC,CACjF;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;EAGQ0B,yDAAyDA,CAACtD,YAAiB;IACjF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,IAAI,CAAC,CAAC;MACvDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BI,OAAO,EAAEf,gCAAgC;MACzCY,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQiD,0CAA0CA,CAACvD,YAAiB;IAClE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAACsD,yDAAyD,CAACtD,YAAY,CAAC,CAChF;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;EAGQ4B,qDAAqDA,CAACxD,YAAiB;IAC7E,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,IAAI,CAAC,CAAC;MACvDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BI,OAAO,EAAEf,gCAAgC;MACzCY,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BI,OAAO,EAAEd,oCAAoC;MAC7CW,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQmD,sCAAsCA,CAACzD,YAAiB;IAC9D,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAACwD,qDAAqD,CAACxD,YAAY,CAAC,CAC5E;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;EAGQ8B,gEAAgEA,CAAC1D,YAAiB;IACxF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,0BAA0B;MACjCC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,IAAI,CAAC,CAAC;MACvDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,0BAA0B;MACjCC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBI,OAAO,EAAEb,yBAAyB;MAClCU,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQqD,iDAAiDA,CAAC3D,YAAiB;IACzE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAAC0D,gEAAgE,CAAC1D,YAAY,CAAC,CACvF;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;EAGQgC,mEAAmEA,CAAC5D,YAAiB;IAC3F,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,IAAI,CAAC,CAAC;MACvDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdI,OAAO,EAAEhB,mBAAmB;MAC5Ba,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQuD,8DAA8DA,CAAC7D,YAAiB;IACtF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,IAAI,CAAC,CAAC;MACvDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdI,OAAO,EAAEhB,mBAAmB;MAC5Ba,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAErB,yBAAyB;MAClCkB,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQwD,oDAAoDA,CAAC9D,YAAiB;IAC5E,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAAC4D,mEAAmE,CAAC5D,YAAY,CAAC,CAC1F;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;EAGQmC,+CAA+CA,CAAC/D,YAAiB;IACvE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAAC6D,8DAA8D,CAAC7D,YAAY,CAAC,CACrF;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;EAGQoC,0DAA0DA,CAAChE,YAAiB;IAClF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,IAAI,CAAC,CAAC;MACvDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdI,OAAO,EAAEhB,mBAAmB;MAC5Ba,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEZ,+BAA+B;MACxCS,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQ2D,2CAA2CA,CAACjE,YAAiB;IACnE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAACgE,0DAA0D,CAAChE,YAAY,CAAC,CACjF;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;EAGQsC,gEAAgEA,CAAClE,YAAiB;IACxF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,IAAI,CAAC,CAAC;MACvDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdI,OAAO,EAAEhB,mBAAmB;MAC5Ba,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBI,OAAO,EAAExB,uBAAuB;MAChCqB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBI,OAAO,EAAEtB,8BAA8B;MACvCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEZ,+BAA+B;MACxCS,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBI,OAAO,EAAEX,sBAAsB;MAC/BQ,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQ6D,iDAAiDA,CAACnE,YAAiB;IACzE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAACkE,gEAAgE,CAAClE,YAAY,CAAC,CACvF;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;;EAIQwC,0DAA0DA,CAACpE,YAAiB;IAClF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,0BAA0B;MACjCC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,IAAI,CAAC,CAAC;MACvDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,0BAA0B;MACjCC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC+B,GAAG,CAAC,IAAI,CAAC,CAAC;MACvDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BI,OAAO,EAAEd,oCAAoC;MAC7CW,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEZ,+BAA+B;MACxCS,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBI,OAAO,EAAEX,sBAAsB;MAC/BQ,UAAU,EAAE,CAACvB,UAAU,CAAC2B,QAAQ,CAAC;MACjCH,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BI,OAAO,EAAEpB,yBAAyB;MAClCiB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQ+D,6CAA6CA,CAACrE,YAAiB;IACrE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAACoE,0DAA0D,CAACpE,YAAY,CAAC,CACjF;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;EAGQ0C,0CAA0CA,CAACtE,YAAiB;IAClE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAACoE,0DAA0D,CAACpE,YAAY,CAAC,CACjF;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;EAGQ2C,2CAA2CA,CAACvE,YAAiB;IACnE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAACoE,0DAA0D,CAACpE,YAAY,CAAC,CACjF;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;EAGQ4C,+CAA+CA,CAACxE,YAAiB;IACvE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAACoE,0DAA0D,CAACpE,YAAY,CAAC,CACjF;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;;;EAGQ6C,kEAAkEA,CAACzE,YAAiB;IAC1F,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACiB,yCAAyC,CAAC7C,YAAY,CAAC,EAC/D,GAAG,IAAI,CAACoE,0DAA0D,CAACpE,YAAY,CAAC,CACjF;IAED;IAEA;IACA4B,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACkB,0CAA0C,CAAC/C,YAAY,CAAC,CAAC;IAE7E,OAAO4B,MAAM;EACf;EAEA;EACA;EACA;EAEA;;;EAGA8C,eAAeA,CAAC1E,YAAiB;IAC/B,OAAO;IACL;IACA;MACE2E,GAAG,EAAE,8CAA8C;MACnDC,KAAK,EAAE,IAAI,CAACjD,4CAA4C,CAAC3B,YAAY;KACtE,EACD;MACE2E,GAAG,EAAE,4CAA4C;MACjDC,KAAK,EAAE,IAAI,CAAC9C,0CAA0C,CAAC9B,YAAY;KACpE,EACD;MACE2E,GAAG,EAAE,2CAA2C;MAChDC,KAAK,EAAE,IAAI,CAAC7C,yCAAyC,CAAC/B,YAAY;KACnE,EACD;MACE2E,GAAG,EAAE,8CAA8C;MACnDC,KAAK,EAAE,IAAI,CAAC5C,4CAA4C,CAAChC,YAAY;KACtE,EACD;MACE2E,GAAG,EAAE,6CAA6C;MAClDC,KAAK,EAAE,IAAI,CAAC3C,2CAA2C,CAACjC,YAAY;KACrE,EACD;MACE2E,GAAG,EAAE,yCAAyC;MAC9CC,KAAK,EAAE,IAAI,CAAC1C,uCAAuC,CAAClC,YAAY;KACjE,EACD;MACE2E,GAAG,EAAE,qDAAqD;MAC1DC,KAAK,EAAE,IAAI,CAACzC,kDAAkD,CAACnC,YAAY;KAC5E,EACD;MACE2E,GAAG,EAAE,wDAAwD;MAC7DC,KAAK,EAAE,IAAI,CAACxC,qDAAqD,CAACpC,YAAY;KAC/E,EACD;MACE2E,GAAG,EAAE,mDAAmD;MACxDC,KAAK,EAAE,IAAI,CAACvC,gDAAgD,CAACrC,YAAY;KAC1E,EACD;MACE2E,GAAG,EAAE,8CAA8C;MACnDC,KAAK,EAAE,IAAI,CAACtC,4CAA4C,CAACtC,YAAY;KACtE,EACD;MACE2E,GAAG,EAAE,qDAAqD;MAC1DC,KAAK,EAAE,IAAI,CAACrC,kDAAkD,CAACvC,YAAY;KAC5E,EACD;MACE2E,GAAG,EAAE,iDAAiD;MACtDC,KAAK,EAAE,IAAI,CAACpC,8CAA8C,CAACxC,YAAY;KACxE,EACD;MACE2E,GAAG,EAAE,6CAA6C;MAClDC,KAAK,EAAE,IAAI,CAACnC,2CAA2C,CAACzC,YAAY;KACrE,EACD;MACE2E,GAAG,EAAE,8CAA8C;MACnDC,KAAK,EAAE,IAAI,CAAClC,4CAA4C,CAAC1C,YAAY;KACtE,EACD;MACE2E,GAAG,EAAE,mDAAmD;MACxDC,KAAK,EAAE,IAAI,CAACjC,gDAAgD,CAAC3C,YAAY;KAC1E,EACD;MACE2E,GAAG,EAAE,uEAAuE;MAC5EC,KAAK,EAAE,IAAI,CAAChC,mEAAmE,CAAC5C,YAAY;KAC7F;IACD;IACA;MACE2E,GAAG,EAAE,6CAA6C;MAClDC,KAAK,EAAE,IAAI,CAAC5B,2CAA2C,CAAChD,YAAY;KACrE,EACD;MACE2E,GAAG,EAAE,2CAA2C;MAChDC,KAAK,EAAE,IAAI,CAAC3B,yCAAyC,CAACjD,YAAY;KACnE,EACD;MACE2E,GAAG,EAAE,0CAA0C;MAC/CC,KAAK,EAAE,IAAI,CAACzB,wCAAwC,CAACnD,YAAY;KAClE,EACD;MACE2E,GAAG,EAAE,6CAA6C;MAClDC,KAAK,EAAE,IAAI,CAACvB,2CAA2C,CAACrD,YAAY;KACrE,EACD;MACE2E,GAAG,EAAE,4CAA4C;MACjDC,KAAK,EAAE,IAAI,CAACrB,0CAA0C,CAACvD,YAAY;KACpE,EACD;MACE2E,GAAG,EAAE,wCAAwC;MAC7CC,KAAK,EAAE,IAAI,CAACnB,sCAAsC,CAACzD,YAAY;KAChE,EACD;MACE2E,GAAG,EAAE,oDAAoD;MACzDC,KAAK,EAAE,IAAI,CAACjB,iDAAiD,CAAC3D,YAAY;KAC3E,EACD;MACE2E,GAAG,EAAE,uDAAuD;MAC5DC,KAAK,EAAE,IAAI,CAACd,oDAAoD,CAAC9D,YAAY;KAC9E,EACD;MACE2E,GAAG,EAAE,kDAAkD;MACvDC,KAAK,EAAE,IAAI,CAACb,+CAA+C,CAAC/D,YAAY;KACzE,EACD;MACE2E,GAAG,EAAE,6CAA6C;MAClDC,KAAK,EAAE,IAAI,CAACX,2CAA2C,CAACjE,YAAY;KACrE,EACD;MACE2E,GAAG,EAAE,oDAAoD;MACzDC,KAAK,EAAE,IAAI,CAACT,iDAAiD,CAACnE,YAAY;KAC3E,EACD;MACE2E,GAAG,EAAE,gDAAgD;MACrDC,KAAK,EAAE,IAAI,CAACP,6CAA6C,CAACrE,YAAY;KACvE,EACD;MACE2E,GAAG,EAAE,4CAA4C;MACjDC,KAAK,EAAE,IAAI,CAACN,0CAA0C,CAACtE,YAAY;KACpE,EACD;MACE2E,GAAG,EAAE,6CAA6C;MAClDC,KAAK,EAAE,IAAI,CAACL,2CAA2C,CAACvE,YAAY;KACrE,EACD;MACE2E,GAAG,EAAE,kDAAkD;MACvDC,KAAK,EAAE,IAAI,CAACJ,+CAA+C,CAACxE,YAAY;KACzE,EACD;MACE2E,GAAG,EAAE,sEAAsE;MAC3EC,KAAK,EAAE,IAAI,CAACH,kEAAkE,CAACzE,YAAY;KAC5F,CACF;EACH;EAEA;;;EAGA6E,yCAAyCA,CAAA;IACvC,OAAO;IACL;IACA,8CAA8C,EAC9C,4CAA4C,EAC5C,2CAA2C,EAC3C,8CAA8C,EAC9C,6CAA6C,EAC7C,yCAAyC,EACzC,qDAAqD,EACrD,wDAAwD,EACxD,mDAAmD,EACnD,8CAA8C,EAC9C,qDAAqD,EACrD,iDAAiD,EACjD,6CAA6C,EAC7C,8CAA8C,EAC9C,mDAAmD,EACnD,uEAAuE;IACvE;IACA,6CAA6C,EAC7C,2CAA2C,EAC3C,0CAA0C,EAC1C,6CAA6C,EAC7C,4CAA4C,EAC5C,wCAAwC,EACxC,oDAAoD,EACpD,uDAAuD,EACvD,kDAAkD,EAClD,6CAA6C,EAC7C,oDAAoD,EACpD,gDAAgD,EAChD,4CAA4C,EAC5C,6CAA6C,EAC7C,kDAAkD,EAClD,sEAAsE,CACvE;EACH;EAEA;;;EAGAC,qCAAqCA,CAACH,GAAW;IAC/C,OAAOA,GAAG,CAACI,QAAQ,CAAC,oCAAoC,CAAC;EAC3D;EAEA;;;EAGAC,oCAAoCA,CAACL,GAAW;IAC9C,OAAOA,GAAG,CAACI,QAAQ,CAAC,mCAAmC,CAAC;EAC1D;;;;6IAhjGWjF,kCAAkC,IAAAmF,iBAAA,IAAlCnF,kCAAkC;IAAA;EAAA;;WAAlCA,kCAAkC;IAAAoF,OAAA,EAAlCpF,kCAAkC,CAAAqF,IAAA;IAAAC,UAAA,EAFjC;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}