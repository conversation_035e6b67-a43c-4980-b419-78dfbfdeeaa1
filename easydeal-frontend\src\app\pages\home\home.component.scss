// Home Header Styles
.home-header {
  position: relative;
  width: 100%;
  min-height: 120vh;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);

  // Navigation Bar
  .navbar {
    position: relative;
    z-index: 10;

    backdrop-filter: blur(10px);
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    .container-fluid {
      max-width: 1400px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .navbar-brand {
      .logo-img {
        height: 50px;
        width: auto;
        object-fit: contain;
      }
    }

    .nav-list {
      list-style: none;
      gap: 2rem;
      margin: 0;
      padding: 0;

      .nav-item {
        .nav-link {
          color: #2c3e50;
          text-decoration: none;
          font-weight: 500;
          font-size: 1rem;
          padding: 0.5rem 1rem;
          border-radius: 8px;
          transition: all 0.3s ease;
          direction: rtl;

          &:hover {
            background-color: #f8f9fa;
            color: #27ae60;
            transform: translateY(-2px);
          }

          &.user-link {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border-radius: 25px;
            padding: 0.7rem 1.5rem;

            &:hover {
              background: linear-gradient(135deg, #229954, #27ae60);
              transform: translateY(-2px);
              box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
            }

            i {
              font-size: 0.9rem;
            }
          }
        }
      }
    }

    // User Registration Link (separate from nav list)
    .user-link {
      background: linear-gradient(135deg, #27ae60, #2ecc71);
      color: white !important;
      border-radius: 25px;
      padding: 0.7rem 1.5rem;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;

      &:hover {
        background: linear-gradient(135deg, #229954, #27ae60);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        color: white !important;
        text-decoration: none;
      }

      i {
        font-size: 0.9rem;
      }
    }

    // User Profile (when logged in)
    .user-profile {
      display: flex;
      align-items: center;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 25px;
      padding: 0.5rem 1rem;
      border: 1px solid rgba(250, 250, 250, 0.3);
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(39, 174, 96, 0.5);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.2);
      }

      .user-avatar {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #27ae60;
      }

      .user-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.95rem;
      }

      i.fa-chevron-down {
        font-size: 0.8rem;
        color: #27ae60;
        transition: transform 0.3s ease;
      }
    }

    // User Dropdown Menu
    .user-dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
      min-width: 250px;
      z-index: 1000;
      border: 1px solid rgba(0, 0, 0, 0.1);
      overflow: hidden;
      animation: dropdownFadeIn 0.3s ease;

      .dropdown-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        &:hover {
          background-color: #f8f9fa;
          transform: translateX(-3px);
        }

        i, .menu-icon {
          width: 20px;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .menu-icon {
          svg {
            width: 18px;
            height: 18px;
          }
        }

        span {
          font-weight: 500;
          color: #2c3e50;
          font-size: 0.9rem;
        }

        &.logout-item {
          &:hover {
            background-color: #fff5f5;
          }

          span {
            color: #e74c3c;
          }
        }

        &.new-request-item {
          background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
          border-top: 2px solid #27ae60;

          &:hover {
            background: linear-gradient(135deg, #d4f4d4, #e8f5e8);
          }

          span {
            color: #27ae60;
            font-weight: 600;
            text-align: center;
            width: 100%;
          }
        }
      }

      .dropdown-divider {
        height: 1px;
        background: rgba(0, 0, 0, 0.1);
        margin: 0;
      }
    }

    @keyframes dropdownFadeIn {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  // Hero Section
  .hero-section {
    position: relative;
    height: calc(100vh - 80px);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;

    .hero-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;

      .hero-bg-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }

      .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          135deg,
          rgba(44, 62, 80, 0.8) 0%,
          rgba(52, 73, 94, 0.6) 50%,
          rgba(39, 174, 96, 0.7) 100%
        );
      }
    }

    .hero-content {
      position: relative;
      z-index: 5;
      width: 100%;

      .hero-text-container {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 4rem;
        flex-wrap: wrap;

        .hero-text-item {
          text-align: center;
          animation: fadeInUp 1s ease-out;

          &:nth-child(1) {
            animation-delay: 0.2s;
          }

          &:nth-child(2) {
            animation-delay: 0.4s;
          }

          &:nth-child(3) {
            animation-delay: 0.6s;
          }

          .hero-text {
            font-size: 4rem;
            font-weight: 700;
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            margin: 0;
            padding: 1rem 2rem;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-5px) scale(1.05);
              background: rgba(255, 255, 255, 0.2);
              box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            }
          }
        }
      }
    }
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .home-header {
    .hero-section {
      .hero-content {
        .hero-text-container {
          gap: 2rem;

          .hero-text-item {
            .hero-text {
              font-size: 3rem;
              padding: 0.8rem 1.5rem;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .home-header {
    .navbar {
      .container-fluid {
        flex-direction: column;
        gap: 1rem;
      }

      .nav-list {
        gap: 1rem;
        flex-direction: column;
        text-align: center;

        .nav-item {
          .nav-link {
            font-size: 0.9rem;
            padding: 0.4rem 0.8rem;
          }
        }
      }

      .user-link {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
      }

      .user-profile {
        padding: 0.4rem 0.8rem;

        .user-avatar {
          width: 30px;
          height: 30px;
        }

        .user-name {
          font-size: 0.85rem;
        }
      }

      .user-dropdown {
        min-width: 220px;
        right: -20px;

        .dropdown-item {
          padding: 10px 14px;

          span {
            font-size: 0.85rem;
          }
        }
      }
    }

    .hero-section {
      .hero-content {
        .hero-text-container {
          flex-direction: column;
          gap: 1.5rem;

          .hero-text-item {
            .hero-text {
              font-size: 2.5rem;
              padding: 0.6rem 1rem;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .home-header {
    .navbar {
      .navbar-brand {
        .logo-img {
          height: 40px;
        }
      }
    }

    .hero-section {
      .hero-content {
        .hero-text-container {
          .hero-text-item {
            .hero-text {
              font-size: 2rem;
              padding: 0.5rem 0.8rem;
            }
          }
        }
      }
    }
  }
}
