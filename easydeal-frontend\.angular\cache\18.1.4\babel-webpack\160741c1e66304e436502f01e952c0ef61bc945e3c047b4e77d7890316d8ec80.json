{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { FLOOR_TYPES_OPTIONS, ACTIVITY_TYPES_OPTIONS, PAYMENT_METHOD_OPTIONS, DELIVERY_STATUS_TYPES_OPTIONS, FIT_OUT_CONDITION_TYPES_OPTIONS, FINISHING_STATUS_TYPES_OPTIONS, UNIT_VIEW_TYPES_OPTIONS, OTHER_ACCESSORIES_OPTIONS } from '../stepper-modal.constants';\nimport * as i0 from \"@angular/core\";\nexport class BaseConfigService {\n  /**\n   * Helper function to create common location inputs for step 2\n   */\n  createLocationInputs(stepperModal) {\n    return [{\n      step: 2,\n      name: 'compoundName',\n      type: 'text',\n      label: 'Compound Name',\n      validators: [Validators.required],\n      visibility: () => stepperModal.getInsideCompoundPrivilege()\n    }, {\n      step: 2,\n      name: 'locationSuggestions',\n      type: 'checkbox',\n      label: 'Location Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 2,\n      name: 'cityId',\n      type: 'select',\n      label: 'City',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'areaId',\n      type: 'select',\n      label: 'Area',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'subAreaId',\n      type: 'select',\n      label: 'Sub Area',\n      options: [],\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Helper function to create common unit information inputs for step 3\n   */\n  createUnitInformationInputs(stepperModal, includeRooms = true) {\n    const baseInputs = [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Unit Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Unit Area Max (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n    // Add room inputs only for certain unit types\n    if (includeRooms) {\n      baseInputs.push({\n        step: 3,\n        name: 'rooms',\n        type: 'number',\n        label: 'Number of Rooms',\n        validators: [Validators.min(0)],\n        visibility: () => true\n      }, {\n        step: 3,\n        name: 'bathRooms',\n        type: 'number',\n        label: 'Number of Bathrooms',\n        validators: [Validators.min(0)],\n        visibility: () => true\n      });\n    }\n    // Add common finishing and delivery inputs\n    baseInputs.push({\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    });\n    return baseInputs;\n  }\n  /**\n   * Helper function to create penthouse-specific unit information inputs for step 3\n   */\n  createPenthouseUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Unit Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Unit Area Max (m²)',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Helper function to create villa-specific unit information inputs for step 3\n   */\n  createVillaUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Unit Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Unit Area Max (m²)',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Helper function to create house-type unit information inputs for step 3\n   */\n  createHouseUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Unit Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Unit Area Max (m²)',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'buildingAreaMin',\n      type: 'number',\n      label: 'Building Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingAreaMax',\n      type: 'number',\n      label: 'Building Area Max (m²)',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingAreaSuggestions',\n      type: 'checkbox',\n      label: 'Building Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Helper function to create commercial unit information inputs for step 3\n   */\n  createCommercialUnitInformationInputs(stepperModal, includeFitOut = false) {\n    const baseInputs = [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Unit Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Unit Area Max (m²)',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n    // Add fitout condition for administrative units\n    if (includeFitOut) {\n      baseInputs.push({\n        step: 3,\n        name: 'fitOutCondition',\n        type: 'select',\n        label: 'Fitout Condition',\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n        validators: [Validators.required],\n        visibility: () => true\n      });\n    }\n    // Add common ending inputs\n    baseInputs.push({\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    });\n    return baseInputs;\n  }\n  /**\n   * Helper function to create shop unit information inputs for step 3\n   */\n  createShopUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Unit Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Unit Area Max (m²)',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity Status',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Helper function to create commercial administrative building unit information inputs for step 3\n   */\n  createCommercialAdminBuildingUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Unit Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Unit Area Max (m²)',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'buildingAreaMin',\n      type: 'number',\n      label: 'Building Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingAreaMax',\n      type: 'number',\n      label: 'Building Area Max (m²)',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingAreaSuggestions',\n      type: 'checkbox',\n      label: 'Building Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity Status',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Helper function to create common financial inputs for step 5\n   */\n  createFinancialInputs(stepperModal) {\n    return [{\n      step: 5,\n      name: 'averageUnitPriceMin',\n      type: 'number',\n      label: 'Unit Price Min',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'averageUnitPriceMax',\n      type: 'number',\n      label: 'Unit Price Max',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'averageUnitPriceSuggestion',\n      type: 'checkbox',\n      label: 'Unit Price Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 5,\n      name: 'paymentMethod',\n      type: 'select',\n      label: 'Payment Methods',\n      options: PAYMENT_METHOD_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Helper function to create document inputs for step 4\n   */\n  createDocumentInputs() {\n    return [{\n      step: 4,\n      name: 'mainImage',\n      type: 'file',\n      label: 'Main Image',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 4,\n      name: 'galleryImages',\n      type: 'file',\n      label: 'Gallery Images',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Get default inputs for a step when no specific configuration is found\n   */\n  getDefaultInputsForStep(step) {\n    if (step === 2) {\n      return [{\n        step: 2,\n        name: 'cityId',\n        type: 'select',\n        label: 'City',\n        options: [],\n        validators: [Validators.required],\n        visibility: () => true\n      }, {\n        step: 2,\n        name: 'areaId',\n        type: 'select',\n        label: 'Area',\n        options: [],\n        validators: [Validators.required],\n        visibility: () => true\n      }, {\n        step: 2,\n        name: 'subAreaId',\n        type: 'select',\n        label: 'Sub Area',\n        options: [],\n        validators: [],\n        visibility: () => true\n      }];\n    }\n    return [];\n  }\n  /**\n   * Get inputs for a specific configuration key and step\n   */\n  getInputsForKey(key, step, stepperModal) {\n    const config = this.getInputConfigs(stepperModal).find(c => c.key === key);\n    return config ? config.value.filter(input => input.step === step && input.visibility()) : this.getDefaultInputsForStep(step);\n  }\n  /**\n   * Check if a configuration key exists\n   */\n  hasConfiguration(key, stepperModal) {\n    return this.getInputConfigs(stepperModal).some(config => config.key === key);\n  }\n  static ɵfac = function BaseConfigService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BaseConfigService)();\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: BaseConfigService,\n    factory: BaseConfigService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["Validators", "FLOOR_TYPES_OPTIONS", "ACTIVITY_TYPES_OPTIONS", "PAYMENT_METHOD_OPTIONS", "DELIVERY_STATUS_TYPES_OPTIONS", "FIT_OUT_CONDITION_TYPES_OPTIONS", "FINISHING_STATUS_TYPES_OPTIONS", "UNIT_VIEW_TYPES_OPTIONS", "OTHER_ACCESSORIES_OPTIONS", "BaseConfigService", "createLocationInputs", "stepperModal", "step", "name", "type", "label", "validators", "required", "visibility", "getInsideCompoundPrivilege", "isClient", "options", "createUnitInformationInputs", "includeRooms", "baseInputs", "min", "push", "createPenthouseUnitInformationInputs", "createVillaUnitInformationInputs", "createHouseUnitInformationInputs", "createCommercialUnitInformationInputs", "includeFitOut", "createShopUnitInformationInputs", "createCommercialAdminBuildingUnitInformationInputs", "createFinancialInputs", "createDocumentInputs", "getDefaultInputsForStep", "getInputsForKey", "key", "config", "getInputConfigs", "find", "c", "value", "filter", "input", "hasConfiguration", "some", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\shared\\stepper-modal\\services\\base-config.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Validators, ValidatorFn } from '@angular/forms';\r\nimport {\r\n  FLOOR_TYPES_OPTIONS,\r\n  BUILDING_LICENSE_TYPES_OPTIONS,\r\n  UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n  BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n  GROUND_LAYOUT_STATUS_TYPES_OPTIONS,\r\n  UNIT_DESIGN_TYPES_OPTIONS,\r\n  ACTIVITY_TYPES_OPTIONS,\r\n  UNIT_DESCRIPTION_TYPES_OPTIONS,\r\n  SUB_UNIT_TYPE_OPTIONS,\r\n  RENT_RECURRENCE_OPTIONS,\r\n  PAYMENT_METHOD_OPTIONS,\r\n  REQUIRED_INSURANCE_TYPES_OPTIONS,\r\n  OTHER_EXPENSES_OPTIONS,\r\n  FURNISHING_STATUS_OPTIONS,\r\n  DELIVERY_STATUS_TYPES_OPTIONS,\r\n  FINANCIAL_STATUS_TYPES_OPTIONS,\r\n  LEGAL_STATUS_TYPES_OPTIONS,\r\n  FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n  FINISHING_STATUS_TYPES_OPTIONS,\r\n  UNIT_VIEW_TYPES_OPTIONS,\r\n  UNIT_FACING_TYPES_OPTIONS,\r\n  OTHER_ACCESSORIES_OPTIONS,\r\n  BUILDING_DEADLINE_TYPES_OPTIONS,\r\n  OptionItem\r\n} from '../stepper-modal.constants';\r\n\r\nexport interface InputConfig {\r\n  step: number;\r\n  name: string;\r\n  type: 'text' | 'number' | 'select' | 'multiSelect' | 'checkbox' | 'file' | 'textarea' | 'url';\r\n  label: string;\r\n  options?: OptionItem[];\r\n  validators?: ValidatorFn[];\r\n  visibility: () => boolean;\r\n  isDynamic?: boolean;\r\n}\r\n\r\nexport interface StepperConfiguration {\r\n  key: string;\r\n  value: InputConfig[];\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport abstract class BaseConfigService {\r\n\r\n  /**\r\n   * Helper function to create common location inputs for step 2\r\n   */\r\n  protected createLocationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 2,\r\n        name: 'compoundName',\r\n        type: 'text',\r\n        label: 'Compound Name',\r\n        validators: [Validators.required],\r\n        visibility: () => stepperModal.getInsideCompoundPrivilege(),\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'locationSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Location Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'cityId',\r\n        type: 'select',\r\n        label: 'City',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'areaId',\r\n        type: 'select',\r\n        label: 'Area',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'subAreaId',\r\n        type: 'select',\r\n        label: 'Sub Area',\r\n        options: [],\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Helper function to create common unit information inputs for step 3\r\n   */\r\n  protected createUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {\r\n    const baseInputs: InputConfig[] = [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Unit Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Unit Area Max (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n\r\n    // Add room inputs only for certain unit types\r\n    if (includeRooms) {\r\n      baseInputs.push(\r\n        {\r\n          step: 3,\r\n          name: 'rooms',\r\n          type: 'number',\r\n          label: 'Number of Rooms',\r\n          validators: [Validators.min(0)],\r\n          visibility: () => true,\r\n        },\r\n        {\r\n          step: 3,\r\n          name: 'bathRooms',\r\n          type: 'number',\r\n          label: 'Number of Bathrooms',\r\n          validators: [Validators.min(0)],\r\n          visibility: () => true,\r\n        }\r\n      );\r\n    }\r\n\r\n    // Add common finishing and delivery inputs\r\n    baseInputs.push(\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      }\r\n    );\r\n\r\n    return baseInputs;\r\n  }\r\n\r\n  /**\r\n   * Helper function to create penthouse-specific unit information inputs for step 3\r\n   */\r\n  protected createPenthouseUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Unit Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Unit Area Max (m²)',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Helper function to create villa-specific unit information inputs for step 3\r\n   */\r\n  protected createVillaUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Unit Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Unit Area Max (m²)',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Helper function to create house-type unit information inputs for step 3\r\n   */\r\n  protected createHouseUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Unit Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Unit Area Max (m²)',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaMin',\r\n        type: 'number',\r\n        label: 'Building Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaMax',\r\n        type: 'number',\r\n        label: 'Building Area Max (m²)',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Building Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Helper function to create commercial unit information inputs for step 3\r\n   */\r\n  protected createCommercialUnitInformationInputs(stepperModal: any, includeFitOut: boolean = false): InputConfig[] {\r\n    const baseInputs: InputConfig[] = [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Unit Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Unit Area Max (m²)',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n\r\n    // Add fitout condition for administrative units\r\n    if (includeFitOut) {\r\n      baseInputs.push({\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fitout Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      });\r\n    }\r\n\r\n    // Add common ending inputs\r\n    baseInputs.push(\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      }\r\n    );\r\n\r\n    return baseInputs;\r\n  }\r\n\r\n  /**\r\n   * Helper function to create shop unit information inputs for step 3\r\n   */\r\n  protected createShopUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Unit Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Unit Area Max (m²)',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity Status',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Helper function to create commercial administrative building unit information inputs for step 3\r\n   */\r\n  protected createCommercialAdminBuildingUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Unit Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Unit Area Max (m²)',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaMin',\r\n        type: 'number',\r\n        label: 'Building Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaMax',\r\n        type: 'number',\r\n        label: 'Building Area Max (m²)',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Building Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity Status',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Helper function to create common financial inputs for step 5\r\n   */\r\n  protected createFinancialInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 5,\r\n        name: 'averageUnitPriceMin',\r\n        type: 'number',\r\n        label: 'Unit Price Min',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'averageUnitPriceMax',\r\n        type: 'number',\r\n        label: 'Unit Price Max',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'averageUnitPriceSuggestion',\r\n        type: 'checkbox',\r\n        label: 'Unit Price Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'paymentMethod',\r\n        type: 'select',\r\n        label: 'Payment Methods',\r\n        options: PAYMENT_METHOD_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Helper function to create document inputs for step 4\r\n   */\r\n  protected createDocumentInputs(): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 4,\r\n        name: 'mainImage',\r\n        type: 'file',\r\n        label: 'Main Image',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 4,\r\n        name: 'galleryImages',\r\n        type: 'file',\r\n        label: 'Gallery Images',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get default inputs for a step when no specific configuration is found\r\n   */\r\n  protected getDefaultInputsForStep(step: number): InputConfig[] {\r\n    if (step === 2) {\r\n      return [\r\n        {\r\n          step: 2,\r\n          name: 'cityId',\r\n          type: 'select',\r\n          label: 'City',\r\n          options: [],\r\n          validators: [Validators.required],\r\n          visibility: () => true,\r\n        },\r\n        {\r\n          step: 2,\r\n          name: 'areaId',\r\n          type: 'select',\r\n          label: 'Area',\r\n          options: [],\r\n          validators: [Validators.required],\r\n          visibility: () => true,\r\n        },\r\n        {\r\n          step: 2,\r\n          name: 'subAreaId',\r\n          type: 'select',\r\n          label: 'Sub Area',\r\n          options: [],\r\n          validators: [],\r\n          visibility: () => true,\r\n        },\r\n      ];\r\n    }\r\n    return [];\r\n  }\r\n\r\n  /**\r\n   * Abstract method to get input configurations\r\n   */\r\n  abstract getInputConfigs(stepperModal: any): StepperConfiguration[];\r\n\r\n  /**\r\n   * Get inputs for a specific configuration key and step\r\n   */\r\n  getInputsForKey(key: string, step: number, stepperModal: any): InputConfig[] {\r\n    const config = this.getInputConfigs(stepperModal).find((c) => c.key === key);\r\n    return config\r\n      ? config.value.filter((input) => input.step === step && input.visibility())\r\n      : this.getDefaultInputsForStep(step);\r\n  }\r\n\r\n  /**\r\n   * Check if a configuration key exists\r\n   */\r\n  hasConfiguration(key: string, stepperModal: any): boolean {\r\n    return this.getInputConfigs(stepperModal).some(config => config.key === key);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,UAAU,QAAqB,gBAAgB;AACxD,SACEC,mBAAmB,EAMnBC,sBAAsB,EAItBC,sBAAsB,EAItBC,6BAA6B,EAG7BC,+BAA+B,EAC/BC,8BAA8B,EAC9BC,uBAAuB,EAEvBC,yBAAyB,QAGpB,4BAA4B;;AAqBnC,OAAM,MAAgBC,iBAAiB;EAErC;;;EAGUC,oBAAoBA,CAACC,YAAiB;IAC9C,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,eAAe;MACtBC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACQ,0BAA0B;KAC1D,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,QAAQ;KACxC,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,EAAE;MACXL,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,EAAE;MACXL,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBM,OAAO,EAAE,EAAE;MACXL,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGUI,2BAA2BA,CAACX,YAAiB,EAAEY,YAAA,GAAwB,IAAI;IACnF,MAAMC,UAAU,GAAkB,CAChC;MACEZ,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,QAAQ;KACxC,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdM,OAAO,EAAEpB,mBAAmB;MAC5Be,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED;IACA,IAAIK,YAAY,EAAE;MAChBC,UAAU,CAACE,IAAI,CACb;QACEd,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,iBAAiB;QACxBC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/BP,UAAU,EAAEA,CAAA,KAAM;OACnB,EACD;QACEN,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,qBAAqB;QAC5BC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/BP,UAAU,EAAEA,CAAA,KAAM;OACnB,CACF;IACH;IAEA;IACAM,UAAU,CAACE,IAAI,CACb;MACEd,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbM,OAAO,EAAEd,uBAAuB;MAChCS,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAEf,8BAA8B;MACvCU,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBM,OAAO,EAAEjB,6BAA6B;MACtCY,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BM,OAAO,EAAEb,yBAAyB;MAClCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,OAAOM,UAAU;EACnB;EAEA;;;EAGUG,oCAAoCA,CAAChB,YAAiB;IAC9D,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,QAAQ;KACxC,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbM,OAAO,EAAEd,uBAAuB;MAChCS,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAEf,8BAA8B;MACvCU,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBM,OAAO,EAAEjB,6BAA6B;MACtCY,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BM,OAAO,EAAEb,yBAAyB;MAClCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGUU,gCAAgCA,CAACjB,YAAiB;IAC1D,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,QAAQ;KACxC,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbM,OAAO,EAAEd,uBAAuB;MAChCS,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAEf,8BAA8B;MACvCU,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBM,OAAO,EAAEjB,6BAA6B;MACtCY,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BM,OAAO,EAAEb,yBAAyB;MAClCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGUW,gCAAgCA,CAAClB,YAAiB;IAC1D,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,QAAQ;KACxC,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,yBAAyB;MAC/BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,2BAA2B;MAClCC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,QAAQ;KACxC,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbM,OAAO,EAAEd,uBAAuB;MAChCS,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAEf,8BAA8B;MACvCU,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBM,OAAO,EAAEjB,6BAA6B;MACtCY,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BM,OAAO,EAAEb,yBAAyB;MAClCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGUY,qCAAqCA,CAACnB,YAAiB,EAAEoB,aAAA,GAAyB,KAAK;IAC/F,MAAMP,UAAU,GAAkB,CAChC;MACEZ,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,QAAQ;KACxC,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdM,OAAO,EAAEpB,mBAAmB;MAC5Be,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbM,OAAO,EAAEd,uBAAuB;MAChCS,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAEf,8BAA8B;MACvCU,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBM,OAAO,EAAEjB,6BAA6B;MACtCY,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED;IACA,IAAIa,aAAa,EAAE;MACjBP,UAAU,CAACE,IAAI,CAAC;QACdd,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,iBAAiB;QACvBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,kBAAkB;QACzBM,OAAO,EAAEhB,+BAA+B;QACxCW,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;QACjCC,UAAU,EAAEA,CAAA,KAAM;OACnB,CAAC;IACJ;IAEA;IACAM,UAAU,CAACE,IAAI,CACb;MACEd,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BM,OAAO,EAAEb,yBAAyB;MAClCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,OAAOM,UAAU;EACnB;EAEA;;;EAGUQ,+BAA+BA,CAACrB,YAAiB;IACzD,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,QAAQ;KACxC,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdM,OAAO,EAAEpB,mBAAmB;MAC5Be,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbM,OAAO,EAAEd,uBAAuB;MAChCS,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAEf,8BAA8B;MACvCU,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBM,OAAO,EAAEjB,6BAA6B;MACtCY,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBM,OAAO,EAAEnB,sBAAsB;MAC/Bc,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BM,OAAO,EAAEb,yBAAyB;MAClCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGUe,kDAAkDA,CAACtB,YAAiB;IAC5E,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,QAAQ;KACxC,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAAChB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,yBAAyB;MAC/BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,2BAA2B;MAClCC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,QAAQ;KACxC,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbM,OAAO,EAAEd,uBAAuB;MAChCS,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAEf,8BAA8B;MACvCU,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBM,OAAO,EAAEjB,6BAA6B;MACtCY,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBM,OAAO,EAAEnB,sBAAsB;MAC/Bc,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BM,OAAO,EAAEb,yBAAyB;MAClCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGUgB,qBAAqBA,CAACvB,YAAiB;IAC/C,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,EAAEjB,UAAU,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,4BAA4B;MAClCC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,QAAQ;KACxC,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBM,OAAO,EAAElB,sBAAsB;MAC/Ba,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGUiB,oBAAoBA,CAAA;IAC5B,OAAO,CACL;MACEvB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,YAAY;MACnBC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGUkB,uBAAuBA,CAACxB,IAAY;IAC5C,IAAIA,IAAI,KAAK,CAAC,EAAE;MACd,OAAO,CACL;QACEA,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,MAAM;QACbM,OAAO,EAAE,EAAE;QACXL,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;QACjCC,UAAU,EAAEA,CAAA,KAAM;OACnB,EACD;QACEN,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,MAAM;QACbM,OAAO,EAAE,EAAE;QACXL,UAAU,EAAE,CAAChB,UAAU,CAACiB,QAAQ,CAAC;QACjCC,UAAU,EAAEA,CAAA,KAAM;OACnB,EACD;QACEN,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,UAAU;QACjBM,OAAO,EAAE,EAAE;QACXL,UAAU,EAAE,EAAE;QACdE,UAAU,EAAEA,CAAA,KAAM;OACnB,CACF;IACH;IACA,OAAO,EAAE;EACX;EAOA;;;EAGAmB,eAAeA,CAACC,GAAW,EAAE1B,IAAY,EAAED,YAAiB;IAC1D,MAAM4B,MAAM,GAAG,IAAI,CAACC,eAAe,CAAC7B,YAAY,CAAC,CAAC8B,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACJ,GAAG,KAAKA,GAAG,CAAC;IAC5E,OAAOC,MAAM,GACTA,MAAM,CAACI,KAAK,CAACC,MAAM,CAAEC,KAAK,IAAKA,KAAK,CAACjC,IAAI,KAAKA,IAAI,IAAIiC,KAAK,CAAC3B,UAAU,EAAE,CAAC,GACzE,IAAI,CAACkB,uBAAuB,CAACxB,IAAI,CAAC;EACxC;EAEA;;;EAGAkC,gBAAgBA,CAACR,GAAW,EAAE3B,YAAiB;IAC7C,OAAO,IAAI,CAAC6B,eAAe,CAAC7B,YAAY,CAAC,CAACoC,IAAI,CAACR,MAAM,IAAIA,MAAM,CAACD,GAAG,KAAKA,GAAG,CAAC;EAC9E;;qCAx7BoB7B,iBAAiB;EAAA;;WAAjBA,iBAAiB;IAAAuC,OAAA,EAAjBvC,iBAAiB,CAAAwC,IAAA;IAAAC,UAAA,EAFzB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}