{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../authentication/services/authentication.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../../_metronic/shared/keenicon/keenicon.component\";\nfunction HomeComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_27_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleUserDropdown());\n    });\n    i0.ɵɵelement(1, \"img\", 29);\n    i0.ɵɵelementStart(2, \"span\", 30);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getUserProfileImage(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.getUserDisplayName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getUserDisplayName());\n  }\n}\nfunction HomeComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(2, \"span\", 34);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 35)(4, \"g\", 36);\n    i0.ɵɵelement(5, \"path\", 37)(6, \"path\", 38)(7, \"path\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"defs\")(9, \"clipPath\", 40);\n    i0.ɵɵelement(10, \"rect\", 41);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Requests\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(14, \"span\", 34);\n    i0.ɵɵelement(15, \"app-keenicon\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \" My Profile \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_18_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(19, \"span\", 34);\n    i0.ɵɵelement(20, \"app-keenicon\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \" Messages \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_23_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(24, \"span\", 34);\n    i0.ɵɵelement(25, \"i\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27, \" Help \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_28_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(29, \"span\", 34);\n    i0.ɵɵelement(30, \"app-keenicon\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \" Notifications \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(33, \"div\", 46);\n    i0.ɵɵelementStart(34, \"div\", 47);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_34_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelementStart(35, \"span\", 34);\n    i0.ɵɵelement(36, \"i\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \" Logout \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(39, \"div\", 46);\n    i0.ɵɵelementStart(40, \"div\", 49);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_28_Template_div_click_40_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(41, \"span\", 50);\n    i0.ɵɵtext(42, \" New Request \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HomeComponent_a_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 51);\n    i0.ɵɵelement(1, \"i\", 52);\n    i0.ɵɵtext(2, \" Register Guest \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class HomeComponent {\n  authService;\n  currentUser = null;\n  isLoggedIn = false;\n  showUserDropdown = false;\n  showMobileMenu = false;\n  constructor(authService) {\n    this.authService = authService;\n  }\n  ngOnInit() {\n    this.checkUserSession();\n  }\n  checkUserSession() {\n    // Check if user is logged in by checking localStorage\n    const authToken = localStorage.getItem('authToken');\n    const currentUser = localStorage.getItem('currentUser');\n    if (authToken && currentUser) {\n      try {\n        this.currentUser = JSON.parse(currentUser);\n        this.isLoggedIn = true;\n      } catch (error) {\n        // If parsing fails, user is not logged in\n        this.isLoggedIn = false;\n        this.currentUser = null;\n      }\n    } else {\n      this.isLoggedIn = false;\n      this.currentUser = null;\n    }\n  }\n  getUserDisplayName() {\n    if (this.currentUser) {\n      return this.currentUser.fullName || 'User';\n    }\n    return 'Guest';\n  }\n  getUserProfileImage() {\n    if (this.currentUser && this.currentUser.image) {\n      return this.currentUser.image;\n    }\n    // Return default avatar if no profile image\n    return 'assets/media/avatars/blank.png';\n  }\n  toggleUserDropdown() {\n    this.showUserDropdown = !this.showUserDropdown;\n  }\n  closeUserDropdown() {\n    this.showUserDropdown = false;\n  }\n  logout() {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('currentUser');\n    this.isLoggedIn = false;\n    this.currentUser = null;\n    this.showUserDropdown = false;\n    // Optionally redirect to login page\n    // this.router.navigate(['/authentication/login']);\n  }\n  toggleMobileMenu() {\n    this.showMobileMenu = !this.showMobileMenu;\n    // Close user dropdown when mobile menu is toggled\n    if (this.showMobileMenu) {\n      this.showUserDropdown = false;\n    }\n  }\n  closeMobileMenu() {\n    this.showMobileMenu = false;\n  }\n  onDocumentClick(event) {\n    const target = event.target;\n    const userProfile = target.closest('.user-profile');\n    const userDropdown = target.closest('.user-dropdown');\n    const navbarToggler = target.closest('.navbar-toggler');\n    const navbarCollapse = target.closest('.navbar-collapse');\n    // Close user dropdown if clicked outside of user profile and dropdown\n    if (!userProfile && !userDropdown && this.showUserDropdown) {\n      this.showUserDropdown = false;\n    }\n    // Close mobile menu if clicked outside of navbar toggler and navbar collapse\n    if (!navbarToggler && !navbarCollapse && this.showMobileMenu) {\n      this.showMobileMenu = false;\n    }\n  }\n  static ɵfac = function HomeComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HomeComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomeComponent,\n    selectors: [[\"app-home\"]],\n    hostBindings: function HomeComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function HomeComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 48,\n    vars: 10,\n    consts: [[1, \"home-header\"], [1, \"navbar\", \"navbar-expand-lg\"], [1, \"container-fluid\", \"px-2\", \"px-md-4\"], [1, \"navbar-brand\"], [\"alt\", \"Logo\", \"src\", \"./assets/media/easydeallogos/loading-logo.png\", 1, \"h-30px\", \"h-md-40px\", \"app-sidebar-logo-default\"], [\"type\", \"button\", \"aria-label\", \"Toggle navigation\", 1, \"navbar-toggler\", \"d-lg-none\", 3, \"click\"], [1, \"navbar-toggler-icon\"], [1, \"fas\"], [\"id\", \"navbarNav\", 1, \"navbar-collapse\"], [1, \"navbar-nav\", \"mx-auto\"], [1, \"nav-list\", \"d-flex\", \"flex-column\", \"flex-lg-row\", \"align-items-center\", \"mb-0\"], [1, \"nav-item\"], [\"href\", \"#\", 1, \"nav-link\"], [1, \"navbar-nav\", \"position-relative\", \"mt-3\", \"mt-lg-0\"], [\"class\", \"nav-link user-profile\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"user-dropdown\", 4, \"ngIf\"], [\"href\", \"#\", \"class\", \"nav-link user-link\", 4, \"ngIf\"], [1, \"hero-section\"], [1, \"hero-background\"], [\"src\", \"./assets/media/home/<USER>\", \"alt\", \"Hero Background\", 1, \"hero-bg-image\"], [1, \"hero-overlay\"], [1, \"hero-content\"], [1, \"container\"], [1, \"row\", \"justify-content-center\"], [1, \"col-12\"], [1, \"hero-text-container\"], [1, \"hero-text-item\"], [1, \"hero-text\"], [1, \"nav-link\", \"user-profile\", 3, \"click\"], [1, \"user-avatar\", \"me-2\", 3, \"src\", \"alt\"], [1, \"user-name\"], [1, \"fas\", \"fa-chevron-down\", \"ms-2\"], [1, \"user-dropdown\"], [1, \"dropdown-item\", 3, \"click\"], [1, \"menu-icon\", \"me-2\"], [\"width\", \"19\", \"height\", \"19\", \"viewBox\", \"0 0 19 19\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"clip-path\", \"url(#clip0_24_2533)\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z\"], [\"id\", \"clip0_24_2533\"], [\"width\", \"19\", \"height\", \"19\", \"fill\", \"white\"], [\"name\", \"user\", \"type\", \"outline\", 1, \"fs-5\", \"text-primary\"], [\"name\", \"messages\", \"type\", \"outline\", 1, \"fs-5\", \"text-info\"], [1, \"fa-regular\", \"fa-circle-question\", \"fs-6\", \"text-warning\"], [\"name\", \"notification-on\", \"type\", \"outline\", 1, \"fs-5\", \"text-gray-600\"], [1, \"dropdown-divider\"], [1, \"dropdown-item\", \"logout-item\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\", \"fs-6\", \"text-danger\"], [1, \"dropdown-item\", \"new-request-item\", 3, \"click\"], [1, \"text-success\"], [\"href\", \"#\", 1, \"nav-link\", \"user-link\"], [1, \"fas\", \"fa-user\", \"me-2\"]],\n    template: function HomeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"header\", 0)(1, \"nav\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵelement(4, \"img\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"button\", 5);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_5_listener() {\n          return ctx.toggleMobileMenu();\n        });\n        i0.ɵɵelementStart(6, \"span\", 6);\n        i0.ɵɵelement(7, \"i\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 8)(9, \"div\", 9)(10, \"ul\", 10)(11, \"li\", 11)(12, \"a\", 12);\n        i0.ɵɵtext(13, \" Home \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"li\", 11)(15, \"a\", 12);\n        i0.ɵɵtext(16, \" About EasyDeal \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"li\", 11)(18, \"a\", 12);\n        i0.ɵɵtext(19, \" New Projects \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"li\", 11)(21, \"a\", 12);\n        i0.ɵɵtext(22, \" Advertisements \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"li\", 11)(24, \"a\", 12);\n        i0.ɵɵtext(25, \" Contact Us \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(26, \"div\", 13);\n        i0.ɵɵtemplate(27, HomeComponent_div_27_Template, 5, 3, \"div\", 14)(28, HomeComponent_div_28_Template, 43, 0, \"div\", 15)(29, HomeComponent_a_29_Template, 3, 0, \"a\", 16);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(30, \"div\", 17)(31, \"div\", 18);\n        i0.ɵɵelement(32, \"img\", 19)(33, \"div\", 20);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"div\", 21)(35, \"div\", 22)(36, \"div\", 23)(37, \"div\", 24)(38, \"div\", 25)(39, \"div\", 26)(40, \"h2\", 27);\n        i0.ɵɵtext(41, \" Easy\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(42, \"div\", 26)(43, \"h2\", 27);\n        i0.ɵɵtext(44, \" Speed \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(45, \"div\", 26)(46, \"h2\", 27);\n        i0.ɵɵtext(47, \" Reliability \");\n        i0.ɵɵelementEnd()()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵattribute(\"aria-expanded\", ctx.showMobileMenu);\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassProp(\"fa-bars\", !ctx.showMobileMenu)(\"fa-times\", ctx.showMobileMenu);\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"show\", ctx.showMobileMenu);\n        i0.ɵɵadvance(19);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn && ctx.showUserDropdown);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoggedIn);\n      }\n    },\n    dependencies: [i2.NgIf, i3.KeeniconComponent],\n    styles: [\".home-header[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  min-height: 120vh;\\n  overflow: hidden;\\n  background: rgba(255, 255, 255, 0.95);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 10;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 1rem 0;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%] {\\n  border: none;\\n  background: none;\\n  padding: 0.5rem;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  outline: none;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%]   .navbar-toggler-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 1.2rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%]:hover {\\n  background: rgba(44, 62, 80, 0.1);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .logo-img[_ngcontent-%COMP%] {\\n  height: 50px;\\n  width: auto;\\n  object-fit: contain;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  gap: 2rem;\\n  margin: 0;\\n  padding: 0;\\n  width: 100%;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  text-decoration: none;\\n  font-weight: 500;\\n  font-size: 1rem;\\n  padding: 0.5rem 1rem;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n  direction: rtl;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  color: #27ae60;\\n  transform: translateY(-2px);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\\n  color: white;\\n  border-radius: 25px;\\n  padding: 0.7rem 1.5rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #229954, #27ae60);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\\n  color: white !important;\\n  border-radius: 25px;\\n  padding: 0.7rem 1.5rem;\\n  text-decoration: none;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #229954, #27ae60);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);\\n  color: white !important;\\n  text-decoration: none;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 25px;\\n  padding: 0.5rem 1rem;\\n  border: 1px solid rgba(250, 250, 250, 0.3);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(39, 174, 96, 0.5);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.2);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 35px;\\n  height: 35px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid #27ae60;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n  font-size: 0.95rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   i.fa-chevron-down[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #27ae60;\\n  transition: transform 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  right: 0;\\n  background: white;\\n  border-radius: 10px;\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);\\n  min-width: 220px;\\n  z-index: 1000;\\n  border: 1px solid rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_dropdownFadeIn 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 14px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateX(-3px);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n  width: 18px;\\n  text-align: center;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.logout-item[_ngcontent-%COMP%]:hover {\\n  background-color: #fff5f5;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.logout-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8, #f0f8f0);\\n  border-top: 2px solid #27ae60;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #d4f4d4, #e8f5e8);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #27ae60;\\n  font-weight: 600;\\n  text-align: center;\\n  width: 100%;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: rgba(0, 0, 0, 0.1);\\n  margin: 0;\\n}\\n@keyframes _ngcontent-%COMP%_dropdownFadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: calc(100vh - 80px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 1;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .hero-bg-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .hero-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(44, 62, 80, 0.8) 0%, rgba(52, 73, 94, 0.6) 50%, rgba(39, 174, 96, 0.7) 100%);\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 5;\\n  width: 100%;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 4rem;\\n  flex-wrap: wrap;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  animation: _ngcontent-%COMP%_fadeInUp 1s ease-out;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0.2s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.4s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.6s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  font-weight: 700;\\n  color: white;\\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\\n  margin: 0;\\n  padding: 1rem 2rem;\\n  border-radius: 15px;\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px) scale(1.05);\\n  background: rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n    gap: 2rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 3rem;\\n    padding: 0.8rem 1.5rem;\\n  }\\n}\\n@media (max-width: 991.98px) {\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-collapse.show[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.98);\\n    border-radius: 12px;\\n    margin-top: 1rem;\\n    padding: 1rem;\\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n    flex-direction: column;\\n    text-align: center;\\n    width: 100%;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-size: 0.95rem;\\n    padding: 0.8rem 1rem;\\n    border-radius: 8px;\\n    margin: 0.2rem 0;\\n    display: block;\\n    width: 100%;\\n    text-align: center;\\n    background: rgba(52, 152, 219, 0.05);\\n    border: 1px solid rgba(52, 152, 219, 0.1);\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n    background: rgba(52, 152, 219, 0.1);\\n    transform: none;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n    font-size: 0.95rem;\\n    padding: 0.8rem 1rem;\\n    margin-top: 1rem;\\n    border-radius: 8px;\\n    background: rgba(52, 152, 219, 0.1);\\n    border: 1px solid rgba(52, 152, 219, 0.2);\\n    text-align: center;\\n    width: 100%;\\n    display: block;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n    padding: 0.8rem 1rem;\\n    margin-top: 1rem;\\n    border-radius: 8px;\\n    background: rgba(52, 152, 219, 0.1);\\n    border: 1px solid rgba(52, 152, 219, 0.2);\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%] {\\n    min-width: 200px;\\n    right: -15px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n    width: 16px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 14px;\\n    height: 14px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1.5rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n    padding: 0.6rem 1rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n    padding: 0.6rem 0;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n    padding: 0 1rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    height: 35px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%] {\\n    padding: 0.4rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-toggler[_ngcontent-%COMP%]   .navbar-toggler-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n    padding: 0.7rem 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n    padding: 0.7rem 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n    padding: 0.7rem 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%] {\\n    min-width: 180px;\\n    right: -10px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n    padding: 7px 10px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n    width: 14px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 12px;\\n    height: 12px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    padding: 0.5rem 0.8rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "HomeComponent_div_27_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleUserDropdown", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "getUserProfileImage", "ɵɵsanitizeUrl", "getUserDisplayName", "ɵɵtextInterpolate", "HomeComponent_div_28_Template_div_click_1_listener", "_r3", "closeUserDropdown", "HomeComponent_div_28_Template_div_click_13_listener", "HomeComponent_div_28_Template_div_click_18_listener", "HomeComponent_div_28_Template_div_click_23_listener", "HomeComponent_div_28_Template_div_click_28_listener", "HomeComponent_div_28_Template_div_click_34_listener", "logout", "HomeComponent_div_28_Template_div_click_40_listener", "HomeComponent", "authService", "currentUser", "isLoggedIn", "showUserDropdown", "showMobileMenu", "constructor", "ngOnInit", "checkUserSession", "authToken", "localStorage", "getItem", "JSON", "parse", "error", "fullName", "image", "removeItem", "toggleMobileMenu", "closeMobileMenu", "onDocumentClick", "event", "target", "userProfile", "closest", "userDropdown", "<PERSON>v<PERSON><PERSON><PERSON><PERSON>", "navbarCollapse", "ɵɵdirectiveInject", "i1", "AuthenticationService", "selectors", "hostBindings", "HomeComponent_HostBindings", "rf", "ctx", "HomeComponent_click_HostBindingHandler", "$event", "ɵɵresolveDocument", "HomeComponent_Template_button_click_5_listener", "ɵɵtemplate", "HomeComponent_div_27_Template", "HomeComponent_div_28_Template", "HomeComponent_a_29_Template", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit, HostListener } from '@angular/core';\r\nimport { AuthenticationService } from '../authentication/services/authentication.service';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrls: ['./home.component.scss']\r\n})\r\nexport class HomeComponent implements OnInit {\r\n  currentUser: any = null;\r\n  isLoggedIn: boolean = false;\r\n  showUserDropdown: boolean = false;\r\n  showMobileMenu: boolean = false;\r\n\r\n  constructor(private authService: AuthenticationService) { }\r\n\r\n  ngOnInit(): void {\r\n    this.checkUserSession();\r\n  }\r\n\r\n  checkUserSession(): void {\r\n    // Check if user is logged in by checking localStorage\r\n    const authToken = localStorage.getItem('authToken');\r\n    const currentUser = localStorage.getItem('currentUser');\r\n\r\n    if (authToken && currentUser) {\r\n      try {\r\n        this.currentUser = JSON.parse(currentUser);\r\n        this.isLoggedIn = true;\r\n      } catch (error) {\r\n        // If parsing fails, user is not logged in\r\n        this.isLoggedIn = false;\r\n        this.currentUser = null;\r\n      }\r\n    } else {\r\n      this.isLoggedIn = false;\r\n      this.currentUser = null;\r\n    }\r\n  }\r\n\r\n  getUserDisplayName(): string {\r\n    if (this.currentUser) {\r\n      return this.currentUser.fullName  || 'User';\r\n    }\r\n    return 'Guest';\r\n  }\r\n\r\n  getUserProfileImage(): string {\r\n    if (this.currentUser && this.currentUser.image) {\r\n      return this.currentUser.image;\r\n    }\r\n    // Return default avatar if no profile image\r\n    return 'assets/media/avatars/blank.png';\r\n  }\r\n\r\n  toggleUserDropdown(): void {\r\n    this.showUserDropdown = !this.showUserDropdown;\r\n  }\r\n\r\n  closeUserDropdown(): void {\r\n    this.showUserDropdown = false;\r\n  }\r\n\r\n  logout(): void {\r\n    localStorage.removeItem('authToken');\r\n    localStorage.removeItem('currentUser');\r\n    this.isLoggedIn = false;\r\n    this.currentUser = null;\r\n    this.showUserDropdown = false;\r\n    // Optionally redirect to login page\r\n    // this.router.navigate(['/authentication/login']);\r\n  }\r\n\r\n  toggleMobileMenu(): void {\r\n    this.showMobileMenu = !this.showMobileMenu;\r\n    // Close user dropdown when mobile menu is toggled\r\n    if (this.showMobileMenu) {\r\n      this.showUserDropdown = false;\r\n    }\r\n  }\r\n\r\n  closeMobileMenu(): void {\r\n    this.showMobileMenu = false;\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event): void {\r\n    const target = event.target as HTMLElement;\r\n    const userProfile = target.closest('.user-profile');\r\n    const userDropdown = target.closest('.user-dropdown');\r\n    const navbarToggler = target.closest('.navbar-toggler');\r\n    const navbarCollapse = target.closest('.navbar-collapse');\r\n\r\n    // Close user dropdown if clicked outside of user profile and dropdown\r\n    if (!userProfile && !userDropdown && this.showUserDropdown) {\r\n      this.showUserDropdown = false;\r\n    }\r\n\r\n    // Close mobile menu if clicked outside of navbar toggler and navbar collapse\r\n    if (!navbarToggler && !navbarCollapse && this.showMobileMenu) {\r\n      this.showMobileMenu = false;\r\n    }\r\n  }\r\n\r\n}\r\n", "<!-- Header Section -->\r\n<header class=\"home-header\">\r\n  <!-- Navigation Bar -->\r\n  <nav class=\"navbar navbar-expand-lg\">\r\n    <div class=\"container-fluid px-2 px-md-4\">\r\n      <!-- Logo -->\r\n      <div class=\"navbar-brand\">\r\n        <img alt=\"Logo\" src=\"./assets/media/easydeallogos/loading-logo.png\"\r\n          class=\"h-30px h-md-40px app-sidebar-logo-default\" />\r\n      </div>\r\n\r\n      <!-- Mobile Menu Toggle Button -->\r\n      <button class=\"navbar-toggler d-lg-none\" type=\"button\" (click)=\"toggleMobileMenu()\"\r\n        [attr.aria-expanded]=\"showMobileMenu\" aria-label=\"Toggle navigation\">\r\n        <span class=\"navbar-toggler-icon\">\r\n          <i class=\"fas\" [class.fa-bars]=\"!showMobileMenu\" [class.fa-times]=\"showMobileMenu\"></i>\r\n        </span>\r\n      </button>\r\n\r\n      <!-- Collapsible Navigation Content -->\r\n      <div class=\"navbar-collapse\" [class.show]=\"showMobileMenu\" id=\"navbarNav\">\r\n        <!-- Navigation Menu -->\r\n        <div class=\"navbar-nav mx-auto\">\r\n          <ul class=\"nav-list d-flex flex-column flex-lg-row align-items-center mb-0\">\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> Home </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> About EasyDeal </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> New Projects </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> Advertisements </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a href=\"#\" class=\"nav-link\"> Contact Us </a>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n\r\n        <!-- User Registration Link / User Profile -->\r\n        <div class=\"navbar-nav position-relative mt-3 mt-lg-0\">\r\n          <!-- If user is logged in, show user profile -->\r\n          <div *ngIf=\"isLoggedIn\" class=\"nav-link user-profile\" (click)=\"toggleUserDropdown()\">\r\n            <img [src]=\"getUserProfileImage()\" [alt]=\"getUserDisplayName()\" class=\"user-avatar me-2\">\r\n            <span class=\"user-name\">{{ getUserDisplayName() }}</span>\r\n            <i class=\"fas fa-chevron-down ms-2\"></i>\r\n          </div>\r\n\r\n          <!-- User Dropdown Menu -->\r\n          <div *ngIf=\"isLoggedIn && showUserDropdown\" class=\"user-dropdown\">\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <svg width=\"19\" height=\"19\" viewBox=\"0 0 19 19\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <g clip-path=\"url(#clip0_24_2533)\">\r\n                    <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                      d=\"M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z\" />\r\n                    <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                      d=\"M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z\" />\r\n                    <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                      d=\"M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z\" />\r\n                  </g>\r\n                  <defs>\r\n                    <clipPath id=\"clip0_24_2533\">\r\n                      <rect width=\"19\" height=\"19\" fill=\"white\" />\r\n                    </clipPath>\r\n                  </defs>\r\n                </svg>\r\n              </span>\r\n              <span>Requests</span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <app-keenicon name=\"user\" class=\"fs-5 text-primary\" type=\"outline\"></app-keenicon>\r\n              </span>\r\n              <span> My Profile </span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <app-keenicon name=\"messages\" class=\"fs-5 text-info\" type=\"outline\"></app-keenicon>\r\n              </span>\r\n              <span> Messages </span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <i class=\"fa-regular fa-circle-question fs-6 text-warning\"></i>\r\n              </span>\r\n              <span> Help </span>\r\n            </div>\r\n            <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <app-keenicon name=\"notification-on\" class=\"fs-5 text-gray-600\" type=\"outline\"></app-keenicon>\r\n              </span>\r\n              <span> Notifications </span>\r\n            </div>\r\n            <div class=\"dropdown-divider\"></div>\r\n            <div class=\"dropdown-item logout-item\" (click)=\"logout()\">\r\n              <span class=\"menu-icon me-2\">\r\n                <i class=\"fas fa-sign-out-alt fs-6 text-danger\"></i>\r\n              </span>\r\n              <span> Logout </span>\r\n            </div>\r\n            <div class=\"dropdown-divider\"></div>\r\n            <div class=\"dropdown-item new-request-item\" (click)=\"closeUserDropdown()\">\r\n              <span class=\"text-success\"> New Request </span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- If user is not logged in, show register button -->\r\n          <a *ngIf=\"!isLoggedIn\" href=\"#\" class=\"nav-link user-link\">\r\n            <i class=\"fas fa-user me-2\"></i>\r\n            Register Guest\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nav>\r\n\r\n  <!-- Hero Section -->\r\n  <div class=\"hero-section\">\r\n    <div class=\"hero-background\">\r\n      <img\r\n        src=\"./assets/media/home/<USER>\"\r\n        alt=\"Hero Background\" class=\"hero-bg-image\">\r\n      <div class=\"hero-overlay\"></div>\r\n    </div>\r\n\r\n    <div class=\"hero-content\">\r\n      <div class=\"container\">\r\n        <div class=\"row justify-content-center\">\r\n          <div class=\"col-12\">\r\n            <div class=\"hero-text-container\">\r\n              <div class=\"hero-text-item\">\r\n                <h2 class=\"hero-text\"> Easy</h2>\r\n              </div>\r\n              <div class=\"hero-text-item\">\r\n                <h2 class=\"hero-text\"> Speed </h2>\r\n              </div>\r\n              <div class=\"hero-text-item\">\r\n                <h2 class=\"hero-text\"> Reliability </h2>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</header>"], "mappings": ";;;;;;;IC6CUA,EAAA,CAAAC,cAAA,cAAqF;IAA/BD,EAAA,CAAAE,UAAA,mBAAAC,mDAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IAClFT,EAAA,CAAAU,SAAA,cAAyF;IACzFV,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAW,MAAA,GAA0B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACzDZ,EAAA,CAAAU,SAAA,YAAwC;IAC1CV,EAAA,CAAAY,YAAA,EAAM;;;;IAHCZ,EAAA,CAAAa,SAAA,EAA6B;IAACb,EAA9B,CAAAc,UAAA,QAAAR,MAAA,CAAAS,mBAAA,IAAAf,EAAA,CAAAgB,aAAA,CAA6B,QAAAV,MAAA,CAAAW,kBAAA,GAA6B;IACvCjB,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAkB,iBAAA,CAAAZ,MAAA,CAAAW,kBAAA,GAA0B;;;;;;IAMlDjB,EADF,CAAAC,cAAA,cAAkE,cACP;IAA9BD,EAAA,CAAAE,UAAA,mBAAAiB,mDAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,eAA6B;;IAEzBD,EADF,CAAAC,cAAA,cAA+F,YAC1D;IAKjCD,EAJA,CAAAU,SAAA,eACmN,eAEuZ,eAE4O;IACx1BV,EAAA,CAAAY,YAAA,EAAI;IAEFZ,EADF,CAAAC,cAAA,WAAM,mBACyB;IAC3BD,EAAA,CAAAU,SAAA,gBAA4C;IAIpDV,EAHM,CAAAY,YAAA,EAAW,EACN,EACH,EACD;;IACPZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAW,MAAA,gBAAQ;IAChBX,EADgB,CAAAY,YAAA,EAAO,EACjB;IACNZ,EAAA,CAAAC,cAAA,eAAyD;IAA9BD,EAAA,CAAAE,UAAA,mBAAAoB,oDAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,gBAA6B;IAC3BD,EAAA,CAAAU,SAAA,wBAAkF;IACpFV,EAAA,CAAAY,YAAA,EAAO;IACPZ,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAW,MAAA,oBAAW;IACpBX,EADoB,CAAAY,YAAA,EAAO,EACrB;IACNZ,EAAA,CAAAC,cAAA,eAAyD;IAA9BD,EAAA,CAAAE,UAAA,mBAAAqB,oDAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,gBAA6B;IAC3BD,EAAA,CAAAU,SAAA,wBAAmF;IACrFV,EAAA,CAAAY,YAAA,EAAO;IACPZ,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAW,MAAA,kBAAS;IAClBX,EADkB,CAAAY,YAAA,EAAO,EACnB;IACNZ,EAAA,CAAAC,cAAA,eAAyD;IAA9BD,EAAA,CAAAE,UAAA,mBAAAsB,oDAAA;MAAAxB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,gBAA6B;IAC3BD,EAAA,CAAAU,SAAA,aAA+D;IACjEV,EAAA,CAAAY,YAAA,EAAO;IACPZ,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAW,MAAA,cAAK;IACdX,EADc,CAAAY,YAAA,EAAO,EACf;IACNZ,EAAA,CAAAC,cAAA,eAAyD;IAA9BD,EAAA,CAAAE,UAAA,mBAAAuB,oDAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,gBAA6B;IAC3BD,EAAA,CAAAU,SAAA,wBAA8F;IAChGV,EAAA,CAAAY,YAAA,EAAO;IACPZ,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAW,MAAA,uBAAc;IACvBX,EADuB,CAAAY,YAAA,EAAO,EACxB;IACNZ,EAAA,CAAAU,SAAA,eAAoC;IACpCV,EAAA,CAAAC,cAAA,eAA0D;IAAnBD,EAAA,CAAAE,UAAA,mBAAAwB,oDAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqB,MAAA,EAAQ;IAAA,EAAC;IACvD3B,EAAA,CAAAC,cAAA,gBAA6B;IAC3BD,EAAA,CAAAU,SAAA,aAAoD;IACtDV,EAAA,CAAAY,YAAA,EAAO;IACPZ,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAW,MAAA,gBAAO;IAChBX,EADgB,CAAAY,YAAA,EAAO,EACjB;IACNZ,EAAA,CAAAU,SAAA,eAAoC;IACpCV,EAAA,CAAAC,cAAA,eAA0E;IAA9BD,EAAA,CAAAE,UAAA,mBAAA0B,oDAAA;MAAA5B,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,iBAAA,EAAmB;IAAA,EAAC;IACvErB,EAAA,CAAAC,cAAA,gBAA2B;IAACD,EAAA,CAAAW,MAAA,qBAAY;IAE5CX,EAF4C,CAAAY,YAAA,EAAO,EAC3C,EACF;;;;;IAGNZ,EAAA,CAAAC,cAAA,YAA2D;IACzDD,EAAA,CAAAU,SAAA,YAAgC;IAChCV,EAAA,CAAAW,MAAA,uBACF;IAAAX,EAAA,CAAAY,YAAA,EAAI;;;AD1Gd,OAAM,MAAOiB,aAAa;EAMJC,WAAA;EALpBC,WAAW,GAAQ,IAAI;EACvBC,UAAU,GAAY,KAAK;EAC3BC,gBAAgB,GAAY,KAAK;EACjCC,cAAc,GAAY,KAAK;EAE/BC,YAAoBL,WAAkC;IAAlC,KAAAA,WAAW,GAAXA,WAAW;EAA2B;EAE1DM,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd;IACA,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMT,WAAW,GAAGQ,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAEvD,IAAIF,SAAS,IAAIP,WAAW,EAAE;MAC5B,IAAI;QACF,IAAI,CAACA,WAAW,GAAGU,IAAI,CAACC,KAAK,CAACX,WAAW,CAAC;QAC1C,IAAI,CAACC,UAAU,GAAG,IAAI;MACxB,CAAC,CAAC,OAAOW,KAAK,EAAE;QACd;QACA,IAAI,CAACX,UAAU,GAAG,KAAK;QACvB,IAAI,CAACD,WAAW,GAAG,IAAI;MACzB;IACF,CAAC,MAAM;MACL,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAACD,WAAW,GAAG,IAAI;IACzB;EACF;EAEAd,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACc,WAAW,EAAE;MACpB,OAAO,IAAI,CAACA,WAAW,CAACa,QAAQ,IAAK,MAAM;IAC7C;IACA,OAAO,OAAO;EAChB;EAEA7B,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACgB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACc,KAAK,EAAE;MAC9C,OAAO,IAAI,CAACd,WAAW,CAACc,KAAK;IAC/B;IACA;IACA,OAAO,gCAAgC;EACzC;EAEApC,kBAAkBA,CAAA;IAChB,IAAI,CAACwB,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;EAChD;EAEAZ,iBAAiBA,CAAA;IACf,IAAI,CAACY,gBAAgB,GAAG,KAAK;EAC/B;EAEAN,MAAMA,CAAA;IACJY,YAAY,CAACO,UAAU,CAAC,WAAW,CAAC;IACpCP,YAAY,CAACO,UAAU,CAAC,aAAa,CAAC;IACtC,IAAI,CAACd,UAAU,GAAG,KAAK;IACvB,IAAI,CAACD,WAAW,GAAG,IAAI;IACvB,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B;IACA;EACF;EAEAc,gBAAgBA,CAAA;IACd,IAAI,CAACb,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C;IACA,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACD,gBAAgB,GAAG,KAAK;IAC/B;EACF;EAEAe,eAAeA,CAAA;IACb,IAAI,CAACd,cAAc,GAAG,KAAK;EAC7B;EAGAe,eAAeA,CAACC,KAAY;IAC1B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAqB;IAC1C,MAAMC,WAAW,GAAGD,MAAM,CAACE,OAAO,CAAC,eAAe,CAAC;IACnD,MAAMC,YAAY,GAAGH,MAAM,CAACE,OAAO,CAAC,gBAAgB,CAAC;IACrD,MAAME,aAAa,GAAGJ,MAAM,CAACE,OAAO,CAAC,iBAAiB,CAAC;IACvD,MAAMG,cAAc,GAAGL,MAAM,CAACE,OAAO,CAAC,kBAAkB,CAAC;IAEzD;IACA,IAAI,CAACD,WAAW,IAAI,CAACE,YAAY,IAAI,IAAI,CAACrB,gBAAgB,EAAE;MAC1D,IAAI,CAACA,gBAAgB,GAAG,KAAK;IAC/B;IAEA;IACA,IAAI,CAACsB,aAAa,IAAI,CAACC,cAAc,IAAI,IAAI,CAACtB,cAAc,EAAE;MAC5D,IAAI,CAACA,cAAc,GAAG,KAAK;IAC7B;EACF;;qCA9FWL,aAAa,EAAA7B,EAAA,CAAAyD,iBAAA,CAAAC,EAAA,CAAAC,qBAAA;EAAA;;UAAb9B,aAAa;IAAA+B,SAAA;IAAAC,YAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAb/D,EAAA,CAAAE,UAAA,mBAAA+D,uCAAAC,MAAA;UAAA,OAAAF,GAAA,CAAAf,eAAA,CAAAiB,MAAA,CAAuB;QAAA,UAAAlE,EAAA,CAAAmE,iBAAA,CAAV;;;;;;;;QCFpBnE,EALN,CAAAC,cAAA,gBAA4B,aAEW,aACO,aAEd;QACxBD,EAAA,CAAAU,SAAA,aACsD;QACxDV,EAAA,CAAAY,YAAA,EAAM;QAGNZ,EAAA,CAAAC,cAAA,gBACuE;QADhBD,EAAA,CAAAE,UAAA,mBAAAkE,+CAAA;UAAA,OAASJ,GAAA,CAAAjB,gBAAA,EAAkB;QAAA,EAAC;QAEjF/C,EAAA,CAAAC,cAAA,cAAkC;QAChCD,EAAA,CAAAU,SAAA,WAAuF;QAE3FV,EADE,CAAAY,YAAA,EAAO,EACA;QAQDZ,EALR,CAAAC,cAAA,aAA0E,aAExC,cAC8C,cACrD,aACU;QAACD,EAAA,CAAAW,MAAA,cAAK;QACrCX,EADqC,CAAAY,YAAA,EAAI,EACpC;QAEHZ,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAW,MAAA,wBAAe;QAC/CX,EAD+C,CAAAY,YAAA,EAAI,EAC9C;QAEHZ,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAW,MAAA,sBAAa;QAC7CX,EAD6C,CAAAY,YAAA,EAAI,EAC5C;QAEHZ,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAW,MAAA,wBAAe;QAC/CX,EAD+C,CAAAY,YAAA,EAAI,EAC9C;QAEHZ,EADF,CAAAC,cAAA,cAAqB,aACU;QAACD,EAAA,CAAAW,MAAA,oBAAW;QAG/CX,EAH+C,CAAAY,YAAA,EAAI,EAC1C,EACF,EACD;QAGNZ,EAAA,CAAAC,cAAA,eAAuD;QAoErDD,EAlEA,CAAAqE,UAAA,KAAAC,6BAAA,kBAAqF,KAAAC,6BAAA,mBAOnB,KAAAC,2BAAA,gBA2DP;QAOnExE,EAHM,CAAAY,YAAA,EAAM,EACF,EACF,EACF;QAIJZ,EADF,CAAAC,cAAA,eAA0B,eACK;QAI3BD,EAHA,CAAAU,SAAA,eAE8C,eACd;QAClCV,EAAA,CAAAY,YAAA,EAAM;QAQMZ,EANZ,CAAAC,cAAA,eAA0B,eACD,eACmB,eAClB,eACe,eACH,cACJ;QAACD,EAAA,CAAAW,MAAA,aAAI;QAC7BX,EAD6B,CAAAY,YAAA,EAAK,EAC5B;QAEJZ,EADF,CAAAC,cAAA,eAA4B,cACJ;QAACD,EAAA,CAAAW,MAAA,eAAM;QAC/BX,EAD+B,CAAAY,YAAA,EAAK,EAC9B;QAEJZ,EADF,CAAAC,cAAA,eAA4B,cACJ;QAACD,EAAA,CAAAW,MAAA,qBAAY;QAQnDX,EARmD,CAAAY,YAAA,EAAK,EACpC,EACF,EACF,EACF,EACF,EACF,EACF,EACC;;;QAxIDZ,EAAA,CAAAa,SAAA,GAAqC;;QAEpBb,EAAA,CAAAa,SAAA,GAAiC;QAACb,EAAlC,CAAAyE,WAAA,aAAAT,GAAA,CAAA9B,cAAA,CAAiC,aAAA8B,GAAA,CAAA9B,cAAA,CAAkC;QAKzDlC,EAAA,CAAAa,SAAA,EAA6B;QAA7Bb,EAAA,CAAAyE,WAAA,SAAAT,GAAA,CAAA9B,cAAA,CAA6B;QAyBhDlC,EAAA,CAAAa,SAAA,IAAgB;QAAhBb,EAAA,CAAAc,UAAA,SAAAkD,GAAA,CAAAhC,UAAA,CAAgB;QAOhBhC,EAAA,CAAAa,SAAA,EAAoC;QAApCb,EAAA,CAAAc,UAAA,SAAAkD,GAAA,CAAAhC,UAAA,IAAAgC,GAAA,CAAA/B,gBAAA,CAAoC;QA2DtCjC,EAAA,CAAAa,SAAA,EAAiB;QAAjBb,EAAA,CAAAc,UAAA,UAAAkD,GAAA,CAAAhC,UAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}