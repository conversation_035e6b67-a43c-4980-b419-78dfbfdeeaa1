{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/taskes/New folder/easydeal-frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';\nimport Swal from 'sweetalert2';\nimport { environment } from 'src/environments/environment';\nimport { MenuComponent } from 'src/app/_metronic/kt/components';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./../../services/request.service\";\nimport * as i2 from \"src/app/pages/authentication\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"../../../../pagination/pagination.component\";\nimport * as i6 from \"../sent-requests-actions-menu/sent-requests-actions-menu.component\";\nconst _c0 = a0 => [\"/requests/render\", a0];\nfunction SentRequestsComponent_tr_30_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const request_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", request_r1.status, \" \");\n  }\n}\nfunction SentRequestsComponent_tr_30_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const request_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", request_r1.status, \" \");\n  }\n}\nfunction SentRequestsComponent_tr_30_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const request_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", request_r1.status, \" \");\n  }\n}\nfunction SentRequestsComponent_tr_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 14)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"input\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"div\", 16)(6, \"div\", 17)(7, \"a\", 18);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 19);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\")(13, \"span\", 20);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"td\")(16, \"span\", 21);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtemplate(19, SentRequestsComponent_tr_30_span_19_Template, 2, 1, \"span\", 22)(20, SentRequestsComponent_tr_30_span_20_Template, 2, 1, \"span\", 23)(21, SentRequestsComponent_tr_30_span_21_Template, 2, 1, \"span\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"td\", 24);\n    i0.ɵɵelement(23, \"app-sent-requests-actions-menu\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const request_r1 = ctx.$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(9, _c0, request_r1.id));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", request_r1.title, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", request_r1.createdAt, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", request_r1.specializationScope, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", request_r1.type, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", request_r1.status === \"new\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", request_r1.status === \"in_processing\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", request_r1.status === \"finished\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"requestId\", request_r1.id);\n  }\n}\nexport class SentRequestsComponent extends BaseGridComponent {\n  cd;\n  requestService;\n  authenticationService;\n  activeFilters = {};\n  newRequestsCountChanged = new EventEmitter();\n  user;\n  userId;\n  constructor(cd, requestService, authenticationService) {\n    super(cd);\n    this.cd = cd;\n    this.requestService = requestService;\n    this.authenticationService = authenticationService;\n    this.setService(requestService);\n    this.orderBy = 'id';\n    this.orderDir = 'desc';\n    this.user = this.authenticationService.getSessionUser();\n    this.userId = this.user.id;\n    this.page.filters = {\n      userId: this.userId\n    };\n  }\n  ngOnInit() {\n    this.page.pageNumber = 0;\n    this.page.size = environment.TABLE_LIMIT;\n    this.page.orderBy = this.orderBy;\n    this.page.orderDir = this.orderDir;\n  }\n  ngAfterViewInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.requestService.getFilters().subscribe( /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (filters) {\n          _this.page.filters = {\n            userId: _this.userId,\n            ...(filters || {})\n          };\n          yield _this.reloadTable(_this.page);\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n  reloadTable(pageInfo) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.page.pageNumber = pageInfo.pageNumber ?? pageInfo;\n      _this2.loading = true;\n      try {\n        // Clone filters to avoid modifying the original\n        const requestFilters = {\n          ..._this2.page.filters\n        };\n        // Remove null/empty filters before sending to backend\n        Object.keys(requestFilters).forEach(key => {\n          if (requestFilters[key] === null || requestFilters[key] === '') {\n            delete requestFilters[key];\n          }\n        });\n        const pagedData = yield _this2._service.getAll({\n          ..._this2.page,\n          filters: requestFilters\n        }).toPromise();\n        _this2.processResponse(pagedData);\n      } catch (error) {\n        _this2.handleError(error);\n      }\n    })();\n  }\n  processResponse(pagedData) {\n    this.rows = Array.isArray(pagedData?.data?.data) ? pagedData.data.data : [];\n    this.rows = [...this.rows];\n    this.page.totalElements = pagedData?.data.count || 0;\n    this.page.count = Math.ceil(this.page.totalElements / this.page.size);\n    this.cd.markForCheck();\n    this.loading = false;\n    this.afterGridLoaded();\n    MenuComponent.reinitialization();\n  }\n  handleError(error) {\n    console.error('API Error:', error);\n    console.error('API Error:', error.error.message);\n    this.cd.markForCheck();\n    this.loading = false;\n    Swal.fire(error.error.message, '', 'error');\n  }\n  static ɵfac = function SentRequestsComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SentRequestsComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.RequestService), i0.ɵɵdirectiveInject(i2.AuthenticationService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SentRequestsComponent,\n    selectors: [[\"app-sent-requests\"]],\n    inputs: {\n      activeFilters: \"activeFilters\"\n    },\n    outputs: {\n      newRequestsCountChanged: \"newRequestsCountChanged\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 33,\n    vars: 9,\n    consts: [[1, \"table-responsive\"], [1, \"table\", \"table-row-bordered\", \"table-row-gray-100\", \"align-middle\", \"gs-0\", \"gy-3\", \"mt-5\"], [1, \"fw-bold\", \"bg-light-dark-blue\", \"text-dark-blue\", \"me-1\", \"ms-1\"], [1, \"w-25px\", \"ps-4\", \"rounded-start\"], [1, \"form-check\", \"form-check-sm\", \"form-check-custom\", \"form-check-solid\"], [\"type\", \"checkbox\", \"value\", \"1\", \"data-kt-check\", \"true\", \"data-kt-check-target\", \".widget-13-check\", 1, \"form-check-input\"], [1, \"min-w-150px\", \"cursor-pointer\", 3, \"click\"], [1, \"ms-1\", \"text-primary\", \"fw-bold\"], [1, \"min-w-140px\", \"cursor-pointer\", 3, \"click\"], [1, \"min-w-120px\", \"cursor-pointer\", 3, \"click\"], [1, \"min-w-100px\", \"text-end\", \"rounded-end\", \"pe-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"m-2\"], [3, \"pageChange\", \"totalItems\", \"itemsPerPage\", \"currentPage\"], [1, \"ps-4\"], [\"type\", \"checkbox\", \"value\", \"1\", 1, \"form-check-input\", \"widget-13-check\"], [1, \"d-flex\", \"align-items-center\"], [1, \"d-flex\", \"justify-content-start\", \"flex-column\"], [1, \"text-gray-900\", \"fw-bold\", \"text-hover-dark-blue\", \"fs-6\", 3, \"routerLink\"], [1, \"text-gray-900\", \"fw-bold\", \"d-block\", \"mb-1\", \"fs-6\"], [1, \"badge\", \"badge-dark-blue\", \"fs-6\"], [1, \"badge\", \"badge-light-info\", \"fs-6\"], [\"class\", \"badge badge-light-danger fs-6\", 4, \"ngIf\"], [\"class\", \"badge badge-light-warning fs-6\", 4, \"ngIf\"], [1, \"text-end\", \"pe-4\"], [3, \"requestId\"], [1, \"badge\", \"badge-light-danger\", \"fs-6\"], [1, \"badge\", \"badge-light-warning\", \"fs-6\"]],\n    template: function SentRequestsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"table\", 1)(2, \"thead\")(3, \"tr\", 2)(4, \"th\", 3)(5, \"div\", 4);\n        i0.ɵɵelement(6, \"input\", 5);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"th\", 6);\n        i0.ɵɵlistener(\"click\", function SentRequestsComponent_Template_th_click_7_listener() {\n          return ctx.sortData(\"title\");\n        });\n        i0.ɵɵtext(8, \" Request Title \");\n        i0.ɵɵelementStart(9, \"span\", 7);\n        i0.ɵɵtext(10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"th\", 8);\n        i0.ɵɵlistener(\"click\", function SentRequestsComponent_Template_th_click_11_listener() {\n          return ctx.sortData(\"created_at\");\n        });\n        i0.ɵɵtext(12, \" Request Date \");\n        i0.ɵɵelementStart(13, \"span\", 7);\n        i0.ɵɵtext(14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"th\", 9);\n        i0.ɵɵlistener(\"click\", function SentRequestsComponent_Template_th_click_15_listener() {\n          return ctx.sortData(\"specialization_scope\");\n        });\n        i0.ɵɵtext(16, \" Specialization \");\n        i0.ɵɵelementStart(17, \"span\", 7);\n        i0.ɵɵtext(18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"th\", 9);\n        i0.ɵɵlistener(\"click\", function SentRequestsComponent_Template_th_click_19_listener() {\n          return ctx.sortData(\"type\");\n        });\n        i0.ɵɵtext(20, \" Type \");\n        i0.ɵɵelementStart(21, \"span\", 7);\n        i0.ɵɵtext(22);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"th\", 9);\n        i0.ɵɵlistener(\"click\", function SentRequestsComponent_Template_th_click_23_listener() {\n          return ctx.sortData(\"status\");\n        });\n        i0.ɵɵtext(24, \" Status \");\n        i0.ɵɵelementStart(25, \"span\", 7);\n        i0.ɵɵtext(26);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"th\", 10);\n        i0.ɵɵtext(28, \"Actions\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(29, \"tbody\");\n        i0.ɵɵtemplate(30, SentRequestsComponent_tr_30_Template, 24, 11, \"tr\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(31, \"div\", 12)(32, \"app-pagination\", 13);\n        i0.ɵɵlistener(\"pageChange\", function SentRequestsComponent_Template_app_pagination_pageChange_32_listener($event) {\n          return ctx.onPageChange($event);\n        });\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(10);\n        i0.ɵɵtextInterpolate(ctx.getSortArrow(\"title\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.getSortArrow(\"created_at\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.getSortArrow(\"specialization_scope\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.getSortArrow(\"type\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.getSortArrow(\"status\"));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.rows);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"totalItems\", ctx.page.totalElements)(\"itemsPerPage\", ctx.page.size)(\"currentPage\", ctx.page.pageNumber);\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgIf, i4.RouterLink, i5.PaginationComponent, i6.SentRequestsActionsMenuComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "BaseGridComponent", "<PERSON><PERSON>", "environment", "MenuComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "request_r1", "status", "ɵɵelement", "ɵɵtemplate", "SentRequestsComponent_tr_30_span_19_Template", "SentRequestsComponent_tr_30_span_20_Template", "SentRequestsComponent_tr_30_span_21_Template", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "id", "title", "createdAt", "specializationScope", "type", "SentRequestsComponent", "cd", "requestService", "authenticationService", "activeFilters", "newRequestsCountChanged", "user", "userId", "constructor", "setService", "orderBy", "orderDir", "getSessionUser", "page", "filters", "ngOnInit", "pageNumber", "size", "TABLE_LIMIT", "ngAfterViewInit", "_this", "_asyncToGenerator", "getFilters", "subscribe", "_ref", "reloadTable", "_x", "apply", "arguments", "pageInfo", "_this2", "loading", "requestFilters", "Object", "keys", "for<PERSON>ach", "key", "pagedData", "_service", "getAll", "to<PERSON>romise", "processResponse", "error", "handleError", "rows", "Array", "isArray", "data", "totalElements", "count", "Math", "ceil", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "afterGridLoaded", "reinitialization", "console", "message", "fire", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "RequestService", "i2", "AuthenticationService", "selectors", "inputs", "outputs", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "SentRequestsComponent_Template", "rf", "ctx", "ɵɵlistener", "SentRequestsComponent_Template_th_click_7_listener", "sortData", "SentRequestsComponent_Template_th_click_11_listener", "SentRequestsComponent_Template_th_click_15_listener", "SentRequestsComponent_Template_th_click_19_listener", "SentRequestsComponent_Template_th_click_23_listener", "SentRequestsComponent_tr_30_Template", "SentRequestsComponent_Template_app_pagination_pageChange_32_listener", "$event", "onPageChange", "ɵɵtextInterpolate", "getSortArrow"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\requests\\components\\sent-requests\\sent-requests.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\requests\\components\\sent-requests\\sent-requests.component.html"], "sourcesContent": ["import { RequestService } from './../../services/request.service';\r\nimport { ChangeDetectorRef, Component, Input, Output, EventEmitter, OnInit, AfterViewInit } from '@angular/core';\r\nimport { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';\r\nimport Swal from 'sweetalert2';\r\nimport { environment } from 'src/environments/environment';\r\nimport { MenuComponent } from 'src/app/_metronic/kt/components';\r\nimport { AuthenticationService } from 'src/app/pages/authentication';\r\n\r\n@Component({\r\n  selector: 'app-sent-requests',\r\n  templateUrl: './sent-requests.component.html',\r\n  styleUrl: './sent-requests.component.scss',\r\n})\r\n\r\nexport class SentRequestsComponent extends BaseGridComponent implements AfterViewInit {\r\n\r\n  @Input() activeFilters: any = {};\r\n  @Output() newRequestsCountChanged = new EventEmitter<number>();\r\n  user : any;\r\n  userId :any;\r\n\r\n  constructor(protected cd: ChangeDetectorRef, protected requestService: RequestService, private authenticationService:AuthenticationService) {\r\n    super(cd);\r\n    this.setService(requestService);\r\n    this.orderBy = 'id';\r\n    this.orderDir = 'desc';\r\n    this.user = this.authenticationService.getSessionUser();\r\n    this.userId = this.user.id;\r\n    this.page.filters = {userId : this.userId};\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.page.pageNumber = 0;\r\n    this.page.size = environment.TABLE_LIMIT;\r\n    this.page.orderBy = this.orderBy;\r\n    this.page.orderDir = this.orderDir;\r\n  }\r\n\r\n  async ngAfterViewInit(): Promise<void> {\r\n    this.requestService.getFilters().subscribe(async (filters: any) => {\r\n      this.page.filters = { userId: this.userId, ...(filters || {}) };\r\n      await this.reloadTable(this.page);\r\n    });\r\n  }\r\n\r\n  async reloadTable(pageInfo: any) {\r\n    this.page.pageNumber = pageInfo.pageNumber ?? pageInfo;\r\n\r\n    this.loading = true;\r\n\r\n    try {\r\n      // Clone filters to avoid modifying the original\r\n      const requestFilters = { ...this.page.filters };\r\n\r\n      // Remove null/empty filters before sending to backend\r\n      Object.keys(requestFilters).forEach(key => {\r\n        if (requestFilters[key] === null || requestFilters[key] === '') {\r\n          delete requestFilters[key];\r\n        }\r\n      });\r\n\r\n      const pagedData = await this._service.getAll({\r\n        ...this.page,\r\n        filters: requestFilters\r\n      }).toPromise();\r\n\r\n      this.processResponse(pagedData);\r\n    } catch (error) {\r\n      this.handleError(error);\r\n    }\r\n  }\r\n\r\n  private processResponse(pagedData: any) {\r\n    this.rows = Array.isArray(pagedData?.data?.data) ? pagedData.data.data : [];\r\n    this.rows = [...this.rows];\r\n    this.page.totalElements = pagedData?.data.count || 0;\r\n    this.page.count = Math.ceil(this.page.totalElements / this.page.size);\r\n\r\n    this.cd.markForCheck();\r\n    this.loading = false;\r\n    this.afterGridLoaded();\r\n    MenuComponent.reinitialization();\r\n  }\r\n\r\n  private handleError(error: any) {\r\n    console.error('API Error:', error);\r\n    console.error('API Error:', error.error.message);\r\n    this.cd.markForCheck();\r\n    this.loading = false;\r\n    Swal.fire(error.error.message, '', 'error');\r\n  }\r\n}\r\n", "<div class=\"table-responsive\">\r\n  <table class=\"table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5\">\r\n    <thead>\r\n      <tr class=\"fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1\">\r\n        <th class=\"w-25px ps-4 rounded-start\">\r\n          <div class=\"form-check form-check-sm form-check-custom form-check-solid\">\r\n            <input class=\"form-check-input\" type=\"checkbox\" value=\"1\" data-kt-check=\"true\"\r\n              data-kt-check-target=\".widget-13-check\" />\r\n          </div>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('title')\">\r\n          Request Title\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('title') }}</span>\r\n        </th>\r\n        <th class=\"min-w-140px cursor-pointer\" (click)=\"sortData('created_at')\">\r\n          Request Date\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('created_at') }}</span>\r\n        </th>\r\n        <th class=\"min-w-120px cursor-pointer\" (click)=\"sortData('specialization_scope')\">\r\n          Specialization\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('specialization_scope') }}</span>\r\n        </th>\r\n        <th class=\"min-w-120px cursor-pointer\" (click)=\"sortData('type')\">\r\n          Type\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('type') }}</span>\r\n        </th>\r\n        <th class=\"min-w-120px cursor-pointer\" (click)=\"sortData('status')\">\r\n          Status\r\n          <span class=\"ms-1 text-primary fw-bold\">{{ getSortArrow('status') }}</span>\r\n        </th>\r\n        <th class=\"min-w-100px text-end rounded-end pe-4\">Actions</th>\r\n      </tr>\r\n    </thead>\r\n    <tbody>\r\n      <tr *ngFor=\"let request of rows\">\r\n        <td class=\"ps-4\">\r\n          <div class=\"form-check form-check-sm form-check-custom form-check-solid\">\r\n            <input class=\"form-check-input widget-13-check\" type=\"checkbox\" value=\"1\" />\r\n          </div>\r\n        </td>\r\n        <td>\r\n          <div class=\"d-flex align-items-center\">\r\n            <div class=\"d-flex justify-content-start flex-column\">\r\n              <a [routerLink]=\"['/requests/render', request.id]\"\r\n                class=\"text-gray-900 fw-bold text-hover-dark-blue fs-6\">\r\n                {{ request.title }}\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </td>\r\n        <td>\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ request.createdAt }}\r\n          </span>\r\n        </td>\r\n        <td>\r\n          <span class=\"badge badge-dark-blue fs-6\">\r\n            {{ request.specializationScope }}\r\n          </span>\r\n        </td>\r\n        <td>\r\n          <span class=\"badge badge-light-info fs-6\">\r\n            {{ request.type }}\r\n          </span>\r\n        </td>\r\n        <td>\r\n          <span *ngIf=\"request.status === 'new'\" class=\"badge badge-light-danger fs-6\">\r\n            {{ request.status }}\r\n          </span>\r\n          <span *ngIf=\"request.status === 'in_processing'\" class=\"badge badge-light-warning fs-6\">\r\n            {{ request.status }}\r\n          </span>\r\n          <span *ngIf=\"request.status === 'finished'\" class=\"badge badge-light-warning fs-6\">\r\n            {{ request.status }}\r\n          </span>\r\n        </td>\r\n        <td class=\"text-end pe-4\">\r\n          <app-sent-requests-actions-menu [requestId]=\"request.id\"></app-sent-requests-actions-menu>\r\n        </td>\r\n      </tr>\r\n    </tbody>\r\n  </table>\r\n\r\n  <div class=\"m-2\">\r\n    <app-pagination [totalItems]=\"page.totalElements\" [itemsPerPage]=\"page.size\" [currentPage]=\"page.pageNumber\"\r\n      (pageChange)=\"onPageChange($event)\">\r\n    </app-pagination>\r\n  </div>\r\n</div>"], "mappings": ";AACA,SAAsDA,YAAY,QAA+B,eAAe;AAChH,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,aAAa,QAAQ,iCAAiC;;;;;;;;;;;IC6DrDC,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,UAAA,CAAAC,MAAA,MACF;;;;;IACAP,EAAA,CAAAC,cAAA,eAAwF;IACtFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,UAAA,CAAAC,MAAA,MACF;;;;;IACAP,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,UAAA,CAAAC,MAAA,MACF;;;;;IAtCAP,EAFJ,CAAAC,cAAA,SAAiC,aACd,aAC0D;IACvED,EAAA,CAAAQ,SAAA,gBAA4E;IAEhFR,EADE,CAAAG,YAAA,EAAM,EACH;IAICH,EAHN,CAAAC,cAAA,SAAI,cACqC,cACiB,YAEM;IACxDD,EAAA,CAAAE,MAAA,GACF;IAGNF,EAHM,CAAAG,YAAA,EAAI,EACA,EACF,EACH;IAEHH,EADF,CAAAC,cAAA,SAAI,gBACoD;IACpDD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACuC;IACvCD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACwC;IACxCD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IACLH,EAAA,CAAAC,cAAA,UAAI;IAOFD,EANA,CAAAS,UAAA,KAAAC,4CAAA,mBAA6E,KAAAC,4CAAA,mBAGW,KAAAC,4CAAA,mBAGL;IAGrFZ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAQ,SAAA,0CAA0F;IAE9FR,EADE,CAAAG,YAAA,EAAK,EACF;;;;IApCMH,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAa,UAAA,eAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAT,UAAA,CAAAU,EAAA,EAA+C;IAEhDhB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,UAAA,CAAAW,KAAA,MACF;IAMFjB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,UAAA,CAAAY,SAAA,MACF;IAIElB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,UAAA,CAAAa,mBAAA,MACF;IAIEnB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,UAAA,CAAAc,IAAA,MACF;IAGOpB,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAa,UAAA,SAAAP,UAAA,CAAAC,MAAA,WAA8B;IAG9BP,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAAa,UAAA,SAAAP,UAAA,CAAAC,MAAA,qBAAwC;IAGxCP,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAa,UAAA,SAAAP,UAAA,CAAAC,MAAA,gBAAmC;IAKVP,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAa,UAAA,cAAAP,UAAA,CAAAU,EAAA,CAAwB;;;AD/DlE,OAAM,MAAOK,qBAAsB,SAAQzB,iBAAiB;EAOpC0B,EAAA;EAAiCC,cAAA;EAAwCC,qBAAA;EALtFC,aAAa,GAAQ,EAAE;EACtBC,uBAAuB,GAAG,IAAI/B,YAAY,EAAU;EAC9DgC,IAAI;EACJC,MAAM;EAENC,YAAsBP,EAAqB,EAAYC,cAA8B,EAAUC,qBAA2C;IACxI,KAAK,CAACF,EAAE,CAAC;IADW,KAAAA,EAAE,GAAFA,EAAE;IAA+B,KAAAC,cAAc,GAAdA,cAAc;IAA0B,KAAAC,qBAAqB,GAArBA,qBAAqB;IAElH,IAAI,CAACM,UAAU,CAACP,cAAc,CAAC;IAC/B,IAAI,CAACQ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,QAAQ,GAAG,MAAM;IACtB,IAAI,CAACL,IAAI,GAAG,IAAI,CAACH,qBAAqB,CAACS,cAAc,EAAE;IACvD,IAAI,CAACL,MAAM,GAAG,IAAI,CAACD,IAAI,CAACX,EAAE;IAC1B,IAAI,CAACkB,IAAI,CAACC,OAAO,GAAG;MAACP,MAAM,EAAG,IAAI,CAACA;IAAM,CAAC;EAC5C;EAEAQ,QAAQA,CAAA;IACN,IAAI,CAACF,IAAI,CAACG,UAAU,GAAG,CAAC;IACxB,IAAI,CAACH,IAAI,CAACI,IAAI,GAAGxC,WAAW,CAACyC,WAAW;IACxC,IAAI,CAACL,IAAI,CAACH,OAAO,GAAG,IAAI,CAACA,OAAO;IAChC,IAAI,CAACG,IAAI,CAACF,QAAQ,GAAG,IAAI,CAACA,QAAQ;EACpC;EAEMQ,eAAeA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACnBD,KAAI,CAAClB,cAAc,CAACoB,UAAU,EAAE,CAACC,SAAS;QAAA,IAAAC,IAAA,GAAAH,iBAAA,CAAC,WAAOP,OAAY,EAAI;UAChEM,KAAI,CAACP,IAAI,CAACC,OAAO,GAAG;YAAEP,MAAM,EAAEa,KAAI,CAACb,MAAM;YAAE,IAAIO,OAAO,IAAI,EAAE;UAAC,CAAE;UAC/D,MAAMM,KAAI,CAACK,WAAW,CAACL,KAAI,CAACP,IAAI,CAAC;QACnC,CAAC;QAAA,iBAAAa,EAAA;UAAA,OAAAF,IAAA,CAAAG,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IAAC;EACL;EAEMH,WAAWA,CAACI,QAAa;IAAA,IAAAC,MAAA;IAAA,OAAAT,iBAAA;MAC7BS,MAAI,CAACjB,IAAI,CAACG,UAAU,GAAGa,QAAQ,CAACb,UAAU,IAAIa,QAAQ;MAEtDC,MAAI,CAACC,OAAO,GAAG,IAAI;MAEnB,IAAI;QACF;QACA,MAAMC,cAAc,GAAG;UAAE,GAAGF,MAAI,CAACjB,IAAI,CAACC;QAAO,CAAE;QAE/C;QACAmB,MAAM,CAACC,IAAI,CAACF,cAAc,CAAC,CAACG,OAAO,CAACC,GAAG,IAAG;UACxC,IAAIJ,cAAc,CAACI,GAAG,CAAC,KAAK,IAAI,IAAIJ,cAAc,CAACI,GAAG,CAAC,KAAK,EAAE,EAAE;YAC9D,OAAOJ,cAAc,CAACI,GAAG,CAAC;UAC5B;QACF,CAAC,CAAC;QAEF,MAAMC,SAAS,SAASP,MAAI,CAACQ,QAAQ,CAACC,MAAM,CAAC;UAC3C,GAAGT,MAAI,CAACjB,IAAI;UACZC,OAAO,EAAEkB;SACV,CAAC,CAACQ,SAAS,EAAE;QAEdV,MAAI,CAACW,eAAe,CAACJ,SAAS,CAAC;MACjC,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdZ,MAAI,CAACa,WAAW,CAACD,KAAK,CAAC;MACzB;IAAC;EACH;EAEQD,eAAeA,CAACJ,SAAc;IACpC,IAAI,CAACO,IAAI,GAAGC,KAAK,CAACC,OAAO,CAACT,SAAS,EAAEU,IAAI,EAAEA,IAAI,CAAC,GAAGV,SAAS,CAACU,IAAI,CAACA,IAAI,GAAG,EAAE;IAC3E,IAAI,CAACH,IAAI,GAAG,CAAC,GAAG,IAAI,CAACA,IAAI,CAAC;IAC1B,IAAI,CAAC/B,IAAI,CAACmC,aAAa,GAAGX,SAAS,EAAEU,IAAI,CAACE,KAAK,IAAI,CAAC;IACpD,IAAI,CAACpC,IAAI,CAACoC,KAAK,GAAGC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACtC,IAAI,CAACmC,aAAa,GAAG,IAAI,CAACnC,IAAI,CAACI,IAAI,CAAC;IAErE,IAAI,CAAChB,EAAE,CAACmD,YAAY,EAAE;IACtB,IAAI,CAACrB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACsB,eAAe,EAAE;IACtB3E,aAAa,CAAC4E,gBAAgB,EAAE;EAClC;EAEQX,WAAWA,CAACD,KAAU;IAC5Ba,OAAO,CAACb,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClCa,OAAO,CAACb,KAAK,CAAC,YAAY,EAAEA,KAAK,CAACA,KAAK,CAACc,OAAO,CAAC;IAChD,IAAI,CAACvD,EAAE,CAACmD,YAAY,EAAE;IACtB,IAAI,CAACrB,OAAO,GAAG,KAAK;IACpBvD,IAAI,CAACiF,IAAI,CAACf,KAAK,CAACA,KAAK,CAACc,OAAO,EAAE,EAAE,EAAE,OAAO,CAAC;EAC7C;;qCA5EWxD,qBAAqB,EAAArB,EAAA,CAAA+E,iBAAA,CAAA/E,EAAA,CAAAgF,iBAAA,GAAAhF,EAAA,CAAA+E,iBAAA,CAAAE,EAAA,CAAAC,cAAA,GAAAlF,EAAA,CAAA+E,iBAAA,CAAAI,EAAA,CAAAC,qBAAA;EAAA;;UAArB/D,qBAAqB;IAAAgE,SAAA;IAAAC,MAAA;MAAA7D,aAAA;IAAA;IAAA8D,OAAA;MAAA7D,uBAAA;IAAA;IAAA8D,QAAA,GAAAxF,EAAA,CAAAyF,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTxB/F,EALV,CAAAC,cAAA,aAA8B,eAC2D,YAC9E,YAC2D,YACxB,aACqC;QACvED,EAAA,CAAAQ,SAAA,eAC4C;QAEhDR,EADE,CAAAG,YAAA,EAAM,EACH;QACLH,EAAA,CAAAC,cAAA,YAAmE;QAA5BD,EAAA,CAAAiG,UAAA,mBAAAC,mDAAA;UAAA,OAASF,GAAA,CAAAG,QAAA,CAAS,OAAO,CAAC;QAAA,EAAC;QAChEnG,EAAA,CAAAE,MAAA,sBACA;QAAAF,EAAA,CAAAC,cAAA,cAAwC;QAAAD,EAAA,CAAAE,MAAA,IAA2B;QACrEF,EADqE,CAAAG,YAAA,EAAO,EACvE;QACLH,EAAA,CAAAC,cAAA,aAAwE;QAAjCD,EAAA,CAAAiG,UAAA,mBAAAG,oDAAA;UAAA,OAASJ,GAAA,CAAAG,QAAA,CAAS,YAAY,CAAC;QAAA,EAAC;QACrEnG,EAAA,CAAAE,MAAA,sBACA;QAAAF,EAAA,CAAAC,cAAA,eAAwC;QAAAD,EAAA,CAAAE,MAAA,IAAgC;QAC1EF,EAD0E,CAAAG,YAAA,EAAO,EAC5E;QACLH,EAAA,CAAAC,cAAA,aAAkF;QAA3CD,EAAA,CAAAiG,UAAA,mBAAAI,oDAAA;UAAA,OAASL,GAAA,CAAAG,QAAA,CAAS,sBAAsB,CAAC;QAAA,EAAC;QAC/EnG,EAAA,CAAAE,MAAA,wBACA;QAAAF,EAAA,CAAAC,cAAA,eAAwC;QAAAD,EAAA,CAAAE,MAAA,IAA0C;QACpFF,EADoF,CAAAG,YAAA,EAAO,EACtF;QACLH,EAAA,CAAAC,cAAA,aAAkE;QAA3BD,EAAA,CAAAiG,UAAA,mBAAAK,oDAAA;UAAA,OAASN,GAAA,CAAAG,QAAA,CAAS,MAAM,CAAC;QAAA,EAAC;QAC/DnG,EAAA,CAAAE,MAAA,cACA;QAAAF,EAAA,CAAAC,cAAA,eAAwC;QAAAD,EAAA,CAAAE,MAAA,IAA0B;QACpEF,EADoE,CAAAG,YAAA,EAAO,EACtE;QACLH,EAAA,CAAAC,cAAA,aAAoE;QAA7BD,EAAA,CAAAiG,UAAA,mBAAAM,oDAAA;UAAA,OAASP,GAAA,CAAAG,QAAA,CAAS,QAAQ,CAAC;QAAA,EAAC;QACjEnG,EAAA,CAAAE,MAAA,gBACA;QAAAF,EAAA,CAAAC,cAAA,eAAwC;QAAAD,EAAA,CAAAE,MAAA,IAA4B;QACtEF,EADsE,CAAAG,YAAA,EAAO,EACxE;QACLH,EAAA,CAAAC,cAAA,cAAkD;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAE7DF,EAF6D,CAAAG,YAAA,EAAK,EAC3D,EACC;QACRH,EAAA,CAAAC,cAAA,aAAO;QACLD,EAAA,CAAAS,UAAA,KAAA+F,oCAAA,mBAAiC;QA+CrCxG,EADE,CAAAG,YAAA,EAAQ,EACF;QAGNH,EADF,CAAAC,cAAA,eAAiB,0BAEuB;QAApCD,EAAA,CAAAiG,UAAA,wBAAAQ,qEAAAC,MAAA;UAAA,OAAcV,GAAA,CAAAW,YAAA,CAAAD,MAAA,CAAoB;QAAA,EAAC;QAGzC1G,EAFI,CAAAG,YAAA,EAAiB,EACb,EACF;;;QA5E4CH,EAAA,CAAAI,SAAA,IAA2B;QAA3BJ,EAAA,CAAA4G,iBAAA,CAAAZ,GAAA,CAAAa,YAAA,UAA2B;QAI3B7G,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAA4G,iBAAA,CAAAZ,GAAA,CAAAa,YAAA,eAAgC;QAIhC7G,EAAA,CAAAI,SAAA,GAA0C;QAA1CJ,EAAA,CAAA4G,iBAAA,CAAAZ,GAAA,CAAAa,YAAA,yBAA0C;QAI1C7G,EAAA,CAAAI,SAAA,GAA0B;QAA1BJ,EAAA,CAAA4G,iBAAA,CAAAZ,GAAA,CAAAa,YAAA,SAA0B;QAI1B7G,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAA4G,iBAAA,CAAAZ,GAAA,CAAAa,YAAA,WAA4B;QAMhD7G,EAAA,CAAAI,SAAA,GAAO;QAAPJ,EAAA,CAAAa,UAAA,YAAAmF,GAAA,CAAA/B,IAAA,CAAO;QAkDjBjE,EAAA,CAAAI,SAAA,GAAiC;QAA4BJ,EAA7D,CAAAa,UAAA,eAAAmF,GAAA,CAAA9D,IAAA,CAAAmC,aAAA,CAAiC,iBAAA2B,GAAA,CAAA9D,IAAA,CAAAI,IAAA,CAA2B,gBAAA0D,GAAA,CAAA9D,IAAA,CAAAG,UAAA,CAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}