{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./core/layout-init.service\";\nimport * as i2 from \"./core/layout.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../partials/layout/extras/scroll-top/scroll-top.component\";\nimport * as i6 from \"./components/header/header.component\";\nimport * as i7 from \"./components/content/content.component\";\nimport * as i8 from \"./components/footer/footer.component\";\nimport * as i9 from \"./components/scripts-init/scripts-init.component\";\nimport * as i10 from \"./components/sidebar/sidebar.component\";\nconst _c0 = [\"ktSidebar\"];\nconst _c1 = [\"ktAside\"];\nconst _c2 = [\"ktHeaderMobile\"];\nconst _c3 = [\"ktHeader\"];\nconst _c4 = a0 => ({\n  \"home-page-layout\": a0\n});\nfunction LayoutComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-header\", 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.appHeaderDefaultClass);\n  }\n}\nfunction LayoutComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-sidebar\", 10, 0);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.appSidebarDefaultClass);\n  }\n}\nfunction LayoutComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-footer\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.appFooterCSSClass)(\"appFooterContainerCSSClass\", ctx_r0.appFooterContainerCSSClass);\n  }\n}\nexport class LayoutComponent {\n  initService;\n  layout;\n  router;\n  activatedRoute;\n  unsubscribe = [];\n  // Public variables\n  // page\n  pageContainerCSSClasses;\n  // header\n  appHeaderDefaultClass = '';\n  appHeaderDisplay;\n  appHeaderDefaultStickyEnabled;\n  appHeaderDefaultStickyAttributes = {};\n  appHeaderDefaultMinimizeEnabled;\n  appHeaderDefaultMinimizeAttributes = {};\n  // toolbar\n  appToolbarDisplay;\n  appToolbarLayout;\n  appToolbarCSSClass = '';\n  appToolbarSwapEnabled;\n  appToolbarSwapAttributes = {};\n  appToolbarStickyEnabled;\n  appToolbarStickyAttributes = {};\n  appToolbarMinimizeEnabled;\n  appToolbarMinimizeAttributes = {};\n  // content\n  appContentContiner;\n  appContentContainerClass;\n  contentCSSClasses;\n  contentContainerCSSClass;\n  // sidebar\n  appSidebarDefaultClass;\n  appSidebarDefaultDrawerEnabled;\n  appSidebarDefaultDrawerAttributes = {};\n  appSidebarDisplay;\n  appSidebarDefaultStickyEnabled;\n  appSidebarDefaultStickyAttributes = {};\n  ktSidebar;\n  /// sidebar panel\n  appSidebarPanelDisplay;\n  // footer\n  appFooterDisplay;\n  appFooterCSSClass = '';\n  appFooterContainer = '';\n  appFooterContainerCSSClass = '';\n  appFooterFixedDesktop;\n  appFooterFixedMobile;\n  // scrolltop\n  scrolltopDisplay;\n  // authentication check\n  isAuthenticationRoute = false;\n  // home page check\n  isHomePage = false;\n  ktAside;\n  ktHeaderMobile;\n  ktHeader;\n  constructor(initService, layout, router, activatedRoute) {\n    this.initService = initService;\n    this.layout = layout;\n    this.router = router;\n    this.activatedRoute = activatedRoute;\n    // define layout type and load layout\n    this.router.events.subscribe(event => {\n      if (event instanceof NavigationEnd) {\n        // Check if current route is authentication\n        this.isAuthenticationRoute = event.url.includes('/authentication');\n        // Check if current route is home page\n        this.isHomePage = event.url === '/home' || event.url === '/';\n        const currentLayoutType = this.layout.currentLayoutTypeSubject.value;\n        const nextLayoutType = this.activatedRoute?.firstChild?.snapshot.data.layout || this.layout.getBaseLayoutTypeFromLocalStorage();\n        if (currentLayoutType !== nextLayoutType || !currentLayoutType) {\n          this.layout.currentLayoutTypeSubject.next(nextLayoutType);\n          this.initService.reInitProps(nextLayoutType);\n        }\n      }\n    });\n  }\n  ngOnInit() {\n    const subscr = this.layout.layoutConfigSubject.asObservable().subscribe(config => {\n      this.updateProps(config);\n    });\n    this.unsubscribe.push(subscr);\n  }\n  updateProps(config) {\n    this.scrolltopDisplay = this.layout.getProp('scrolltop.display', config);\n    this.pageContainerCSSClasses = this.layout.getStringCSSClasses('pageContainer');\n    this.appHeaderDefaultClass = this.layout.getProp('app.header.default.class', config);\n    this.appHeaderDisplay = this.layout.getProp('app.header.display', config);\n    this.appFooterDisplay = this.layout.getProp('app.footer.display', config);\n    this.appSidebarDisplay = this.layout.getProp('app.sidebar.display', config);\n    // Hide sidebar for authentication routes and home page\n    if (this.isAuthenticationRoute || this.isHomePage) {\n      this.appSidebarDisplay = false;\n    }\n    // Hide header and toolbar for home page\n    if (this.isHomePage) {\n      this.appHeaderDisplay = false;\n      this.appToolbarDisplay = false;\n    }\n    this.appSidebarPanelDisplay = this.layout.getProp('app.sidebar-panel.display', config);\n    if (!this.isHomePage) {\n      this.appToolbarDisplay = this.layout.getProp('app.toolbar.display', config);\n    }\n    this.contentCSSClasses = this.layout.getStringCSSClasses('content');\n    this.contentContainerCSSClass = this.layout.getStringCSSClasses('contentContainer');\n    this.appContentContiner = this.layout.getProp('app.content.container', config);\n    this.appContentContainerClass = this.layout.getProp('app.content.containerClass', config);\n    // footer\n    if (this.appFooterDisplay) {\n      this.updateFooter(config);\n    }\n    // sidebar\n    if (this.appSidebarDisplay) {\n      this.updateSidebar(config);\n    }\n    // header\n    if (this.appHeaderDisplay) {\n      this.updateHeader(config);\n    }\n    // toolbar\n    if (this.appToolbarDisplay) {\n      this.updateToolbar(config);\n    }\n  }\n  updateSidebar(config) {\n    this.appSidebarDefaultClass = this.layout.getProp('app.sidebar.default.class', config);\n    this.appSidebarDefaultDrawerEnabled = this.layout.getProp('app.sidebar.default.drawer.enabled', config);\n    if (this.appSidebarDefaultDrawerEnabled) {\n      this.appSidebarDefaultDrawerAttributes = this.layout.getProp('app.sidebar.default.drawer.attributes', config);\n    }\n    this.appSidebarDefaultStickyEnabled = this.layout.getProp('app.sidebar.default.sticky.enabled', config);\n    if (this.appSidebarDefaultStickyEnabled) {\n      this.appSidebarDefaultStickyAttributes = this.layout.getProp('app.sidebar.default.sticky.attributes', config);\n    }\n    setTimeout(() => {\n      const sidebarElement = document.getElementById('kt_app_sidebar');\n      // sidebar\n      if (this.appSidebarDisplay && sidebarElement) {\n        const sidebarAttributes = sidebarElement.getAttributeNames().filter(t => t.indexOf('data-') > -1);\n        sidebarAttributes.forEach(attr => sidebarElement.removeAttribute(attr));\n        if (this.appSidebarDefaultDrawerEnabled) {\n          for (const key in this.appSidebarDefaultDrawerAttributes) {\n            if (this.appSidebarDefaultDrawerAttributes.hasOwnProperty(key)) {\n              sidebarElement.setAttribute(key, this.appSidebarDefaultDrawerAttributes[key]);\n            }\n          }\n        }\n        if (this.appSidebarDefaultStickyEnabled) {\n          for (const key in this.appSidebarDefaultStickyAttributes) {\n            if (this.appSidebarDefaultStickyAttributes.hasOwnProperty(key)) {\n              sidebarElement.setAttribute(key, this.appSidebarDefaultStickyAttributes[key]);\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n  updateHeader(config) {\n    this.appHeaderDefaultStickyEnabled = this.layout.getProp('app.header.default.sticky.enabled', config);\n    if (this.appHeaderDefaultStickyEnabled) {\n      this.appHeaderDefaultStickyAttributes = this.layout.getProp('app.header.default.sticky.attributes', config);\n    }\n    this.appHeaderDefaultMinimizeEnabled = this.layout.getProp('app.header.default.minimize.enabled', config);\n    if (this.appHeaderDefaultMinimizeEnabled) {\n      this.appHeaderDefaultMinimizeAttributes = this.layout.getProp('app.header.default.minimize.attributes', config);\n    }\n    setTimeout(() => {\n      const headerElement = document.getElementById('kt_app_header');\n      // header\n      if (this.appHeaderDisplay && headerElement) {\n        const headerAttributes = headerElement.getAttributeNames().filter(t => t.indexOf('data-') > -1);\n        headerAttributes.forEach(attr => headerElement.removeAttribute(attr));\n        if (this.appHeaderDefaultStickyEnabled) {\n          for (const key in this.appHeaderDefaultStickyAttributes) {\n            if (this.appHeaderDefaultStickyAttributes.hasOwnProperty(key)) {\n              headerElement.setAttribute(key, this.appHeaderDefaultStickyAttributes[key]);\n            }\n          }\n        }\n        if (this.appHeaderDefaultMinimizeEnabled) {\n          for (const key in this.appHeaderDefaultMinimizeAttributes) {\n            if (this.appHeaderDefaultMinimizeAttributes.hasOwnProperty(key)) {\n              headerElement.setAttribute(key, this.appHeaderDefaultMinimizeAttributes[key]);\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n  updateFooter(config) {\n    this.appFooterCSSClass = this.layout.getProp('app.footer.class', config);\n    this.appFooterContainer = this.layout.getProp('app.footer.container', config);\n    this.appFooterContainerCSSClass = this.layout.getProp('app.footer.containerClass', config);\n    if (this.appFooterContainer === 'fixed') {\n      this.appFooterContainerCSSClass += ' container-xxl';\n    } else {\n      if (this.appFooterContainer === 'fluid') {\n        this.appFooterContainerCSSClass += ' container-fluid';\n      }\n    }\n    this.appFooterFixedDesktop = this.layout.getProp('app.footer.fixed.desktop', config);\n    if (this.appFooterFixedDesktop) {\n      document.body.setAttribute('data-kt-app-footer-fixed', 'true');\n    }\n    this.appFooterFixedMobile = this.layout.getProp('app.footer.fixed.mobile');\n    if (this.appFooterFixedMobile) {\n      document.body.setAttribute('data-kt-app-footer-fixed-mobile', 'true');\n    }\n  }\n  updateToolbar(config) {\n    this.appToolbarLayout = this.layout.getProp('app.toolbar.layout', config);\n    this.appToolbarSwapEnabled = this.layout.getProp('app.toolbar.swap.enabled', config);\n    if (this.appToolbarSwapEnabled) {\n      this.appToolbarSwapAttributes = this.layout.getProp('app.toolbar.swap.attributes', config);\n    }\n    this.appToolbarStickyEnabled = this.layout.getProp('app.toolbar.sticky.enabled', config);\n    if (this.appToolbarStickyEnabled) {\n      this.appToolbarStickyAttributes = this.layout.getProp('app.toolbar.sticky.attributes', config);\n    }\n    this.appToolbarCSSClass = this.layout.getProp('app.toolbar.class', config) || '';\n    this.appToolbarMinimizeEnabled = this.layout.getProp('app.toolbar.minimize.enabled', config);\n    if (this.appToolbarMinimizeEnabled) {\n      this.appToolbarMinimizeAttributes = this.layout.getProp('app.toolbar.minimize.attributes', config);\n      this.appToolbarCSSClass += ' app-toolbar-minimize';\n    }\n    setTimeout(() => {\n      const toolbarElement = document.getElementById('kt_app_toolbar');\n      // toolbar\n      if (this.appToolbarDisplay && toolbarElement) {\n        const toolbarAttributes = toolbarElement.getAttributeNames().filter(t => t.indexOf('data-') > -1);\n        toolbarAttributes.forEach(attr => toolbarElement.removeAttribute(attr));\n        if (this.appToolbarSwapEnabled) {\n          for (const key in this.appToolbarSwapAttributes) {\n            if (this.appToolbarSwapAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(key, this.appToolbarSwapAttributes[key]);\n            }\n          }\n        }\n        if (this.appToolbarStickyEnabled) {\n          for (const key in this.appToolbarStickyAttributes) {\n            if (this.appToolbarStickyAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(key, this.appToolbarStickyAttributes[key]);\n            }\n          }\n        }\n        if (this.appToolbarMinimizeEnabled) {\n          for (const key in this.appToolbarMinimizeAttributes) {\n            if (this.appToolbarMinimizeAttributes.hasOwnProperty(key)) {\n              toolbarElement.setAttribute(key, this.appToolbarMinimizeAttributes[key]);\n            }\n          }\n        }\n      }\n    }, 0);\n  }\n  ngOnDestroy() {\n    this.unsubscribe.forEach(sb => sb.unsubscribe());\n  }\n  static ɵfac = function LayoutComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LayoutComponent)(i0.ɵɵdirectiveInject(i1.LayoutInitService), i0.ɵɵdirectiveInject(i2.LayoutService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LayoutComponent,\n    selectors: [[\"app-layout\"]],\n    viewQuery: function LayoutComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n        i0.ɵɵviewQuery(_c1, 7);\n        i0.ɵɵviewQuery(_c2, 7);\n        i0.ɵɵviewQuery(_c3, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ktSidebar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ktAside = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ktHeaderMobile = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ktHeader = _t.first);\n      }\n    },\n    decls: 12,\n    vars: 10,\n    consts: [[\"ktSidebar\", \"\"], [\"id\", \"kt_app_root\", 1, \"d-flex\", \"flex-column\", \"flex-root\", \"app-root\", 3, \"ngClass\"], [\"id\", \"kt_app_page\", 1, \"app-page\", \"flex-column\", \"flex-column-fluid\"], [4, \"ngIf\"], [\"id\", \"kt_app_wrapper\", 1, \"app-wrapper\", \"flex-column\", \"flex-row-fluid\", \"mt-2\"], [\"id\", \"kt_app_main\", 1, \"app-main\", \"flex-column\", \"flex-row-fluid\"], [1, \"d-flex\", \"flex-column\", \"flex-column-fluid\"], [\"id\", \" kt_app_content\", 1, \"app-content\", \"pt-3\", 3, \"ngClass\", \"contentContainerCSSClass\", \"appContentContiner\", \"appContentContainerClass\"], [\"id\", \"kt_scrolltop\", \"data-kt-scrolltop\", \"true\", 1, \"scrolltop\"], [\"id\", \"kt_app_header\", \"data-kt-sticky\", \"true\", \"data-kt-sticky-activate\", \"{default: true, lg: true}\", \"data-kt-sticky-name\", \"app-header-minimize\", \"data-kt-sticky-offset\", \"{default: '200px', lg: '0'}\", \"data-kt-sticky-animation\", \"false\", 1, \"app-header\", \"d-flex\", \"d-lg-none\", 3, \"ngClass\"], [\"id\", \"kt_app_sidebar\", 1, \"app-sidebar\", \"flex-column\", 3, \"ngClass\"], [\"id\", \"kt_app_footer\", 1, \"app-footer\", 3, \"ngClass\", \"appFooterContainerCSSClass\"]],\n    template: function LayoutComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n        i0.ɵɵtemplate(2, LayoutComponent_ng_container_2_Template, 2, 1, \"ng-container\", 3);\n        i0.ɵɵelementStart(3, \"div\", 4);\n        i0.ɵɵtemplate(4, LayoutComponent_ng_container_4_Template, 3, 1, \"ng-container\", 3);\n        i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n        i0.ɵɵelement(7, \"app-content\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(8, LayoutComponent_ng_container_8_Template, 2, 2, \"ng-container\", 3);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelement(9, \"app-scripts-init\");\n        i0.ɵɵelementContainerStart(10);\n        i0.ɵɵelement(11, \"app-scroll-top\", 8);\n        i0.ɵɵelementContainerEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c4, ctx.isHomePage));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.appHeaderDisplay);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.appSidebarDisplay);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", ctx.contentCSSClasses)(\"contentContainerCSSClass\", ctx.contentContainerCSSClass)(\"appContentContiner\", ctx.appContentContiner)(\"appContentContainerClass\", ctx.appContentContainerClass);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.appFooterDisplay);\n      }\n    },\n    dependencies: [i4.NgClass, i4.NgIf, i5.LayoutScrollTopComponent, i6.HeaderComponent, i7.ContentComponent, i8.FooterComponent, i9.ScriptsInitComponent, i10.SidebarComponent],\n    styles: [\"[_nghost-%COMP%] {\\n  height: 100%;\\n  margin: 0;\\n}\\n[_nghost-%COMP%]   .flex-root[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n.page-loaded[_ngcontent-%COMP%]   app-layout[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transition: opacity 1s ease-in-out;\\n}\\n\\n.home-page-layout[_ngcontent-%COMP%]   .app-wrapper[_ngcontent-%COMP%] {\\n  margin-top: 0 !important;\\n}\\n.home-page-layout[_ngcontent-%COMP%]   .app-main[_ngcontent-%COMP%] {\\n  margin-left: 0 !important;\\n  width: 100% !important;\\n}\\n.home-page-layout[_ngcontent-%COMP%]   .app-content[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvX21ldHJvbmljL2xheW91dC9sYXlvdXQuY29tcG9uZW50LnNjc3MiLCJ3ZWJwYWNrOi8vLi8uLi8uLi8uLi8uLi8uLi9jb21wdSUyMHpvbmUvRGVza3RvcC90YXNrZXMvTmV3JTIwZm9sZGVyL2Vhc3lkZWFsLWZyb250ZW5kL3NyYy9hcHAvX21ldHJvbmljL2xheW91dC9sYXlvdXQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxZQUFBO0VBQ0EsU0FBQTtBQ0NGO0FEQ0U7RUFDRSxZQUFBO0FDQ0o7O0FESUU7RUFDRSxVQUFBO0VBQ0Esa0NBQUE7QUNESjs7QURPRTtFQUNFLHdCQUFBO0FDSko7QURPRTtFQUNFLHlCQUFBO0VBQ0Esc0JBQUE7QUNMSjtBRFFFO0VBQ0UscUJBQUE7QUNOSiIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IHtcclxuICBoZWlnaHQ6IDEwMCU7XHJcbiAgbWFyZ2luOiAwO1xyXG5cclxuICAuZmxleC1yb290IHtcclxuICAgIGhlaWdodDogMTAwJTtcclxuICB9XHJcbn1cclxuXHJcbi5wYWdlLWxvYWRlZCB7XHJcbiAgYXBwLWxheW91dCB7XHJcbiAgICBvcGFjaXR5OiAxO1xyXG4gICAgdHJhbnNpdGlvbjogb3BhY2l0eSAxcyBlYXNlLWluLW91dDtcclxuICB9XHJcbn1cclxuXHJcbi8vIEhvbWUgcGFnZSBzcGVjaWZpYyBzdHlsZXNcclxuLmhvbWUtcGFnZS1sYXlvdXQge1xyXG4gIC5hcHAtd3JhcHBlciB7XHJcbiAgICBtYXJnaW4tdG9wOiAwICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG5cclxuICAuYXBwLW1haW4ge1xyXG4gICAgbWFyZ2luLWxlZnQ6IDAgIWltcG9ydGFudDtcclxuICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG5cclxuICAuYXBwLWNvbnRlbnQge1xyXG4gICAgcGFkZGluZzogMCAhaW1wb3J0YW50O1xyXG4gIH1cclxufVxyXG4iLCI6aG9zdCB7XG4gIGhlaWdodDogMTAwJTtcbiAgbWFyZ2luOiAwO1xufVxuOmhvc3QgLmZsZXgtcm9vdCB7XG4gIGhlaWdodDogMTAwJTtcbn1cblxuLnBhZ2UtbG9hZGVkIGFwcC1sYXlvdXQge1xuICBvcGFjaXR5OiAxO1xuICB0cmFuc2l0aW9uOiBvcGFjaXR5IDFzIGVhc2UtaW4tb3V0O1xufVxuXG4uaG9tZS1wYWdlLWxheW91dCAuYXBwLXdyYXBwZXIge1xuICBtYXJnaW4tdG9wOiAwICFpbXBvcnRhbnQ7XG59XG4uaG9tZS1wYWdlLWxheW91dCAuYXBwLW1haW4ge1xuICBtYXJnaW4tbGVmdDogMCAhaW1wb3J0YW50O1xuICB3aWR0aDogMTAwJSAhaW1wb3J0YW50O1xufVxuLmhvbWUtcGFnZS1sYXlvdXQgLmFwcC1jb250ZW50IHtcbiAgcGFkZGluZzogMCAhaW1wb3J0YW50O1xufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["NavigationEnd", "i0", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "appHeaderDefaultClass", "appSidebarDefaultClass", "appFooterCSSClass", "appFooterContainerCSSClass", "LayoutComponent", "initService", "layout", "router", "activatedRoute", "unsubscribe", "pageContainerCSSClasses", "appHeaderDisplay", "appHeaderDefaultStickyEnabled", "appHeaderDefaultStickyAttributes", "appHeaderDefaultMinimizeEnabled", "appHeaderDefaultMinimizeAttributes", "appToolbarDisplay", "appToolbarLayout", "appToolbarCSSClass", "appToolbarSwapEnabled", "appToolbarSwapAttributes", "appToolbarStickyEnabled", "appToolbarStickyAttributes", "appToolbarMinimizeEnabled", "appToolbarMinimizeAttributes", "appContentContiner", "appContentContainerClass", "contentCSSClasses", "contentContainerCSSClass", "appSidebarDefaultDrawerEnabled", "appSidebarDefaultDrawerAttributes", "appSidebarDisplay", "appSidebarDefaultStickyEnabled", "appSidebarDefaultStickyAttributes", "ktSidebar", "appSidebarPanelDisplay", "appFooterDisplay", "appFooter<PERSON><PERSON><PERSON>", "appFooterFixedDesktop", "appFooterFixedMobile", "scrolltopDisplay", "isAuthenticationRoute", "isHomePage", "ktAside", "ktHeaderMobile", "ktHeader", "constructor", "events", "subscribe", "event", "url", "includes", "currentLayoutType", "currentLayoutTypeSubject", "value", "nextLayoutType", "<PERSON><PERSON><PERSON><PERSON>", "snapshot", "data", "getBaseLayoutTypeFromLocalStorage", "next", "reInitProps", "ngOnInit", "subscr", "layoutConfigSubject", "asObservable", "config", "updateProps", "push", "getProp", "getStringCSSClasses", "updateFooter", "updateSidebar", "updateHeader", "updateToolbar", "setTimeout", "sidebarElement", "document", "getElementById", "sidebarAttributes", "getAttributeNames", "filter", "t", "indexOf", "for<PERSON>ach", "attr", "removeAttribute", "key", "hasOwnProperty", "setAttribute", "headerElement", "headerAttributes", "body", "toolbarElement", "toolbarAttributes", "ngOnDestroy", "sb", "ɵɵdirectiveInject", "i1", "LayoutInitService", "i2", "LayoutService", "i3", "Router", "ActivatedRoute", "selectors", "viewQuery", "LayoutComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵtemplate", "LayoutComponent_ng_container_2_Template", "LayoutComponent_ng_container_4_Template", "ɵɵelementEnd", "LayoutComponent_ng_container_8_Template", "ɵɵpureFunction1", "_c4"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\_metronic\\layout\\layout.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\_metronic\\layout\\layout.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  ViewChild,\r\n  ElementRef,\r\n  On<PERSON><PERSON>roy,\r\n} from '@angular/core';\r\nimport { ActivatedRoute, NavigationEnd, Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport { LayoutService } from './core/layout.service';\r\nimport { LayoutInitService } from './core/layout-init.service';\r\nimport { ILayout, LayoutType } from './core/configs/config';\r\n\r\n@Component({\r\n  selector: 'app-layout',\r\n  templateUrl: './layout.component.html',\r\n  styleUrls: ['./layout.component.scss'],\r\n})\r\nexport class LayoutComponent implements OnInit, OnDestroy {\r\n  private unsubscribe: Subscription[] = [];\r\n\r\n  // Public variables\r\n  // page\r\n  pageContainerCSSClasses: string;\r\n  // header\r\n  appHeaderDefaultClass: string = '';\r\n  appHeaderDisplay: boolean;\r\n  appHeaderDefaultStickyEnabled: boolean;\r\n  appHeaderDefaultStickyAttributes: { [attrName: string]: string } = {};\r\n  appHeaderDefaultMinimizeEnabled: boolean;\r\n  appHeaderDefaultMinimizeAttributes: { [attrName: string]: string } = {};\r\n  // toolbar\r\n  appToolbarDisplay: boolean;\r\n  appToolbarLayout: 'classic' | 'accounting' | 'extended' | 'reports' | 'saas';\r\n  appToolbarCSSClass: string = '';\r\n  appToolbarSwapEnabled: boolean;\r\n  appToolbarSwapAttributes: { [attrName: string]: string } = {};\r\n  appToolbarStickyEnabled: boolean;\r\n  appToolbarStickyAttributes: { [attrName: string]: string } = {};\r\n  appToolbarMinimizeEnabled: boolean;\r\n  appToolbarMinimizeAttributes: { [attrName: string]: string } = {};\r\n\r\n  // content\r\n  appContentContiner?: 'fixed' | 'fluid';\r\n  appContentContainerClass: string;\r\n  contentCSSClasses: string;\r\n  contentContainerCSSClass: string;\r\n  // sidebar\r\n  appSidebarDefaultClass: string;\r\n  appSidebarDefaultDrawerEnabled: boolean;\r\n  appSidebarDefaultDrawerAttributes: { [attrName: string]: string } = {};\r\n  appSidebarDisplay: boolean;\r\n  appSidebarDefaultStickyEnabled: boolean;\r\n  appSidebarDefaultStickyAttributes: { [attrName: string]: string } = {};\r\n  @ViewChild('ktSidebar', { static: true }) ktSidebar: ElementRef;\r\n  /// sidebar panel\r\n  appSidebarPanelDisplay: boolean;\r\n  // footer\r\n  appFooterDisplay: boolean;\r\n  appFooterCSSClass: string = '';\r\n  appFooterContainer: string = '';\r\n  appFooterContainerCSSClass: string = '';\r\n  appFooterFixedDesktop: boolean;\r\n  appFooterFixedMobile: boolean;\r\n\r\n  // scrolltop\r\n  scrolltopDisplay: boolean;\r\n\r\n  // authentication check\r\n  isAuthenticationRoute: boolean = false;\r\n  // home page check\r\n  isHomePage: boolean = false;\r\n\r\n  @ViewChild('ktAside', { static: true }) ktAside: ElementRef;\r\n  @ViewChild('ktHeaderMobile', { static: true }) ktHeaderMobile: ElementRef;\r\n  @ViewChild('ktHeader', { static: true }) ktHeader: ElementRef;\r\n\r\n  constructor(\r\n    private initService: LayoutInitService,\r\n    private layout: LayoutService,\r\n    private router: Router,\r\n    private activatedRoute: ActivatedRoute\r\n  ) {\r\n    // define layout type and load layout\r\n    this.router.events.subscribe((event) => {\r\n      if (event instanceof NavigationEnd) {\r\n        // Check if current route is authentication\r\n        this.isAuthenticationRoute = event.url.includes('/authentication');\r\n\r\n        // Check if current route is home page\r\n        this.isHomePage = event.url === '/home' || event.url === '/';\r\n\r\n        const currentLayoutType = this.layout.currentLayoutTypeSubject.value;\r\n\r\n        const nextLayoutType: LayoutType =\r\n          this.activatedRoute?.firstChild?.snapshot.data.layout ||\r\n          this.layout.getBaseLayoutTypeFromLocalStorage();\r\n\r\n        if (currentLayoutType !== nextLayoutType || !currentLayoutType) {\r\n          this.layout.currentLayoutTypeSubject.next(nextLayoutType);\r\n          this.initService.reInitProps(nextLayoutType);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnInit() {\r\n    const subscr = this.layout.layoutConfigSubject\r\n      .asObservable()\r\n      .subscribe((config) => {\r\n        this.updateProps(config);\r\n      });\r\n    this.unsubscribe.push(subscr);\r\n  }\r\n\r\n  updateProps(config: ILayout) {\r\n    this.scrolltopDisplay = this.layout.getProp(\r\n      'scrolltop.display',\r\n      config\r\n    ) as boolean;\r\n    this.pageContainerCSSClasses =\r\n      this.layout.getStringCSSClasses('pageContainer');\r\n    this.appHeaderDefaultClass = this.layout.getProp(\r\n      'app.header.default.class',\r\n      config\r\n    ) as string;\r\n    this.appHeaderDisplay = this.layout.getProp(\r\n      'app.header.display',\r\n      config\r\n    ) as boolean;\r\n    this.appFooterDisplay = this.layout.getProp(\r\n      'app.footer.display',\r\n      config\r\n    ) as boolean;\r\n    this.appSidebarDisplay = this.layout.getProp(\r\n      'app.sidebar.display',\r\n      config\r\n    ) as boolean;\r\n\r\n    // Hide sidebar for authentication routes and home page\r\n    if (this.isAuthenticationRoute || this.isHomePage) {\r\n      this.appSidebarDisplay = false;\r\n    }\r\n\r\n    // Hide header and toolbar for home page\r\n    if (this.isHomePage) {\r\n      this.appHeaderDisplay = false;\r\n      this.appToolbarDisplay = false;\r\n    }\r\n\r\n    this.appSidebarPanelDisplay = this.layout.getProp(\r\n      'app.sidebar-panel.display',\r\n      config\r\n    ) as boolean;\r\n\r\n    if (!this.isHomePage) {\r\n      this.appToolbarDisplay = this.layout.getProp(\r\n        'app.toolbar.display',\r\n        config\r\n      ) as boolean;\r\n    }\r\n    this.contentCSSClasses = this.layout.getStringCSSClasses('content');\r\n    this.contentContainerCSSClass =\r\n      this.layout.getStringCSSClasses('contentContainer');\r\n    this.appContentContiner = this.layout.getProp(\r\n      'app.content.container',\r\n      config\r\n    ) as 'fixed' | 'fluid';\r\n    this.appContentContainerClass = this.layout.getProp(\r\n      'app.content.containerClass',\r\n      config\r\n    ) as string;\r\n    // footer\r\n    if (this.appFooterDisplay) {\r\n      this.updateFooter(config);\r\n    }\r\n    // sidebar\r\n    if (this.appSidebarDisplay) {\r\n      this.updateSidebar(config);\r\n    }\r\n    // header\r\n    if (this.appHeaderDisplay) {\r\n      this.updateHeader(config);\r\n    }\r\n    // toolbar\r\n    if (this.appToolbarDisplay) {\r\n      this.updateToolbar(config);\r\n    }\r\n  }\r\n\r\n  updateSidebar(config: ILayout) {\r\n    this.appSidebarDefaultClass = this.layout.getProp(\r\n      'app.sidebar.default.class',\r\n      config\r\n    ) as string;\r\n\r\n    this.appSidebarDefaultDrawerEnabled = this.layout.getProp(\r\n      'app.sidebar.default.drawer.enabled',\r\n      config\r\n    ) as boolean;\r\n    if (this.appSidebarDefaultDrawerEnabled) {\r\n      this.appSidebarDefaultDrawerAttributes = this.layout.getProp(\r\n        'app.sidebar.default.drawer.attributes',\r\n        config\r\n      ) as { [attrName: string]: string };\r\n    }\r\n\r\n    this.appSidebarDefaultStickyEnabled = this.layout.getProp(\r\n      'app.sidebar.default.sticky.enabled',\r\n      config\r\n    ) as boolean;\r\n    if (this.appSidebarDefaultStickyEnabled) {\r\n      this.appSidebarDefaultStickyAttributes = this.layout.getProp(\r\n        'app.sidebar.default.sticky.attributes',\r\n        config\r\n      ) as { [attrName: string]: string };\r\n    }\r\n\r\n    setTimeout(() => {\r\n      const sidebarElement = document.getElementById('kt_app_sidebar');\r\n      // sidebar\r\n      if (this.appSidebarDisplay && sidebarElement) {\r\n        const sidebarAttributes = sidebarElement\r\n          .getAttributeNames()\r\n          .filter((t) => t.indexOf('data-') > -1);\r\n        sidebarAttributes.forEach((attr) =>\r\n          sidebarElement.removeAttribute(attr)\r\n        );\r\n\r\n        if (this.appSidebarDefaultDrawerEnabled) {\r\n          for (const key in this.appSidebarDefaultDrawerAttributes) {\r\n            if (this.appSidebarDefaultDrawerAttributes.hasOwnProperty(key)) {\r\n              sidebarElement.setAttribute(\r\n                key,\r\n                this.appSidebarDefaultDrawerAttributes[key]\r\n              );\r\n            }\r\n          }\r\n        }\r\n\r\n        if (this.appSidebarDefaultStickyEnabled) {\r\n          for (const key in this.appSidebarDefaultStickyAttributes) {\r\n            if (this.appSidebarDefaultStickyAttributes.hasOwnProperty(key)) {\r\n              sidebarElement.setAttribute(\r\n                key,\r\n                this.appSidebarDefaultStickyAttributes[key]\r\n              );\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }, 0);\r\n  }\r\n\r\n  updateHeader(config: ILayout) {\r\n    this.appHeaderDefaultStickyEnabled = this.layout.getProp(\r\n      'app.header.default.sticky.enabled',\r\n      config\r\n    ) as boolean;\r\n    if (this.appHeaderDefaultStickyEnabled) {\r\n      this.appHeaderDefaultStickyAttributes = this.layout.getProp(\r\n        'app.header.default.sticky.attributes',\r\n        config\r\n      ) as { [attrName: string]: string };\r\n    }\r\n\r\n    this.appHeaderDefaultMinimizeEnabled = this.layout.getProp(\r\n      'app.header.default.minimize.enabled',\r\n      config\r\n    ) as boolean;\r\n    if (this.appHeaderDefaultMinimizeEnabled) {\r\n      this.appHeaderDefaultMinimizeAttributes = this.layout.getProp(\r\n        'app.header.default.minimize.attributes',\r\n        config\r\n      ) as { [attrName: string]: string };\r\n    }\r\n\r\n    setTimeout(() => {\r\n      const headerElement = document.getElementById('kt_app_header');\r\n      // header\r\n      if (this.appHeaderDisplay && headerElement) {\r\n        const headerAttributes = headerElement\r\n          .getAttributeNames()\r\n          .filter((t) => t.indexOf('data-') > -1);\r\n        headerAttributes.forEach((attr) => headerElement.removeAttribute(attr));\r\n\r\n        if (this.appHeaderDefaultStickyEnabled) {\r\n          for (const key in this.appHeaderDefaultStickyAttributes) {\r\n            if (this.appHeaderDefaultStickyAttributes.hasOwnProperty(key)) {\r\n              headerElement.setAttribute(\r\n                key,\r\n                this.appHeaderDefaultStickyAttributes[key]\r\n              );\r\n            }\r\n          }\r\n        }\r\n\r\n        if (this.appHeaderDefaultMinimizeEnabled) {\r\n          for (const key in this.appHeaderDefaultMinimizeAttributes) {\r\n            if (this.appHeaderDefaultMinimizeAttributes.hasOwnProperty(key)) {\r\n              headerElement.setAttribute(\r\n                key,\r\n                this.appHeaderDefaultMinimizeAttributes[key]\r\n              );\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }, 0);\r\n  }\r\n\r\n  updateFooter(config: ILayout) {\r\n    this.appFooterCSSClass = this.layout.getProp(\r\n      'app.footer.class',\r\n      config\r\n    ) as string;\r\n    this.appFooterContainer = this.layout.getProp(\r\n      'app.footer.container',\r\n      config\r\n    ) as string;\r\n    this.appFooterContainerCSSClass = this.layout.getProp(\r\n      'app.footer.containerClass',\r\n      config\r\n    ) as string;\r\n    if (this.appFooterContainer === 'fixed') {\r\n      this.appFooterContainerCSSClass += ' container-xxl';\r\n    } else {\r\n      if (this.appFooterContainer === 'fluid') {\r\n        this.appFooterContainerCSSClass += ' container-fluid';\r\n      }\r\n    }\r\n\r\n    this.appFooterFixedDesktop = this.layout.getProp(\r\n      'app.footer.fixed.desktop',\r\n      config\r\n    ) as boolean;\r\n    if (this.appFooterFixedDesktop) {\r\n      document.body.setAttribute('data-kt-app-footer-fixed', 'true');\r\n    }\r\n\r\n    this.appFooterFixedMobile = this.layout.getProp(\r\n      'app.footer.fixed.mobile'\r\n    ) as boolean;\r\n    if (this.appFooterFixedMobile) {\r\n      document.body.setAttribute('data-kt-app-footer-fixed-mobile', 'true');\r\n    }\r\n  }\r\n\r\n  updateToolbar(config: ILayout) {\r\n    this.appToolbarLayout = this.layout.getProp(\r\n      'app.toolbar.layout',\r\n      config\r\n    ) as 'classic' | 'accounting' | 'extended' | 'reports' | 'saas';\r\n    this.appToolbarSwapEnabled = this.layout.getProp(\r\n      'app.toolbar.swap.enabled',\r\n      config\r\n    ) as boolean;\r\n    if (this.appToolbarSwapEnabled) {\r\n      this.appToolbarSwapAttributes = this.layout.getProp(\r\n        'app.toolbar.swap.attributes',\r\n        config\r\n      ) as { [attrName: string]: string };\r\n    }\r\n\r\n    this.appToolbarStickyEnabled = this.layout.getProp(\r\n      'app.toolbar.sticky.enabled',\r\n      config\r\n    ) as boolean;\r\n    if (this.appToolbarStickyEnabled) {\r\n      this.appToolbarStickyAttributes = this.layout.getProp(\r\n        'app.toolbar.sticky.attributes',\r\n        config\r\n      ) as { [attrName: string]: string };\r\n    }\r\n\r\n    this.appToolbarCSSClass =\r\n      (this.layout.getProp('app.toolbar.class', config) as string) || '';\r\n    this.appToolbarMinimizeEnabled = this.layout.getProp(\r\n      'app.toolbar.minimize.enabled',\r\n      config\r\n    ) as boolean;\r\n    if (this.appToolbarMinimizeEnabled) {\r\n      this.appToolbarMinimizeAttributes = this.layout.getProp(\r\n        'app.toolbar.minimize.attributes',\r\n        config\r\n      ) as { [attrName: string]: string };\r\n      this.appToolbarCSSClass += ' app-toolbar-minimize';\r\n    }\r\n\r\n    setTimeout(() => {\r\n      const toolbarElement = document.getElementById('kt_app_toolbar');\r\n      // toolbar\r\n      if (this.appToolbarDisplay && toolbarElement) {\r\n        const toolbarAttributes = toolbarElement\r\n          .getAttributeNames()\r\n          .filter((t) => t.indexOf('data-') > -1);\r\n        toolbarAttributes.forEach((attr) =>\r\n          toolbarElement.removeAttribute(attr)\r\n        );\r\n\r\n        if (this.appToolbarSwapEnabled) {\r\n          for (const key in this.appToolbarSwapAttributes) {\r\n            if (this.appToolbarSwapAttributes.hasOwnProperty(key)) {\r\n              toolbarElement.setAttribute(\r\n                key,\r\n                this.appToolbarSwapAttributes[key]\r\n              );\r\n            }\r\n          }\r\n        }\r\n\r\n        if (this.appToolbarStickyEnabled) {\r\n          for (const key in this.appToolbarStickyAttributes) {\r\n            if (this.appToolbarStickyAttributes.hasOwnProperty(key)) {\r\n              toolbarElement.setAttribute(\r\n                key,\r\n                this.appToolbarStickyAttributes[key]\r\n              );\r\n            }\r\n          }\r\n        }\r\n\r\n        if (this.appToolbarMinimizeEnabled) {\r\n          for (const key in this.appToolbarMinimizeAttributes) {\r\n            if (this.appToolbarMinimizeAttributes.hasOwnProperty(key)) {\r\n              toolbarElement.setAttribute(\r\n                key,\r\n                this.appToolbarMinimizeAttributes[key]\r\n              );\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }, 0);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.unsubscribe.forEach((sb) => sb.unsubscribe());\r\n  }\r\n}\r\n", "<!--begin::App-->\r\n<div class=\"d-flex flex-column flex-root app-root\" id=\"kt_app_root\" [ngClass]=\"{'home-page-layout': isHomePage}\">\r\n  <!--begin::Page-->\r\n  <div class=\"app-page flex-column flex-column-fluid\" id=\"kt_app_page\">\r\n    <ng-container *ngIf=\"appHeaderDisplay\">\r\n      <!--begin::Header-->\r\n      <app-header [ngClass]=\"appHeaderDefaultClass\" id=\"kt_app_header\" class=\"app-header d-flex d-lg-none\"\r\n        data-kt-sticky=\"true\" data-kt-sticky-activate=\"{default: true, lg: true}\"\r\n        data-kt-sticky-name=\"app-header-minimize\" data-kt-sticky-offset=\"{default: '200px', lg: '0'}\"\r\n        data-kt-sticky-animation=\"false\">\r\n      </app-header>\r\n      <!--end::Header-->\r\n    </ng-container>\r\n\r\n    <!--begin::Wrapper-->\r\n    <div class=\"app-wrapper flex-column flex-row-fluid mt-2\" id=\"kt_app_wrapper\">\r\n      <ng-container *ngIf=\"appSidebarDisplay\">\r\n        <!--begin::sidebar-->\r\n        <app-sidebar #ktSidebar id=\"kt_app_sidebar\" class=\"app-sidebar flex-column\" [ngClass]=\"appSidebarDefaultClass\">\r\n        </app-sidebar>\r\n        <!--end::sidebar-->\r\n      </ng-container>\r\n\r\n      <!--begin::Main-->\r\n      <div class=\"app-main flex-column flex-row-fluid\" id=\"kt_app_main\">\r\n        <!--begin::Content wrapper-->\r\n        <div class=\"d-flex flex-column flex-column-fluid\">\r\n          <app-content id=\" kt_app_content\" class=\"app-content pt-3\" [ngClass]=\"contentCSSClasses\"\r\n            [contentContainerCSSClass]=\"contentContainerCSSClass\" [appContentContiner]=\"appContentContiner\"\r\n            [appContentContainerClass]=\"appContentContainerClass\">\r\n          </app-content>\r\n        </div>\r\n        <!--end::Content wrapper-->\r\n\r\n        <ng-container *ngIf=\"appFooterDisplay\">\r\n          <app-footer class=\"app-footer\" [ngClass]=\"appFooterCSSClass\" id=\"kt_app_footer\"\r\n            [appFooterContainerCSSClass]=\"appFooterContainerCSSClass\">\r\n          </app-footer>\r\n        </ng-container>\r\n\r\n      </div>\r\n      <!--end:::Main-->\r\n    </div>\r\n    <!--end::Wrapper-->\r\n  </div>\r\n  <!--end::Page-->\r\n</div>\r\n<!--end::App-->\r\n\r\n<app-scripts-init></app-scripts-init>\r\n\r\n<ng-container>\r\n  <app-scroll-top id=\"kt_scrolltop\" class=\"scrolltop\" data-kt-scrolltop=\"true\"></app-scroll-top>\r\n</ng-container>"], "mappings": "AAOA,SAAyBA,aAAa,QAAgB,iBAAiB;;;;;;;;;;;;;;;;;;;;;ICHnEC,EAAA,CAAAC,uBAAA,GAAuC;IAErCD,EAAA,CAAAE,SAAA,oBAIa;;;;;IAJDF,EAAA,CAAAG,SAAA,EAAiC;IAAjCH,EAAA,CAAAI,UAAA,YAAAC,MAAA,CAAAC,qBAAA,CAAiC;;;;;IAU7CN,EAAA,CAAAC,uBAAA,GAAwC;IAEtCD,EAAA,CAAAE,SAAA,yBACc;;;;;IAD8DF,EAAA,CAAAG,SAAA,EAAkC;IAAlCH,EAAA,CAAAI,UAAA,YAAAC,MAAA,CAAAE,sBAAA,CAAkC;;;;;IAgB9GP,EAAA,CAAAC,uBAAA,GAAuC;IACrCD,EAAA,CAAAE,SAAA,qBAEa;;;;;IAFkBF,EAAA,CAAAG,SAAA,EAA6B;IAC1DH,EAD6B,CAAAI,UAAA,YAAAC,MAAA,CAAAG,iBAAA,CAA6B,+BAAAH,MAAA,CAAAI,0BAAA,CACD;;;ADlBrE,OAAM,MAAOC,eAAe;EA4DhBC,WAAA;EACAC,MAAA;EACAC,MAAA;EACAC,cAAA;EA9DFC,WAAW,GAAmB,EAAE;EAExC;EACA;EACAC,uBAAuB;EACvB;EACAV,qBAAqB,GAAW,EAAE;EAClCW,gBAAgB;EAChBC,6BAA6B;EAC7BC,gCAAgC,GAAmC,EAAE;EACrEC,+BAA+B;EAC/BC,kCAAkC,GAAmC,EAAE;EACvE;EACAC,iBAAiB;EACjBC,gBAAgB;EAChBC,kBAAkB,GAAW,EAAE;EAC/BC,qBAAqB;EACrBC,wBAAwB,GAAmC,EAAE;EAC7DC,uBAAuB;EACvBC,0BAA0B,GAAmC,EAAE;EAC/DC,yBAAyB;EACzBC,4BAA4B,GAAmC,EAAE;EAEjE;EACAC,kBAAkB;EAClBC,wBAAwB;EACxBC,iBAAiB;EACjBC,wBAAwB;EACxB;EACA3B,sBAAsB;EACtB4B,8BAA8B;EAC9BC,iCAAiC,GAAmC,EAAE;EACtEC,iBAAiB;EACjBC,8BAA8B;EAC9BC,iCAAiC,GAAmC,EAAE;EAC5BC,SAAS;EACnD;EACAC,sBAAsB;EACtB;EACAC,gBAAgB;EAChBlC,iBAAiB,GAAW,EAAE;EAC9BmC,kBAAkB,GAAW,EAAE;EAC/BlC,0BAA0B,GAAW,EAAE;EACvCmC,qBAAqB;EACrBC,oBAAoB;EAEpB;EACAC,gBAAgB;EAEhB;EACAC,qBAAqB,GAAY,KAAK;EACtC;EACAC,UAAU,GAAY,KAAK;EAEaC,OAAO;EACAC,cAAc;EACpBC,QAAQ;EAEjDC,YACUzC,WAA8B,EAC9BC,MAAqB,EACrBC,MAAc,EACdC,cAA8B;IAH9B,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IAEtB;IACA,IAAI,CAACD,MAAM,CAACwC,MAAM,CAACC,SAAS,CAAEC,KAAK,IAAI;MACrC,IAAIA,KAAK,YAAYxD,aAAa,EAAE;QAClC;QACA,IAAI,CAACgD,qBAAqB,GAAGQ,KAAK,CAACC,GAAG,CAACC,QAAQ,CAAC,iBAAiB,CAAC;QAElE;QACA,IAAI,CAACT,UAAU,GAAGO,KAAK,CAACC,GAAG,KAAK,OAAO,IAAID,KAAK,CAACC,GAAG,KAAK,GAAG;QAE5D,MAAME,iBAAiB,GAAG,IAAI,CAAC9C,MAAM,CAAC+C,wBAAwB,CAACC,KAAK;QAEpE,MAAMC,cAAc,GAClB,IAAI,CAAC/C,cAAc,EAAEgD,UAAU,EAAEC,QAAQ,CAACC,IAAI,CAACpD,MAAM,IACrD,IAAI,CAACA,MAAM,CAACqD,iCAAiC,EAAE;QAEjD,IAAIP,iBAAiB,KAAKG,cAAc,IAAI,CAACH,iBAAiB,EAAE;UAC9D,IAAI,CAAC9C,MAAM,CAAC+C,wBAAwB,CAACO,IAAI,CAACL,cAAc,CAAC;UACzD,IAAI,CAAClD,WAAW,CAACwD,WAAW,CAACN,cAAc,CAAC;QAC9C;MACF;IACF,CAAC,CAAC;EACJ;EAEAO,QAAQA,CAAA;IACN,MAAMC,MAAM,GAAG,IAAI,CAACzD,MAAM,CAAC0D,mBAAmB,CAC3CC,YAAY,EAAE,CACdjB,SAAS,CAAEkB,MAAM,IAAI;MACpB,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC;IAC1B,CAAC,CAAC;IACJ,IAAI,CAACzD,WAAW,CAAC2D,IAAI,CAACL,MAAM,CAAC;EAC/B;EAEAI,WAAWA,CAACD,MAAe;IACzB,IAAI,CAAC1B,gBAAgB,GAAG,IAAI,CAAClC,MAAM,CAAC+D,OAAO,CACzC,mBAAmB,EACnBH,MAAM,CACI;IACZ,IAAI,CAACxD,uBAAuB,GAC1B,IAAI,CAACJ,MAAM,CAACgE,mBAAmB,CAAC,eAAe,CAAC;IAClD,IAAI,CAACtE,qBAAqB,GAAG,IAAI,CAACM,MAAM,CAAC+D,OAAO,CAC9C,0BAA0B,EAC1BH,MAAM,CACG;IACX,IAAI,CAACvD,gBAAgB,GAAG,IAAI,CAACL,MAAM,CAAC+D,OAAO,CACzC,oBAAoB,EACpBH,MAAM,CACI;IACZ,IAAI,CAAC9B,gBAAgB,GAAG,IAAI,CAAC9B,MAAM,CAAC+D,OAAO,CACzC,oBAAoB,EACpBH,MAAM,CACI;IACZ,IAAI,CAACnC,iBAAiB,GAAG,IAAI,CAACzB,MAAM,CAAC+D,OAAO,CAC1C,qBAAqB,EACrBH,MAAM,CACI;IAEZ;IACA,IAAI,IAAI,CAACzB,qBAAqB,IAAI,IAAI,CAACC,UAAU,EAAE;MACjD,IAAI,CAACX,iBAAiB,GAAG,KAAK;IAChC;IAEA;IACA,IAAI,IAAI,CAACW,UAAU,EAAE;MACnB,IAAI,CAAC/B,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACK,iBAAiB,GAAG,KAAK;IAChC;IAEA,IAAI,CAACmB,sBAAsB,GAAG,IAAI,CAAC7B,MAAM,CAAC+D,OAAO,CAC/C,2BAA2B,EAC3BH,MAAM,CACI;IAEZ,IAAI,CAAC,IAAI,CAACxB,UAAU,EAAE;MACpB,IAAI,CAAC1B,iBAAiB,GAAG,IAAI,CAACV,MAAM,CAAC+D,OAAO,CAC1C,qBAAqB,EACrBH,MAAM,CACI;IACd;IACA,IAAI,CAACvC,iBAAiB,GAAG,IAAI,CAACrB,MAAM,CAACgE,mBAAmB,CAAC,SAAS,CAAC;IACnE,IAAI,CAAC1C,wBAAwB,GAC3B,IAAI,CAACtB,MAAM,CAACgE,mBAAmB,CAAC,kBAAkB,CAAC;IACrD,IAAI,CAAC7C,kBAAkB,GAAG,IAAI,CAACnB,MAAM,CAAC+D,OAAO,CAC3C,uBAAuB,EACvBH,MAAM,CACc;IACtB,IAAI,CAACxC,wBAAwB,GAAG,IAAI,CAACpB,MAAM,CAAC+D,OAAO,CACjD,4BAA4B,EAC5BH,MAAM,CACG;IACX;IACA,IAAI,IAAI,CAAC9B,gBAAgB,EAAE;MACzB,IAAI,CAACmC,YAAY,CAACL,MAAM,CAAC;IAC3B;IACA;IACA,IAAI,IAAI,CAACnC,iBAAiB,EAAE;MAC1B,IAAI,CAACyC,aAAa,CAACN,MAAM,CAAC;IAC5B;IACA;IACA,IAAI,IAAI,CAACvD,gBAAgB,EAAE;MACzB,IAAI,CAAC8D,YAAY,CAACP,MAAM,CAAC;IAC3B;IACA;IACA,IAAI,IAAI,CAAClD,iBAAiB,EAAE;MAC1B,IAAI,CAAC0D,aAAa,CAACR,MAAM,CAAC;IAC5B;EACF;EAEAM,aAAaA,CAACN,MAAe;IAC3B,IAAI,CAACjE,sBAAsB,GAAG,IAAI,CAACK,MAAM,CAAC+D,OAAO,CAC/C,2BAA2B,EAC3BH,MAAM,CACG;IAEX,IAAI,CAACrC,8BAA8B,GAAG,IAAI,CAACvB,MAAM,CAAC+D,OAAO,CACvD,oCAAoC,EACpCH,MAAM,CACI;IACZ,IAAI,IAAI,CAACrC,8BAA8B,EAAE;MACvC,IAAI,CAACC,iCAAiC,GAAG,IAAI,CAACxB,MAAM,CAAC+D,OAAO,CAC1D,uCAAuC,EACvCH,MAAM,CAC2B;IACrC;IAEA,IAAI,CAAClC,8BAA8B,GAAG,IAAI,CAAC1B,MAAM,CAAC+D,OAAO,CACvD,oCAAoC,EACpCH,MAAM,CACI;IACZ,IAAI,IAAI,CAAClC,8BAA8B,EAAE;MACvC,IAAI,CAACC,iCAAiC,GAAG,IAAI,CAAC3B,MAAM,CAAC+D,OAAO,CAC1D,uCAAuC,EACvCH,MAAM,CAC2B;IACrC;IAEAS,UAAU,CAAC,MAAK;MACd,MAAMC,cAAc,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;MAChE;MACA,IAAI,IAAI,CAAC/C,iBAAiB,IAAI6C,cAAc,EAAE;QAC5C,MAAMG,iBAAiB,GAAGH,cAAc,CACrCI,iBAAiB,EAAE,CACnBC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QACzCJ,iBAAiB,CAACK,OAAO,CAAEC,IAAI,IAC7BT,cAAc,CAACU,eAAe,CAACD,IAAI,CAAC,CACrC;QAED,IAAI,IAAI,CAACxD,8BAA8B,EAAE;UACvC,KAAK,MAAM0D,GAAG,IAAI,IAAI,CAACzD,iCAAiC,EAAE;YACxD,IAAI,IAAI,CAACA,iCAAiC,CAAC0D,cAAc,CAACD,GAAG,CAAC,EAAE;cAC9DX,cAAc,CAACa,YAAY,CACzBF,GAAG,EACH,IAAI,CAACzD,iCAAiC,CAACyD,GAAG,CAAC,CAC5C;YACH;UACF;QACF;QAEA,IAAI,IAAI,CAACvD,8BAA8B,EAAE;UACvC,KAAK,MAAMuD,GAAG,IAAI,IAAI,CAACtD,iCAAiC,EAAE;YACxD,IAAI,IAAI,CAACA,iCAAiC,CAACuD,cAAc,CAACD,GAAG,CAAC,EAAE;cAC9DX,cAAc,CAACa,YAAY,CACzBF,GAAG,EACH,IAAI,CAACtD,iCAAiC,CAACsD,GAAG,CAAC,CAC5C;YACH;UACF;QACF;MACF;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAd,YAAYA,CAACP,MAAe;IAC1B,IAAI,CAACtD,6BAA6B,GAAG,IAAI,CAACN,MAAM,CAAC+D,OAAO,CACtD,mCAAmC,EACnCH,MAAM,CACI;IACZ,IAAI,IAAI,CAACtD,6BAA6B,EAAE;MACtC,IAAI,CAACC,gCAAgC,GAAG,IAAI,CAACP,MAAM,CAAC+D,OAAO,CACzD,sCAAsC,EACtCH,MAAM,CAC2B;IACrC;IAEA,IAAI,CAACpD,+BAA+B,GAAG,IAAI,CAACR,MAAM,CAAC+D,OAAO,CACxD,qCAAqC,EACrCH,MAAM,CACI;IACZ,IAAI,IAAI,CAACpD,+BAA+B,EAAE;MACxC,IAAI,CAACC,kCAAkC,GAAG,IAAI,CAACT,MAAM,CAAC+D,OAAO,CAC3D,wCAAwC,EACxCH,MAAM,CAC2B;IACrC;IAEAS,UAAU,CAAC,MAAK;MACd,MAAMe,aAAa,GAAGb,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC;MAC9D;MACA,IAAI,IAAI,CAACnE,gBAAgB,IAAI+E,aAAa,EAAE;QAC1C,MAAMC,gBAAgB,GAAGD,aAAa,CACnCV,iBAAiB,EAAE,CACnBC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QACzCQ,gBAAgB,CAACP,OAAO,CAAEC,IAAI,IAAKK,aAAa,CAACJ,eAAe,CAACD,IAAI,CAAC,CAAC;QAEvE,IAAI,IAAI,CAACzE,6BAA6B,EAAE;UACtC,KAAK,MAAM2E,GAAG,IAAI,IAAI,CAAC1E,gCAAgC,EAAE;YACvD,IAAI,IAAI,CAACA,gCAAgC,CAAC2E,cAAc,CAACD,GAAG,CAAC,EAAE;cAC7DG,aAAa,CAACD,YAAY,CACxBF,GAAG,EACH,IAAI,CAAC1E,gCAAgC,CAAC0E,GAAG,CAAC,CAC3C;YACH;UACF;QACF;QAEA,IAAI,IAAI,CAACzE,+BAA+B,EAAE;UACxC,KAAK,MAAMyE,GAAG,IAAI,IAAI,CAACxE,kCAAkC,EAAE;YACzD,IAAI,IAAI,CAACA,kCAAkC,CAACyE,cAAc,CAACD,GAAG,CAAC,EAAE;cAC/DG,aAAa,CAACD,YAAY,CACxBF,GAAG,EACH,IAAI,CAACxE,kCAAkC,CAACwE,GAAG,CAAC,CAC7C;YACH;UACF;QACF;MACF;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAhB,YAAYA,CAACL,MAAe;IAC1B,IAAI,CAAChE,iBAAiB,GAAG,IAAI,CAACI,MAAM,CAAC+D,OAAO,CAC1C,kBAAkB,EAClBH,MAAM,CACG;IACX,IAAI,CAAC7B,kBAAkB,GAAG,IAAI,CAAC/B,MAAM,CAAC+D,OAAO,CAC3C,sBAAsB,EACtBH,MAAM,CACG;IACX,IAAI,CAAC/D,0BAA0B,GAAG,IAAI,CAACG,MAAM,CAAC+D,OAAO,CACnD,2BAA2B,EAC3BH,MAAM,CACG;IACX,IAAI,IAAI,CAAC7B,kBAAkB,KAAK,OAAO,EAAE;MACvC,IAAI,CAAClC,0BAA0B,IAAI,gBAAgB;IACrD,CAAC,MAAM;MACL,IAAI,IAAI,CAACkC,kBAAkB,KAAK,OAAO,EAAE;QACvC,IAAI,CAAClC,0BAA0B,IAAI,kBAAkB;MACvD;IACF;IAEA,IAAI,CAACmC,qBAAqB,GAAG,IAAI,CAAChC,MAAM,CAAC+D,OAAO,CAC9C,0BAA0B,EAC1BH,MAAM,CACI;IACZ,IAAI,IAAI,CAAC5B,qBAAqB,EAAE;MAC9BuC,QAAQ,CAACe,IAAI,CAACH,YAAY,CAAC,0BAA0B,EAAE,MAAM,CAAC;IAChE;IAEA,IAAI,CAAClD,oBAAoB,GAAG,IAAI,CAACjC,MAAM,CAAC+D,OAAO,CAC7C,yBAAyB,CACf;IACZ,IAAI,IAAI,CAAC9B,oBAAoB,EAAE;MAC7BsC,QAAQ,CAACe,IAAI,CAACH,YAAY,CAAC,iCAAiC,EAAE,MAAM,CAAC;IACvE;EACF;EAEAf,aAAaA,CAACR,MAAe;IAC3B,IAAI,CAACjD,gBAAgB,GAAG,IAAI,CAACX,MAAM,CAAC+D,OAAO,CACzC,oBAAoB,EACpBH,MAAM,CACuD;IAC/D,IAAI,CAAC/C,qBAAqB,GAAG,IAAI,CAACb,MAAM,CAAC+D,OAAO,CAC9C,0BAA0B,EAC1BH,MAAM,CACI;IACZ,IAAI,IAAI,CAAC/C,qBAAqB,EAAE;MAC9B,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAACd,MAAM,CAAC+D,OAAO,CACjD,6BAA6B,EAC7BH,MAAM,CAC2B;IACrC;IAEA,IAAI,CAAC7C,uBAAuB,GAAG,IAAI,CAACf,MAAM,CAAC+D,OAAO,CAChD,4BAA4B,EAC5BH,MAAM,CACI;IACZ,IAAI,IAAI,CAAC7C,uBAAuB,EAAE;MAChC,IAAI,CAACC,0BAA0B,GAAG,IAAI,CAAChB,MAAM,CAAC+D,OAAO,CACnD,+BAA+B,EAC/BH,MAAM,CAC2B;IACrC;IAEA,IAAI,CAAChD,kBAAkB,GACpB,IAAI,CAACZ,MAAM,CAAC+D,OAAO,CAAC,mBAAmB,EAAEH,MAAM,CAAY,IAAI,EAAE;IACpE,IAAI,CAAC3C,yBAAyB,GAAG,IAAI,CAACjB,MAAM,CAAC+D,OAAO,CAClD,8BAA8B,EAC9BH,MAAM,CACI;IACZ,IAAI,IAAI,CAAC3C,yBAAyB,EAAE;MAClC,IAAI,CAACC,4BAA4B,GAAG,IAAI,CAAClB,MAAM,CAAC+D,OAAO,CACrD,iCAAiC,EACjCH,MAAM,CAC2B;MACnC,IAAI,CAAChD,kBAAkB,IAAI,uBAAuB;IACpD;IAEAyD,UAAU,CAAC,MAAK;MACd,MAAMkB,cAAc,GAAGhB,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;MAChE;MACA,IAAI,IAAI,CAAC9D,iBAAiB,IAAI6E,cAAc,EAAE;QAC5C,MAAMC,iBAAiB,GAAGD,cAAc,CACrCb,iBAAiB,EAAE,CACnBC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;QACzCW,iBAAiB,CAACV,OAAO,CAAEC,IAAI,IAC7BQ,cAAc,CAACP,eAAe,CAACD,IAAI,CAAC,CACrC;QAED,IAAI,IAAI,CAAClE,qBAAqB,EAAE;UAC9B,KAAK,MAAMoE,GAAG,IAAI,IAAI,CAACnE,wBAAwB,EAAE;YAC/C,IAAI,IAAI,CAACA,wBAAwB,CAACoE,cAAc,CAACD,GAAG,CAAC,EAAE;cACrDM,cAAc,CAACJ,YAAY,CACzBF,GAAG,EACH,IAAI,CAACnE,wBAAwB,CAACmE,GAAG,CAAC,CACnC;YACH;UACF;QACF;QAEA,IAAI,IAAI,CAAClE,uBAAuB,EAAE;UAChC,KAAK,MAAMkE,GAAG,IAAI,IAAI,CAACjE,0BAA0B,EAAE;YACjD,IAAI,IAAI,CAACA,0BAA0B,CAACkE,cAAc,CAACD,GAAG,CAAC,EAAE;cACvDM,cAAc,CAACJ,YAAY,CACzBF,GAAG,EACH,IAAI,CAACjE,0BAA0B,CAACiE,GAAG,CAAC,CACrC;YACH;UACF;QACF;QAEA,IAAI,IAAI,CAAChE,yBAAyB,EAAE;UAClC,KAAK,MAAMgE,GAAG,IAAI,IAAI,CAAC/D,4BAA4B,EAAE;YACnD,IAAI,IAAI,CAACA,4BAA4B,CAACgE,cAAc,CAACD,GAAG,CAAC,EAAE;cACzDM,cAAc,CAACJ,YAAY,CACzBF,GAAG,EACH,IAAI,CAAC/D,4BAA4B,CAAC+D,GAAG,CAAC,CACvC;YACH;UACF;QACF;MACF;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAQ,WAAWA,CAAA;IACT,IAAI,CAACtF,WAAW,CAAC2E,OAAO,CAAEY,EAAE,IAAKA,EAAE,CAACvF,WAAW,EAAE,CAAC;EACpD;;qCApaWL,eAAe,EAAAV,EAAA,CAAAuG,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAzG,EAAA,CAAAuG,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAA3G,EAAA,CAAAuG,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA7G,EAAA,CAAAuG,iBAAA,CAAAK,EAAA,CAAAE,cAAA;EAAA;;UAAfpG,eAAe;IAAAqG,SAAA;IAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;QCf1BlH,EAFF,CAAAoH,cAAA,aAAiH,aAE1C;QACnEpH,EAAA,CAAAqH,UAAA,IAAAC,uCAAA,0BAAuC;QAWvCtH,EAAA,CAAAoH,cAAA,aAA6E;QAC3EpH,EAAA,CAAAqH,UAAA,IAAAE,uCAAA,0BAAwC;QAUtCvH,EAFF,CAAAoH,cAAA,aAAkE,aAEd;QAChDpH,EAAA,CAAAE,SAAA,qBAGc;QAChBF,EAAA,CAAAwH,YAAA,EAAM;QAGNxH,EAAA,CAAAqH,UAAA,IAAAI,uCAAA,0BAAuC;QAY/CzH,EANM,CAAAwH,YAAA,EAAM,EAEF,EAEF,EAEF;QAGNxH,EAAA,CAAAE,SAAA,uBAAqC;QAErCF,EAAA,CAAAC,uBAAA,IAAc;QACZD,EAAA,CAAAE,SAAA,yBAA8F;;;;QAnD5BF,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA0H,eAAA,IAAAC,GAAA,EAAAR,GAAA,CAAAnE,UAAA,EAA4C;QAG7FhD,EAAA,CAAAG,SAAA,GAAsB;QAAtBH,EAAA,CAAAI,UAAA,SAAA+G,GAAA,CAAAlG,gBAAA,CAAsB;QAYpBjB,EAAA,CAAAG,SAAA,GAAuB;QAAvBH,EAAA,CAAAI,UAAA,SAAA+G,GAAA,CAAA9E,iBAAA,CAAuB;QAWyBrC,EAAA,CAAAG,SAAA,GAA6B;QAEtFH,EAFyD,CAAAI,UAAA,YAAA+G,GAAA,CAAAlF,iBAAA,CAA6B,6BAAAkF,GAAA,CAAAjF,wBAAA,CACjC,uBAAAiF,GAAA,CAAApF,kBAAA,CAA0C,6BAAAoF,GAAA,CAAAnF,wBAAA,CAC1C;QAK1ChC,EAAA,CAAAG,SAAA,EAAsB;QAAtBH,EAAA,CAAAI,UAAA,SAAA+G,GAAA,CAAAzE,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}