{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction HomeComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55)(2, \"div\", 56);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 57);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 58);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const card_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    i0.ɵɵclassMap(\"hero-card-\" + (i_r2 + 1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"bg-light-\" + card_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(card_r1.icon + \" fs-1 text-\" + card_r1.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r1.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r1.description);\n  }\n}\nfunction HomeComponent_div_71_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 78);\n    i0.ɵɵtext(1, \"\\u062C\\u062F\\u064A\\u062F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_71_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelement(1, \"i\", 79);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", property_r4.bedrooms, \" \\u063A\\u0631\\u0641\");\n  }\n}\nfunction HomeComponent_div_71_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelement(1, \"i\", 80);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", property_r4.bathrooms, \" \\u062D\\u0645\\u0627\\u0645\");\n  }\n}\nfunction HomeComponent_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_71_Template_div_click_1_listener() {\n      const property_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.viewProperty(property_r4.id));\n    });\n    i0.ɵɵelementStart(2, \"div\", 61);\n    i0.ɵɵelement(3, \"img\", 62);\n    i0.ɵɵelementStart(4, \"div\", 63);\n    i0.ɵɵtemplate(5, HomeComponent_div_71_span_5_Template, 2, 0, \"span\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 65);\n    i0.ɵɵelement(7, \"i\", 66);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 67)(11, \"h4\", 68);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 69);\n    i0.ɵɵelement(14, \"i\", 70);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 71);\n    i0.ɵɵtemplate(17, HomeComponent_div_71_div_17_Template, 4, 1, \"div\", 72)(18, HomeComponent_div_71_div_18_Template, 4, 1, \"div\", 72);\n    i0.ɵɵelementStart(19, \"div\", 73);\n    i0.ɵɵelement(20, \"i\", 74);\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 75)(24, \"span\", 76);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 77);\n    i0.ɵɵtext(27, \"\\u062C\\u0646\\u064A\\u0647\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const property_r4 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", property_r4.image, i0.ɵɵsanitizeUrl)(\"alt\", property_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", property_r4.isNew);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(property_r4.rating);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(property_r4.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", property_r4.location, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", property_r4.bedrooms > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", property_r4.bathrooms > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", property_r4.area, \" \\u0645\\u00B2\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(property_r4.price);\n  }\n}\nfunction HomeComponent_div_88_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 82);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_88_Template_div_click_1_listener() {\n      const city_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.viewCity(city_r7.name));\n    });\n    i0.ɵɵelementStart(2, \"div\", 83);\n    i0.ɵɵelement(3, \"img\", 62);\n    i0.ɵɵelementStart(4, \"div\", 84)(5, \"div\", 85)(6, \"h4\", 86);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 87);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const city_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", city_r7.image, i0.ɵɵsanitizeUrl)(\"alt\", city_r7.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(city_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", city_r7.propertiesCount, \" \\u0639\\u0642\\u0627\\u0631\");\n  }\n}\nfunction HomeComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 89);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_99_Template_div_click_1_listener() {\n      const article_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.readArticle(article_r9.id));\n    });\n    i0.ɵɵelementStart(2, \"div\", 90);\n    i0.ɵɵelement(3, \"img\", 62);\n    i0.ɵɵelementStart(4, \"div\", 91)(5, \"span\", 92);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 93)(8, \"h4\", 94);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 95);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 96)(13, \"div\", 97);\n    i0.ɵɵelement(14, \"i\", 98);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 99);\n    i0.ɵɵelement(18, \"i\", 100);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 101);\n    i0.ɵɵelement(22, \"i\", 102);\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const article_r9 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", article_r9.image, i0.ɵɵsanitizeUrl)(\"alt\", article_r9.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r9.category);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r9.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(article_r9.excerpt);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(article_r9.author);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(article_r9.date);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(article_r9.readTime);\n  }\n}\nexport class HomeComponent {\n  static ɵfac = function HomeComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HomeComponent)();\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomeComponent,\n    selectors: [[\"app-home\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 121,\n    vars: 4,\n    consts: [[1, \"hero-section\"], [1, \"hero-background\"], [1, \"hero-overlay\"], [1, \"container\"], [1, \"row\", \"align-items-center\", \"min-vh-100\"], [1, \"col-lg-6\"], [1, \"hero-content\"], [1, \"hero-title\"], [1, \"text-primary\"], [1, \"hero-subtitle\"], [1, \"hero-search-form\"], [1, \"search-tabs\"], [1, \"search-tab\", \"active\"], [1, \"search-tab\"], [1, \"search-inputs\"], [1, \"search-input-group\"], [1, \"ki-outline\", \"ki-geolocation\", \"fs-2\"], [1, \"form-select\"], [1, \"ki-outline\", \"ki-home\", \"fs-2\"], [1, \"ki-outline\", \"ki-dollar\", \"fs-2\"], [1, \"btn\", \"btn-primary\", \"search-btn\", 3, \"click\"], [1, \"ki-outline\", \"ki-magnifier\", \"fs-2\"], [1, \"hero-cards\"], [\"class\", \"hero-card\", 3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"featured-properties-section\", \"py-15\"], [1, \"row\", \"mb-10\"], [1, \"col-12\", \"text-center\"], [1, \"section-title\"], [1, \"ki-outline\", \"ki-star\", \"fs-1\", \"text-warning\", \"me-3\"], [1, \"section-subtitle\"], [1, \"row\", \"g-6\"], [\"class\", \"col-lg-3 col-md-6\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"mt-10\"], [1, \"btn\", \"btn-outline-primary\", \"btn-lg\"], [1, \"ki-outline\", \"ki-arrow-left\", \"ms-2\"], [1, \"cities-section\", \"py-15\", \"bg-light\"], [1, \"ki-outline\", \"ki-map\", \"fs-1\", \"text-primary\", \"me-3\"], [1, \"cities-carousel\"], [1, \"row\", \"g-4\"], [\"class\", \"col-lg-2 col-md-4 col-6\", 4, \"ngFor\", \"ngForOf\"], [1, \"blog-section\", \"py-15\"], [1, \"ki-outline\", \"ki-book\", \"fs-1\", \"text-info\", \"me-3\"], [\"class\", \"col-lg-4 col-md-6\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-outline-info\", \"btn-lg\"], [1, \"newsletter-section\", \"py-15\", \"bg-primary\"], [1, \"row\", \"align-items-center\"], [1, \"newsletter-content\", \"text-white\"], [1, \"newsletter-title\"], [1, \"newsletter-subtitle\"], [1, \"newsletter-form\"], [1, \"input-group\"], [\"type\", \"email\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0628\\u0631\\u064A\\u062F\\u0643 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\", 1, \"form-control\", \"form-control-lg\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", \"btn-lg\"], [1, \"ki-outline\", \"ki-send\", \"fs-2\"], [1, \"hero-card\"], [1, \"hero-card-content\"], [1, \"hero-card-icon\"], [1, \"hero-card-title\"], [1, \"hero-card-description\"], [1, \"col-lg-3\", \"col-md-6\"], [1, \"property-card\", 3, \"click\"], [1, \"property-image\"], [1, \"w-100\", 3, \"src\", \"alt\"], [1, \"property-badges\"], [\"class\", \"badge badge-success\", 4, \"ngIf\"], [1, \"property-rating\"], [1, \"ki-solid\", \"ki-star\", \"text-warning\"], [1, \"property-content\"], [1, \"property-title\"], [1, \"property-location\"], [1, \"ki-outline\", \"ki-geolocation\", \"text-muted\", \"me-2\"], [1, \"property-details\"], [\"class\", \"property-detail\", 4, \"ngIf\"], [1, \"property-detail\"], [1, \"ki-outline\", \"ki-resize\", \"text-muted\"], [1, \"property-price\"], [1, \"price\"], [1, \"currency\"], [1, \"badge\", \"badge-success\"], [1, \"ki-outline\", \"ki-home\", \"text-muted\"], [1, \"ki-outline\", \"ki-droplet\", \"text-muted\"], [1, \"col-lg-2\", \"col-md-4\", \"col-6\"], [1, \"city-card\", 3, \"click\"], [1, \"city-image\"], [1, \"city-overlay\"], [1, \"city-content\"], [1, \"city-name\"], [1, \"city-count\"], [1, \"col-lg-4\", \"col-md-6\"], [1, \"article-card\", 3, \"click\"], [1, \"article-image\"], [1, \"article-category\"], [1, \"badge\", \"badge-primary\"], [1, \"article-content\"], [1, \"article-title\"], [1, \"article-excerpt\"], [1, \"article-meta\"], [1, \"article-author\"], [1, \"ki-outline\", \"ki-profile-user\", \"text-muted\", \"me-2\"], [1, \"article-date\"], [1, \"ki-outline\", \"ki-calendar\", \"text-muted\", \"me-2\"], [1, \"article-read-time\"], [1, \"ki-outline\", \"ki-time\", \"text-muted\", \"me-2\"]],\n    template: function HomeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"div\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"h1\", 7);\n        i0.ɵɵtext(8, \" \\u0627\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631 \\u0627\\u0644\\u0645\\u062B\\u0627\\u0644\\u064A \");\n        i0.ɵɵelementStart(9, \"span\", 8);\n        i0.ɵɵtext(10, \"\\u0628\\u0633\\u0647\\u0648\\u0644\\u0629 \\u0648\\u062B\\u0642\\u0629\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"p\", 9);\n        i0.ɵɵtext(12, \" \\u0645\\u0646\\u0635\\u0629 \\u0625\\u064A\\u0632\\u064A \\u062F\\u064A\\u0644 \\u062A\\u0642\\u062F\\u0645 \\u0644\\u0643 \\u0623\\u0641\\u0636\\u0644 \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631\\u0627\\u062A \\u0641\\u064A \\u0645\\u0635\\u0631 \\u0645\\u0639 \\u062E\\u062F\\u0645\\u0627\\u062A \\u0645\\u062A\\u0645\\u064A\\u0632\\u0629 \\u0648\\u0623\\u0633\\u0639\\u0627\\u0631 \\u062A\\u0646\\u0627\\u0641\\u0633\\u064A\\u0629 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"div\", 10)(14, \"div\", 11)(15, \"button\", 12);\n        i0.ɵɵtext(16, \"\\u0634\\u0631\\u0627\\u0621\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"button\", 13);\n        i0.ɵɵtext(18, \"\\u0625\\u064A\\u062C\\u0627\\u0631\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"button\", 13);\n        i0.ɵɵtext(20, \"\\u0627\\u0633\\u062A\\u062B\\u0645\\u0627\\u0631\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(21, \"div\", 14)(22, \"div\", 15);\n        i0.ɵɵelement(23, \"i\", 16);\n        i0.ɵɵelementStart(24, \"select\", 17)(25, \"option\");\n        i0.ɵɵtext(26, \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0645\\u062F\\u064A\\u0646\\u0629\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"option\");\n        i0.ɵɵtext(28, \"\\u0627\\u0644\\u0642\\u0627\\u0647\\u0631\\u0629 \\u0627\\u0644\\u062C\\u062F\\u064A\\u062F\\u0629\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"option\");\n        i0.ɵɵtext(30, \"\\u0627\\u0644\\u0634\\u064A\\u062E \\u0632\\u0627\\u064A\\u062F\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"option\");\n        i0.ɵɵtext(32, \"\\u0627\\u0644\\u0639\\u0627\\u0635\\u0645\\u0629 \\u0627\\u0644\\u0625\\u062F\\u0627\\u0631\\u064A\\u0629\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(33, \"div\", 15);\n        i0.ɵɵelement(34, \"i\", 18);\n        i0.ɵɵelementStart(35, \"select\", 17)(36, \"option\");\n        i0.ɵɵtext(37, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"option\");\n        i0.ɵɵtext(39, \"\\u0634\\u0642\\u0629\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"option\");\n        i0.ɵɵtext(41, \"\\u0641\\u064A\\u0644\\u0627\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"option\");\n        i0.ɵɵtext(43, \"\\u0645\\u0643\\u062A\\u0628\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(44, \"div\", 15);\n        i0.ɵɵelement(45, \"i\", 19);\n        i0.ɵɵelementStart(46, \"select\", 17)(47, \"option\");\n        i0.ɵɵtext(48, \"\\u0627\\u0644\\u0645\\u064A\\u0632\\u0627\\u0646\\u064A\\u0629\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(49, \"option\");\n        i0.ɵɵtext(50, \"\\u0623\\u0642\\u0644 \\u0645\\u0646 \\u0645\\u0644\\u064A\\u0648\\u0646\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(51, \"option\");\n        i0.ɵɵtext(52, \"1-3 \\u0645\\u0644\\u064A\\u0648\\u0646\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(53, \"option\");\n        i0.ɵɵtext(54, \"\\u0623\\u0643\\u062B\\u0631 \\u0645\\u0646 3 \\u0645\\u0644\\u064A\\u0648\\u0646\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(55, \"button\", 20);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_55_listener() {\n          return ctx.searchProperties();\n        });\n        i0.ɵɵelement(56, \"i\", 21);\n        i0.ɵɵtext(57, \" \\u0627\\u0628\\u062D\\u062B \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(58, \"div\", 5)(59, \"div\", 22);\n        i0.ɵɵtemplate(60, HomeComponent_div_60_Template, 8, 8, \"div\", 23);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(61, \"section\", 24)(62, \"div\", 3)(63, \"div\", 25)(64, \"div\", 26)(65, \"h2\", 27);\n        i0.ɵɵelement(66, \"i\", 28);\n        i0.ɵɵtext(67, \" \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631\\u0627\\u062A \\u0627\\u0644\\u0645\\u0645\\u064A\\u0632\\u0629 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(68, \"p\", 29);\n        i0.ɵɵtext(69, \"\\u0627\\u0643\\u062A\\u0634\\u0641 \\u0623\\u0641\\u0636\\u0644 \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631\\u0627\\u062A \\u0627\\u0644\\u0645\\u062A\\u0627\\u062D\\u0629 \\u062D\\u0627\\u0644\\u064A\\u0627\\u064B\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(70, \"div\", 30);\n        i0.ɵɵtemplate(71, HomeComponent_div_71_Template, 28, 10, \"div\", 31);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(72, \"div\", 32)(73, \"div\", 26)(74, \"button\", 33);\n        i0.ɵɵtext(75, \" \\u0639\\u0631\\u0636 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631\\u0627\\u062A \");\n        i0.ɵɵelement(76, \"i\", 34);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(77, \"section\", 35)(78, \"div\", 3)(79, \"div\", 25)(80, \"div\", 26)(81, \"h2\", 27);\n        i0.ɵɵelement(82, \"i\", 36);\n        i0.ɵɵtext(83, \" \\u0627\\u0633\\u062A\\u0643\\u0634\\u0641 \\u0627\\u0644\\u0645\\u062F\\u0646 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(84, \"p\", 29);\n        i0.ɵɵtext(85, \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0645\\u062F\\u064A\\u0646\\u0629 \\u0627\\u0644\\u062A\\u064A \\u062A\\u0646\\u0627\\u0633\\u0628\\u0643\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(86, \"div\", 37)(87, \"div\", 38);\n        i0.ɵɵtemplate(88, HomeComponent_div_88_Template, 10, 4, \"div\", 39);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(89, \"section\", 40)(90, \"div\", 3)(91, \"div\", 25)(92, \"div\", 26)(93, \"h2\", 27);\n        i0.ɵɵelement(94, \"i\", 41);\n        i0.ɵɵtext(95, \" \\u0645\\u0642\\u0627\\u0644\\u0627\\u062A \\u062A\\u0647\\u0645\\u0643 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(96, \"p\", 29);\n        i0.ɵɵtext(97, \"\\u0627\\u0642\\u0631\\u0623 \\u0623\\u062D\\u062F\\u062B \\u0627\\u0644\\u0645\\u0642\\u0627\\u0644\\u0627\\u062A \\u0648\\u0627\\u0644\\u0646\\u0635\\u0627\\u0626\\u062D \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631\\u064A\\u0629\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(98, \"div\", 30);\n        i0.ɵɵtemplate(99, HomeComponent_div_99_Template, 25, 8, \"div\", 42);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(100, \"div\", 32)(101, \"div\", 26)(102, \"button\", 43);\n        i0.ɵɵtext(103, \" \\u0639\\u0631\\u0636 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0642\\u0627\\u0644\\u0627\\u062A \");\n        i0.ɵɵelement(104, \"i\", 34);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(105, \"section\", 44)(106, \"div\", 3)(107, \"div\", 45)(108, \"div\", 5)(109, \"div\", 46)(110, \"h3\", 47);\n        i0.ɵɵtext(111, \"\\u0627\\u0634\\u062A\\u0631\\u0643 \\u0641\\u064A \\u0627\\u0644\\u0646\\u0634\\u0631\\u0629 \\u0627\\u0644\\u0625\\u062E\\u0628\\u0627\\u0631\\u064A\\u0629\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(112, \"p\", 48);\n        i0.ɵɵtext(113, \" \\u0627\\u062D\\u0635\\u0644 \\u0639\\u0644\\u0649 \\u0623\\u062D\\u062F\\u062B \\u0627\\u0644\\u0639\\u0631\\u0648\\u0636 \\u0648\\u0627\\u0644\\u0645\\u0642\\u0627\\u0644\\u0627\\u062A \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631\\u064A\\u0629 \\u0645\\u0628\\u0627\\u0634\\u0631\\u0629 \\u0641\\u064A \\u0628\\u0631\\u064A\\u062F\\u0643 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(114, \"div\", 5)(115, \"div\", 49)(116, \"div\", 50);\n        i0.ɵɵelement(117, \"input\", 51);\n        i0.ɵɵelementStart(118, \"button\", 52);\n        i0.ɵɵelement(119, \"i\", 53);\n        i0.ɵɵtext(120, \" \\u0627\\u0634\\u062A\\u0631\\u0643 \");\n        i0.ɵɵelementEnd()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(60);\n        i0.ɵɵproperty(\"ngForOf\", ctx.heroCards);\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"ngForOf\", ctx.featuredProperties);\n        i0.ɵɵadvance(17);\n        i0.ɵɵproperty(\"ngForOf\", ctx.cities);\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"ngForOf\", ctx.blogArticles);\n      }\n    },\n    dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule],\n    styles: [\".hero-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  min-height: 100vh;\\n  overflow: hidden;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);\\n  background-size: cover;\\n  background-position: center;\\n  background-attachment: fixed;\\n  min-height: 100vh;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .hero-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(30, 60, 114, 0.8);\\n  z-index: 1;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%] {\\n  color: white;\\n  padding: 2rem 0;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n  font-size: 3.5rem;\\n  font-weight: 700;\\n  line-height: 1.2;\\n  margin-bottom: 1.5rem;\\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  margin-bottom: 2rem;\\n  opacity: 0.9;\\n  line-height: 1.6;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-tabs[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 1.5rem;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  background: #f8f9fa;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-tabs[_ngcontent-%COMP%]   .search-tab[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0.75rem 1rem;\\n  border: none;\\n  background: transparent;\\n  color: #6c757d;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-tabs[_ngcontent-%COMP%]   .search-tab.active[_ngcontent-%COMP%] {\\n  background: var(--bs-primary);\\n  color: white;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-tabs[_ngcontent-%COMP%]   .search-tab[_ngcontent-%COMP%]:hover:not(.active) {\\n  background: #e9ecef;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr 1fr auto;\\n  gap: 1rem;\\n  align-items: end;\\n}\\n@media (max-width: 768px) {\\n  .hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 1rem;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: #6c757d;\\n  z-index: 3;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%] {\\n  padding-left: 3rem;\\n  border: 2px solid #e9ecef;\\n  border-radius: 10px;\\n  height: 50px;\\n  font-weight: 500;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]:focus {\\n  border-color: var(--bs-primary);\\n  box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%] {\\n  height: 50px;\\n  padding: 0 2rem;\\n  border-radius: 10px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  white-space: nowrap;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr;\\n  gap: 2rem;\\n  padding: 2rem 0;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card.hero-card-1[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInRight 0.6s ease-out 0.2s both;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card.hero-card-2[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInRight 0.6s ease-out 0.4s both;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card.hero-card-3[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInRight 0.6s ease-out 0.6s both;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card[_ngcontent-%COMP%]   .hero-card-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card[_ngcontent-%COMP%]   .hero-card-content[_ngcontent-%COMP%]   .hero-card-icon[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto 1.5rem;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card[_ngcontent-%COMP%]   .hero-card-content[_ngcontent-%COMP%]   .hero-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  margin-bottom: 1rem;\\n  color: #2c3e50;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card[_ngcontent-%COMP%]   .hero-card-content[_ngcontent-%COMP%]   .hero-card-description[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  line-height: 1.6;\\n  margin: 0;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInRight {\\n  from {\\n    opacity: 0;\\n    transform: translateX(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 1rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n@media (max-width: 768px) {\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n}\\n\\n.section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  color: #6c757d;\\n  margin-bottom: 0;\\n}\\n\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  height: 100%;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-badges[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  z-index: 2;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 1rem;\\n  left: 1rem;\\n  background: rgba(255, 255, 255, 0.9);\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-title[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n  color: #2c3e50;\\n  line-height: 1.4;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-location[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-bottom: 1rem;\\n  display: flex;\\n  align-items: center;\\n  font-size: 0.875rem;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n  flex-wrap: wrap;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-details[_ngcontent-%COMP%]   .property-detail[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-details[_ngcontent-%COMP%]   .property-detail[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: baseline;\\n  gap: 0.5rem;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: var(--bs-primary);\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%]   .currency[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.875rem;\\n}\\n\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%] {\\n  border-radius: 15px;\\n  overflow: hidden;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  height: 200px;\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]   .city-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]   .city-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]   .city-image[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]   .city-image[_ngcontent-%COMP%]   .city-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);\\n  display: flex;\\n  align-items: end;\\n  padding: 1.5rem;\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]   .city-image[_ngcontent-%COMP%]   .city-overlay[_ngcontent-%COMP%]   .city-content[_ngcontent-%COMP%] {\\n  color: white;\\n  text-align: center;\\n  width: 100%;\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]   .city-image[_ngcontent-%COMP%]   .city-overlay[_ngcontent-%COMP%]   .city-content[_ngcontent-%COMP%]   .city-name[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]   .city-image[_ngcontent-%COMP%]   .city-overlay[_ngcontent-%COMP%]   .city-content[_ngcontent-%COMP%]   .city-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  opacity: 0.9;\\n  margin: 0;\\n}\\n\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  height: 100%;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-category[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  z-index: 2;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   .article-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  margin-bottom: 1rem;\\n  color: #2c3e50;\\n  line-height: 1.4;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   .article-excerpt[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-bottom: 1.5rem;\\n  line-height: 1.6;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   .article-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   .article-meta[_ngcontent-%COMP%]   .article-author[_ngcontent-%COMP%], \\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   .article-meta[_ngcontent-%COMP%]   .article-date[_ngcontent-%COMP%], \\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   .article-meta[_ngcontent-%COMP%]   .article-read-time[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #6c757d;\\n}\\n\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-content[_ngcontent-%COMP%]   .newsletter-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  margin-bottom: 1rem;\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-content[_ngcontent-%COMP%]   .newsletter-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  opacity: 0.9;\\n  margin: 0;\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 10px 0 0 10px;\\n  padding: 1rem 1.5rem;\\n  font-size: 1rem;\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: transparent;\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 0 10px 10px 0;\\n  padding: 1rem 2rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  white-space: nowrap;\\n}\\n\\n@media (max-width: 768px) {\\n  .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 1rem;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n  .newsletter-section[_ngcontent-%COMP%]   .newsletter-content[_ngcontent-%COMP%] {\\n    text-align: center;\\n    margin-bottom: 2rem;\\n  }\\n  .newsletter-section[_ngcontent-%COMP%]   .newsletter-content[_ngcontent-%COMP%]   .newsletter-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    border-radius: 10px;\\n    margin-bottom: 1rem;\\n  }\\n  .newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    border-radius: 10px;\\n    justify-content: center;\\n  }\\n}\\n.fade-in[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.6s ease-out;\\n}\\n\\n.slide-up[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideUp 0.6s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.bg-light-dark-blue[_ngcontent-%COMP%] {\\n  background-color: rgba(var(--bs-primary-rgb), 0.1) !important;\\n}\\n\\n.text-dark-blue[_ngcontent-%COMP%] {\\n  color: var(--bs-primary) !important;\\n}\\n\\n.btn-dark-blue[_ngcontent-%COMP%] {\\n  background-color: var(--bs-primary);\\n  border-color: var(--bs-primary);\\n  color: white;\\n}\\n.btn-dark-blue[_ngcontent-%COMP%]:hover, .btn-dark-blue[_ngcontent-%COMP%]:focus, .btn-dark-blue[_ngcontent-%COMP%]:active {\\n  background-color: rgba(var(--bs-primary-rgb), 0.9);\\n  border-color: rgba(var(--bs-primary-rgb), 0.9);\\n  color: white;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵclassMap", "i_r2", "ɵɵadvance", "card_r1", "color", "icon", "ɵɵtextInterpolate", "title", "description", "ɵɵtextInterpolate1", "property_r4", "bedrooms", "bathrooms", "ɵɵlistener", "HomeComponent_div_71_Template_div_click_1_listener", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "viewProperty", "id", "ɵɵtemplate", "HomeComponent_div_71_span_5_Template", "HomeComponent_div_71_div_17_Template", "HomeComponent_div_71_div_18_Template", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "isNew", "rating", "location", "area", "price", "HomeComponent_div_88_Template_div_click_1_listener", "city_r7", "_r6", "viewCity", "name", "propertiesCount", "HomeComponent_div_99_Template_div_click_1_listener", "article_r9", "_r8", "readArticle", "category", "excerpt", "author", "date", "readTime", "HomeComponent", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "HomeComponent_Template_button_click_55_listener", "searchProperties", "HomeComponent_div_60_Template", "HomeComponent_div_71_Template", "HomeComponent_div_88_Template", "HomeComponent_div_99_Template", "heroCards", "featuredProperties", "cities", "blogArticles", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule],\r\n  templateUrl: './home.component.html',\r\n  styleUrl: './home.component.scss'\r\n})\r\nexport class HomeComponent implements OnInit {\r\n\r\n}\r\n", "<!-- Hero Section -->\r\n<section class=\"hero-section\">\r\n  <div class=\"hero-background\">\r\n    <div class=\"hero-overlay\"></div>\r\n    <div class=\"container\">\r\n      <div class=\"row align-items-center min-vh-100\">\r\n        <!-- Hero Content -->\r\n        <div class=\"col-lg-6\">\r\n          <div class=\"hero-content\">\r\n            <h1 class=\"hero-title\">\r\n              ابحث عن العقار المثالي\r\n              <span class=\"text-primary\">بسهولة وثقة</span>\r\n            </h1>\r\n            <p class=\"hero-subtitle\">\r\n              منصة إيزي ديل تقدم لك أفضل العقارات في مصر مع خدمات متميزة وأسعار تنافسية\r\n            </p>\r\n\r\n            <!-- Search Form -->\r\n            <div class=\"hero-search-form\">\r\n              <div class=\"search-tabs\">\r\n                <button class=\"search-tab active\">شراء</button>\r\n                <button class=\"search-tab\">إيجار</button>\r\n                <button class=\"search-tab\">استثمار</button>\r\n              </div>\r\n\r\n              <div class=\"search-inputs\">\r\n                <div class=\"search-input-group\">\r\n                  <i class=\"ki-outline ki-geolocation fs-2\"></i>\r\n                  <select class=\"form-select\">\r\n                    <option>اختر المدينة</option>\r\n                    <option>القاهرة الجديدة</option>\r\n                    <option>الشيخ زايد</option>\r\n                    <option>العاصمة الإدارية</option>\r\n                  </select>\r\n                </div>\r\n\r\n                <div class=\"search-input-group\">\r\n                  <i class=\"ki-outline ki-home fs-2\"></i>\r\n                  <select class=\"form-select\">\r\n                    <option>نوع العقار</option>\r\n                    <option>شقة</option>\r\n                    <option>فيلا</option>\r\n                    <option>مكتب</option>\r\n                  </select>\r\n                </div>\r\n\r\n                <div class=\"search-input-group\">\r\n                  <i class=\"ki-outline ki-dollar fs-2\"></i>\r\n                  <select class=\"form-select\">\r\n                    <option>الميزانية</option>\r\n                    <option>أقل من مليون</option>\r\n                    <option>1-3 مليون</option>\r\n                    <option>أكثر من 3 مليون</option>\r\n                  </select>\r\n                </div>\r\n\r\n                <button class=\"btn btn-primary search-btn\" (click)=\"searchProperties()\">\r\n                  <i class=\"ki-outline ki-magnifier fs-2\"></i>\r\n                  ابحث\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Hero Cards -->\r\n        <div class=\"col-lg-6\">\r\n          <div class=\"hero-cards\">\r\n            <div class=\"hero-card\" *ngFor=\"let card of heroCards; let i = index\" [class]=\"'hero-card-' + (i + 1)\">\r\n              <div class=\"hero-card-content\">\r\n                <div class=\"hero-card-icon\" [class]=\"'bg-light-' + card.color\">\r\n                  <i [class]=\"card.icon + ' fs-1 text-' + card.color\"></i>\r\n                </div>\r\n                <h3 class=\"hero-card-title\">{{ card.title }}</h3>\r\n                <p class=\"hero-card-description\">{{ card.description }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Featured Properties Section -->\r\n<section class=\"featured-properties-section py-15\">\r\n  <div class=\"container\">\r\n    <!-- Section Header -->\r\n    <div class=\"row mb-10\">\r\n      <div class=\"col-12 text-center\">\r\n        <h2 class=\"section-title\">\r\n          <i class=\"ki-outline ki-star fs-1 text-warning me-3\"></i>\r\n          العقارات المميزة\r\n        </h2>\r\n        <p class=\"section-subtitle\">اكتشف أفضل العقارات المتاحة حالياً</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Properties Grid -->\r\n    <div class=\"row g-6\">\r\n      <div class=\"col-lg-3 col-md-6\" *ngFor=\"let property of featuredProperties\">\r\n        <div class=\"property-card\" (click)=\"viewProperty(property.id)\">\r\n          <!-- Property Image -->\r\n          <div class=\"property-image\">\r\n            <img [src]=\"property.image\" [alt]=\"property.title\" class=\"w-100\">\r\n            <div class=\"property-badges\">\r\n              <span class=\"badge badge-success\" *ngIf=\"property.isNew\">جديد</span>\r\n            </div>\r\n            <div class=\"property-rating\">\r\n              <i class=\"ki-solid ki-star text-warning\"></i>\r\n              <span>{{ property.rating }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Property Content -->\r\n          <div class=\"property-content\">\r\n            <h4 class=\"property-title\">{{ property.title }}</h4>\r\n            <p class=\"property-location\">\r\n              <i class=\"ki-outline ki-geolocation text-muted me-2\"></i>\r\n              {{ property.location }}\r\n            </p>\r\n\r\n            <div class=\"property-details\">\r\n              <div class=\"property-detail\" *ngIf=\"property.bedrooms > 0\">\r\n                <i class=\"ki-outline ki-home text-muted\"></i>\r\n                <span>{{ property.bedrooms }} غرف</span>\r\n              </div>\r\n              <div class=\"property-detail\" *ngIf=\"property.bathrooms > 0\">\r\n                <i class=\"ki-outline ki-droplet text-muted\"></i>\r\n                <span>{{ property.bathrooms }} حمام</span>\r\n              </div>\r\n              <div class=\"property-detail\">\r\n                <i class=\"ki-outline ki-resize text-muted\"></i>\r\n                <span>{{ property.area }} م²</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"property-price\">\r\n              <span class=\"price\">{{ property.price }}</span>\r\n              <span class=\"currency\">جنيه</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- View All Button -->\r\n    <div class=\"row mt-10\">\r\n      <div class=\"col-12 text-center\">\r\n        <button class=\"btn btn-outline-primary btn-lg\">\r\n          عرض جميع العقارات\r\n          <i class=\"ki-outline ki-arrow-left ms-2\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Cities Carousel Section -->\r\n<section class=\"cities-section py-15 bg-light\">\r\n  <div class=\"container\">\r\n    <!-- Section Header -->\r\n    <div class=\"row mb-10\">\r\n      <div class=\"col-12 text-center\">\r\n        <h2 class=\"section-title\">\r\n          <i class=\"ki-outline ki-map fs-1 text-primary me-3\"></i>\r\n          استكشف المدن\r\n        </h2>\r\n        <p class=\"section-subtitle\">اختر المدينة التي تناسبك</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Cities Carousel -->\r\n    <div class=\"cities-carousel\">\r\n      <div class=\"row g-4\">\r\n        <div class=\"col-lg-2 col-md-4 col-6\" *ngFor=\"let city of cities\">\r\n          <div class=\"city-card\" (click)=\"viewCity(city.name)\">\r\n            <div class=\"city-image\">\r\n              <img [src]=\"city.image\" [alt]=\"city.name\" class=\"w-100\">\r\n              <div class=\"city-overlay\">\r\n                <div class=\"city-content\">\r\n                  <h4 class=\"city-name\">{{ city.name }}</h4>\r\n                  <p class=\"city-count\">{{ city.propertiesCount }} عقار</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Blog Articles Section -->\r\n<section class=\"blog-section py-15\">\r\n  <div class=\"container\">\r\n    <!-- Section Header -->\r\n    <div class=\"row mb-10\">\r\n      <div class=\"col-12 text-center\">\r\n        <h2 class=\"section-title\">\r\n          <i class=\"ki-outline ki-book fs-1 text-info me-3\"></i>\r\n          مقالات تهمك\r\n        </h2>\r\n        <p class=\"section-subtitle\">اقرأ أحدث المقالات والنصائح العقارية</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Articles Grid -->\r\n    <div class=\"row g-6\">\r\n      <div class=\"col-lg-4 col-md-6\" *ngFor=\"let article of blogArticles\">\r\n        <div class=\"article-card\" (click)=\"readArticle(article.id)\">\r\n          <!-- Article Image -->\r\n          <div class=\"article-image\">\r\n            <img [src]=\"article.image\" [alt]=\"article.title\" class=\"w-100\">\r\n            <div class=\"article-category\">\r\n              <span class=\"badge badge-primary\">{{ article.category }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Article Content -->\r\n          <div class=\"article-content\">\r\n            <h4 class=\"article-title\">{{ article.title }}</h4>\r\n            <p class=\"article-excerpt\">{{ article.excerpt }}</p>\r\n\r\n            <div class=\"article-meta\">\r\n              <div class=\"article-author\">\r\n                <i class=\"ki-outline ki-profile-user text-muted me-2\"></i>\r\n                <span>{{ article.author }}</span>\r\n              </div>\r\n              <div class=\"article-date\">\r\n                <i class=\"ki-outline ki-calendar text-muted me-2\"></i>\r\n                <span>{{ article.date }}</span>\r\n              </div>\r\n              <div class=\"article-read-time\">\r\n                <i class=\"ki-outline ki-time text-muted me-2\"></i>\r\n                <span>{{ article.readTime }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- View All Articles Button -->\r\n    <div class=\"row mt-10\">\r\n      <div class=\"col-12 text-center\">\r\n        <button class=\"btn btn-outline-info btn-lg\">\r\n          عرض جميع المقالات\r\n          <i class=\"ki-outline ki-arrow-left ms-2\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Newsletter Section -->\r\n<section class=\"newsletter-section py-15 bg-primary\">\r\n  <div class=\"container\">\r\n    <div class=\"row align-items-center\">\r\n      <div class=\"col-lg-6\">\r\n        <div class=\"newsletter-content text-white\">\r\n          <h3 class=\"newsletter-title\">اشترك في النشرة الإخبارية</h3>\r\n          <p class=\"newsletter-subtitle\">\r\n            احصل على أحدث العروض والمقالات العقارية مباشرة في بريدك الإلكتروني\r\n          </p>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-lg-6\">\r\n        <div class=\"newsletter-form\">\r\n          <div class=\"input-group\">\r\n            <input type=\"email\" class=\"form-control form-control-lg\" placeholder=\"أدخل بريدك الإلكتروني\">\r\n            <button class=\"btn btn-light btn-lg\" type=\"button\">\r\n              <i class=\"ki-outline ki-send fs-2\"></i>\r\n              اشترك\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;;;ICoE9BC,EAFJ,CAAAC,cAAA,cAAsG,cACrE,cACkC;IAC7DD,EAAA,CAAAE,SAAA,QAAwD;IAC1DF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAI,MAAA,GAAsB;IAE3DJ,EAF2D,CAAAG,YAAA,EAAI,EACvD,EACF;;;;;IAR+DH,EAAA,CAAAK,UAAA,iBAAAC,IAAA,MAAgC;IAErEN,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAK,UAAA,eAAAG,OAAA,CAAAC,KAAA,CAAkC;IACzDT,EAAA,CAAAO,SAAA,EAAgD;IAAhDP,EAAA,CAAAK,UAAA,CAAAG,OAAA,CAAAE,IAAA,mBAAAF,OAAA,CAAAC,KAAA,CAAgD;IAEzBT,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAW,iBAAA,CAAAH,OAAA,CAAAI,KAAA,CAAgB;IACXZ,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAW,iBAAA,CAAAH,OAAA,CAAAK,WAAA,CAAsB;;;;;IAgCzDb,EAAA,CAAAC,cAAA,eAAyD;IAAAD,EAAA,CAAAI,MAAA,+BAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAiBpEH,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,SAAA,YAA6C;IAC7CF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAA2B;IACnCJ,EADmC,CAAAG,YAAA,EAAO,EACpC;;;;IADEH,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAc,kBAAA,KAAAC,WAAA,CAAAC,QAAA,wBAA2B;;;;;IAEnChB,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAA6B;IACrCJ,EADqC,CAAAG,YAAA,EAAO,EACtC;;;;IADEH,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAc,kBAAA,KAAAC,WAAA,CAAAE,SAAA,8BAA6B;;;;;;IA5B3CjB,EADF,CAAAC,cAAA,cAA2E,cACV;IAApCD,EAAA,CAAAkB,UAAA,mBAAAC,mDAAA;MAAA,MAAAJ,WAAA,GAAAf,EAAA,CAAAoB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAX,WAAA,CAAAY,EAAA,CAAyB;IAAA,EAAC;IAE5D3B,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAE,SAAA,cAAiE;IACjEF,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAA4B,UAAA,IAAAC,oCAAA,mBAAyD;IAC3D7B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAE,SAAA,YAA6C;IAC7CF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAAqB;IAE/BJ,EAF+B,CAAAG,YAAA,EAAO,EAC9B,EACF;IAIJH,EADF,CAAAC,cAAA,eAA8B,cACD;IAAAD,EAAA,CAAAI,MAAA,IAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAE,SAAA,aAAyD;IACzDF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAC,cAAA,eAA8B;IAK5BD,EAJA,CAAA4B,UAAA,KAAAE,oCAAA,kBAA2D,KAAAC,oCAAA,kBAIC;IAI5D/B,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAE,SAAA,aAA+C;IAC/CF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAAsB;IAEhCJ,EAFgC,CAAAG,YAAA,EAAO,EAC/B,EACF;IAGJH,EADF,CAAAC,cAAA,eAA4B,gBACN;IAAAD,EAAA,CAAAI,MAAA,IAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAI,MAAA,gCAAI;IAInCJ,EAJmC,CAAAG,YAAA,EAAO,EAC9B,EACF,EACF,EACF;;;;IAvCKH,EAAA,CAAAO,SAAA,GAAsB;IAACP,EAAvB,CAAAgC,UAAA,QAAAjB,WAAA,CAAAkB,KAAA,EAAAjC,EAAA,CAAAkC,aAAA,CAAsB,QAAAnB,WAAA,CAAAH,KAAA,CAAuB;IAEbZ,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAgC,UAAA,SAAAjB,WAAA,CAAAoB,KAAA,CAAoB;IAIjDnC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAW,iBAAA,CAAAI,WAAA,CAAAqB,MAAA,CAAqB;IAMFpC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAW,iBAAA,CAAAI,WAAA,CAAAH,KAAA,CAAoB;IAG7CZ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAC,WAAA,CAAAsB,QAAA,MACF;IAGgCrC,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAgC,UAAA,SAAAjB,WAAA,CAAAC,QAAA,KAA2B;IAI3BhB,EAAA,CAAAO,SAAA,EAA4B;IAA5BP,EAAA,CAAAgC,UAAA,SAAAjB,WAAA,CAAAE,SAAA,KAA4B;IAMlDjB,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAc,kBAAA,KAAAC,WAAA,CAAAuB,IAAA,kBAAsB;IAKVtC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAW,iBAAA,CAAAI,WAAA,CAAAwB,KAAA,CAAoB;;;;;;IAsC5CvC,EADF,CAAAC,cAAA,cAAiE,cACV;IAA9BD,EAAA,CAAAkB,UAAA,mBAAAsB,mDAAA;MAAA,MAAAC,OAAA,GAAAzC,EAAA,CAAAoB,aAAA,CAAAsB,GAAA,EAAApB,SAAA;MAAA,MAAAC,MAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASF,MAAA,CAAAoB,QAAA,CAAAF,OAAA,CAAAG,IAAA,CAAmB;IAAA,EAAC;IAClD5C,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,SAAA,cAAwD;IAGpDF,EAFJ,CAAAC,cAAA,cAA0B,cACE,aACF;IAAAD,EAAA,CAAAI,MAAA,GAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAI,MAAA,GAA+B;IAK/DJ,EAL+D,CAAAG,YAAA,EAAI,EACrD,EACF,EACF,EACF,EACF;;;;IATKH,EAAA,CAAAO,SAAA,GAAkB;IAACP,EAAnB,CAAAgC,UAAA,QAAAS,OAAA,CAAAR,KAAA,EAAAjC,EAAA,CAAAkC,aAAA,CAAkB,QAAAO,OAAA,CAAAG,IAAA,CAAkB;IAGf5C,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAW,iBAAA,CAAA8B,OAAA,CAAAG,IAAA,CAAe;IACf5C,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAc,kBAAA,KAAA2B,OAAA,CAAAI,eAAA,8BAA+B;;;;;;IA4B/D7C,EADF,CAAAC,cAAA,cAAoE,cACN;IAAlCD,EAAA,CAAAkB,UAAA,mBAAA4B,mDAAA;MAAA,MAAAC,UAAA,GAAA/C,EAAA,CAAAoB,aAAA,CAAA4B,GAAA,EAAA1B,SAAA;MAAA,MAAAC,MAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASF,MAAA,CAAA0B,WAAA,CAAAF,UAAA,CAAApB,EAAA,CAAuB;IAAA,EAAC;IAEzD3B,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAE,SAAA,cAA+D;IAE7DF,EADF,CAAAC,cAAA,cAA8B,eACM;IAAAD,EAAA,CAAAI,MAAA,GAAsB;IAE5DJ,EAF4D,CAAAG,YAAA,EAAO,EAC3D,EACF;IAIJH,EADF,CAAAC,cAAA,cAA6B,aACD;IAAAD,EAAA,CAAAI,MAAA,GAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAI,MAAA,IAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGlDH,EADF,CAAAC,cAAA,eAA0B,eACI;IAC1BD,EAAA,CAAAE,SAAA,aAA0D;IAC1DF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAAoB;IAC5BJ,EAD4B,CAAAG,YAAA,EAAO,EAC7B;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,SAAA,cAAsD;IACtDF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAAkB;IAC1BJ,EAD0B,CAAAG,YAAA,EAAO,EAC3B;IACNH,EAAA,CAAAC,cAAA,gBAA+B;IAC7BD,EAAA,CAAAE,SAAA,cAAkD;IAClDF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAAsB;IAKtCJ,EALsC,CAAAG,YAAA,EAAO,EAC/B,EACF,EACF,EACF,EACF;;;;IA3BKH,EAAA,CAAAO,SAAA,GAAqB;IAACP,EAAtB,CAAAgC,UAAA,QAAAe,UAAA,CAAAd,KAAA,EAAAjC,EAAA,CAAAkC,aAAA,CAAqB,QAAAa,UAAA,CAAAnC,KAAA,CAAsB;IAEZZ,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAW,iBAAA,CAAAoC,UAAA,CAAAG,QAAA,CAAsB;IAMhClD,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAW,iBAAA,CAAAoC,UAAA,CAAAnC,KAAA,CAAmB;IAClBZ,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAW,iBAAA,CAAAoC,UAAA,CAAAI,OAAA,CAAqB;IAKtCnD,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAW,iBAAA,CAAAoC,UAAA,CAAAK,MAAA,CAAoB;IAIpBpD,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAW,iBAAA,CAAAoC,UAAA,CAAAM,IAAA,CAAkB;IAIlBrD,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAW,iBAAA,CAAAoC,UAAA,CAAAO,QAAA,CAAsB;;;ADhO5C,OAAM,MAAOC,aAAa;;qCAAbA,aAAa;EAAA;;UAAbA,aAAa;IAAAC,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAA1D,EAAA,CAAA2D,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTxBjE,EADF,CAAAC,cAAA,iBAA8B,aACC;QAC3BD,EAAA,CAAAE,SAAA,aAAgC;QAMxBF,EALR,CAAAC,cAAA,aAAuB,aAC0B,aAEvB,aACM,YACD;QACrBD,EAAA,CAAAI,MAAA,8HACA;QAAAJ,EAAA,CAAAC,cAAA,cAA2B;QAAAD,EAAA,CAAAI,MAAA,qEAAW;QACxCJ,EADwC,CAAAG,YAAA,EAAO,EAC1C;QACLH,EAAA,CAAAC,cAAA,YAAyB;QACvBD,EAAA,CAAAI,MAAA,+XACF;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAKAH,EAFJ,CAAAC,cAAA,eAA8B,eACH,kBACW;QAAAD,EAAA,CAAAI,MAAA,gCAAI;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAC/CH,EAAA,CAAAC,cAAA,kBAA2B;QAAAD,EAAA,CAAAI,MAAA,sCAAK;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QACzCH,EAAA,CAAAC,cAAA,kBAA2B;QAAAD,EAAA,CAAAI,MAAA,kDAAO;QACpCJ,EADoC,CAAAG,YAAA,EAAS,EACvC;QAGJH,EADF,CAAAC,cAAA,eAA2B,eACO;QAC9BD,EAAA,CAAAE,SAAA,aAA8C;QAE5CF,EADF,CAAAC,cAAA,kBAA4B,cAClB;QAAAD,EAAA,CAAAI,MAAA,2EAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAC7BH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,6FAAe;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAChCH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,+DAAU;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAC3BH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,mGAAgB;QAE5BJ,EAF4B,CAAAG,YAAA,EAAS,EAC1B,EACL;QAENH,EAAA,CAAAC,cAAA,eAAgC;QAC9BD,EAAA,CAAAE,SAAA,aAAuC;QAErCF,EADF,CAAAC,cAAA,kBAA4B,cAClB;QAAAD,EAAA,CAAAI,MAAA,+DAAU;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAC3BH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,0BAAG;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QACpBH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,gCAAI;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QACrBH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,gCAAI;QAEhBJ,EAFgB,CAAAG,YAAA,EAAS,EACd,EACL;QAENH,EAAA,CAAAC,cAAA,eAAgC;QAC9BD,EAAA,CAAAE,SAAA,aAAyC;QAEvCF,EADF,CAAAC,cAAA,kBAA4B,cAClB;QAAAD,EAAA,CAAAI,MAAA,8DAAS;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAC1BH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,sEAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAC7BH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,0CAAS;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAC1BH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,8EAAe;QAE3BJ,EAF2B,CAAAG,YAAA,EAAS,EACzB,EACL;QAENH,EAAA,CAAAC,cAAA,kBAAwE;QAA7BD,EAAA,CAAAkB,UAAA,mBAAAiD,gDAAA;UAAA,OAASD,GAAA,CAAAE,gBAAA,EAAkB;QAAA,EAAC;QACrEpE,EAAA,CAAAE,SAAA,aAA4C;QAC5CF,EAAA,CAAAI,MAAA,kCACF;QAIRJ,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;QAIJH,EADF,CAAAC,cAAA,cAAsB,eACI;QACtBD,EAAA,CAAA4B,UAAA,KAAAyC,6BAAA,kBAAsG;QAclHrE,EALU,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF,EACE;QAQFH,EALR,CAAAC,cAAA,mBAAmD,cAC1B,eAEE,eACW,cACJ;QACxBD,EAAA,CAAAE,SAAA,aAAyD;QACzDF,EAAA,CAAAI,MAAA,qGACF;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,aAA4B;QAAAD,EAAA,CAAAI,MAAA,gMAAkC;QAElEJ,EAFkE,CAAAG,YAAA,EAAI,EAC9D,EACF;QAGNH,EAAA,CAAAC,cAAA,eAAqB;QACnBD,EAAA,CAAA4B,UAAA,KAAA0C,6BAAA,oBAA2E;QA4C7EtE,EAAA,CAAAG,YAAA,EAAM;QAKFH,EAFJ,CAAAC,cAAA,eAAuB,eACW,kBACiB;QAC7CD,EAAA,CAAAI,MAAA,sGACA;QAAAJ,EAAA,CAAAE,SAAA,aAA6C;QAKvDF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACE;QAQFH,EALR,CAAAC,cAAA,mBAA+C,cACtB,eAEE,eACW,cACJ;QACxBD,EAAA,CAAAE,SAAA,aAAwD;QACxDF,EAAA,CAAAI,MAAA,6EACF;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,aAA4B;QAAAD,EAAA,CAAAI,MAAA,yIAAwB;QAExDJ,EAFwD,CAAAG,YAAA,EAAI,EACpD,EACF;QAIJH,EADF,CAAAC,cAAA,eAA6B,eACN;QACnBD,EAAA,CAAA4B,UAAA,KAAA2C,6BAAA,mBAAiE;QAgBzEvE,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACE;QAQFH,EALR,CAAAC,cAAA,mBAAoC,cACX,eAEE,eACW,cACJ;QACxBD,EAAA,CAAAE,SAAA,aAAsD;QACtDF,EAAA,CAAAI,MAAA,uEACF;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,aAA4B;QAAAD,EAAA,CAAAI,MAAA,4MAAoC;QAEpEJ,EAFoE,CAAAG,YAAA,EAAI,EAChE,EACF;QAGNH,EAAA,CAAAC,cAAA,eAAqB;QACnBD,EAAA,CAAA4B,UAAA,KAAA4C,6BAAA,mBAAoE;QAgCtExE,EAAA,CAAAG,YAAA,EAAM;QAKFH,EAFJ,CAAAC,cAAA,gBAAuB,gBACW,mBACc;QAC1CD,EAAA,CAAAI,MAAA,uGACA;QAAAJ,EAAA,CAAAE,SAAA,cAA6C;QAKvDF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACE;QAQAH,EALV,CAAAC,cAAA,oBAAqD,eAC5B,gBACe,eACZ,gBACuB,eACZ;QAAAD,EAAA,CAAAI,MAAA,gJAAyB;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC3DH,EAAA,CAAAC,cAAA,cAA+B;QAC7BD,EAAA,CAAAI,MAAA,0WACF;QAEJJ,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;QAGFH,EAFJ,CAAAC,cAAA,eAAsB,gBACS,gBACF;QACvBD,EAAA,CAAAE,SAAA,kBAA6F;QAC7FF,EAAA,CAAAC,cAAA,mBAAmD;QACjDD,EAAA,CAAAE,SAAA,cAAuC;QACvCF,EAAA,CAAAI,MAAA,yCACF;QAMZJ,EANY,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF,EACE;;;QApN0CH,EAAA,CAAAO,SAAA,IAAc;QAAdP,EAAA,CAAAgC,UAAA,YAAAkC,GAAA,CAAAO,SAAA,CAAc;QAgCRzE,EAAA,CAAAO,SAAA,IAAqB;QAArBP,EAAA,CAAAgC,UAAA,YAAAkC,GAAA,CAAAQ,kBAAA,CAAqB;QA2EjB1E,EAAA,CAAAO,SAAA,IAAS;QAATP,EAAA,CAAAgC,UAAA,YAAAkC,GAAA,CAAAS,MAAA,CAAS;QAkCd3E,EAAA,CAAAO,SAAA,IAAe;QAAfP,EAAA,CAAAgC,UAAA,YAAAkC,GAAA,CAAAU,YAAA,CAAe;;;mBD1M5D9E,YAAY,EAAA+E,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEhF,YAAY;IAAAiF,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}