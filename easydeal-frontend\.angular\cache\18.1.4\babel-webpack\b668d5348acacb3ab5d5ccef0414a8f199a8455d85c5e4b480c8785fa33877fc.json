{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/config-factory.service\";\nexport class StepperInputConfigService {\n  configFactory;\n  constructor(configFactory) {\n    this.configFactory = configFactory;\n  }\n  /**\n   * Get input configurations for the stepper modal\n   */\n  getInputConfigs(stepperModal) {\n    return this.configFactory.getInputConfigs(stepperModal);\n  }\n  /**\n   * Get inputs for a specific configuration key and step\n   */\n  getInputsForKey(key, step, stepperModal) {\n    return this.configFactory.getInputsForKey(key, step, stepperModal);\n  }\n  /**\n   * Check if a configuration key exists\n   */\n  hasConfiguration(key, stepperModal) {\n    return this.configFactory.hasConfiguration(key, stepperModal);\n  }\n  /**\n   * Get all available configuration keys\n   */\n  getAvailableConfigKeys(stepperModal) {\n    return this.configFactory.getAllAvailableConfigKeys(stepperModal);\n  }\n  /**\n   * Get configuration type based on key\n   */\n  getConfigurationType(key) {\n    return this.configFactory.getConfigurationType(key);\n  }\n  static ɵfac = function StepperInputConfigService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || StepperInputConfigService)(i0.ɵɵinject(i1.ConfigFactoryService));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: StepperInputConfigService,\n    factory: StepperInputConfigService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["StepperInputConfigService", "configFactory", "constructor", "getInputConfigs", "stepperModal", "getInputsForKey", "key", "step", "hasConfiguration", "getAvailableConfigKeys", "getAllAvailableConfigKeys", "getConfigurationType", "i0", "ɵɵinject", "i1", "ConfigFactoryService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\shared\\stepper-modal\\stepper-input-config.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { ConfigFactoryService } from './services/config-factory.service';\r\nimport { InputConfig, StepperConfiguration } from './services/base-config.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class StepperInputConfigService {\r\n\r\n  constructor(private configFactory: ConfigFactoryService) { }\r\n\r\n  /**\r\n   * Get input configurations for the stepper modal\r\n   */\r\n  getInputConfigs(stepperModal: any): StepperConfiguration[] {\r\n    return this.configFactory.getInputConfigs(stepperModal);\r\n  }\r\n\r\n  /**\r\n   * Get inputs for a specific configuration key and step\r\n   */\r\n  getInputsForKey(key: string, step: number, stepperModal: any): InputConfig[] {\r\n    return this.configFactory.getInputsForKey(key, step, stepperModal);\r\n  }\r\n\r\n  /**\r\n   * Check if a configuration key exists\r\n   */\r\n  hasConfiguration(key: string, stepperModal: any): boolean {\r\n    return this.configFactory.hasConfiguration(key, stepperModal);\r\n  }\r\n\r\n  /**\r\n   * Get all available configuration keys\r\n   */\r\n  getAvailableConfigKeys(stepperModal: any): string[] {\r\n    return this.configFactory.getAllAvailableConfigKeys(stepperModal);\r\n  }\r\n\r\n  /**\r\n   * Get configuration type based on key\r\n   */\r\n  getConfigurationType(key: string): 'sell' | 'purchase' | 'rental' | 'rentals_outside_compound' | 'sell_outside_compound' | 'purchase_outside_compound' | 'unknown' {\r\n    return this.configFactory.getConfigurationType(key);\r\n  }\r\n}\r\n"], "mappings": ";;AAOA,OAAM,MAAOA,yBAAyB;EAEhBC,aAAA;EAApBC,YAAoBD,aAAmC;IAAnC,KAAAA,aAAa,GAAbA,aAAa;EAA0B;EAE3D;;;EAGAE,eAAeA,CAACC,YAAiB;IAC/B,OAAO,IAAI,CAACH,aAAa,CAACE,eAAe,CAACC,YAAY,CAAC;EACzD;EAEA;;;EAGAC,eAAeA,CAACC,GAAW,EAAEC,IAAY,EAAEH,YAAiB;IAC1D,OAAO,IAAI,CAACH,aAAa,CAACI,eAAe,CAACC,GAAG,EAAEC,IAAI,EAAEH,YAAY,CAAC;EACpE;EAEA;;;EAGAI,gBAAgBA,CAACF,GAAW,EAAEF,YAAiB;IAC7C,OAAO,IAAI,CAACH,aAAa,CAACO,gBAAgB,CAACF,GAAG,EAAEF,YAAY,CAAC;EAC/D;EAEA;;;EAGAK,sBAAsBA,CAACL,YAAiB;IACtC,OAAO,IAAI,CAACH,aAAa,CAACS,yBAAyB,CAACN,YAAY,CAAC;EACnE;EAEA;;;EAGAO,oBAAoBA,CAACL,GAAW;IAC9B,OAAO,IAAI,CAACL,aAAa,CAACU,oBAAoB,CAACL,GAAG,CAAC;EACrD;;qCArCWN,yBAAyB,EAAAY,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,oBAAA;EAAA;;WAAzBf,yBAAyB;IAAAgB,OAAA,EAAzBhB,yBAAyB,CAAAiB,IAAA;IAAAC,UAAA,EAFxB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}