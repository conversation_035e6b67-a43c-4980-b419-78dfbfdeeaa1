{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { HomeComponent } from './home.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class HomeModule {\n  static ɵfac = function HomeModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HomeModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: HomeModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild([{\n      path: '',\n      component: HomeComponent\n    }])]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HomeModule, {\n    declarations: [HomeComponent],\n    imports: [CommonModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "HomeComponent", "HomeModule", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\nimport { HomeComponent } from './home.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    HomeComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule.forChild([\r\n      {\r\n        path: '',\r\n        component: HomeComponent\r\n      }\r\n    ])\r\n  ]\r\n})\r\nexport class HomeModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,kBAAkB;;;AAgBhD,OAAM,MAAOC,UAAU;;qCAAVA,UAAU;EAAA;;UAAVA;EAAU;;cATnBH,YAAY,EACZC,YAAY,CAACG,QAAQ,CAAC,CACpB;MACEC,IAAI,EAAE,EAAE;MACRC,SAAS,EAAEJ;KACZ,CACF,CAAC;EAAA;;;2EAGOC,UAAU;IAAAI,YAAA,GAZnBL,aAAa;IAAAM,OAAA,GAGbR,YAAY,EAAAS,EAAA,CAAAR,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}