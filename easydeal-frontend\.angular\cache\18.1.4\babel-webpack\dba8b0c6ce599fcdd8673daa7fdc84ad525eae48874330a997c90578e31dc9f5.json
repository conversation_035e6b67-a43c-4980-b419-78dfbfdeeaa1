{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { BaseConfigService } from '../base-config.service';\nimport { FLOOR_TYPES_OPTIONS, UNIT_FACING_TYPES_OPTIONS, UNIT_VIEW_TYPES_OPTIONS, FINISHING_STATUS_TYPES_OPTIONS, DELIVERY_STATUS_TYPES_OPTIONS, LEGAL_STATUS_TYPES_OPTIONS, OTHER_ACCESSORIES_OPTIONS, PAYMENT_METHOD_OPTIONS, UNIT_LAYOUT_STATUS_TYPES_OPTIONS, BUILDING_LAYOUT_STATUS_TYPES_OPTIONS, UNIT_DESIGN_TYPES_OPTIONS, FINANCIAL_STATUS_TYPES_OPTIONS, FIT_OUT_CONDITION_TYPES_OPTIONS, ACTIVITY_TYPES_OPTIONS, BUILDING_DEADLINE_TYPES_OPTIONS, BUILDING_LICENSE_TYPES_OPTIONS, UNIT_DESCRIPTION_TYPES_OPTIONS } from '../../stepper-modal.constants';\nimport * as i0 from \"@angular/core\";\nexport class SellOutsideCompoundConfigService extends BaseConfigService {\n  // ============================================================================\n  // SELL CONFIGURATIONS (Property owner looking to sell)\n  // ============================================================================\n  /**\n   * Create sell-specific location inputs for sell outside compound scenarios\n   */\n  createSellOutsideCompoundLocationInputs(stepperModal) {\n    return [{\n      step: 2,\n      name: 'cityId',\n      type: 'select',\n      label: 'City',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'areaId',\n      type: 'select',\n      label: 'Area',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'subAreaId',\n      type: 'select',\n      label: 'Sub Area',\n      options: [],\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'detailedAddress',\n      type: 'text',\n      label: 'Detailed Address',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'addressLink',\n      type: 'url',\n      label: 'Address Link',\n      validators: [Validators.pattern(/^https?:\\/\\/.+/)],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell-specific location inputs for mall-based units (administrative_units, medical_clinics)\n   */\n  createSellOutsideCompoundMallLocationInputs(stepperModal) {\n    return [{\n      step: 2,\n      name: 'cityId',\n      type: 'select',\n      label: 'City',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'areaId',\n      type: 'select',\n      label: 'Area',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'subAreaId',\n      type: 'select',\n      label: 'Sub Area',\n      options: [],\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'mallName',\n      type: 'text',\n      label: 'Mall Name',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'detailedAddress',\n      type: 'text',\n      label: 'Detailed Address',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'addressLink',\n      type: 'url',\n      label: 'Address Link',\n      validators: [Validators.pattern(/^https?:\\/\\/.+/)],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for apartments\n   */\n  createSellOutsideCompoundApartmentsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitFacing',\n      type: 'select',\n      label: 'Unit Facing',\n      options: UNIT_FACING_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for duplexes\n   */\n  createSellOutsideCompoundDuplexesUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'gardenArea',\n      type: 'number',\n      label: 'Garden Area (m²)',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitFacing',\n      type: 'select',\n      label: 'Unit Facing',\n      options: UNIT_FACING_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'legalStatus',\n      type: 'select',\n      label: 'Legal Status',\n      options: LEGAL_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for studios\n   */\n  createSellOutsideCompoundStudiosUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitFacing',\n      type: 'select',\n      label: 'Unit Facing',\n      options: UNIT_FACING_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for penthouses\n   */\n  createSellOutsideCompoundPenthousesUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'terraceArea',\n      type: 'number',\n      label: 'Terrace Area (m²)',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitFacing',\n      type: 'select',\n      label: 'Unit Facing',\n      options: UNIT_FACING_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'legalStatus',\n      type: 'select',\n      label: 'Legal Status',\n      options: LEGAL_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for basements\n   */\n  createSellOutsideCompoundBasementsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitLayoutStatus',\n      type: 'select',\n      label: 'Unit Layout Status',\n      options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'legalStatus',\n      type: 'select',\n      label: 'Legal Status',\n      options: LEGAL_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for roofs\n   */\n  createSellOutsideCompoundRoofsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitLayoutStatus',\n      type: 'select',\n      label: 'Unit Layout Status',\n      options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingLayoutStatus',\n      type: 'select',\n      label: 'Building Layout Status',\n      options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'legalStatus',\n      type: 'select',\n      label: 'Legal Status',\n      options: LEGAL_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for standalone villas\n   */\n  createSellOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitDesign',\n      type: 'select',\n      label: 'Unit Design',\n      options: UNIT_DESIGN_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingLayoutStatus',\n      type: 'select',\n      label: 'Building Layout Status',\n      options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for administrative units\n   */\n  createSellOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for medical clinics\n   */\n  createSellOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for pharmacies\n   */\n  createSellOutsideCompoundPharmaciesUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Fit Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for commercial stores\n   */\n  createSellOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for factory lands\n   */\n  createSellOutsideCompoundFactoryLandsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingLayoutStatus',\n      type: 'select',\n      label: 'Building Layout Status',\n      options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Fit Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for warehouses\n   */\n  createSellOutsideCompoundWarehousesUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingLayoutStatus',\n      type: 'select',\n      label: 'Building Layout Status',\n      options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Fit Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for commercial administrative buildings\n   */\n  createSellOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingLicense',\n      type: 'select',\n      label: 'Building License',\n      options: BUILDING_LICENSE_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingDeadline',\n      type: 'select',\n      label: 'Building Deadline',\n      options: BUILDING_DEADLINE_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound unit information inputs for residential buildings\n   */\n  createSellOutsideCompoundResidentialBuildingsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingLicense',\n      type: 'select',\n      label: 'Building License',\n      options: BUILDING_LICENSE_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingLayoutStatus',\n      type: 'select',\n      label: 'Building Layout Status',\n      options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingDeadline',\n      type: 'select',\n      label: 'Building Deadline',\n      options: BUILDING_DEADLINE_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitDescription',\n      type: 'select',\n      label: 'Unit Description',\n      options: UNIT_DESCRIPTION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create media inputs for sell outside compound\n   */\n  createSellOutsideCompoundMediaInputs() {\n    return [{\n      step: 4,\n      name: 'mainImage',\n      type: 'file',\n      label: 'Main Image',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 4,\n      name: 'unitInMasterPlanImage',\n      type: 'file',\n      label: 'Unit in Master Plan Image',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 4,\n      name: 'galleryImages',\n      type: 'file',\n      label: 'Gallery Images',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 4,\n      name: 'video',\n      type: 'file',\n      label: 'Video',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound financial inputs\n   */\n  createSellOutsideCompoundFinancialInputs(stepperModal) {\n    return [{\n      step: 5,\n      name: 'paymentMethod',\n      type: 'select',\n      label: 'Payment Method',\n      options: PAYMENT_METHOD_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'unitPriceSuggestions',\n      type: 'checkbox',\n      label: 'Unit Price Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'unitPrice',\n      type: 'number',\n      label: 'Unit Price',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound financial inputs with financial status (for mall units)\n   */\n  createSellOutsideCompoundMallFinancialInputs(stepperModal) {\n    return [{\n      step: 5,\n      name: 'paymentMethod',\n      type: 'select',\n      label: 'Payment Method',\n      options: PAYMENT_METHOD_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'unitPriceSuggestions',\n      type: 'checkbox',\n      label: 'Unit Price Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'unitPrice',\n      type: 'number',\n      label: 'Unit Price',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'financialStatus',\n      type: 'select',\n      label: 'Financial Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell outside compound financial inputs with financial status (for industrial properties)\n   */\n  createSellOutsideCompoundIndustrialFinancialInputs(stepperModal) {\n    return [{\n      step: 5,\n      name: 'paymentMethod',\n      type: 'select',\n      label: 'Payment Method',\n      options: PAYMENT_METHOD_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'unitPriceSuggestions',\n      type: 'checkbox',\n      label: 'Unit Price Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'unitPrice',\n      type: 'number',\n      label: 'Unit Price',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'financialStatus',\n      type: 'select',\n      label: 'Financial Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Configuration for sell outside compound apartments\n   */\n  createSellOutsideCompoundApartmentsConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundLocationInputs(stepperModal), ...this.createSellOutsideCompoundApartmentsUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for sell outside compound duplexes\n   */\n  createSellOutsideCompoundDuplexesConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundLocationInputs(stepperModal), ...this.createSellOutsideCompoundDuplexesUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for sell outside compound studios\n   */\n  createSellOutsideCompoundStudiosConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundLocationInputs(stepperModal), ...this.createSellOutsideCompoundStudiosUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for sell outside compound penthouses\n   */\n  createSellOutsideCompoundPenthousesConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundLocationInputs(stepperModal), ...this.createSellOutsideCompoundPenthousesUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for sell outside compound basements\n   */\n  createSellOutsideCompoundBasementsConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundLocationInputs(stepperModal), ...this.createSellOutsideCompoundBasementsUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for sell outside compound roofs\n   */\n  createSellOutsideCompoundRoofsConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundLocationInputs(stepperModal), ...this.createSellOutsideCompoundRoofsUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for sell outside compound standalone villas\n   */\n  createSellOutsideCompoundStandaloneVillasConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundLocationInputs(stepperModal), ...this.createSellOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for sell outside compound administrative units\n   */\n  createSellOutsideCompoundAdministrativeUnitsConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundMallLocationInputs(stepperModal), ...this.createSellOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundMallFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for sell outside compound medical clinics\n   */\n  createSellOutsideCompoundMedicalClinicsConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundMallLocationInputs(stepperModal), ...this.createSellOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundMallFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for sell outside compound pharmacies\n   */\n  createSellOutsideCompoundPharmaciesConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundMallLocationInputs(stepperModal), ...this.createSellOutsideCompoundPharmaciesUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundMallFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for sell outside compound commercial stores\n   */\n  createSellOutsideCompoundCommercialStoresConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundMallLocationInputs(stepperModal), ...this.createSellOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundMallFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for sell outside compound factory lands\n   */\n  createSellOutsideCompoundFactoryLandsConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundLocationInputs(stepperModal), ...this.createSellOutsideCompoundFactoryLandsUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundIndustrialFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for sell outside compound warehouses\n   */\n  createSellOutsideCompoundWarehousesConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundLocationInputs(stepperModal), ...this.createSellOutsideCompoundWarehousesUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundIndustrialFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for sell outside compound commercial administrative buildings\n   */\n  createSellOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundLocationInputs(stepperModal), ...this.createSellOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundIndustrialFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for sell outside compound residential buildings\n   */\n  createSellOutsideCompoundResidentialBuildingsConfig(stepperModal) {\n    const config = [...this.createSellOutsideCompoundLocationInputs(stepperModal), ...this.createSellOutsideCompoundResidentialBuildingsUnitInformationInputs(stepperModal), ...this.createSellOutsideCompoundMediaInputs(), ...this.createSellOutsideCompoundIndustrialFinancialInputs(stepperModal)];\n    return config;\n  }\n  // ============================================================================\n  // PUBLIC API\n  // ============================================================================\n  /**\n   * Get input configurations for sell outside compound cases\n   */\n  getInputConfigs(stepperModal) {\n    return [\n    // SELL OUTSIDE COMPOUND CONFIGURATIONS\n    {\n      key: 'purchase_sell_outside_compound_sell_apartments',\n      value: this.createSellOutsideCompoundApartmentsConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_sell_duplexes',\n      value: this.createSellOutsideCompoundDuplexesConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_sell_studios',\n      value: this.createSellOutsideCompoundStudiosConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_sell_penthouses',\n      value: this.createSellOutsideCompoundPenthousesConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_sell_basements',\n      value: this.createSellOutsideCompoundBasementsConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_sell_roofs',\n      value: this.createSellOutsideCompoundRoofsConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_sell_standalone_villas',\n      value: this.createSellOutsideCompoundStandaloneVillasConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_sell_administrative_units',\n      value: this.createSellOutsideCompoundAdministrativeUnitsConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_sell_medical_clinics',\n      value: this.createSellOutsideCompoundMedicalClinicsConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_sell_pharmacies',\n      value: this.createSellOutsideCompoundPharmaciesConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_sell_commercial_stores',\n      value: this.createSellOutsideCompoundCommercialStoresConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_sell_factory_lands',\n      value: this.createSellOutsideCompoundFactoryLandsConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_sell_warehouses',\n      value: this.createSellOutsideCompoundWarehousesConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_sell_commercial_administrative_buildings',\n      value: this.createSellOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_sell_residential_buildings',\n      value: this.createSellOutsideCompoundResidentialBuildingsConfig(stepperModal)\n    }];\n  }\n  /**\n   * Get all available sell outside compound configuration keys\n   */\n  getSellOutsideCompoundConfigurationKeys() {\n    return ['purchase_sell_outside_compound_sell_apartments', 'purchase_sell_outside_compound_sell_duplexes', 'purchase_sell_outside_compound_sell_studios', 'purchase_sell_outside_compound_sell_penthouses', 'purchase_sell_outside_compound_sell_basements', 'purchase_sell_outside_compound_sell_roofs', 'purchase_sell_outside_compound_sell_standalone_villas', 'purchase_sell_outside_compound_sell_administrative_units', 'purchase_sell_outside_compound_sell_medical_clinics', 'purchase_sell_outside_compound_sell_pharmacies', 'purchase_sell_outside_compound_sell_commercial_stores', 'purchase_sell_outside_compound_sell_factory_lands', 'purchase_sell_outside_compound_sell_warehouses'];\n  }\n  /**\n   * Check if a key is sell outside compound configuration\n   */\n  isSellOutsideCompoundConfiguration(key) {\n    return key.includes('purchase_sell_outside_compound_sell_');\n  }\n  static ɵfac = /*@__PURE__*/(() => {\n    let ɵSellOutsideCompoundConfigService_BaseFactory;\n    return function SellOutsideCompoundConfigService_Factory(__ngFactoryType__) {\n      return (ɵSellOutsideCompoundConfigService_BaseFactory || (ɵSellOutsideCompoundConfigService_BaseFactory = i0.ɵɵgetInheritedFactory(SellOutsideCompoundConfigService)))(__ngFactoryType__ || SellOutsideCompoundConfigService);\n    };\n  })();\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: SellOutsideCompoundConfigService,\n    factory: SellOutsideCompoundConfigService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["Validators", "BaseConfigService", "FLOOR_TYPES_OPTIONS", "UNIT_FACING_TYPES_OPTIONS", "UNIT_VIEW_TYPES_OPTIONS", "FINISHING_STATUS_TYPES_OPTIONS", "DELIVERY_STATUS_TYPES_OPTIONS", "LEGAL_STATUS_TYPES_OPTIONS", "OTHER_ACCESSORIES_OPTIONS", "PAYMENT_METHOD_OPTIONS", "UNIT_LAYOUT_STATUS_TYPES_OPTIONS", "BUILDING_LAYOUT_STATUS_TYPES_OPTIONS", "UNIT_DESIGN_TYPES_OPTIONS", "FINANCIAL_STATUS_TYPES_OPTIONS", "FIT_OUT_CONDITION_TYPES_OPTIONS", "ACTIVITY_TYPES_OPTIONS", "BUILDING_DEADLINE_TYPES_OPTIONS", "BUILDING_LICENSE_TYPES_OPTIONS", "UNIT_DESCRIPTION_TYPES_OPTIONS", "SellOutsideCompoundConfigService", "createSellOutsideCompoundLocationInputs", "stepperModal", "step", "name", "type", "label", "options", "validators", "required", "visibility", "pattern", "createSellOutsideCompoundMallLocationInputs", "createSellOutsideCompoundApartmentsUnitInformationInputs", "min", "createSellOutsideCompoundDuplexesUnitInformationInputs", "createSellOutsideCompoundStudiosUnitInformationInputs", "createSellOutsideCompoundPenthousesUnitInformationInputs", "createSellOutsideCompoundBasementsUnitInformationInputs", "createSellOutsideCompoundRoofsUnitInformationInputs", "createSellOutsideCompoundStandaloneVillasUnitInformationInputs", "createSellOutsideCompoundAdministrativeUnitsUnitInformationInputs", "createSellOutsideCompoundMedicalClinicsUnitInformationInputs", "createSellOutsideCompoundPharmaciesUnitInformationInputs", "createSellOutsideCompoundCommercialStoresUnitInformationInputs", "createSellOutsideCompoundFactoryLandsUnitInformationInputs", "createSellOutsideCompoundWarehousesUnitInformationInputs", "createSellOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs", "createSellOutsideCompoundResidentialBuildingsUnitInformationInputs", "createSellOutsideCompoundMediaInputs", "createSellOutsideCompoundFinancialInputs", "createSellOutsideCompoundMallFinancialInputs", "createSellOutsideCompoundIndustrialFinancialInputs", "createSellOutsideCompoundApartmentsConfig", "config", "createSellOutsideCompoundDuplexesConfig", "createSellOutsideCompoundStudiosConfig", "createSellOutsideCompoundPenthousesConfig", "createSellOutsideCompoundBasementsConfig", "createSellOutsideCompoundRoofsConfig", "createSellOutsideCompoundStandaloneVillasConfig", "createSellOutsideCompoundAdministrativeUnitsConfig", "createSellOutsideCompoundMedicalClinicsConfig", "createSellOutsideCompoundPharmaciesConfig", "createSellOutsideCompoundCommercialStoresConfig", "createSellOutsideCompoundFactoryLandsConfig", "createSellOutsideCompoundWarehousesConfig", "createSellOutsideCompoundCommercialAdministrativeBuildingsConfig", "createSellOutsideCompoundResidentialBuildingsConfig", "getInputConfigs", "key", "value", "getSellOutsideCompoundConfigurationKeys", "isSellOutsideCompoundConfiguration", "includes", "__ngFactoryType__", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\shared\\stepper-modal\\services\\outside-compound\\sell-outside-compound-config.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Validators } from '@angular/forms';\r\nimport { BaseConfigService, InputConfig, StepperConfiguration } from '../base-config.service';\r\nimport {\r\n  FLOOR_TYPES_OPTIONS,\r\n  UNIT_FACING_TYPES_OPTIONS,\r\n  UNIT_VIEW_TYPES_OPTIONS,\r\n  FINISHING_STATUS_TYPES_OPTIONS,\r\n  DELIVERY_STATUS_TYPES_OPTIONS,\r\n  LEGAL_STATUS_TYPES_OPTIONS,\r\n  OTHER_ACCESSORIES_OPTIONS,\r\n  PAYMENT_METHOD_OPTIONS,\r\n  UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n  BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n  UNIT_DESIGN_TYPES_OPTIONS,\r\n  FINANCIAL_STATUS_TYPES_OPTIONS,\r\n  FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n  ACTIVITY_TYPES_OPTIONS,\r\n  BUILDING_DEADLINE_TYPES_OPTIONS,\r\n  BUILDING_LICENSE_TYPES_OPTIONS,\r\n  UNIT_DESCRIPTION_TYPES_OPTIONS,\r\n} from '../../stepper-modal.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class SellOutsideCompoundConfigService extends BaseConfigService {\r\n\r\n  // ============================================================================\r\n  // SELL CONFIGURATIONS (Property owner looking to sell)\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Create sell-specific location inputs for sell outside compound scenarios\r\n   */\r\n  private createSellOutsideCompoundLocationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 2,\r\n        name: 'cityId',\r\n        type: 'select',\r\n        label: 'City',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'areaId',\r\n        type: 'select',\r\n        label: 'Area',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'subAreaId',\r\n        type: 'select',\r\n        label: 'Sub Area',\r\n        options: [],\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'detailedAddress',\r\n        type: 'text',\r\n        label: 'Detailed Address',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'addressLink',\r\n        type: 'url',\r\n        label: 'Address Link',\r\n        validators: [Validators.pattern(/^https?:\\/\\/.+/)],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell-specific location inputs for mall-based units (administrative_units, medical_clinics)\r\n   */\r\n  private createSellOutsideCompoundMallLocationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 2,\r\n        name: 'cityId',\r\n        type: 'select',\r\n        label: 'City',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'areaId',\r\n        type: 'select',\r\n        label: 'Area',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'subAreaId',\r\n        type: 'select',\r\n        label: 'Sub Area',\r\n        options: [],\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'mallName',\r\n        type: 'text',\r\n        label: 'Mall Name',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'detailedAddress',\r\n        type: 'text',\r\n        label: 'Detailed Address',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'addressLink',\r\n        type: 'url',\r\n        label: 'Address Link',\r\n        validators: [Validators.pattern(/^https?:\\/\\/.+/)],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for apartments\r\n   */\r\n  private createSellOutsideCompoundApartmentsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitFacing',\r\n        type: 'select',\r\n        label: 'Unit Facing',\r\n        options: UNIT_FACING_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for duplexes\r\n   */\r\n  private createSellOutsideCompoundDuplexesUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'gardenArea',\r\n        type: 'number',\r\n        label: 'Garden Area (m²)',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitFacing',\r\n        type: 'select',\r\n        label: 'Unit Facing',\r\n        options: UNIT_FACING_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'legalStatus',\r\n        type: 'select',\r\n        label: 'Legal Status',\r\n        options: LEGAL_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for studios\r\n   */\r\n  private createSellOutsideCompoundStudiosUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitFacing',\r\n        type: 'select',\r\n        label: 'Unit Facing',\r\n        options: UNIT_FACING_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for penthouses\r\n   */\r\n  private createSellOutsideCompoundPenthousesUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'terraceArea',\r\n        type: 'number',\r\n        label: 'Terrace Area (m²)',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitFacing',\r\n        type: 'select',\r\n        label: 'Unit Facing',\r\n        options: UNIT_FACING_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'legalStatus',\r\n        type: 'select',\r\n        label: 'Legal Status',\r\n        options: LEGAL_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for basements\r\n   */\r\n  private createSellOutsideCompoundBasementsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitLayoutStatus',\r\n        type: 'select',\r\n        label: 'Unit Layout Status',\r\n        options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'legalStatus',\r\n        type: 'select',\r\n        label: 'Legal Status',\r\n        options: LEGAL_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for roofs\r\n   */\r\n  private createSellOutsideCompoundRoofsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitLayoutStatus',\r\n        type: 'select',\r\n        label: 'Unit Layout Status',\r\n        options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingLayoutStatus',\r\n        type: 'select',\r\n        label: 'Building Layout Status',\r\n        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'legalStatus',\r\n        type: 'select',\r\n        label: 'Legal Status',\r\n        options: LEGAL_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for standalone villas\r\n   */\r\n  private createSellOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitDesign',\r\n        type: 'select',\r\n        label: 'Unit Design',\r\n        options: UNIT_DESIGN_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingLayoutStatus',\r\n        type: 'select',\r\n        label: 'Building Layout Status',\r\n        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for administrative units\r\n   */\r\n  private createSellOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for medical clinics\r\n   */\r\n  private createSellOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for pharmacies\r\n   */\r\n  private createSellOutsideCompoundPharmaciesUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fit Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for commercial stores\r\n   */\r\n  private createSellOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for factory lands\r\n   */\r\n  private createSellOutsideCompoundFactoryLandsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingLayoutStatus',\r\n        type: 'select',\r\n        label: 'Building Layout Status',\r\n        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fit Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for warehouses\r\n   */\r\n  private createSellOutsideCompoundWarehousesUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingLayoutStatus',\r\n        type: 'select',\r\n        label: 'Building Layout Status',\r\n        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fit Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for commercial administrative buildings\r\n   */\r\n  private createSellOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingLicense',\r\n        type: 'select',\r\n        label: 'Building License',\r\n        options: BUILDING_LICENSE_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingDeadline',\r\n        type: 'select',\r\n        label: 'Building Deadline',\r\n        options: BUILDING_DEADLINE_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound unit information inputs for residential buildings\r\n   */\r\n  private createSellOutsideCompoundResidentialBuildingsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingLicense',\r\n        type: 'select',\r\n        label: 'Building License',\r\n        options: BUILDING_LICENSE_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingLayoutStatus',\r\n        type: 'select',\r\n        label: 'Building Layout Status',\r\n        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingDeadline',\r\n        type: 'select',\r\n        label: 'Building Deadline',\r\n        options: BUILDING_DEADLINE_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitDescription',\r\n        type: 'select',\r\n        label: 'Unit Description',\r\n        options: UNIT_DESCRIPTION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create media inputs for sell outside compound\r\n   */\r\n  private createSellOutsideCompoundMediaInputs(): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 4,\r\n        name: 'mainImage',\r\n        type: 'file',\r\n        label: 'Main Image',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 4,\r\n        name: 'unitInMasterPlanImage',\r\n        type: 'file',\r\n        label: 'Unit in Master Plan Image',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 4,\r\n        name: 'galleryImages',\r\n        type: 'file',\r\n        label: 'Gallery Images',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 4,\r\n        name: 'video',\r\n        type: 'file',\r\n        label: 'Video',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound financial inputs\r\n   */\r\n  private createSellOutsideCompoundFinancialInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 5,\r\n        name: 'paymentMethod',\r\n        type: 'select',\r\n        label: 'Payment Method',\r\n        options: PAYMENT_METHOD_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'unitPriceSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Unit Price Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'unitPrice',\r\n        type: 'number',\r\n        label: 'Unit Price',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound financial inputs with financial status (for mall units)\r\n   */\r\n  private createSellOutsideCompoundMallFinancialInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 5,\r\n        name: 'paymentMethod',\r\n        type: 'select',\r\n        label: 'Payment Method',\r\n        options: PAYMENT_METHOD_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'unitPriceSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Unit Price Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'unitPrice',\r\n        type: 'number',\r\n        label: 'Unit Price',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'financialStatus',\r\n        type: 'select',\r\n        label: 'Financial Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell outside compound financial inputs with financial status (for industrial properties)\r\n   */\r\n  private createSellOutsideCompoundIndustrialFinancialInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 5,\r\n        name: 'paymentMethod',\r\n        type: 'select',\r\n        label: 'Payment Method',\r\n        options: PAYMENT_METHOD_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'unitPriceSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Unit Price Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'unitPrice',\r\n        type: 'number',\r\n        label: 'Unit Price',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'financialStatus',\r\n        type: 'select',\r\n        label: 'Financial Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound apartments\r\n   */\r\n  private createSellOutsideCompoundApartmentsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundApartmentsUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound duplexes\r\n   */\r\n  private createSellOutsideCompoundDuplexesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundDuplexesUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound studios\r\n   */\r\n  private createSellOutsideCompoundStudiosConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundStudiosUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound penthouses\r\n   */\r\n  private createSellOutsideCompoundPenthousesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundPenthousesUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound basements\r\n   */\r\n  private createSellOutsideCompoundBasementsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundBasementsUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound roofs\r\n   */\r\n  private createSellOutsideCompoundRoofsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundRoofsUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound standalone villas\r\n   */\r\n  private createSellOutsideCompoundStandaloneVillasConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound administrative units\r\n   */\r\n  private createSellOutsideCompoundAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundMallLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundMallFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound medical clinics\r\n   */\r\n  private createSellOutsideCompoundMedicalClinicsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundMallLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundMallFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound pharmacies\r\n   */\r\n  private createSellOutsideCompoundPharmaciesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundMallLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundPharmaciesUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundMallFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound commercial stores\r\n   */\r\n  private createSellOutsideCompoundCommercialStoresConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundMallLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundMallFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound factory lands\r\n   */\r\n  private createSellOutsideCompoundFactoryLandsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundFactoryLandsUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundIndustrialFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound warehouses\r\n   */\r\n  private createSellOutsideCompoundWarehousesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundWarehousesUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundIndustrialFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound commercial administrative buildings\r\n   */\r\n  private createSellOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundIndustrialFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for sell outside compound residential buildings\r\n   */\r\n  private createSellOutsideCompoundResidentialBuildingsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createSellOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundResidentialBuildingsUnitInformationInputs(stepperModal),\r\n      ...this.createSellOutsideCompoundMediaInputs(),\r\n      ...this.createSellOutsideCompoundIndustrialFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  // ============================================================================\r\n  // PUBLIC API\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Get input configurations for sell outside compound cases\r\n   */\r\n  getInputConfigs(stepperModal: any): StepperConfiguration[] {\r\n    return [\r\n      // SELL OUTSIDE COMPOUND CONFIGURATIONS\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_apartments',\r\n        value: this.createSellOutsideCompoundApartmentsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_duplexes',\r\n        value: this.createSellOutsideCompoundDuplexesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_studios',\r\n        value: this.createSellOutsideCompoundStudiosConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_penthouses',\r\n        value: this.createSellOutsideCompoundPenthousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_basements',\r\n        value: this.createSellOutsideCompoundBasementsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_roofs',\r\n        value: this.createSellOutsideCompoundRoofsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_standalone_villas',\r\n        value: this.createSellOutsideCompoundStandaloneVillasConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_administrative_units',\r\n        value: this.createSellOutsideCompoundAdministrativeUnitsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_medical_clinics',\r\n        value: this.createSellOutsideCompoundMedicalClinicsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_pharmacies',\r\n        value: this.createSellOutsideCompoundPharmaciesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_commercial_stores',\r\n        value: this.createSellOutsideCompoundCommercialStoresConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_factory_lands',\r\n        value: this.createSellOutsideCompoundFactoryLandsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_warehouses',\r\n        value: this.createSellOutsideCompoundWarehousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_commercial_administrative_buildings',\r\n        value: this.createSellOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_sell_residential_buildings',\r\n        value: this.createSellOutsideCompoundResidentialBuildingsConfig(stepperModal),\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get all available sell outside compound configuration keys\r\n   */\r\n  getSellOutsideCompoundConfigurationKeys(): string[] {\r\n    return [\r\n      'purchase_sell_outside_compound_sell_apartments',\r\n      'purchase_sell_outside_compound_sell_duplexes',\r\n      'purchase_sell_outside_compound_sell_studios',\r\n      'purchase_sell_outside_compound_sell_penthouses',\r\n      'purchase_sell_outside_compound_sell_basements',\r\n      'purchase_sell_outside_compound_sell_roofs',\r\n      'purchase_sell_outside_compound_sell_standalone_villas',\r\n      'purchase_sell_outside_compound_sell_administrative_units',\r\n      'purchase_sell_outside_compound_sell_medical_clinics',\r\n      'purchase_sell_outside_compound_sell_pharmacies',\r\n      'purchase_sell_outside_compound_sell_commercial_stores',\r\n      'purchase_sell_outside_compound_sell_factory_lands',\r\n      'purchase_sell_outside_compound_sell_warehouses',\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Check if a key is sell outside compound configuration\r\n   */\r\n  isSellOutsideCompoundConfiguration(key: string): boolean {\r\n    return key.includes('purchase_sell_outside_compound_sell_');\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,iBAAiB,QAA2C,wBAAwB;AAC7F,SACEC,mBAAmB,EACnBC,yBAAyB,EACzBC,uBAAuB,EACvBC,8BAA8B,EAC9BC,6BAA6B,EAC7BC,0BAA0B,EAC1BC,yBAAyB,EACzBC,sBAAsB,EACtBC,gCAAgC,EAChCC,oCAAoC,EACpCC,yBAAyB,EACzBC,8BAA8B,EAC9BC,+BAA+B,EAC/BC,sBAAsB,EACtBC,+BAA+B,EAC/BC,8BAA8B,EAC9BC,8BAA8B,QACzB,+BAA+B;;AAKtC,OAAM,MAAOC,gCAAiC,SAAQlB,iBAAiB;EAErE;EACA;EACA;EAEA;;;EAGQmB,uCAAuCA,CAACC,YAAiB;IAC/D,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,kBAAkB;MACzBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,cAAc;MACrBE,UAAU,EAAE,CAAC3B,UAAU,CAAC8B,OAAO,CAAC,gBAAgB,CAAC,CAAC;MAClDD,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQE,2CAA2CA,CAACV,YAAiB;IACnE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,WAAW;MAClBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,kBAAkB;MACzBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,cAAc;MACrBE,UAAU,EAAE,CAAC3B,UAAU,CAAC8B,OAAO,CAAC,gBAAgB,CAAC,CAAC;MAClDD,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQG,wDAAwDA,CAACX,YAAiB;IAChF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAExB,mBAAmB;MAC5ByB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAEvB,yBAAyB;MAClCwB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAEtB,uBAAuB;MAChCuB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAErB,8BAA8B;MACvCsB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAEpB,6BAA6B;MACtCqB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAElB,yBAAyB;MAClCmB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQK,sDAAsDA,CAACb,YAAiB;IAC9E,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAExB,mBAAmB;MAC5ByB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAEvB,yBAAyB;MAClCwB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAEtB,uBAAuB;MAChCuB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAErB,8BAA8B;MACvCsB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAEpB,6BAA6B;MACtCqB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,cAAc;MACrBC,OAAO,EAAEnB,0BAA0B;MACnCoB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAElB,yBAAyB;MAClCmB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQM,qDAAqDA,CAACd,YAAiB;IAC7E,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAExB,mBAAmB;MAC5ByB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAEvB,yBAAyB;MAClCwB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAEtB,uBAAuB;MAChCuB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAErB,8BAA8B;MACvCsB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAEpB,6BAA6B;MACtCqB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAElB,yBAAyB;MAClCmB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQO,wDAAwDA,CAACf,YAAiB;IAChF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAEvB,yBAAyB;MAClCwB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAEtB,uBAAuB;MAChCuB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAErB,8BAA8B;MACvCsB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAEpB,6BAA6B;MACtCqB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,cAAc;MACrBC,OAAO,EAAEnB,0BAA0B;MACnCoB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAElB,yBAAyB;MAClCmB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQQ,uDAAuDA,CAAChB,YAAiB;IAC/E,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,OAAO,EAAEhB,gCAAgC;MACzCiB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAErB,8BAA8B;MACvCsB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAEpB,6BAA6B;MACtCqB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,cAAc;MACrBC,OAAO,EAAEnB,0BAA0B;MACnCoB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAElB,yBAAyB;MAClCmB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQS,mDAAmDA,CAACjB,YAAiB;IAC3E,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,OAAO,EAAEhB,gCAAgC;MACzCiB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,OAAO,EAAEf,oCAAoC;MAC7CgB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAErB,8BAA8B;MACvCsB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAEpB,6BAA6B;MACtCqB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,cAAc;MACrBC,OAAO,EAAEnB,0BAA0B;MACnCoB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAElB,yBAAyB;MAClCmB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQU,8DAA8DA,CAAClB,YAAiB;IACtF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAEd,yBAAyB;MAClCe,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,OAAO,EAAEf,oCAAoC;MAC7CgB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAEtB,uBAAuB;MAChCuB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAErB,8BAA8B;MACvCsB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAElB,yBAAyB;MAClCmB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQW,iEAAiEA,CAACnB,YAAiB;IACzF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAExB,mBAAmB;MAC5ByB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAEtB,uBAAuB;MAChCuB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAErB,8BAA8B;MACvCsB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAEpB,6BAA6B;MACtCqB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAElB,yBAAyB;MAClCmB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQY,4DAA4DA,CAACpB,YAAiB;IACpF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAExB,mBAAmB;MAC5ByB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAEtB,uBAAuB;MAChCuB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAErB,8BAA8B;MACvCsB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAEpB,6BAA6B;MACtCqB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAElB,yBAAyB;MAClCmB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQa,wDAAwDA,CAACrB,YAAiB;IAChF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAExB,mBAAmB;MAC5ByB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAEtB,uBAAuB;MAChCuB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAErB,8BAA8B;MACvCsB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAEpB,6BAA6B;MACtCqB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAEZ,+BAA+B;MACxCa,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAElB,yBAAyB;MAClCmB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQc,8DAA8DA,CAACtB,YAAiB;IACtF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAExB,mBAAmB;MAC5ByB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAEtB,uBAAuB;MAChCuB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAErB,8BAA8B;MACvCsB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAEpB,6BAA6B;MACtCqB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBC,OAAO,EAAEX,sBAAsB;MAC/BY,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAElB,yBAAyB;MAClCmB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQe,0DAA0DA,CAACvB,YAAiB;IAClF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,OAAO,EAAEf,oCAAoC;MAC7CgB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBC,OAAO,EAAEX,sBAAsB;MAC/BY,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAEZ,+BAA+B;MACxCa,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQgB,wDAAwDA,CAACxB,YAAiB;IAChF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,OAAO,EAAEf,oCAAoC;MAC7CgB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBC,OAAO,EAAEX,sBAAsB;MAC/BY,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAEZ,+BAA+B;MACxCa,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQiB,+EAA+EA,CAACzB,YAAiB;IACvG,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAET,8BAA8B;MACvCU,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAEV,+BAA+B;MACxCW,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBC,OAAO,EAAEX,sBAAsB;MAC/BY,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAEtB,uBAAuB;MAChCuB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQkB,kEAAkEA,CAAC1B,YAAiB;IAC1F,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAET,8BAA8B;MACvCU,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,OAAO,EAAEf,oCAAoC;MAC7CgB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAEV,+BAA+B;MACxCW,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAEtB,uBAAuB;MAChCuB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAER,8BAA8B;MACvCS,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAEpB,6BAA6B;MACtCqB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQmB,oCAAoCA,CAAA;IAC1C,OAAO,CACL;MACE1B,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,YAAY;MACnBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,uBAAuB;MAC7BC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,2BAA2B;MAClCE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,gBAAgB;MACvBE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,OAAO;MACdE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQoB,wCAAwCA,CAAC5B,YAAiB;IAChE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,OAAO,EAAEjB,sBAAsB;MAC/BkB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,wBAAwB;MAC/BE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,YAAY;MACnBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQqB,4CAA4CA,CAAC7B,YAAiB;IACpE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,OAAO,EAAEjB,sBAAsB;MAC/BkB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,wBAAwB;MAC/BE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,YAAY;MACnBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAEb,8BAA8B;MACvCc,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQsB,kDAAkDA,CAAC9B,YAAiB;IAC1E,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,OAAO,EAAEjB,sBAAsB;MAC/BkB,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,wBAAwB;MAC/BE,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,YAAY;MACnBE,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,EAAE5B,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAEb,8BAA8B;MACvCc,UAAU,EAAE,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQuB,yCAAyCA,CAAC/B,YAAiB;IACjE,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjC,uCAAuC,CAACC,YAAY,CAAC,EAC7D,GAAG,IAAI,CAACW,wDAAwD,CAACX,YAAY,CAAC,EAC9E,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACC,wCAAwC,CAAC5B,YAAY,CAAC,CAC/D;IAED,OAAOgC,MAAM;EACf;EAEA;;;EAGQC,uCAAuCA,CAACjC,YAAiB;IAC/D,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjC,uCAAuC,CAACC,YAAY,CAAC,EAC7D,GAAG,IAAI,CAACa,sDAAsD,CAACb,YAAY,CAAC,EAC5E,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACC,wCAAwC,CAAC5B,YAAY,CAAC,CAC/D;IAED,OAAOgC,MAAM;EACf;EAEA;;;EAGQE,sCAAsCA,CAAClC,YAAiB;IAC9D,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjC,uCAAuC,CAACC,YAAY,CAAC,EAC7D,GAAG,IAAI,CAACc,qDAAqD,CAACd,YAAY,CAAC,EAC3E,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACC,wCAAwC,CAAC5B,YAAY,CAAC,CAC/D;IAED,OAAOgC,MAAM;EACf;EAEA;;;EAGQG,yCAAyCA,CAACnC,YAAiB;IACjE,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjC,uCAAuC,CAACC,YAAY,CAAC,EAC7D,GAAG,IAAI,CAACe,wDAAwD,CAACf,YAAY,CAAC,EAC9E,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACC,wCAAwC,CAAC5B,YAAY,CAAC,CAC/D;IAED,OAAOgC,MAAM;EACf;EAEA;;;EAGQI,wCAAwCA,CAACpC,YAAiB;IAChE,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjC,uCAAuC,CAACC,YAAY,CAAC,EAC7D,GAAG,IAAI,CAACgB,uDAAuD,CAAChB,YAAY,CAAC,EAC7E,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACC,wCAAwC,CAAC5B,YAAY,CAAC,CAC/D;IAED,OAAOgC,MAAM;EACf;EAEA;;;EAGQK,oCAAoCA,CAACrC,YAAiB;IAC5D,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjC,uCAAuC,CAACC,YAAY,CAAC,EAC7D,GAAG,IAAI,CAACiB,mDAAmD,CAACjB,YAAY,CAAC,EACzE,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACC,wCAAwC,CAAC5B,YAAY,CAAC,CAC/D;IAED,OAAOgC,MAAM;EACf;EAEA;;;EAGQM,+CAA+CA,CAACtC,YAAiB;IACvE,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjC,uCAAuC,CAACC,YAAY,CAAC,EAC7D,GAAG,IAAI,CAACkB,8DAA8D,CAAClB,YAAY,CAAC,EACpF,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACC,wCAAwC,CAAC5B,YAAY,CAAC,CAC/D;IAED,OAAOgC,MAAM;EACf;EAEA;;;EAGQO,kDAAkDA,CAACvC,YAAiB;IAC1E,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACtB,2CAA2C,CAACV,YAAY,CAAC,EACjE,GAAG,IAAI,CAACmB,iEAAiE,CAACnB,YAAY,CAAC,EACvF,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACE,4CAA4C,CAAC7B,YAAY,CAAC,CACnE;IAED,OAAOgC,MAAM;EACf;EAEA;;;EAGQQ,6CAA6CA,CAACxC,YAAiB;IACrE,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACtB,2CAA2C,CAACV,YAAY,CAAC,EACjE,GAAG,IAAI,CAACoB,4DAA4D,CAACpB,YAAY,CAAC,EAClF,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACE,4CAA4C,CAAC7B,YAAY,CAAC,CACnE;IAED,OAAOgC,MAAM;EACf;EAEA;;;EAGQS,yCAAyCA,CAACzC,YAAiB;IACjE,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACtB,2CAA2C,CAACV,YAAY,CAAC,EACjE,GAAG,IAAI,CAACqB,wDAAwD,CAACrB,YAAY,CAAC,EAC9E,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACE,4CAA4C,CAAC7B,YAAY,CAAC,CACnE;IAED,OAAOgC,MAAM;EACf;EAEA;;;EAGQU,+CAA+CA,CAAC1C,YAAiB;IACvE,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACtB,2CAA2C,CAACV,YAAY,CAAC,EACjE,GAAG,IAAI,CAACsB,8DAA8D,CAACtB,YAAY,CAAC,EACpF,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACE,4CAA4C,CAAC7B,YAAY,CAAC,CACnE;IAED,OAAOgC,MAAM;EACf;EAEA;;;EAGQW,2CAA2CA,CAAC3C,YAAiB;IACnE,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjC,uCAAuC,CAACC,YAAY,CAAC,EAC7D,GAAG,IAAI,CAACuB,0DAA0D,CAACvB,YAAY,CAAC,EAChF,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACG,kDAAkD,CAAC9B,YAAY,CAAC,CACzE;IAED,OAAOgC,MAAM;EACf;EAEA;;;EAGQY,yCAAyCA,CAAC5C,YAAiB;IACjE,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjC,uCAAuC,CAACC,YAAY,CAAC,EAC7D,GAAG,IAAI,CAACwB,wDAAwD,CAACxB,YAAY,CAAC,EAC9E,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACG,kDAAkD,CAAC9B,YAAY,CAAC,CACzE;IAED,OAAOgC,MAAM;EACf;EAEA;;;EAGQa,gEAAgEA,CAAC7C,YAAiB;IACxF,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjC,uCAAuC,CAACC,YAAY,CAAC,EAC7D,GAAG,IAAI,CAACyB,+EAA+E,CAACzB,YAAY,CAAC,EACrG,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACG,kDAAkD,CAAC9B,YAAY,CAAC,CACzE;IAED,OAAOgC,MAAM;EACf;EAEA;;;EAGQc,mDAAmDA,CAAC9C,YAAiB;IAC3E,MAAMgC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACjC,uCAAuC,CAACC,YAAY,CAAC,EAC7D,GAAG,IAAI,CAAC0B,kEAAkE,CAAC1B,YAAY,CAAC,EACxF,GAAG,IAAI,CAAC2B,oCAAoC,EAAE,EAC9C,GAAG,IAAI,CAACG,kDAAkD,CAAC9B,YAAY,CAAC,CACzE;IAED,OAAOgC,MAAM;EACf;EAEA;EACA;EACA;EAEA;;;EAGAe,eAAeA,CAAC/C,YAAiB;IAC/B,OAAO;IACL;IACA;MACEgD,GAAG,EAAE,gDAAgD;MACrDC,KAAK,EAAE,IAAI,CAAClB,yCAAyC,CAAC/B,YAAY;KACnE,EACD;MACEgD,GAAG,EAAE,8CAA8C;MACnDC,KAAK,EAAE,IAAI,CAAChB,uCAAuC,CAACjC,YAAY;KACjE,EACD;MACEgD,GAAG,EAAE,6CAA6C;MAClDC,KAAK,EAAE,IAAI,CAACf,sCAAsC,CAAClC,YAAY;KAChE,EACD;MACEgD,GAAG,EAAE,gDAAgD;MACrDC,KAAK,EAAE,IAAI,CAACd,yCAAyC,CAACnC,YAAY;KACnE,EACD;MACEgD,GAAG,EAAE,+CAA+C;MACpDC,KAAK,EAAE,IAAI,CAACb,wCAAwC,CAACpC,YAAY;KAClE,EACD;MACEgD,GAAG,EAAE,2CAA2C;MAChDC,KAAK,EAAE,IAAI,CAACZ,oCAAoC,CAACrC,YAAY;KAC9D,EACD;MACEgD,GAAG,EAAE,uDAAuD;MAC5DC,KAAK,EAAE,IAAI,CAACX,+CAA+C,CAACtC,YAAY;KACzE,EACD;MACEgD,GAAG,EAAE,0DAA0D;MAC/DC,KAAK,EAAE,IAAI,CAACV,kDAAkD,CAACvC,YAAY;KAC5E,EACD;MACEgD,GAAG,EAAE,qDAAqD;MAC1DC,KAAK,EAAE,IAAI,CAACT,6CAA6C,CAACxC,YAAY;KACvE,EACD;MACEgD,GAAG,EAAE,gDAAgD;MACrDC,KAAK,EAAE,IAAI,CAACR,yCAAyC,CAACzC,YAAY;KACnE,EACD;MACEgD,GAAG,EAAE,uDAAuD;MAC5DC,KAAK,EAAE,IAAI,CAACP,+CAA+C,CAAC1C,YAAY;KACzE,EACD;MACEgD,GAAG,EAAE,mDAAmD;MACxDC,KAAK,EAAE,IAAI,CAACN,2CAA2C,CAAC3C,YAAY;KACrE,EACD;MACEgD,GAAG,EAAE,gDAAgD;MACrDC,KAAK,EAAE,IAAI,CAACL,yCAAyC,CAAC5C,YAAY;KACnE,EACD;MACEgD,GAAG,EAAE,yEAAyE;MAC9EC,KAAK,EAAE,IAAI,CAACJ,gEAAgE,CAAC7C,YAAY;KAC1F,EACD;MACEgD,GAAG,EAAE,2DAA2D;MAChEC,KAAK,EAAE,IAAI,CAACH,mDAAmD,CAAC9C,YAAY;KAC7E,CACF;EACH;EAEA;;;EAGAkD,uCAAuCA,CAAA;IACrC,OAAO,CACL,gDAAgD,EAChD,8CAA8C,EAC9C,6CAA6C,EAC7C,gDAAgD,EAChD,+CAA+C,EAC/C,2CAA2C,EAC3C,uDAAuD,EACvD,0DAA0D,EAC1D,qDAAqD,EACrD,gDAAgD,EAChD,uDAAuD,EACvD,mDAAmD,EACnD,gDAAgD,CACjD;EACH;EAEA;;;EAGAC,kCAAkCA,CAACH,GAAW;IAC5C,OAAOA,GAAG,CAACI,QAAQ,CAAC,sCAAsC,CAAC;EAC7D;;;;yIAr9DWtD,gCAAgC,IAAAuD,iBAAA,IAAhCvD,gCAAgC;IAAA;EAAA;;WAAhCA,gCAAgC;IAAAwD,OAAA,EAAhCxD,gCAAgC,CAAAyD,IAAA;IAAAC,UAAA,EAF/B;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}