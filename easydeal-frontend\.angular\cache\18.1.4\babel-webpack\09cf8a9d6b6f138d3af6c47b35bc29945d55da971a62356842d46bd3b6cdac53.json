{"ast": null, "code": "import { CometChat } from '@cometchat-pro/chat';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/chat.service\";\nimport * as i2 from \"@angular/router\";\nexport class ChatComponentComponent {\n  chatService;\n  route;\n  cd;\n  // session user\n  UID;\n  chatWithUID;\n  constructor(chatService, route, cd) {\n    this.chatService = chatService;\n    this.route = route;\n    this.cd = cd;\n  }\n  ngOnInit() {\n    const userJson = localStorage.getItem('currentUser');\n    let user = userJson ? JSON.parse(userJson) : null;\n    this.UID = user?.id;\n    this.route.queryParams.subscribe(params => {\n      this.chatWithUID = params['chatWithUID'] || undefined;\n    });\n    const appID = '277125e04ff598e9';\n    const region = 'us';\n    const authKey = '290ffb4caa0c1c1b6af3a55a08f2acc1180051ea';\n    const widgetID = '42bd2ecc-1917-4988-bc03-fd9a996921e1';\n    CometChatWidget.init({\n      appID,\n      appRegion: region,\n      authKey\n    }).then(() => {\n      console.log('Widget initialized');\n      CometChatWidget.login({\n        uid: this.UID\n      }).then(() => {\n        console.log('Widget login successful');\n        //notificatioon\n        this.addMessageListener();\n        this.launchWidget(widgetID);\n      }, loginError => {\n        console.error('Login failed', loginError);\n      });\n    }, initError => {\n      console.error('Widget init failed', initError);\n    });\n  }\n  ngOnDestroy() {\n    CometChat.removeMessageListener('cometchat-listener');\n  }\n  launchWidget(widgetID) {\n    const config = {\n      widgetID: widgetID,\n      target: '#cometchat-container',\n      roundedCorners: true,\n      height: '600px',\n      width: '100%',\n      defaultType: 'user'\n    };\n    if (this.chatWithUID) {\n      config.defaultID = this.chatWithUID;\n    }\n    CometChatWidget.launch(config);\n  }\n  addMessageListener() {\n    const listenerID = 'cometchat-listener';\n    CometChat.addMessageListener(listenerID, new CometChat.MessageListener({\n      onTextMessageReceived: message => {\n        console.log('New text message received:', message);\n        this.chatService.increment();\n        this.cd.detectChanges();\n        //Display browser or toast notification\n        alert(`📨 New message from ${message.sender.name}: ${message.text}`);\n      },\n      onMediaMessageReceived: message => {\n        console.log('New media message received:', message);\n        alert(`📸 New media message from ${message.sender.name}`);\n      }\n    }));\n  }\n  static ɵfac = function ChatComponentComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ChatComponentComponent)(i0.ɵɵdirectiveInject(i1.ChatService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ChatComponentComponent,\n    selectors: [[\"app-chat-component\"]],\n    decls: 1,\n    vars: 0,\n    consts: [[\"id\", \"cometchat-container\"]],\n    template: function ChatComponentComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"div\", 0);\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["CometChat", "ChatComponentComponent", "chatService", "route", "cd", "UID", "chatWithUID", "constructor", "ngOnInit", "userJson", "localStorage", "getItem", "user", "JSON", "parse", "id", "queryParams", "subscribe", "params", "undefined", "appID", "region", "auth<PERSON><PERSON>", "widgetID", "CometChatWidget", "init", "appRegion", "then", "console", "log", "login", "uid", "addMessageListener", "launchWidget", "loginError", "error", "initError", "ngOnDestroy", "removeMessageListener", "config", "target", "roundedCorners", "height", "width", "defaultType", "defaultID", "launch", "listenerID", "MessageListener", "onTextMessageReceived", "message", "increment", "detectChanges", "alert", "sender", "name", "text", "onMediaMessageReceived", "i0", "ɵɵdirectiveInject", "i1", "ChatService", "i2", "ActivatedRoute", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "ChatComponentComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\shared\\chat-component\\chat-component.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\shared\\chat-component\\chat-component.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { CometChat } from '@cometchat-pro/chat';\r\nimport { ChatService } from '../services/chat.service';\r\ndeclare var CometChatWidget: any;\r\n\r\n@Component({\r\n  selector: 'app-chat-component',\r\n  templateUrl: './chat-component.component.html',\r\n  styleUrls: ['./chat-component.component.scss']\r\n})\r\n\r\nexport class ChatComponentComponent implements OnInit, OnDestroy {\r\n\r\n  // session user\r\n  UID :any;\r\n\r\n chatWithUID?: string;\r\n\r\n  constructor(private chatService: ChatService ,private route: ActivatedRoute, private cd: ChangeDetectorRef) {}\r\n\r\n  ngOnInit(): void {\r\n    const userJson = localStorage.getItem('currentUser');\r\n    let user = userJson ? JSON.parse(userJson) : null;\r\n    this.UID = user?.id;\r\n    this.route.queryParams.subscribe(params => {\r\n    this.chatWithUID = params['chatWithUID'] || undefined;});\r\n\r\n    const appID = '277125e04ff598e9';\r\n    const region = 'us';\r\n    const authKey = '290ffb4caa0c1c1b6af3a55a08f2acc1180051ea';\r\n    const widgetID = '42bd2ecc-1917-4988-bc03-fd9a996921e1';\r\n\r\n    CometChatWidget.init({\r\n      appID,\r\n      appRegion: region,\r\n      authKey\r\n    }).then(() => {\r\n      console.log('Widget initialized');\r\n\r\n      CometChatWidget.login({\r\n        uid: this.UID\r\n      }).then(() => {\r\n        console.log('Widget login successful');\r\n\r\n        //notificatioon\r\n        this.addMessageListener();\r\n\r\n        this.launchWidget(widgetID);\r\n      }, (loginError: any) => {\r\n        console.error('Login failed', loginError);\r\n      });\r\n\r\n    }, (initError: any) => {\r\n      console.error('Widget init failed', initError);\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    CometChat.removeMessageListener('cometchat-listener');\r\n  }\r\n\r\n  launchWidget(widgetID: string): void {\r\n    const config: any = {\r\n      widgetID: widgetID,\r\n      target: '#cometchat-container',\r\n      roundedCorners: true,\r\n      height: '600px',\r\n      width: '100%',\r\n      defaultType: 'user'\r\n    };\r\n\r\n    if (this.chatWithUID) {\r\n      config.defaultID = this.chatWithUID;\r\n    }\r\n\r\n    CometChatWidget.launch(config);\r\n  }\r\n\r\n  addMessageListener(): void {\r\n  const listenerID = 'cometchat-listener';\r\n\r\n  CometChat.addMessageListener(\r\n    listenerID,\r\n    new CometChat.MessageListener({\r\n      onTextMessageReceived: (message: any) => {\r\n        console.log('New text message received:', message);\r\n\r\n        this.chatService.increment();\r\n        this.cd.detectChanges();\r\n\r\n        //Display browser or toast notification\r\n        alert(`📨 New message from ${message.sender.name}: ${message.text}`);\r\n      },\r\n      onMediaMessageReceived: (message: any) => {\r\n        console.log('New media message received:', message);\r\n        alert(`📸 New media message from ${message.sender.name}`);\r\n      },\r\n    })\r\n  );\r\n}\r\n\r\n}\r\n\r\n", "\r\n<div id=\"cometchat-container\"></div>\r\n"], "mappings": "AAEA,SAASA,SAAS,QAAQ,qBAAqB;;;;AAU/C,OAAM,MAAOC,sBAAsB;EAObC,WAAA;EAAkCC,KAAA;EAA+BC,EAAA;EALrF;EACAC,GAAG;EAEJC,WAAW;EAEVC,YAAoBL,WAAwB,EAAUC,KAAqB,EAAUC,EAAqB;IAAtF,KAAAF,WAAW,GAAXA,WAAW;IAAuB,KAAAC,KAAK,GAALA,KAAK;IAA0B,KAAAC,EAAE,GAAFA,EAAE;EAAsB;EAE7GI,QAAQA,CAAA;IACN,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACpD,IAAIC,IAAI,GAAGH,QAAQ,GAAGI,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC,GAAG,IAAI;IACjD,IAAI,CAACJ,GAAG,GAAGO,IAAI,EAAEG,EAAE;IACnB,IAAI,CAACZ,KAAK,CAACa,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MAC1C,IAAI,CAACZ,WAAW,GAAGY,MAAM,CAAC,aAAa,CAAC,IAAIC,SAAS;IAAC,CAAC,CAAC;IAExD,MAAMC,KAAK,GAAG,kBAAkB;IAChC,MAAMC,MAAM,GAAG,IAAI;IACnB,MAAMC,OAAO,GAAG,0CAA0C;IAC1D,MAAMC,QAAQ,GAAG,sCAAsC;IAEvDC,eAAe,CAACC,IAAI,CAAC;MACnBL,KAAK;MACLM,SAAS,EAAEL,MAAM;MACjBC;KACD,CAAC,CAACK,IAAI,CAAC,MAAK;MACXC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MAEjCL,eAAe,CAACM,KAAK,CAAC;QACpBC,GAAG,EAAE,IAAI,CAAC1B;OACX,CAAC,CAACsB,IAAI,CAAC,MAAK;QACXC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;QAEtC;QACA,IAAI,CAACG,kBAAkB,EAAE;QAEzB,IAAI,CAACC,YAAY,CAACV,QAAQ,CAAC;MAC7B,CAAC,EAAGW,UAAe,IAAI;QACrBN,OAAO,CAACO,KAAK,CAAC,cAAc,EAAED,UAAU,CAAC;MAC3C,CAAC,CAAC;IAEJ,CAAC,EAAGE,SAAc,IAAI;MACpBR,OAAO,CAACO,KAAK,CAAC,oBAAoB,EAAEC,SAAS,CAAC;IAChD,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACTrC,SAAS,CAACsC,qBAAqB,CAAC,oBAAoB,CAAC;EACvD;EAEAL,YAAYA,CAACV,QAAgB;IAC3B,MAAMgB,MAAM,GAAQ;MAClBhB,QAAQ,EAAEA,QAAQ;MAClBiB,MAAM,EAAE,sBAAsB;MAC9BC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,OAAO;MACfC,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;KACd;IAED,IAAI,IAAI,CAACtC,WAAW,EAAE;MACpBiC,MAAM,CAACM,SAAS,GAAG,IAAI,CAACvC,WAAW;IACrC;IAEAkB,eAAe,CAACsB,MAAM,CAACP,MAAM,CAAC;EAChC;EAEAP,kBAAkBA,CAAA;IAClB,MAAMe,UAAU,GAAG,oBAAoB;IAEvC/C,SAAS,CAACgC,kBAAkB,CAC1Be,UAAU,EACV,IAAI/C,SAAS,CAACgD,eAAe,CAAC;MAC5BC,qBAAqB,EAAGC,OAAY,IAAI;QACtCtB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEqB,OAAO,CAAC;QAElD,IAAI,CAAChD,WAAW,CAACiD,SAAS,EAAE;QAC5B,IAAI,CAAC/C,EAAE,CAACgD,aAAa,EAAE;QAEvB;QACAC,KAAK,CAAC,uBAAuBH,OAAO,CAACI,MAAM,CAACC,IAAI,KAAKL,OAAO,CAACM,IAAI,EAAE,CAAC;MACtE,CAAC;MACDC,sBAAsB,EAAGP,OAAY,IAAI;QACvCtB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEqB,OAAO,CAAC;QACnDG,KAAK,CAAC,6BAA6BH,OAAO,CAACI,MAAM,CAACC,IAAI,EAAE,CAAC;MAC3D;KACD,CAAC,CACH;EACH;;qCAxFatD,sBAAsB,EAAAyD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAM,iBAAA;EAAA;;UAAtB/D,sBAAsB;IAAAgE,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCXnCb,EAAA,CAAAe,SAAA,aAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}