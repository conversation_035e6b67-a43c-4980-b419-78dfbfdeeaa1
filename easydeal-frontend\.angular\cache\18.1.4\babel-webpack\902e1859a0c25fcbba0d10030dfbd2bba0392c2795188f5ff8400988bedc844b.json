{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { BaseConfigService } from '../base-config.service';\nimport { FLOOR_TYPES_OPTIONS, UNIT_VIEW_TYPES_OPTIONS, FINISHING_STATUS_TYPES_OPTIONS, DELIVERY_STATUS_TYPES_OPTIONS, OTHER_ACCESSORIES_OPTIONS, PAYMENT_METHOD_OPTIONS, UNIT_LAYOUT_STATUS_TYPES_OPTIONS, ACTIVITY_TYPES_OPTIONS, BUILDING_LAYOUT_STATUS_TYPES_OPTIONS, FIT_OUT_CONDITION_TYPES_OPTIONS, GROUND_LAYOUT_STATUS_TYPES_OPTIONS, UNIT_DESCRIPTION_TYPES_OPTIONS, UNIT_DESIGN_TYPES_OPTIONS } from '../../stepper-modal.constants';\nimport * as i0 from \"@angular/core\";\nexport class PurchaseOutsideCompoundConfigService extends BaseConfigService {\n  // ============================================================================\n  // LOCATION INPUTS\n  // ============================================================================\n  /**\n   * Create purchase outside compound location inputs\n   */\n  createPurchaseOutsideCompoundLocationInputs(stepperModal) {\n    return [{\n      step: 2,\n      name: 'locationSuggestions',\n      type: 'checkbox',\n      label: 'Location Suggestion',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }];\n  }\n  // ============================================================================\n  // UNIT INFORMATION INPUTS\n  // ============================================================================\n  /**\n   * Create purchase outside compound unit information inputs for apartments\n   */\n  createPurchaseOutsideCompoundApartmentsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Unit Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Unit Area Max (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'bedrooms',\n      type: 'number',\n      label: 'Bedrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathrooms',\n      type: 'number',\n      label: 'Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create purchase outside compound unit information inputs for duplexes\n   */\n  createPurchaseOutsideCompoundDuplexesUnitInformationInputs(stepperModal) {\n    return this.createPurchaseOutsideCompoundApartmentsUnitInformationInputs(stepperModal);\n  }\n  /**\n   * Create purchase outside compound unit information inputs for studios\n   */\n  createPurchaseOutsideCompoundStudiosUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Unit Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Unit Area Max (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create purchase outside compound unit information inputs for penthouses\n   */\n  createPurchaseOutsideCompoundPenthousesUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Unit Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Unit Area Max (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'bedrooms',\n      type: 'number',\n      label: 'Bedrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathrooms',\n      type: 'number',\n      label: 'Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create purchase outside compound unit information inputs for basements\n   */\n  createPurchaseOutsideCompoundBasementsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Unit Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Unit Area Max (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'unitStatus',\n      type: 'select',\n      label: 'Unit Status',\n      options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bedrooms',\n      type: 'number',\n      label: 'Bedrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathrooms',\n      type: 'number',\n      label: 'Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create purchase outside compound unit information inputs for roofs\n   */\n  createPurchaseOutsideCompoundRoofsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Unit Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Unit Area Max (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'unitStatus',\n      type: 'select',\n      label: 'Unit Status',\n      options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingLayoutStatus',\n      type: 'select',\n      label: 'Building Layout Status',\n      options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bedrooms',\n      type: 'number',\n      label: 'Bedrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathrooms',\n      type: 'number',\n      label: 'Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create purchase outside compound unit information inputs for standalone villas\n   */\n  createPurchaseOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingAreaSuggestions',\n      type: 'checkbox',\n      label: 'Building Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'unitDesign',\n      type: 'select',\n      label: 'Unit Design',\n      options: UNIT_DESIGN_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingLayoutStatus',\n      type: 'select',\n      label: 'Building Layout Status',\n      options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create purchase outside compound unit information inputs for administrative units\n   */\n  createPurchaseOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Unit Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Unit Area Max (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create purchase outside compound unit information inputs for medical clinics\n   */\n  createPurchaseOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal) {\n    return this.createPurchaseOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal);\n  }\n  /**\n   * Create purchase outside compound unit information inputs for pharmacies\n   */\n  createPurchaseOutsideCompoundPharmaciesUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Unit Area Min (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Unit Area Max (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'areaSuggestions',\n      type: 'checkbox',\n      label: 'Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Fit Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create purchase outside compound unit information inputs for commercial stores\n   */\n  createPurchaseOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal) {\n    return this.createPurchaseOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal);\n  }\n  /**\n   * Create purchase outside compound unit information inputs for factory lands\n   */\n  createPurchaseOutsideCompoundFactoryLandsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'groundStatus',\n      type: 'select',\n      label: 'Ground Status',\n      options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingAreaSuggestions',\n      type: 'checkbox',\n      label: 'Building Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Fit Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create purchase outside compound unit information inputs for warehouses\n   */\n  createPurchaseOutsideCompoundWarehousesUnitInformationInputs(stepperModal) {\n    return [\n    // {\n    //   step: 3,\n    //   name: 'unitAreaMin',\n    //   type: 'number',\n    //   label: 'Unit Area Min (m²)',\n    //   validators: [Validators.required, Validators.min(0)],\n    //   visibility: () => true,\n    // },\n    // {\n    //   step: 3,\n    //   name: 'unitAreaMax',\n    //   type: 'number',\n    //   label: 'Unit Area Max (m²)',\n    //   validators: [Validators.required, Validators.min(0)],\n    //   visibility: () => true,\n    // },\n    {\n      step: 3,\n      name: 'groundStatus',\n      type: 'select',\n      label: 'Ground Status',\n      options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingAreaSuggestions',\n      type: 'checkbox',\n      label: 'Building Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Fit Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create purchase outside compound unit information inputs for residential buildings\n   */\n  createPurchaseOutsideCompoundResidentialBuildingsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'groundStatus',\n      type: 'select',\n      label: 'Ground Status',\n      options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingAreaSuggestions',\n      type: 'checkbox',\n      label: 'Building Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitDescription',\n      type: 'select',\n      label: 'Unit Description',\n      options: UNIT_DESCRIPTION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create purchase outside compound unit information inputs for commercial administrative buildings\n   */\n  createPurchaseOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'groundStatus',\n      type: 'select',\n      label: 'Ground Status',\n      options: GROUND_LAYOUT_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingAreaSuggestions',\n      type: 'checkbox',\n      label: 'Building Area Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitDescription',\n      type: 'select',\n      label: 'Unit Description',\n      options: UNIT_DESCRIPTION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  // ============================================================================\n  // FINANCIAL INPUTS\n  // ============================================================================\n  /**\n   * Create purchase outside compound financial inputs\n   */\n  createPurchaseOutsideCompoundFinancialInputs(stepperModal) {\n    return [{\n      step: 5,\n      name: 'paymentMethod',\n      type: 'select',\n      label: 'Payment Method',\n      options: PAYMENT_METHOD_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'averageUnitPriceMin',\n      type: 'number',\n      label: 'Average Unit Price Min',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'averageUnitPriceMax',\n      type: 'number',\n      label: 'Average Unit Price Max',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'unitPriceSuggestions',\n      type: 'checkbox',\n      label: 'Price Suggestion',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }];\n  }\n  // ============================================================================\n  // CONFIGURATION METHODS\n  // ============================================================================\n  /**\n   * Configuration for purchase outside compound apartments\n   */\n  createPurchaseOutsideCompoundApartmentsConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundApartmentsUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for purchase outside compound duplexes\n   */\n  createPurchaseOutsideCompoundDuplexesConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundDuplexesUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for purchase outside compound studios\n   */\n  createPurchaseOutsideCompoundStudiosConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundStudiosUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for purchase outside compound penthouses\n   */\n  createPurchaseOutsideCompoundPenthousesConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundPenthousesUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for purchase outside compound basements\n   */\n  createPurchaseOutsideCompoundBasementsConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundBasementsUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for purchase outside compound roofs\n   */\n  createPurchaseOutsideCompoundRoofsConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundRoofsUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for purchase outside compound standalone villas\n   */\n  createPurchaseOutsideCompoundStandaloneVillasConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for purchase outside compound administrative units\n   */\n  createPurchaseOutsideCompoundAdministrativeUnitsConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for purchase outside compound medical clinics\n   */\n  createPurchaseOutsideCompoundMedicalClinicsConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for purchase outside compound pharmacies\n   */\n  createPurchaseOutsideCompoundPharmaciesConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundPharmaciesUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for purchase outside compound commercial stores\n   */\n  createPurchaseOutsideCompoundCommercialStoresConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for purchase outside compound factory lands\n   */\n  createPurchaseOutsideCompoundFactoryLandsConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFactoryLandsUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for purchase outside compound warehouses\n   */\n  createPurchaseOutsideCompoundWarehousesConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundWarehousesUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for purchase outside compound residential buildings\n   */\n  createPurchaseOutsideCompoundResidentialBuildingsConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundResidentialBuildingsUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  /**\n   * Configuration for purchase outside compound commercial administrative buildings\n   */\n  createPurchaseOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal) {\n    const config = [...this.createPurchaseOutsideCompoundLocationInputs(stepperModal), ...this.createPurchaseOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal), ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal)];\n    return config;\n  }\n  // ============================================================================\n  // PUBLIC METHODS\n  // ============================================================================\n  /**\n   * Get input configurations for purchase outside compound cases\n   */\n  getInputConfigs(stepperModal) {\n    return [\n    // PURCHASE OUTSIDE COMPOUND CONFIGURATIONS\n    {\n      key: 'purchase_sell_outside_compound_purchasing_apartments',\n      value: this.createPurchaseOutsideCompoundApartmentsConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_purchasing_duplexes',\n      value: this.createPurchaseOutsideCompoundDuplexesConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_purchasing_studios',\n      value: this.createPurchaseOutsideCompoundStudiosConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_purchasing_penthouses',\n      value: this.createPurchaseOutsideCompoundPenthousesConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_purchasing_basements',\n      value: this.createPurchaseOutsideCompoundBasementsConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_purchasing_roofs',\n      value: this.createPurchaseOutsideCompoundRoofsConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_purchasing_standalone_villas',\n      value: this.createPurchaseOutsideCompoundStandaloneVillasConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_purchasing_administrative_units',\n      value: this.createPurchaseOutsideCompoundAdministrativeUnitsConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_purchasing_medical_clinics',\n      value: this.createPurchaseOutsideCompoundMedicalClinicsConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_purchasing_pharmacies',\n      value: this.createPurchaseOutsideCompoundPharmaciesConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_purchasing_commercial_stores',\n      value: this.createPurchaseOutsideCompoundCommercialStoresConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_purchasing_factory_lands',\n      value: this.createPurchaseOutsideCompoundFactoryLandsConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_purchasing_warehouses',\n      value: this.createPurchaseOutsideCompoundWarehousesConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_purchasing_residential_buildings',\n      value: this.createPurchaseOutsideCompoundResidentialBuildingsConfig(stepperModal)\n    }, {\n      key: 'purchase_sell_outside_compound_purchasing_commercial_administrative_buildings',\n      value: this.createPurchaseOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal)\n    }];\n  }\n  static ɵfac = /*@__PURE__*/(() => {\n    let ɵPurchaseOutsideCompoundConfigService_BaseFactory;\n    return function PurchaseOutsideCompoundConfigService_Factory(__ngFactoryType__) {\n      return (ɵPurchaseOutsideCompoundConfigService_BaseFactory || (ɵPurchaseOutsideCompoundConfigService_BaseFactory = i0.ɵɵgetInheritedFactory(PurchaseOutsideCompoundConfigService)))(__ngFactoryType__ || PurchaseOutsideCompoundConfigService);\n    };\n  })();\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PurchaseOutsideCompoundConfigService,\n    factory: PurchaseOutsideCompoundConfigService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["Validators", "BaseConfigService", "FLOOR_TYPES_OPTIONS", "UNIT_VIEW_TYPES_OPTIONS", "FINISHING_STATUS_TYPES_OPTIONS", "DELIVERY_STATUS_TYPES_OPTIONS", "OTHER_ACCESSORIES_OPTIONS", "PAYMENT_METHOD_OPTIONS", "UNIT_LAYOUT_STATUS_TYPES_OPTIONS", "ACTIVITY_TYPES_OPTIONS", "BUILDING_LAYOUT_STATUS_TYPES_OPTIONS", "FIT_OUT_CONDITION_TYPES_OPTIONS", "GROUND_LAYOUT_STATUS_TYPES_OPTIONS", "UNIT_DESCRIPTION_TYPES_OPTIONS", "UNIT_DESIGN_TYPES_OPTIONS", "PurchaseOutsideCompoundConfigService", "createPurchaseOutsideCompoundLocationInputs", "stepperModal", "step", "name", "type", "label", "validators", "visibility", "isClient", "createPurchaseOutsideCompoundApartmentsUnitInformationInputs", "options", "required", "min", "createPurchaseOutsideCompoundDuplexesUnitInformationInputs", "createPurchaseOutsideCompoundStudiosUnitInformationInputs", "createPurchaseOutsideCompoundPenthousesUnitInformationInputs", "createPurchaseOutsideCompoundBasementsUnitInformationInputs", "createPurchaseOutsideCompoundRoofsUnitInformationInputs", "createPurchaseOutsideCompoundStandaloneVillasUnitInformationInputs", "createPurchaseOutsideCompoundAdministrativeUnitsUnitInformationInputs", "createPurchaseOutsideCompoundMedicalClinicsUnitInformationInputs", "createPurchaseOutsideCompoundPharmaciesUnitInformationInputs", "createPurchaseOutsideCompoundCommercialStoresUnitInformationInputs", "createPurchaseOutsideCompoundFactoryLandsUnitInformationInputs", "createPurchaseOutsideCompoundWarehousesUnitInformationInputs", "createPurchaseOutsideCompoundResidentialBuildingsUnitInformationInputs", "createPurchaseOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs", "createPurchaseOutsideCompoundFinancialInputs", "createPurchaseOutsideCompoundApartmentsConfig", "config", "createPurchaseOutsideCompoundDuplexesConfig", "createPurchaseOutsideCompoundStudiosConfig", "createPurchaseOutsideCompoundPenthousesConfig", "createPurchaseOutsideCompoundBasementsConfig", "createPurchaseOutsideCompoundRoofsConfig", "createPurchaseOutsideCompoundStandaloneVillasConfig", "createPurchaseOutsideCompoundAdministrativeUnitsConfig", "createPurchaseOutsideCompoundMedicalClinicsConfig", "createPurchaseOutsideCompoundPharmaciesConfig", "createPurchaseOutsideCompoundCommercialStoresConfig", "createPurchaseOutsideCompoundFactoryLandsConfig", "createPurchaseOutsideCompoundWarehousesConfig", "createPurchaseOutsideCompoundResidentialBuildingsConfig", "createPurchaseOutsideCompoundCommercialAdministrativeBuildingsConfig", "getInputConfigs", "key", "value", "__ngFactoryType__", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\shared\\stepper-modal\\services\\outside-compound\\purchase-outside-compound-config.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Validators } from '@angular/forms';\r\nimport { BaseConfigService, InputConfig, StepperConfiguration } from '../base-config.service';\r\nimport {\r\n  FLOOR_TYPES_OPTIONS,\r\n  UNIT_FACING_TYPES_OPTIONS,\r\n  UNIT_VIEW_TYPES_OPTIONS,\r\n  FINISHING_STATUS_TYPES_OPTIONS,\r\n  DELIVERY_STATUS_TYPES_OPTIONS,\r\n  OTHER_ACCESSORIES_OPTIONS,\r\n  PAYMENT_METHOD_OPTIONS,\r\n  UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n  ACTIVITY_TYPES_OPTIONS,\r\n  BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n  FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n  GROUND_LAYOUT_STATUS_TYPES_OPTIONS,\r\n  UNIT_DESCRIPTION_TYPES_OPTIONS,\r\n  UNIT_DESIGN_TYPES_OPTIONS,\r\n} from '../../stepper-modal.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class PurchaseOutsideCompoundConfigService extends BaseConfigService {\r\n  // ============================================================================\r\n  // LOCATION INPUTS\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Create purchase outside compound location inputs\r\n   */\r\n  private createPurchaseOutsideCompoundLocationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 2,\r\n        name: 'locationSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Location Suggestion',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n    ];\r\n  }\r\n\r\n  // ============================================================================\r\n  // UNIT INFORMATION INPUTS\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for apartments\r\n   */\r\n  private createPurchaseOutsideCompoundApartmentsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Unit Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Unit Area Max (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bedrooms',\r\n        type: 'number',\r\n        label: 'Bedrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathrooms',\r\n        type: 'number',\r\n        label: 'Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for duplexes\r\n   */\r\n  private createPurchaseOutsideCompoundDuplexesUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return this.createPurchaseOutsideCompoundApartmentsUnitInformationInputs(stepperModal);\r\n  }\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for studios\r\n   */\r\n  private createPurchaseOutsideCompoundStudiosUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Unit Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Unit Area Max (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for penthouses\r\n   */\r\n  private createPurchaseOutsideCompoundPenthousesUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Unit Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Unit Area Max (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bedrooms',\r\n        type: 'number',\r\n        label: 'Bedrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathrooms',\r\n        type: 'number',\r\n        label: 'Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for basements\r\n   */\r\n  private createPurchaseOutsideCompoundBasementsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Unit Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Unit Area Max (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitStatus',\r\n        type: 'select',\r\n        label: 'Unit Status',\r\n        options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bedrooms',\r\n        type: 'number',\r\n        label: 'Bedrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathrooms',\r\n        type: 'number',\r\n        label: 'Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for roofs\r\n   */\r\n  private createPurchaseOutsideCompoundRoofsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Unit Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Unit Area Max (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitStatus',\r\n        type: 'select',\r\n        label: 'Unit Status',\r\n        options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingLayoutStatus',\r\n        type: 'select',\r\n        label: 'Building Layout Status',\r\n        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bedrooms',\r\n        type: 'number',\r\n        label: 'Bedrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathrooms',\r\n        type: 'number',\r\n        label: 'Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for standalone villas\r\n   */\r\n  private createPurchaseOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Building Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitDesign',\r\n        type: 'select',\r\n        label: 'Unit Design',\r\n        options: UNIT_DESIGN_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingLayoutStatus',\r\n        type: 'select',\r\n        label: 'Building Layout Status',\r\n        options: BUILDING_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for administrative units\r\n   */\r\n  private createPurchaseOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Unit Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Unit Area Max (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for medical clinics\r\n   */\r\n  private createPurchaseOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return this.createPurchaseOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal);\r\n  }\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for pharmacies\r\n   */\r\n  private createPurchaseOutsideCompoundPharmaciesUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Unit Area Min (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Unit Area Max (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'areaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fit Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for commercial stores\r\n   */\r\n  private createPurchaseOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return this.createPurchaseOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal);\r\n  }\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for factory lands\r\n   */\r\n  private createPurchaseOutsideCompoundFactoryLandsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'groundStatus',\r\n        type: 'select',\r\n        label: 'Ground Status',\r\n        options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Building Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fit Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for warehouses\r\n   */\r\n  private createPurchaseOutsideCompoundWarehousesUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      // {\r\n      //   step: 3,\r\n      //   name: 'unitAreaMin',\r\n      //   type: 'number',\r\n      //   label: 'Unit Area Min (m²)',\r\n      //   validators: [Validators.required, Validators.min(0)],\r\n      //   visibility: () => true,\r\n      // },\r\n      // {\r\n      //   step: 3,\r\n      //   name: 'unitAreaMax',\r\n      //   type: 'number',\r\n      //   label: 'Unit Area Max (m²)',\r\n      //   validators: [Validators.required, Validators.min(0)],\r\n      //   visibility: () => true,\r\n      // },\r\n      {\r\n        step: 3,\r\n        name: 'groundStatus',\r\n        type: 'select',\r\n        label: 'Ground Status',\r\n        options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Building Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fit Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for residential buildings\r\n   */\r\n  private createPurchaseOutsideCompoundResidentialBuildingsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'groundStatus',\r\n        type: 'select',\r\n        label: 'Ground Status',\r\n        options: UNIT_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Building Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitDescription',\r\n        type: 'select',\r\n        label: 'Unit Description',\r\n        options: UNIT_DESCRIPTION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create purchase outside compound unit information inputs for commercial administrative buildings\r\n   */\r\n  private createPurchaseOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'groundStatus',\r\n        type: 'select',\r\n        label: 'Ground Status',\r\n        options: GROUND_LAYOUT_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingAreaSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Building Area Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitDescription',\r\n        type: 'select',\r\n        label: 'Unit Description',\r\n        options: UNIT_DESCRIPTION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  // ============================================================================\r\n  // FINANCIAL INPUTS\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Create purchase outside compound financial inputs\r\n   */\r\n  private createPurchaseOutsideCompoundFinancialInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 5,\r\n        name: 'paymentMethod',\r\n        type: 'select',\r\n        label: 'Payment Method',\r\n        options: PAYMENT_METHOD_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'averageUnitPriceMin',\r\n        type: 'number',\r\n        label: 'Average Unit Price Min',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'averageUnitPriceMax',\r\n        type: 'number',\r\n        label: 'Average Unit Price Max',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'unitPriceSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Price Suggestion',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n    ];\r\n  }\r\n\r\n  // ============================================================================\r\n  // CONFIGURATION METHODS\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Configuration for purchase outside compound apartments\r\n   */\r\n  private createPurchaseOutsideCompoundApartmentsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundApartmentsUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for purchase outside compound duplexes\r\n   */\r\n  private createPurchaseOutsideCompoundDuplexesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundDuplexesUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for purchase outside compound studios\r\n   */\r\n  private createPurchaseOutsideCompoundStudiosConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundStudiosUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for purchase outside compound penthouses\r\n   */\r\n  private createPurchaseOutsideCompoundPenthousesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundPenthousesUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for purchase outside compound basements\r\n   */\r\n  private createPurchaseOutsideCompoundBasementsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundBasementsUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for purchase outside compound roofs\r\n   */\r\n  private createPurchaseOutsideCompoundRoofsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundRoofsUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for purchase outside compound standalone villas\r\n   */\r\n  private createPurchaseOutsideCompoundStandaloneVillasConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundStandaloneVillasUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for purchase outside compound administrative units\r\n   */\r\n  private createPurchaseOutsideCompoundAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundAdministrativeUnitsUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for purchase outside compound medical clinics\r\n   */\r\n  private createPurchaseOutsideCompoundMedicalClinicsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundMedicalClinicsUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for purchase outside compound pharmacies\r\n   */\r\n  private createPurchaseOutsideCompoundPharmaciesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundPharmaciesUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for purchase outside compound commercial stores\r\n   */\r\n  private createPurchaseOutsideCompoundCommercialStoresConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundCommercialStoresUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for purchase outside compound factory lands\r\n   */\r\n  private createPurchaseOutsideCompoundFactoryLandsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFactoryLandsUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for purchase outside compound warehouses\r\n   */\r\n  private createPurchaseOutsideCompoundWarehousesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundWarehousesUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for purchase outside compound residential buildings\r\n   */\r\n  private createPurchaseOutsideCompoundResidentialBuildingsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundResidentialBuildingsUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Configuration for purchase outside compound commercial administrative buildings\r\n   */\r\n  private createPurchaseOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createPurchaseOutsideCompoundLocationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal),\r\n      ...this.createPurchaseOutsideCompoundFinancialInputs(stepperModal),\r\n    ];\r\n\r\n    return config;\r\n  }\r\n\r\n  // ============================================================================\r\n  // PUBLIC METHODS\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Get input configurations for purchase outside compound cases\r\n   */\r\n  getInputConfigs(stepperModal: any): StepperConfiguration[] {\r\n    return [\r\n      // PURCHASE OUTSIDE COMPOUND CONFIGURATIONS\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_apartments',\r\n        value: this.createPurchaseOutsideCompoundApartmentsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_duplexes',\r\n        value: this.createPurchaseOutsideCompoundDuplexesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_studios',\r\n        value: this.createPurchaseOutsideCompoundStudiosConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_penthouses',\r\n        value: this.createPurchaseOutsideCompoundPenthousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_basements',\r\n        value: this.createPurchaseOutsideCompoundBasementsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_roofs',\r\n        value: this.createPurchaseOutsideCompoundRoofsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_standalone_villas',\r\n        value: this.createPurchaseOutsideCompoundStandaloneVillasConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_administrative_units',\r\n        value: this.createPurchaseOutsideCompoundAdministrativeUnitsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_medical_clinics',\r\n        value: this.createPurchaseOutsideCompoundMedicalClinicsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_pharmacies',\r\n        value: this.createPurchaseOutsideCompoundPharmaciesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_commercial_stores',\r\n        value: this.createPurchaseOutsideCompoundCommercialStoresConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_factory_lands',\r\n        value: this.createPurchaseOutsideCompoundFactoryLandsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_warehouses',\r\n        value: this.createPurchaseOutsideCompoundWarehousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_residential_buildings',\r\n        value: this.createPurchaseOutsideCompoundResidentialBuildingsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'purchase_sell_outside_compound_purchasing_commercial_administrative_buildings',\r\n        value: this.createPurchaseOutsideCompoundCommercialAdministrativeBuildingsConfig(stepperModal),\r\n      },\r\n    ];\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,iBAAiB,QAA2C,wBAAwB;AAC7F,SACEC,mBAAmB,EAEnBC,uBAAuB,EACvBC,8BAA8B,EAC9BC,6BAA6B,EAC7BC,yBAAyB,EACzBC,sBAAsB,EACtBC,gCAAgC,EAChCC,sBAAsB,EACtBC,oCAAoC,EACpCC,+BAA+B,EAC/BC,kCAAkC,EAClCC,8BAA8B,EAC9BC,yBAAyB,QACpB,+BAA+B;;AAKtC,OAAM,MAAOC,oCAAqC,SAAQd,iBAAiB;EACzE;EACA;EACA;EAEA;;;EAGQe,2CAA2CA,CAACC,YAAiB;IACnE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,CACF;EACH;EAEA;EACA;EACA;EAEA;;;EAGQC,4DAA4DA,CAACR,YAAiB;IACpF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdK,OAAO,EAAExB,mBAAmB;MAC5BoB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbK,OAAO,EAAEvB,uBAAuB;MAChCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBK,OAAO,EAAEtB,8BAA8B;MACvCkB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBK,OAAO,EAAErB,6BAA6B;MACtCiB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BK,OAAO,EAAEpB,yBAAyB;MAClCgB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQM,0DAA0DA,CAACZ,YAAiB;IAClF,OAAO,IAAI,CAACQ,4DAA4D,CAACR,YAAY,CAAC;EACxF;EAEA;;;EAGQa,yDAAyDA,CAACb,YAAiB;IACjF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdK,OAAO,EAAExB,mBAAmB;MAC5BoB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbK,OAAO,EAAEvB,uBAAuB;MAChCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBK,OAAO,EAAEtB,8BAA8B;MACvCkB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBK,OAAO,EAAErB,6BAA6B;MACtCiB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BK,OAAO,EAAEpB,yBAAyB;MAClCgB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQQ,4DAA4DA,CAACd,YAAiB;IACpF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbK,OAAO,EAAEvB,uBAAuB;MAChCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBK,OAAO,EAAEtB,8BAA8B;MACvCkB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBK,OAAO,EAAErB,6BAA6B;MACtCiB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BK,OAAO,EAAEpB,yBAAyB;MAClCgB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQS,2DAA2DA,CAACf,YAAiB;IACnF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBK,OAAO,EAAElB,gCAAgC;MACzCc,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbK,OAAO,EAAEvB,uBAAuB;MAChCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBK,OAAO,EAAEtB,8BAA8B;MACvCkB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBK,OAAO,EAAErB,6BAA6B;MACtCiB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BK,OAAO,EAAEpB,yBAAyB;MAClCgB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQU,uDAAuDA,CAAChB,YAAiB;IAC/E,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBK,OAAO,EAAElB,gCAAgC;MACzCc,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BK,OAAO,EAAEhB,oCAAoC;MAC7CY,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBK,OAAO,EAAEtB,8BAA8B;MACvCkB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBK,OAAO,EAAErB,6BAA6B;MACtCiB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BK,OAAO,EAAEpB,yBAAyB;MAClCgB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQW,kEAAkEA,CAACjB,YAAiB;IAC1F,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACtB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,yBAAyB;MAC/BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,2BAA2B;MAClCC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBK,OAAO,EAAEZ,yBAAyB;MAClCQ,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BK,OAAO,EAAEhB,oCAAoC;MAC7CY,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbK,OAAO,EAAEvB,uBAAuB;MAChCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBK,OAAO,EAAEtB,8BAA8B;MACvCkB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BK,OAAO,EAAEpB,yBAAyB;MAClCgB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQY,qEAAqEA,CAAClB,YAAiB;IAC7F,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdK,OAAO,EAAExB,mBAAmB;MAC5BoB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbK,OAAO,EAAEvB,uBAAuB;MAChCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBK,OAAO,EAAEtB,8BAA8B;MACvCkB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBK,OAAO,EAAErB,6BAA6B;MACtCiB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BK,OAAO,EAAEpB,yBAAyB;MAClCgB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQa,gEAAgEA,CAACnB,YAAiB;IACxF,OAAO,IAAI,CAACkB,qEAAqE,CAAClB,YAAY,CAAC;EACjG;EAEA;;;EAGQoB,4DAA4DA,CAACpB,YAAiB;IACpF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdK,OAAO,EAAExB,mBAAmB;MAC5BoB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbK,OAAO,EAAEvB,uBAAuB;MAChCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBK,OAAO,EAAEtB,8BAA8B;MACvCkB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBK,OAAO,EAAErB,6BAA6B;MACtCiB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BK,OAAO,EAAEf,+BAA+B;MACxCW,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BK,OAAO,EAAEpB,yBAAyB;MAClCgB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQe,kEAAkEA,CAACrB,YAAiB;IAC1F,OAAO,IAAI,CAACkB,qEAAqE,CAAClB,YAAY,CAAC;EACjG;EAEA;;;EAGQsB,8DAA8DA,CAACtB,YAAiB;IACtF,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,eAAe;MACtBK,OAAO,EAAElB,gCAAgC;MACzCc,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,yBAAyB;MAC/BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,2BAA2B;MAClCC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBK,OAAO,EAAEjB,sBAAsB;MAC/Ba,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BK,OAAO,EAAEf,+BAA+B;MACxCW,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBK,OAAO,EAAErB,6BAA6B;MACtCiB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQiB,4DAA4DA,CAACvB,YAAiB;IACpF,OAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,eAAe;MACtBK,OAAO,EAAElB,gCAAgC;MACzCc,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,yBAAyB;MAC/BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,2BAA2B;MAClCC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBK,OAAO,EAAEjB,sBAAsB;MAC/Ba,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBK,OAAO,EAAErB,6BAA6B;MACtCiB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BK,OAAO,EAAEf,+BAA+B;MACxCW,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BK,OAAO,EAAEpB,yBAAyB;MAClCgB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQkB,sEAAsEA,CAACxB,YAAiB;IAC9F,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,eAAe;MACtBK,OAAO,EAAElB,gCAAgC;MACzCc,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,yBAAyB;MAC/BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,2BAA2B;MAClCC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBK,OAAO,EAAErB,6BAA6B;MACtCiB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbK,OAAO,EAAEvB,uBAAuB;MAChCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBK,OAAO,EAAEb,8BAA8B;MACvCS,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQmB,mFAAmFA,CAACzB,YAAiB;IAC3G,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,eAAe;MACtBK,OAAO,EAAEd,kCAAkC;MAC3CU,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,yBAAyB;MAC/BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,2BAA2B;MAClCC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbK,OAAO,EAAEvB,uBAAuB;MAChCmB,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBK,OAAO,EAAEb,8BAA8B;MACvCS,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBK,OAAO,EAAEjB,sBAAsB;MAC/Ba,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBK,OAAO,EAAErB,6BAA6B;MACtCiB,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,CAAC;MACjCJ,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;EACA;EACA;EAEA;;;EAGQoB,4CAA4CA,CAAC1B,YAAiB;IACpE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBK,OAAO,EAAEnB,sBAAsB;MAC/Be,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACtB,UAAU,CAAC2B,QAAQ,EAAE3B,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDL,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEL,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAEA,CAAA,KAAMN,YAAY,CAACO,QAAQ;KACxC,CACF;EACH;EAEA;EACA;EACA;EAEA;;;EAGQoB,6CAA6CA,CAAC3B,YAAiB;IACrE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACQ,4DAA4D,CAACR,YAAY,CAAC,EAClF,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;;;EAGQC,2CAA2CA,CAAC7B,YAAiB;IACnE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACY,0DAA0D,CAACZ,YAAY,CAAC,EAChF,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;;;EAGQE,0CAA0CA,CAAC9B,YAAiB;IAClE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACa,yDAAyD,CAACb,YAAY,CAAC,EAC/E,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;;;EAGQG,6CAA6CA,CAAC/B,YAAiB;IACrE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACc,4DAA4D,CAACd,YAAY,CAAC,EAClF,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;;;EAGQI,4CAA4CA,CAAChC,YAAiB;IACpE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACe,2DAA2D,CAACf,YAAY,CAAC,EACjF,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;;;EAGQK,wCAAwCA,CAACjC,YAAiB;IAChE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACgB,uDAAuD,CAAChB,YAAY,CAAC,EAC7E,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;;;EAGQM,mDAAmDA,CAAClC,YAAiB;IAC3E,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACiB,kEAAkE,CAACjB,YAAY,CAAC,EACxF,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;;;EAGQO,sDAAsDA,CAACnC,YAAiB;IAC9E,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACkB,qEAAqE,CAAClB,YAAY,CAAC,EAC3F,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;;;EAGQQ,iDAAiDA,CAACpC,YAAiB;IACzE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACmB,gEAAgE,CAACnB,YAAY,CAAC,EACtF,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;;;EAGQS,6CAA6CA,CAACrC,YAAiB;IACrE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACoB,4DAA4D,CAACpB,YAAY,CAAC,EAClF,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;;;EAGQU,mDAAmDA,CAACtC,YAAiB;IAC3E,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACqB,kEAAkE,CAACrB,YAAY,CAAC,EACxF,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;;;EAGQW,+CAA+CA,CAACvC,YAAiB;IACvE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACsB,8DAA8D,CAACtB,YAAY,CAAC,EACpF,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;;;EAGQY,6CAA6CA,CAACxC,YAAiB;IACrE,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACuB,4DAA4D,CAACvB,YAAY,CAAC,EAClF,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;;;EAGQa,uDAAuDA,CAACzC,YAAiB;IAC/E,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACwB,sEAAsE,CAACxB,YAAY,CAAC,EAC5F,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;;;EAGQc,oEAAoEA,CAAC1C,YAAiB;IAC5F,MAAM4B,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7B,2CAA2C,CAACC,YAAY,CAAC,EACjE,GAAG,IAAI,CAACyB,mFAAmF,CAACzB,YAAY,CAAC,EACzG,GAAG,IAAI,CAAC0B,4CAA4C,CAAC1B,YAAY,CAAC,CACnE;IAED,OAAO4B,MAAM;EACf;EAEA;EACA;EACA;EAEA;;;EAGAe,eAAeA,CAAC3C,YAAiB;IAC/B,OAAO;IACL;IACA;MACE4C,GAAG,EAAE,sDAAsD;MAC3DC,KAAK,EAAE,IAAI,CAAClB,6CAA6C,CAAC3B,YAAY;KACvE,EACD;MACE4C,GAAG,EAAE,oDAAoD;MACzDC,KAAK,EAAE,IAAI,CAAChB,2CAA2C,CAAC7B,YAAY;KACrE,EACD;MACE4C,GAAG,EAAE,mDAAmD;MACxDC,KAAK,EAAE,IAAI,CAACf,0CAA0C,CAAC9B,YAAY;KACpE,EACD;MACE4C,GAAG,EAAE,sDAAsD;MAC3DC,KAAK,EAAE,IAAI,CAACd,6CAA6C,CAAC/B,YAAY;KACvE,EACD;MACE4C,GAAG,EAAE,qDAAqD;MAC1DC,KAAK,EAAE,IAAI,CAACb,4CAA4C,CAAChC,YAAY;KACtE,EACD;MACE4C,GAAG,EAAE,iDAAiD;MACtDC,KAAK,EAAE,IAAI,CAACZ,wCAAwC,CAACjC,YAAY;KAClE,EACD;MACE4C,GAAG,EAAE,6DAA6D;MAClEC,KAAK,EAAE,IAAI,CAACX,mDAAmD,CAAClC,YAAY;KAC7E,EACD;MACE4C,GAAG,EAAE,gEAAgE;MACrEC,KAAK,EAAE,IAAI,CAACV,sDAAsD,CAACnC,YAAY;KAChF,EACD;MACE4C,GAAG,EAAE,2DAA2D;MAChEC,KAAK,EAAE,IAAI,CAACT,iDAAiD,CAACpC,YAAY;KAC3E,EACD;MACE4C,GAAG,EAAE,sDAAsD;MAC3DC,KAAK,EAAE,IAAI,CAACR,6CAA6C,CAACrC,YAAY;KACvE,EACD;MACE4C,GAAG,EAAE,6DAA6D;MAClEC,KAAK,EAAE,IAAI,CAACP,mDAAmD,CAACtC,YAAY;KAC7E,EACD;MACE4C,GAAG,EAAE,yDAAyD;MAC9DC,KAAK,EAAE,IAAI,CAACN,+CAA+C,CAACvC,YAAY;KACzE,EACD;MACE4C,GAAG,EAAE,sDAAsD;MAC3DC,KAAK,EAAE,IAAI,CAACL,6CAA6C,CAACxC,YAAY;KACvE,EACD;MACE4C,GAAG,EAAE,iEAAiE;MACtEC,KAAK,EAAE,IAAI,CAACJ,uDAAuD,CAACzC,YAAY;KACjF,EACD;MACE4C,GAAG,EAAE,+EAA+E;MACpFC,KAAK,EAAE,IAAI,CAACH,oEAAoE,CAAC1C,YAAY;KAC9F,CACF;EACH;;;;iJA36CWF,oCAAoC,IAAAgD,iBAAA,IAApChD,oCAAoC;IAAA;EAAA;;WAApCA,oCAAoC;IAAAiD,OAAA,EAApCjD,oCAAoC,CAAAkD,IAAA;IAAAC,UAAA,EAFnC;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}