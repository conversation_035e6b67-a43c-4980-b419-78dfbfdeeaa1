{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../authentication/services/authentication.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../../_metronic/shared/keenicon/keenicon.component\";\nfunction HomeComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_23_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleUserDropdown());\n    });\n    i0.ɵɵelement(1, \"img\", 25);\n    i0.ɵɵelementStart(2, \"span\", 26);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getUserProfileImage(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.getUserDisplayName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getUserDisplayName());\n  }\n}\nfunction HomeComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_24_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(2, \"span\", 30);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 31)(4, \"g\", 32);\n    i0.ɵɵelement(5, \"path\", 33)(6, \"path\", 34)(7, \"path\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"defs\")(9, \"clipPath\", 36);\n    i0.ɵɵelement(10, \"rect\", 37);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Requests\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_24_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(14, \"span\", 30);\n    i0.ɵɵelement(15, \"app-keenicon\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \" My Profile \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_24_Template_div_click_18_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(19, \"span\", 30);\n    i0.ɵɵelement(20, \"app-keenicon\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \" Messages \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_24_Template_div_click_23_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(24, \"span\", 30);\n    i0.ɵɵelement(25, \"i\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27, \" Help \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_24_Template_div_click_28_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(29, \"span\", 30);\n    i0.ɵɵelement(30, \"app-keenicon\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \" Notifications \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(33, \"div\", 42);\n    i0.ɵɵelementStart(34, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_24_Template_div_click_34_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelementStart(35, \"span\", 30);\n    i0.ɵɵelement(36, \"i\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \" Logout \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(39, \"div\", 42);\n    i0.ɵɵelementStart(40, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_24_Template_div_click_40_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeUserDropdown());\n    });\n    i0.ɵɵelementStart(41, \"span\", 46);\n    i0.ɵɵtext(42, \" New Request \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HomeComponent_a_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 47);\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵtext(2, \" Register Guest \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class HomeComponent {\n  authService;\n  currentUser = null;\n  isLoggedIn = false;\n  showUserDropdown = false;\n  constructor(authService) {\n    this.authService = authService;\n  }\n  ngOnInit() {\n    this.checkUserSession();\n  }\n  checkUserSession() {\n    // Check if user is logged in by checking localStorage\n    const authToken = localStorage.getItem('authToken');\n    const currentUser = localStorage.getItem('currentUser');\n    if (authToken && currentUser) {\n      try {\n        this.currentUser = JSON.parse(currentUser);\n        this.isLoggedIn = true;\n      } catch (error) {\n        // If parsing fails, user is not logged in\n        this.isLoggedIn = false;\n        this.currentUser = null;\n      }\n    } else {\n      this.isLoggedIn = false;\n      this.currentUser = null;\n    }\n  }\n  getUserDisplayName() {\n    if (this.currentUser) {\n      return this.currentUser.fullName || 'User';\n    }\n    return 'Guest';\n  }\n  getUserProfileImage() {\n    if (this.currentUser && this.currentUser.image) {\n      return this.currentUser.image;\n    }\n    // Return default avatar if no profile image\n    return 'assets/media/avatars/blank.png';\n  }\n  toggleUserDropdown() {\n    this.showUserDropdown = !this.showUserDropdown;\n  }\n  closeUserDropdown() {\n    this.showUserDropdown = false;\n  }\n  logout() {\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('currentUser');\n    this.isLoggedIn = false;\n    this.currentUser = null;\n    this.showUserDropdown = false;\n    // Optionally redirect to login page\n    // this.router.navigate(['/authentication/login']);\n  }\n  onDocumentClick(event) {\n    const target = event.target;\n    const userProfile = target.closest('.user-profile');\n    const userDropdown = target.closest('.user-dropdown');\n    // Close dropdown if clicked outside of user profile and dropdown\n    if (!userProfile && !userDropdown && this.showUserDropdown) {\n      this.showUserDropdown = false;\n    }\n  }\n  static ɵfac = function HomeComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HomeComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomeComponent,\n    selectors: [[\"app-home\"]],\n    hostBindings: function HomeComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function HomeComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 44,\n    vars: 3,\n    consts: [[1, \"home-header\"], [1, \"navbar\", \"navbar-expand-lg\"], [1, \"container-fluid\", \"px-4\"], [1, \"navbar-brand\"], [\"alt\", \"Logo\", \"src\", \"./assets/media/easydeallogos/loading-logo.png\", 1, \"h-40px\", \"app-sidebar-logo-default\"], [1, \"navbar-nav\", \"mx-auto\"], [1, \"nav-list\", \"d-flex\", \"align-items-center\", \"mb-0\"], [1, \"nav-item\"], [\"href\", \"#\", 1, \"nav-link\"], [1, \"navbar-nav\", \"position-relative\"], [\"class\", \"nav-link user-profile\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"user-dropdown\", 4, \"ngIf\"], [\"href\", \"#\", \"class\", \"nav-link user-link\", 4, \"ngIf\"], [1, \"hero-section\"], [1, \"hero-background\"], [\"src\", \"./assets/media/home/<USER>\", \"alt\", \"Hero Background\", 1, \"hero-bg-image\"], [1, \"hero-overlay\"], [1, \"hero-content\"], [1, \"container\"], [1, \"row\", \"justify-content-center\"], [1, \"col-12\"], [1, \"hero-text-container\"], [1, \"hero-text-item\"], [1, \"hero-text\"], [1, \"nav-link\", \"user-profile\", 3, \"click\"], [1, \"user-avatar\", \"me-2\", 3, \"src\", \"alt\"], [1, \"user-name\"], [1, \"fas\", \"fa-chevron-down\", \"ms-2\"], [1, \"user-dropdown\"], [1, \"dropdown-item\", 3, \"click\"], [1, \"menu-icon\", \"me-2\"], [\"width\", \"19\", \"height\", \"19\", \"viewBox\", \"0 0 19 19\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"clip-path\", \"url(#clip0_24_2533)\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z\"], [\"stroke\", \"#e74c3c\", \"stroke-width\", \"1\", \"d\", \"M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z\"], [\"id\", \"clip0_24_2533\"], [\"width\", \"19\", \"height\", \"19\", \"fill\", \"white\"], [\"name\", \"user\", \"type\", \"outline\", 1, \"fs-5\", \"text-primary\"], [\"name\", \"messages\", \"type\", \"outline\", 1, \"fs-5\", \"text-info\"], [1, \"fa-regular\", \"fa-circle-question\", \"fs-6\", \"text-warning\"], [\"name\", \"notification-on\", \"type\", \"outline\", 1, \"fs-5\", \"text-gray-600\"], [1, \"dropdown-divider\"], [1, \"dropdown-item\", \"logout-item\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\", \"fs-6\", \"text-danger\"], [1, \"dropdown-item\", \"new-request-item\", 3, \"click\"], [1, \"text-success\"], [\"href\", \"#\", 1, \"nav-link\", \"user-link\"], [1, \"fas\", \"fa-user\", \"me-2\"]],\n    template: function HomeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"header\", 0)(1, \"nav\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵelement(4, \"img\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 5)(6, \"ul\", 6)(7, \"li\", 7)(8, \"a\", 8);\n        i0.ɵɵtext(9, \" Home \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"li\", 7)(11, \"a\", 8);\n        i0.ɵɵtext(12, \" About EasyDeal \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"li\", 7)(14, \"a\", 8);\n        i0.ɵɵtext(15, \" New Projects \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"li\", 7)(17, \"a\", 8);\n        i0.ɵɵtext(18, \" Advertisements \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"li\", 7)(20, \"a\", 8);\n        i0.ɵɵtext(21, \" Contact Us \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(22, \"div\", 9);\n        i0.ɵɵtemplate(23, HomeComponent_div_23_Template, 5, 3, \"div\", 10)(24, HomeComponent_div_24_Template, 43, 0, \"div\", 11)(25, HomeComponent_a_25_Template, 3, 0, \"a\", 12);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(26, \"div\", 13)(27, \"div\", 14);\n        i0.ɵɵelement(28, \"img\", 15)(29, \"div\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"div\", 17)(31, \"div\", 18)(32, \"div\", 19)(33, \"div\", 20)(34, \"div\", 21)(35, \"div\", 22)(36, \"h2\", 23);\n        i0.ɵɵtext(37, \" EasyDeal \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(38, \"div\", 22)(39, \"h2\", 23);\n        i0.ɵɵtext(40, \" Speed \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(41, \"div\", 22)(42, \"h2\", 23);\n        i0.ɵɵtext(43, \" Reliability \");\n        i0.ɵɵelementEnd()()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(23);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoggedIn && ctx.showUserDropdown);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoggedIn);\n      }\n    },\n    dependencies: [i2.NgIf, i3.KeeniconComponent],\n    styles: [\".home-header[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  min-height: 120vh;\\n  overflow: hidden;\\n  background: rgba(255, 255, 255, 0.95);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 10;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 1rem 0;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .logo-img[_ngcontent-%COMP%] {\\n  height: 50px;\\n  width: auto;\\n  object-fit: contain;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  gap: 2rem;\\n  margin: 0;\\n  padding: 0;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  text-decoration: none;\\n  font-weight: 500;\\n  font-size: 1rem;\\n  padding: 0.5rem 1rem;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n  direction: rtl;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  color: #27ae60;\\n  transform: translateY(-2px);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\\n  color: white;\\n  border-radius: 25px;\\n  padding: 0.7rem 1.5rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #229954, #27ae60);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #27ae60, #2ecc71);\\n  color: white !important;\\n  border-radius: 25px;\\n  padding: 0.7rem 1.5rem;\\n  text-decoration: none;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #229954, #27ae60);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);\\n  color: white !important;\\n  text-decoration: none;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 25px;\\n  padding: 0.5rem 1rem;\\n  border: 1px solid rgba(250, 250, 250, 0.3);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(39, 174, 96, 0.5);\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.2);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 35px;\\n  height: 35px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid #27ae60;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50;\\n  font-size: 0.95rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   i.fa-chevron-down[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #27ae60;\\n  transition: transform 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  right: 0;\\n  background: white;\\n  border-radius: 10px;\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);\\n  min-width: 220px;\\n  z-index: 1000;\\n  border: 1px solid rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_dropdownFadeIn 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 14px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateX(-3px);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n  width: 18px;\\n  text-align: center;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.logout-item[_ngcontent-%COMP%]:hover {\\n  background-color: #fff5f5;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.logout-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8, #f0f8f0);\\n  border-top: 2px solid #27ae60;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #d4f4d4, #e8f5e8);\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item.new-request-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #27ae60;\\n  font-weight: 600;\\n  text-align: center;\\n  width: 100%;\\n}\\n.home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: rgba(0, 0, 0, 0.1);\\n  margin: 0;\\n}\\n@keyframes _ngcontent-%COMP%_dropdownFadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: calc(100vh - 80px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 1;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .hero-bg-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  object-position: center;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .hero-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(44, 62, 80, 0.8) 0%, rgba(52, 73, 94, 0.6) 50%, rgba(39, 174, 96, 0.7) 100%);\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 5;\\n  width: 100%;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 4rem;\\n  flex-wrap: wrap;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  animation: _ngcontent-%COMP%_fadeInUp 1s ease-out;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0.2s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.4s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.6s;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  font-weight: 700;\\n  color: white;\\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\\n  margin: 0;\\n  padding: 1rem 2rem;\\n  border-radius: 15px;\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px) scale(1.05);\\n  background: rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n    gap: 2rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 3rem;\\n    padding: 0.8rem 1.5rem;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n    padding: 0.4rem 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-link[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n    padding: 0.5rem 1rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%] {\\n    padding: 0.4rem 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 30px;\\n    height: 30px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%] {\\n    min-width: 200px;\\n    right: -15px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n    width: 16px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .user-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 14px;\\n    height: 14px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1.5rem;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n    padding: 0.6rem 1rem;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .home-header[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .logo-img[_ngcontent-%COMP%] {\\n    height: 40px;\\n  }\\n  .home-header[_ngcontent-%COMP%]   .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-text-container[_ngcontent-%COMP%]   .hero-text-item[_ngcontent-%COMP%]   .hero-text[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    padding: 0.5rem 0.8rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "HomeComponent_div_23_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleUserDropdown", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "getUserProfileImage", "ɵɵsanitizeUrl", "getUserDisplayName", "ɵɵtextInterpolate", "HomeComponent_div_24_Template_div_click_1_listener", "_r3", "closeUserDropdown", "HomeComponent_div_24_Template_div_click_13_listener", "HomeComponent_div_24_Template_div_click_18_listener", "HomeComponent_div_24_Template_div_click_23_listener", "HomeComponent_div_24_Template_div_click_28_listener", "HomeComponent_div_24_Template_div_click_34_listener", "logout", "HomeComponent_div_24_Template_div_click_40_listener", "HomeComponent", "authService", "currentUser", "isLoggedIn", "showUserDropdown", "constructor", "ngOnInit", "checkUserSession", "authToken", "localStorage", "getItem", "JSON", "parse", "error", "fullName", "image", "removeItem", "onDocumentClick", "event", "target", "userProfile", "closest", "userDropdown", "ɵɵdirectiveInject", "i1", "AuthenticationService", "selectors", "hostBindings", "HomeComponent_HostBindings", "rf", "ctx", "HomeComponent_click_HostBindingHandler", "$event", "ɵɵresolveDocument", "ɵɵtemplate", "HomeComponent_div_23_Template", "HomeComponent_div_24_Template", "HomeComponent_a_25_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit, HostListener } from '@angular/core';\r\nimport { AuthenticationService } from '../authentication/services/authentication.service';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrls: ['./home.component.scss']\r\n})\r\nexport class HomeComponent implements OnInit {\r\n  currentUser: any = null;\r\n  isLoggedIn: boolean = false;\r\n  showUserDropdown: boolean = false;\r\n\r\n  constructor(private authService: AuthenticationService) { }\r\n\r\n  ngOnInit(): void {\r\n    this.checkUserSession();\r\n  }\r\n\r\n  checkUserSession(): void {\r\n    // Check if user is logged in by checking localStorage\r\n    const authToken = localStorage.getItem('authToken');\r\n    const currentUser = localStorage.getItem('currentUser');\r\n\r\n    if (authToken && currentUser) {\r\n      try {\r\n        this.currentUser = JSON.parse(currentUser);\r\n        this.isLoggedIn = true;\r\n      } catch (error) {\r\n        // If parsing fails, user is not logged in\r\n        this.isLoggedIn = false;\r\n        this.currentUser = null;\r\n      }\r\n    } else {\r\n      this.isLoggedIn = false;\r\n      this.currentUser = null;\r\n    }\r\n  }\r\n\r\n  getUserDisplayName(): string {\r\n    if (this.currentUser) {\r\n      return this.currentUser.fullName  || 'User';\r\n    }\r\n    return 'Guest';\r\n  }\r\n\r\n  getUserProfileImage(): string {\r\n    if (this.currentUser && this.currentUser.image) {\r\n      return this.currentUser.image;\r\n    }\r\n    // Return default avatar if no profile image\r\n    return 'assets/media/avatars/blank.png';\r\n  }\r\n\r\n  toggleUserDropdown(): void {\r\n    this.showUserDropdown = !this.showUserDropdown;\r\n  }\r\n\r\n  closeUserDropdown(): void {\r\n    this.showUserDropdown = false;\r\n  }\r\n\r\n  logout(): void {\r\n    localStorage.removeItem('authToken');\r\n    localStorage.removeItem('currentUser');\r\n    this.isLoggedIn = false;\r\n    this.currentUser = null;\r\n    this.showUserDropdown = false;\r\n    // Optionally redirect to login page\r\n    // this.router.navigate(['/authentication/login']);\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event): void {\r\n    const target = event.target as HTMLElement;\r\n    const userProfile = target.closest('.user-profile');\r\n    const userDropdown = target.closest('.user-dropdown');\r\n\r\n    // Close dropdown if clicked outside of user profile and dropdown\r\n    if (!userProfile && !userDropdown && this.showUserDropdown) {\r\n      this.showUserDropdown = false;\r\n    }\r\n  }\r\n\r\n}\r\n", "<!-- Header Section -->\r\n<header class=\"home-header\">\r\n  <!-- Navigation Bar -->\r\n  <nav class=\"navbar navbar-expand-lg\">\r\n    <div class=\"container-fluid px-4\">\r\n      <!-- Logo -->\r\n      <div class=\"navbar-brand\">\r\n        <img alt=\"Logo\" src=\"./assets/media/easydeallogos/loading-logo.png\" class=\"h-40px app-sidebar-logo-default\" />\r\n      </div>\r\n\r\n      <!-- Navigation Menu -->\r\n      <div class=\"navbar-nav mx-auto\">\r\n        <ul class=\"nav-list d-flex align-items-center mb-0\">\r\n          <li class=\"nav-item\">\r\n            <a href=\"#\" class=\"nav-link\"> Home </a>\r\n          </li>\r\n          <li class=\"nav-item\">\r\n            <a href=\"#\" class=\"nav-link\"> About EasyDeal </a>\r\n          </li>\r\n          <li class=\"nav-item\">\r\n            <a href=\"#\" class=\"nav-link\"> New Projects </a>\r\n          </li>\r\n          <li class=\"nav-item\">\r\n            <a href=\"#\" class=\"nav-link\"> Advertisements </a>\r\n          </li>\r\n          <li class=\"nav-item\">\r\n            <a href=\"#\" class=\"nav-link\"> Contact Us </a>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n\r\n      <!-- User Registration Link / User Profile -->\r\n      <div class=\"navbar-nav position-relative\">\r\n        <!-- If user is logged in, show user profile -->\r\n        <div *ngIf=\"isLoggedIn\" class=\"nav-link user-profile\" (click)=\"toggleUserDropdown()\">\r\n          <img [src]=\"getUserProfileImage()\" [alt]=\"getUserDisplayName()\" class=\"user-avatar me-2\">\r\n          <span class=\"user-name\">{{ getUserDisplayName() }}</span>\r\n          <i class=\"fas fa-chevron-down ms-2\"></i>\r\n        </div>\r\n\r\n        <!-- User Dropdown Menu -->\r\n        <div *ngIf=\"isLoggedIn && showUserDropdown\" class=\"user-dropdown\">\r\n          <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n            <span class=\"menu-icon me-2\">\r\n              <svg width=\"19\" height=\"19\" viewBox=\"0 0 19 19\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <g clip-path=\"url(#clip0_24_2533)\">\r\n                  <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                    d=\"M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z\" />\r\n                  <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                    d=\"M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z\" />\r\n                  <path stroke=\"#e74c3c\" stroke-width=\"1\"\r\n                    d=\"M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z\" />\r\n                </g>\r\n                <defs>\r\n                  <clipPath id=\"clip0_24_2533\">\r\n                    <rect width=\"19\" height=\"19\" fill=\"white\" />\r\n                  </clipPath>\r\n                </defs>\r\n              </svg>\r\n            </span>\r\n            <span>Requests</span>\r\n          </div>\r\n          <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n            <span class=\"menu-icon me-2\">\r\n              <app-keenicon name=\"user\" class=\"fs-5 text-primary\" type=\"outline\"></app-keenicon>\r\n            </span>\r\n            <span> My Profile </span>\r\n          </div>\r\n          <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n            <span class=\"menu-icon me-2\">\r\n              <app-keenicon name=\"messages\" class=\"fs-5 text-info\" type=\"outline\"></app-keenicon>\r\n            </span>\r\n            <span> Messages </span>\r\n          </div>\r\n          <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fa-regular fa-circle-question fs-6 text-warning\"></i>\r\n            </span>\r\n            <span> Help </span>\r\n          </div>\r\n          <div class=\"dropdown-item\" (click)=\"closeUserDropdown()\">\r\n            <span class=\"menu-icon me-2\">\r\n              <app-keenicon name=\"notification-on\" class=\"fs-5 text-gray-600\" type=\"outline\"></app-keenicon>\r\n            </span>\r\n            <span> Notifications </span>\r\n          </div>\r\n          <div class=\"dropdown-divider\"></div>\r\n          <div class=\"dropdown-item logout-item\" (click)=\"logout()\">\r\n            <span class=\"menu-icon me-2\">\r\n              <i class=\"fas fa-sign-out-alt fs-6 text-danger\"></i>\r\n            </span>\r\n            <span> Logout </span>\r\n          </div>\r\n          <div class=\"dropdown-divider\"></div>\r\n          <div class=\"dropdown-item new-request-item\" (click)=\"closeUserDropdown()\">\r\n            <span class=\"text-success\"> New Request </span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- If user is not logged in, show register button -->\r\n        <a *ngIf=\"!isLoggedIn\" href=\"#\" class=\"nav-link user-link\">\r\n          <i class=\"fas fa-user me-2\"></i>\r\n          Register Guest\r\n        </a>\r\n      </div>\r\n    </div>\r\n  </nav>\r\n\r\n  <!-- Hero Section -->\r\n  <div class=\"hero-section\">\r\n    <div class=\"hero-background\">\r\n      <img\r\n        src=\"./assets/media/home/<USER>\"\r\n        alt=\"Hero Background\" class=\"hero-bg-image\">\r\n      <div class=\"hero-overlay\"></div>\r\n    </div>\r\n\r\n    <div class=\"hero-content\">\r\n      <div class=\"container\">\r\n        <div class=\"row justify-content-center\">\r\n          <div class=\"col-12\">\r\n            <div class=\"hero-text-container\">\r\n              <div class=\"hero-text-item\">\r\n                <h2 class=\"hero-text\"> EasyDeal </h2>\r\n              </div>\r\n              <div class=\"hero-text-item\">\r\n                <h2 class=\"hero-text\"> Speed </h2>\r\n              </div>\r\n              <div class=\"hero-text-item\">\r\n                <h2 class=\"hero-text\"> Reliability </h2>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</header>"], "mappings": ";;;;;;;ICkCQA,EAAA,CAAAC,cAAA,cAAqF;IAA/BD,EAAA,CAAAE,UAAA,mBAAAC,mDAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IAClFT,EAAA,CAAAU,SAAA,cAAyF;IACzFV,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAW,MAAA,GAA0B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACzDZ,EAAA,CAAAU,SAAA,YAAwC;IAC1CV,EAAA,CAAAY,YAAA,EAAM;;;;IAHCZ,EAAA,CAAAa,SAAA,EAA6B;IAACb,EAA9B,CAAAc,UAAA,QAAAR,MAAA,CAAAS,mBAAA,IAAAf,EAAA,CAAAgB,aAAA,CAA6B,QAAAV,MAAA,CAAAW,kBAAA,GAA6B;IACvCjB,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAkB,iBAAA,CAAAZ,MAAA,CAAAW,kBAAA,GAA0B;;;;;;IAMlDjB,EADF,CAAAC,cAAA,cAAkE,cACP;IAA9BD,EAAA,CAAAE,UAAA,mBAAAiB,mDAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,eAA6B;;IAEzBD,EADF,CAAAC,cAAA,cAA+F,YAC1D;IAKjCD,EAJA,CAAAU,SAAA,eACmN,eAEuZ,eAE4O;IACx1BV,EAAA,CAAAY,YAAA,EAAI;IAEFZ,EADF,CAAAC,cAAA,WAAM,mBACyB;IAC3BD,EAAA,CAAAU,SAAA,gBAA4C;IAIpDV,EAHM,CAAAY,YAAA,EAAW,EACN,EACH,EACD;;IACPZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAW,MAAA,gBAAQ;IAChBX,EADgB,CAAAY,YAAA,EAAO,EACjB;IACNZ,EAAA,CAAAC,cAAA,eAAyD;IAA9BD,EAAA,CAAAE,UAAA,mBAAAoB,oDAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,gBAA6B;IAC3BD,EAAA,CAAAU,SAAA,wBAAkF;IACpFV,EAAA,CAAAY,YAAA,EAAO;IACPZ,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAW,MAAA,oBAAW;IACpBX,EADoB,CAAAY,YAAA,EAAO,EACrB;IACNZ,EAAA,CAAAC,cAAA,eAAyD;IAA9BD,EAAA,CAAAE,UAAA,mBAAAqB,oDAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,gBAA6B;IAC3BD,EAAA,CAAAU,SAAA,wBAAmF;IACrFV,EAAA,CAAAY,YAAA,EAAO;IACPZ,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAW,MAAA,kBAAS;IAClBX,EADkB,CAAAY,YAAA,EAAO,EACnB;IACNZ,EAAA,CAAAC,cAAA,eAAyD;IAA9BD,EAAA,CAAAE,UAAA,mBAAAsB,oDAAA;MAAAxB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,gBAA6B;IAC3BD,EAAA,CAAAU,SAAA,aAA+D;IACjEV,EAAA,CAAAY,YAAA,EAAO;IACPZ,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAW,MAAA,cAAK;IACdX,EADc,CAAAY,YAAA,EAAO,EACf;IACNZ,EAAA,CAAAC,cAAA,eAAyD;IAA9BD,EAAA,CAAAE,UAAA,mBAAAuB,oDAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,iBAAA,EAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAC,cAAA,gBAA6B;IAC3BD,EAAA,CAAAU,SAAA,wBAA8F;IAChGV,EAAA,CAAAY,YAAA,EAAO;IACPZ,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAW,MAAA,uBAAc;IACvBX,EADuB,CAAAY,YAAA,EAAO,EACxB;IACNZ,EAAA,CAAAU,SAAA,eAAoC;IACpCV,EAAA,CAAAC,cAAA,eAA0D;IAAnBD,EAAA,CAAAE,UAAA,mBAAAwB,oDAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqB,MAAA,EAAQ;IAAA,EAAC;IACvD3B,EAAA,CAAAC,cAAA,gBAA6B;IAC3BD,EAAA,CAAAU,SAAA,aAAoD;IACtDV,EAAA,CAAAY,YAAA,EAAO;IACPZ,EAAA,CAAAC,cAAA,YAAM;IAACD,EAAA,CAAAW,MAAA,gBAAO;IAChBX,EADgB,CAAAY,YAAA,EAAO,EACjB;IACNZ,EAAA,CAAAU,SAAA,eAAoC;IACpCV,EAAA,CAAAC,cAAA,eAA0E;IAA9BD,EAAA,CAAAE,UAAA,mBAAA0B,oDAAA;MAAA5B,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,iBAAA,EAAmB;IAAA,EAAC;IACvErB,EAAA,CAAAC,cAAA,gBAA2B;IAACD,EAAA,CAAAW,MAAA,qBAAY;IAE5CX,EAF4C,CAAAY,YAAA,EAAO,EAC3C,EACF;;;;;IAGNZ,EAAA,CAAAC,cAAA,YAA2D;IACzDD,EAAA,CAAAU,SAAA,YAAgC;IAChCV,EAAA,CAAAW,MAAA,uBACF;IAAAX,EAAA,CAAAY,YAAA,EAAI;;;AD/FZ,OAAM,MAAOiB,aAAa;EAKJC,WAAA;EAJpBC,WAAW,GAAQ,IAAI;EACvBC,UAAU,GAAY,KAAK;EAC3BC,gBAAgB,GAAY,KAAK;EAEjCC,YAAoBJ,WAAkC;IAAlC,KAAAA,WAAW,GAAXA,WAAW;EAA2B;EAE1DK,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd;IACA,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMR,WAAW,GAAGO,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAEvD,IAAIF,SAAS,IAAIN,WAAW,EAAE;MAC5B,IAAI;QACF,IAAI,CAACA,WAAW,GAAGS,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;QAC1C,IAAI,CAACC,UAAU,GAAG,IAAI;MACxB,CAAC,CAAC,OAAOU,KAAK,EAAE;QACd;QACA,IAAI,CAACV,UAAU,GAAG,KAAK;QACvB,IAAI,CAACD,WAAW,GAAG,IAAI;MACzB;IACF,CAAC,MAAM;MACL,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAACD,WAAW,GAAG,IAAI;IACzB;EACF;EAEAd,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACc,WAAW,EAAE;MACpB,OAAO,IAAI,CAACA,WAAW,CAACY,QAAQ,IAAK,MAAM;IAC7C;IACA,OAAO,OAAO;EAChB;EAEA5B,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACgB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACa,KAAK,EAAE;MAC9C,OAAO,IAAI,CAACb,WAAW,CAACa,KAAK;IAC/B;IACA;IACA,OAAO,gCAAgC;EACzC;EAEAnC,kBAAkBA,CAAA;IAChB,IAAI,CAACwB,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;EAChD;EAEAZ,iBAAiBA,CAAA;IACf,IAAI,CAACY,gBAAgB,GAAG,KAAK;EAC/B;EAEAN,MAAMA,CAAA;IACJW,YAAY,CAACO,UAAU,CAAC,WAAW,CAAC;IACpCP,YAAY,CAACO,UAAU,CAAC,aAAa,CAAC;IACtC,IAAI,CAACb,UAAU,GAAG,KAAK;IACvB,IAAI,CAACD,WAAW,GAAG,IAAI;IACvB,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B;IACA;EACF;EAGAa,eAAeA,CAACC,KAAY;IAC1B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAqB;IAC1C,MAAMC,WAAW,GAAGD,MAAM,CAACE,OAAO,CAAC,eAAe,CAAC;IACnD,MAAMC,YAAY,GAAGH,MAAM,CAACE,OAAO,CAAC,gBAAgB,CAAC;IAErD;IACA,IAAI,CAACD,WAAW,IAAI,CAACE,YAAY,IAAI,IAAI,CAAClB,gBAAgB,EAAE;MAC1D,IAAI,CAACA,gBAAgB,GAAG,KAAK;IAC/B;EACF;;qCA1EWJ,aAAa,EAAA7B,EAAA,CAAAoD,iBAAA,CAAAC,EAAA,CAAAC,qBAAA;EAAA;;UAAbzB,aAAa;IAAA0B,SAAA;IAAAC,YAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAb1D,EAAA,CAAAE,UAAA,mBAAA0D,uCAAAC,MAAA;UAAA,OAAAF,GAAA,CAAAb,eAAA,CAAAe,MAAA,CAAuB;QAAA,UAAA7D,EAAA,CAAA8D,iBAAA,CAAV;;;;;;;;QCFpB9D,EALN,CAAAC,cAAA,gBAA4B,aAEW,aACD,aAEN;QACxBD,EAAA,CAAAU,SAAA,aAA8G;QAChHV,EAAA,CAAAY,YAAA,EAAM;QAMAZ,EAHN,CAAAC,cAAA,aAAgC,YACsB,YAC7B,WACU;QAACD,EAAA,CAAAW,MAAA,aAAK;QACrCX,EADqC,CAAAY,YAAA,EAAI,EACpC;QAEHZ,EADF,CAAAC,cAAA,aAAqB,YACU;QAACD,EAAA,CAAAW,MAAA,wBAAe;QAC/CX,EAD+C,CAAAY,YAAA,EAAI,EAC9C;QAEHZ,EADF,CAAAC,cAAA,aAAqB,YACU;QAACD,EAAA,CAAAW,MAAA,sBAAa;QAC7CX,EAD6C,CAAAY,YAAA,EAAI,EAC5C;QAEHZ,EADF,CAAAC,cAAA,aAAqB,YACU;QAACD,EAAA,CAAAW,MAAA,wBAAe;QAC/CX,EAD+C,CAAAY,YAAA,EAAI,EAC9C;QAEHZ,EADF,CAAAC,cAAA,aAAqB,YACU;QAACD,EAAA,CAAAW,MAAA,oBAAW;QAG/CX,EAH+C,CAAAY,YAAA,EAAI,EAC1C,EACF,EACD;QAGNZ,EAAA,CAAAC,cAAA,cAA0C;QAoExCD,EAlEA,CAAA+D,UAAA,KAAAC,6BAAA,kBAAqF,KAAAC,6BAAA,mBAOnB,KAAAC,2BAAA,gBA2DP;QAMjElE,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;QAIJZ,EADF,CAAAC,cAAA,eAA0B,eACK;QAI3BD,EAHA,CAAAU,SAAA,eAE8C,eACd;QAClCV,EAAA,CAAAY,YAAA,EAAM;QAQMZ,EANZ,CAAAC,cAAA,eAA0B,eACD,eACmB,eAClB,eACe,eACH,cACJ;QAACD,EAAA,CAAAW,MAAA,kBAAS;QAClCX,EADkC,CAAAY,YAAA,EAAK,EACjC;QAEJZ,EADF,CAAAC,cAAA,eAA4B,cACJ;QAACD,EAAA,CAAAW,MAAA,eAAM;QAC/BX,EAD+B,CAAAY,YAAA,EAAK,EAC9B;QAEJZ,EADF,CAAAC,cAAA,eAA4B,cACJ;QAACD,EAAA,CAAAW,MAAA,qBAAY;QAQnDX,EARmD,CAAAY,YAAA,EAAK,EACpC,EACF,EACF,EACF,EACF,EACF,EACF,EACC;;;QAvGKZ,EAAA,CAAAa,SAAA,IAAgB;QAAhBb,EAAA,CAAAc,UAAA,SAAA6C,GAAA,CAAA3B,UAAA,CAAgB;QAOhBhC,EAAA,CAAAa,SAAA,EAAoC;QAApCb,EAAA,CAAAc,UAAA,SAAA6C,GAAA,CAAA3B,UAAA,IAAA2B,GAAA,CAAA1B,gBAAA,CAAoC;QA2DtCjC,EAAA,CAAAa,SAAA,EAAiB;QAAjBb,EAAA,CAAAc,UAAA,UAAA6C,GAAA,CAAA3B,UAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}