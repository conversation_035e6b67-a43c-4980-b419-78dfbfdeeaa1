import { Component, OnInit, HostListener } from '@angular/core';
import { AuthenticationService } from '../authentication/services/authentication.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {
  currentUser: any = null;
  isLoggedIn: boolean = false;
  showUserDropdown: boolean = false;
  showMobileMenu: boolean = false;

  constructor(private authService: AuthenticationService) { }

  ngOnInit(): void {
    this.checkUserSession();
  }

  checkUserSession(): void {
    // Check if user is logged in by checking localStorage
    const authToken = localStorage.getItem('authToken');
    const currentUser = localStorage.getItem('currentUser');

    if (authToken && currentUser) {
      try {
        this.currentUser = JSON.parse(currentUser);
        this.isLoggedIn = true;
      } catch (error) {
        // If parsing fails, user is not logged in
        this.isLoggedIn = false;
        this.currentUser = null;
      }
    } else {
      this.isLoggedIn = false;
      this.currentUser = null;
    }
  }

  getUserDisplayName(): string {
    if (this.currentUser) {
      return this.currentUser.fullName  || 'User';
    }
    return 'Guest';
  }

  getUserProfileImage(): string {
    if (this.currentUser && this.currentUser.image) {
      return this.currentUser.image;
    }
    // Return default avatar if no profile image
    return 'assets/media/avatars/blank.png';
  }

  toggleUserDropdown(): void {
    this.showUserDropdown = !this.showUserDropdown;
  }

  closeUserDropdown(): void {
    this.showUserDropdown = false;
  }

  logout(): void {
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    this.isLoggedIn = false;
    this.currentUser = null;
    this.showUserDropdown = false;
    // Optionally redirect to login page
    // this.router.navigate(['/authentication/login']);
  }

  toggleMobileMenu(): void {
    this.showMobileMenu = !this.showMobileMenu;
    // Close user dropdown when mobile menu is toggled
    if (this.showMobileMenu) {
      this.showUserDropdown = false;
    }
  }

  closeMobileMenu(): void {
    this.showMobileMenu = false;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const userProfile = target.closest('.user-profile');
    const userDropdown = target.closest('.user-dropdown');
    const navbarToggler = target.closest('.navbar-toggler');
    const navbarCollapse = target.closest('.navbar-collapse');

    // Close user dropdown if clicked outside of user profile and dropdown
    if (!userProfile && !userDropdown && this.showUserDropdown) {
      this.showUserDropdown = false;
    }

    // Close mobile menu if clicked outside of navbar toggler and navbar collapse
    if (!navbarToggler && !navbarCollapse && this.showMobileMenu) {
      this.showMobileMenu = false;
    }
  }

}
