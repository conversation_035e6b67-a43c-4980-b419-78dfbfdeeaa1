{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { HomeComponent } from './home.component';\nlet HomeModule = class HomeModule {};\nHomeModule = __decorate([NgModule({\n  declarations: [HomeComponent],\n  imports: [CommonModule, RouterModule.forChild([{\n    path: '',\n    component: HomeComponent\n  }])]\n})], HomeModule);\nexport { HomeModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "HomeComponent", "HomeModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>", "path", "component"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\nimport { HomeComponent } from './home.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    HomeComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule.forChild([\r\n      {\r\n        path: '',\r\n        component: HomeComponent\r\n      }\r\n    ])\r\n  ]\r\n})\r\nexport class HomeModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,kBAAkB;AAgBzC,IAAMC,UAAU,GAAhB,MAAMA,UAAU,GAAI;AAAdA,UAAU,GAAAC,UAAA,EAdtBL,QAAQ,CAAC;EACRM,YAAY,EAAE,CACZH,aAAa,CACd;EACDI,OAAO,EAAE,CACPN,YAAY,EACZC,YAAY,CAACM,QAAQ,CAAC,CACpB;IACEC,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEP;GACZ,CACF,CAAC;CAEL,CAAC,C,EACWC,UAAU,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}