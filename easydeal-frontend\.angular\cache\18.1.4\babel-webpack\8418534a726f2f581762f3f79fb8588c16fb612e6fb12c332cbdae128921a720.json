{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction HomeComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55)(2, \"div\", 56);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 57);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 58);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const card_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    i0.ɵɵclassMap(\"hero-card-\" + (i_r2 + 1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"bg-light-\" + card_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(card_r1.icon + \" fs-1 text-\" + card_r1.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r1.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r1.description);\n  }\n}\nfunction HomeComponent_div_71_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 78);\n    i0.ɵɵtext(1, \"\\u062C\\u062F\\u064A\\u062F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_71_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelement(1, \"i\", 79);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", property_r4.bedrooms, \" \\u063A\\u0631\\u0641\");\n  }\n}\nfunction HomeComponent_div_71_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelement(1, \"i\", 80);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", property_r4.bathrooms, \" \\u062D\\u0645\\u0627\\u0645\");\n  }\n}\nfunction HomeComponent_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_71_Template_div_click_1_listener() {\n      const property_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.viewProperty(property_r4.id));\n    });\n    i0.ɵɵelementStart(2, \"div\", 61);\n    i0.ɵɵelement(3, \"img\", 62);\n    i0.ɵɵelementStart(4, \"div\", 63);\n    i0.ɵɵtemplate(5, HomeComponent_div_71_span_5_Template, 2, 0, \"span\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 65);\n    i0.ɵɵelement(7, \"i\", 66);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 67)(11, \"h4\", 68);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 69);\n    i0.ɵɵelement(14, \"i\", 70);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 71);\n    i0.ɵɵtemplate(17, HomeComponent_div_71_div_17_Template, 4, 1, \"div\", 72)(18, HomeComponent_div_71_div_18_Template, 4, 1, \"div\", 72);\n    i0.ɵɵelementStart(19, \"div\", 73);\n    i0.ɵɵelement(20, \"i\", 74);\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 75)(24, \"span\", 76);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 77);\n    i0.ɵɵtext(27, \"\\u062C\\u0646\\u064A\\u0647\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const property_r4 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", property_r4.image, i0.ɵɵsanitizeUrl)(\"alt\", property_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", property_r4.isNew);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(property_r4.rating);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(property_r4.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", property_r4.location, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", property_r4.bedrooms > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", property_r4.bathrooms > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", property_r4.area, \" \\u0645\\u00B2\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(property_r4.price);\n  }\n}\nfunction HomeComponent_div_88_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 82);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_88_Template_div_click_1_listener() {\n      const city_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.viewCity(city_r7.name));\n    });\n    i0.ɵɵelementStart(2, \"div\", 83);\n    i0.ɵɵelement(3, \"img\", 62);\n    i0.ɵɵelementStart(4, \"div\", 84)(5, \"div\", 85)(6, \"h4\", 86);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 87);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const city_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", city_r7.image, i0.ɵɵsanitizeUrl)(\"alt\", city_r7.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(city_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", city_r7.propertiesCount, \" \\u0639\\u0642\\u0627\\u0631\");\n  }\n}\nfunction HomeComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 89);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_99_Template_div_click_1_listener() {\n      const article_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.readArticle(article_r9.id));\n    });\n    i0.ɵɵelementStart(2, \"div\", 90);\n    i0.ɵɵelement(3, \"img\", 62);\n    i0.ɵɵelementStart(4, \"div\", 91)(5, \"span\", 92);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 93)(8, \"h4\", 94);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 95);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 96)(13, \"div\", 97);\n    i0.ɵɵelement(14, \"i\", 98);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 99);\n    i0.ɵɵelement(18, \"i\", 100);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 101);\n    i0.ɵɵelement(22, \"i\", 102);\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const article_r9 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", article_r9.image, i0.ɵɵsanitizeUrl)(\"alt\", article_r9.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r9.category);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(article_r9.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(article_r9.excerpt);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(article_r9.author);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(article_r9.date);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(article_r9.readTime);\n  }\n}\nexport class HomeComponent {\n  // Hero section data\n  heroCards = [{\n    title: 'سهولة',\n    description: 'سهولة في التعامل والوصول للعقارات',\n    icon: 'ki-outline ki-check-circle',\n    color: 'success'\n  }, {\n    title: 'سرعة',\n    description: 'سرعة في إنجاز المعاملات',\n    icon: 'ki-outline ki-flash',\n    color: 'warning'\n  }, {\n    title: 'ثقة',\n    description: 'ثقة في التعامل مع أفضل المطورين',\n    icon: 'ki-outline ki-shield-tick',\n    color: 'primary'\n  }];\n  // Featured properties\n  featuredProperties = [{\n    id: 1,\n    title: 'شقة للبيع في التجمع الخامس',\n    location: 'التجمع الخامس، القاهرة الجديدة',\n    price: '2,500,000',\n    area: '120',\n    bedrooms: 3,\n    bathrooms: 2,\n    image: 'assets/media/properties/property-1.jpg',\n    rating: 4.5,\n    isNew: true\n  }, {\n    id: 2,\n    title: 'فيلا للبيع في الشيخ زايد',\n    location: 'الشيخ زايد، الجيزة',\n    price: '8,500,000',\n    area: '350',\n    bedrooms: 5,\n    bathrooms: 4,\n    image: 'assets/media/properties/property-2.jpg',\n    rating: 4.8,\n    isNew: false\n  }, {\n    id: 3,\n    title: 'شقة للإيجار في المعادي',\n    location: 'المعادي، القاهرة',\n    price: '15,000',\n    area: '90',\n    bedrooms: 2,\n    bathrooms: 1,\n    image: 'assets/media/properties/property-3.jpg',\n    rating: 4.2,\n    isNew: true\n  }, {\n    id: 4,\n    title: 'مكتب للبيع في وسط البلد',\n    location: 'وسط البلد، القاهرة',\n    price: '1,200,000',\n    area: '80',\n    bedrooms: 0,\n    bathrooms: 1,\n    image: 'assets/media/properties/property-4.jpg',\n    rating: 4.0,\n    isNew: false\n  }];\n  // Cities carousel\n  cities = [{\n    name: 'القاهرة الجديدة',\n    image: 'assets/media/cities/new-cairo.jpg',\n    propertiesCount: 1250\n  }, {\n    name: 'الشيخ زايد',\n    image: 'assets/media/cities/sheikh-zayed.jpg',\n    propertiesCount: 890\n  }, {\n    name: 'العاصمة الإدارية',\n    image: 'assets/media/cities/new-capital.jpg',\n    propertiesCount: 650\n  }, {\n    name: 'الساحل الشمالي',\n    image: 'assets/media/cities/north-coast.jpg',\n    propertiesCount: 420\n  }, {\n    name: 'العين السخنة',\n    image: 'assets/media/cities/ain-sokhna.jpg',\n    propertiesCount: 320\n  }];\n  // Blog articles\n  blogArticles = [{\n    id: 1,\n    title: 'نصائح لشراء العقار المناسب',\n    excerpt: 'دليل شامل لاختيار العقار المناسب لاحتياجاتك وميزانيتك',\n    image: 'assets/media/blog/article-1.jpg',\n    author: 'أحمد محمد',\n    date: '2024-01-15',\n    readTime: '5 دقائق',\n    category: 'نصائح عقارية'\n  }, {\n    id: 2,\n    title: 'استثمار العقارات في مصر',\n    excerpt: 'كيفية الاستثمار الناجح في العقارات والحصول على عائد مجزي',\n    image: 'assets/media/blog/article-2.jpg',\n    author: 'سارة أحمد',\n    date: '2024-01-10',\n    readTime: '7 دقائق',\n    category: 'استثمار'\n  }, {\n    id: 3,\n    title: 'أحدث المشاريع العقارية',\n    excerpt: 'تعرف على أحدث المشاريع العقارية في القاهرة الجديدة والعاصمة الإدارية',\n    image: 'assets/media/blog/article-3.jpg',\n    author: 'محمد علي',\n    date: '2024-01-05',\n    readTime: '4 دقائق',\n    category: 'مشاريع جديدة'\n  }];\n  constructor() {}\n  ngOnInit() {\n    // Initialize any required data or services\n  }\n  // Navigation methods\n  searchProperties() {\n    // Navigate to properties search page\n    console.log('Searching properties...');\n  }\n  viewProperty(propertyId) {\n    // Navigate to property details\n    console.log('Viewing property:', propertyId);\n  }\n  viewCity(cityName) {\n    // Navigate to city properties\n    console.log('Viewing city:', cityName);\n  }\n  readArticle(articleId) {\n    // Navigate to blog article\n    console.log('Reading article:', articleId);\n  }\n  static ɵfac = function HomeComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HomeComponent)();\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomeComponent,\n    selectors: [[\"app-home\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 121,\n    vars: 4,\n    consts: [[1, \"hero-section\"], [1, \"hero-background\"], [1, \"hero-overlay\"], [1, \"container\"], [1, \"row\", \"align-items-center\", \"min-vh-100\"], [1, \"col-lg-6\"], [1, \"hero-content\"], [1, \"hero-title\"], [1, \"text-primary\"], [1, \"hero-subtitle\"], [1, \"hero-search-form\"], [1, \"search-tabs\"], [1, \"search-tab\", \"active\"], [1, \"search-tab\"], [1, \"search-inputs\"], [1, \"search-input-group\"], [1, \"ki-outline\", \"ki-geolocation\", \"fs-2\"], [1, \"form-select\"], [1, \"ki-outline\", \"ki-home\", \"fs-2\"], [1, \"ki-outline\", \"ki-dollar\", \"fs-2\"], [1, \"btn\", \"btn-primary\", \"search-btn\", 3, \"click\"], [1, \"ki-outline\", \"ki-magnifier\", \"fs-2\"], [1, \"hero-cards\"], [\"class\", \"hero-card\", 3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"featured-properties-section\", \"py-15\"], [1, \"row\", \"mb-10\"], [1, \"col-12\", \"text-center\"], [1, \"section-title\"], [1, \"ki-outline\", \"ki-star\", \"fs-1\", \"text-warning\", \"me-3\"], [1, \"section-subtitle\"], [1, \"row\", \"g-6\"], [\"class\", \"col-lg-3 col-md-6\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"mt-10\"], [1, \"btn\", \"btn-outline-primary\", \"btn-lg\"], [1, \"ki-outline\", \"ki-arrow-left\", \"ms-2\"], [1, \"cities-section\", \"py-15\", \"bg-light\"], [1, \"ki-outline\", \"ki-map\", \"fs-1\", \"text-primary\", \"me-3\"], [1, \"cities-carousel\"], [1, \"row\", \"g-4\"], [\"class\", \"col-lg-2 col-md-4 col-6\", 4, \"ngFor\", \"ngForOf\"], [1, \"blog-section\", \"py-15\"], [1, \"ki-outline\", \"ki-book\", \"fs-1\", \"text-info\", \"me-3\"], [\"class\", \"col-lg-4 col-md-6\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-outline-info\", \"btn-lg\"], [1, \"newsletter-section\", \"py-15\", \"bg-primary\"], [1, \"row\", \"align-items-center\"], [1, \"newsletter-content\", \"text-white\"], [1, \"newsletter-title\"], [1, \"newsletter-subtitle\"], [1, \"newsletter-form\"], [1, \"input-group\"], [\"type\", \"email\", \"placeholder\", \"\\u0623\\u062F\\u062E\\u0644 \\u0628\\u0631\\u064A\\u062F\\u0643 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\", 1, \"form-control\", \"form-control-lg\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", \"btn-lg\"], [1, \"ki-outline\", \"ki-send\", \"fs-2\"], [1, \"hero-card\"], [1, \"hero-card-content\"], [1, \"hero-card-icon\"], [1, \"hero-card-title\"], [1, \"hero-card-description\"], [1, \"col-lg-3\", \"col-md-6\"], [1, \"property-card\", 3, \"click\"], [1, \"property-image\"], [1, \"w-100\", 3, \"src\", \"alt\"], [1, \"property-badges\"], [\"class\", \"badge badge-success\", 4, \"ngIf\"], [1, \"property-rating\"], [1, \"ki-solid\", \"ki-star\", \"text-warning\"], [1, \"property-content\"], [1, \"property-title\"], [1, \"property-location\"], [1, \"ki-outline\", \"ki-geolocation\", \"text-muted\", \"me-2\"], [1, \"property-details\"], [\"class\", \"property-detail\", 4, \"ngIf\"], [1, \"property-detail\"], [1, \"ki-outline\", \"ki-resize\", \"text-muted\"], [1, \"property-price\"], [1, \"price\"], [1, \"currency\"], [1, \"badge\", \"badge-success\"], [1, \"ki-outline\", \"ki-home\", \"text-muted\"], [1, \"ki-outline\", \"ki-droplet\", \"text-muted\"], [1, \"col-lg-2\", \"col-md-4\", \"col-6\"], [1, \"city-card\", 3, \"click\"], [1, \"city-image\"], [1, \"city-overlay\"], [1, \"city-content\"], [1, \"city-name\"], [1, \"city-count\"], [1, \"col-lg-4\", \"col-md-6\"], [1, \"article-card\", 3, \"click\"], [1, \"article-image\"], [1, \"article-category\"], [1, \"badge\", \"badge-primary\"], [1, \"article-content\"], [1, \"article-title\"], [1, \"article-excerpt\"], [1, \"article-meta\"], [1, \"article-author\"], [1, \"ki-outline\", \"ki-profile-user\", \"text-muted\", \"me-2\"], [1, \"article-date\"], [1, \"ki-outline\", \"ki-calendar\", \"text-muted\", \"me-2\"], [1, \"article-read-time\"], [1, \"ki-outline\", \"ki-time\", \"text-muted\", \"me-2\"]],\n    template: function HomeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"div\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"h1\", 7);\n        i0.ɵɵtext(8, \" \\u0627\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631 \\u0627\\u0644\\u0645\\u062B\\u0627\\u0644\\u064A \");\n        i0.ɵɵelementStart(9, \"span\", 8);\n        i0.ɵɵtext(10, \"\\u0628\\u0633\\u0647\\u0648\\u0644\\u0629 \\u0648\\u062B\\u0642\\u0629\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"p\", 9);\n        i0.ɵɵtext(12, \" \\u0645\\u0646\\u0635\\u0629 \\u0625\\u064A\\u0632\\u064A \\u062F\\u064A\\u0644 \\u062A\\u0642\\u062F\\u0645 \\u0644\\u0643 \\u0623\\u0641\\u0636\\u0644 \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631\\u0627\\u062A \\u0641\\u064A \\u0645\\u0635\\u0631 \\u0645\\u0639 \\u062E\\u062F\\u0645\\u0627\\u062A \\u0645\\u062A\\u0645\\u064A\\u0632\\u0629 \\u0648\\u0623\\u0633\\u0639\\u0627\\u0631 \\u062A\\u0646\\u0627\\u0641\\u0633\\u064A\\u0629 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"div\", 10)(14, \"div\", 11)(15, \"button\", 12);\n        i0.ɵɵtext(16, \"\\u0634\\u0631\\u0627\\u0621\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"button\", 13);\n        i0.ɵɵtext(18, \"\\u0625\\u064A\\u062C\\u0627\\u0631\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"button\", 13);\n        i0.ɵɵtext(20, \"\\u0627\\u0633\\u062A\\u062B\\u0645\\u0627\\u0631\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(21, \"div\", 14)(22, \"div\", 15);\n        i0.ɵɵelement(23, \"i\", 16);\n        i0.ɵɵelementStart(24, \"select\", 17)(25, \"option\");\n        i0.ɵɵtext(26, \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0645\\u062F\\u064A\\u0646\\u0629\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"option\");\n        i0.ɵɵtext(28, \"\\u0627\\u0644\\u0642\\u0627\\u0647\\u0631\\u0629 \\u0627\\u0644\\u062C\\u062F\\u064A\\u062F\\u0629\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"option\");\n        i0.ɵɵtext(30, \"\\u0627\\u0644\\u0634\\u064A\\u062E \\u0632\\u0627\\u064A\\u062F\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"option\");\n        i0.ɵɵtext(32, \"\\u0627\\u0644\\u0639\\u0627\\u0635\\u0645\\u0629 \\u0627\\u0644\\u0625\\u062F\\u0627\\u0631\\u064A\\u0629\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(33, \"div\", 15);\n        i0.ɵɵelement(34, \"i\", 18);\n        i0.ɵɵelementStart(35, \"select\", 17)(36, \"option\");\n        i0.ɵɵtext(37, \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"option\");\n        i0.ɵɵtext(39, \"\\u0634\\u0642\\u0629\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"option\");\n        i0.ɵɵtext(41, \"\\u0641\\u064A\\u0644\\u0627\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"option\");\n        i0.ɵɵtext(43, \"\\u0645\\u0643\\u062A\\u0628\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(44, \"div\", 15);\n        i0.ɵɵelement(45, \"i\", 19);\n        i0.ɵɵelementStart(46, \"select\", 17)(47, \"option\");\n        i0.ɵɵtext(48, \"\\u0627\\u0644\\u0645\\u064A\\u0632\\u0627\\u0646\\u064A\\u0629\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(49, \"option\");\n        i0.ɵɵtext(50, \"\\u0623\\u0642\\u0644 \\u0645\\u0646 \\u0645\\u0644\\u064A\\u0648\\u0646\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(51, \"option\");\n        i0.ɵɵtext(52, \"1-3 \\u0645\\u0644\\u064A\\u0648\\u0646\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(53, \"option\");\n        i0.ɵɵtext(54, \"\\u0623\\u0643\\u062B\\u0631 \\u0645\\u0646 3 \\u0645\\u0644\\u064A\\u0648\\u0646\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(55, \"button\", 20);\n        i0.ɵɵlistener(\"click\", function HomeComponent_Template_button_click_55_listener() {\n          return ctx.searchProperties();\n        });\n        i0.ɵɵelement(56, \"i\", 21);\n        i0.ɵɵtext(57, \" \\u0627\\u0628\\u062D\\u062B \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(58, \"div\", 5)(59, \"div\", 22);\n        i0.ɵɵtemplate(60, HomeComponent_div_60_Template, 8, 8, \"div\", 23);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(61, \"section\", 24)(62, \"div\", 3)(63, \"div\", 25)(64, \"div\", 26)(65, \"h2\", 27);\n        i0.ɵɵelement(66, \"i\", 28);\n        i0.ɵɵtext(67, \" \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631\\u0627\\u062A \\u0627\\u0644\\u0645\\u0645\\u064A\\u0632\\u0629 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(68, \"p\", 29);\n        i0.ɵɵtext(69, \"\\u0627\\u0643\\u062A\\u0634\\u0641 \\u0623\\u0641\\u0636\\u0644 \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631\\u0627\\u062A \\u0627\\u0644\\u0645\\u062A\\u0627\\u062D\\u0629 \\u062D\\u0627\\u0644\\u064A\\u0627\\u064B\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(70, \"div\", 30);\n        i0.ɵɵtemplate(71, HomeComponent_div_71_Template, 28, 10, \"div\", 31);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(72, \"div\", 32)(73, \"div\", 26)(74, \"button\", 33);\n        i0.ɵɵtext(75, \" \\u0639\\u0631\\u0636 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631\\u0627\\u062A \");\n        i0.ɵɵelement(76, \"i\", 34);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(77, \"section\", 35)(78, \"div\", 3)(79, \"div\", 25)(80, \"div\", 26)(81, \"h2\", 27);\n        i0.ɵɵelement(82, \"i\", 36);\n        i0.ɵɵtext(83, \" \\u0627\\u0633\\u062A\\u0643\\u0634\\u0641 \\u0627\\u0644\\u0645\\u062F\\u0646 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(84, \"p\", 29);\n        i0.ɵɵtext(85, \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0645\\u062F\\u064A\\u0646\\u0629 \\u0627\\u0644\\u062A\\u064A \\u062A\\u0646\\u0627\\u0633\\u0628\\u0643\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(86, \"div\", 37)(87, \"div\", 38);\n        i0.ɵɵtemplate(88, HomeComponent_div_88_Template, 10, 4, \"div\", 39);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(89, \"section\", 40)(90, \"div\", 3)(91, \"div\", 25)(92, \"div\", 26)(93, \"h2\", 27);\n        i0.ɵɵelement(94, \"i\", 41);\n        i0.ɵɵtext(95, \" \\u0645\\u0642\\u0627\\u0644\\u0627\\u062A \\u062A\\u0647\\u0645\\u0643 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(96, \"p\", 29);\n        i0.ɵɵtext(97, \"\\u0627\\u0642\\u0631\\u0623 \\u0623\\u062D\\u062F\\u062B \\u0627\\u0644\\u0645\\u0642\\u0627\\u0644\\u0627\\u062A \\u0648\\u0627\\u0644\\u0646\\u0635\\u0627\\u0626\\u062D \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631\\u064A\\u0629\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(98, \"div\", 30);\n        i0.ɵɵtemplate(99, HomeComponent_div_99_Template, 25, 8, \"div\", 42);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(100, \"div\", 32)(101, \"div\", 26)(102, \"button\", 43);\n        i0.ɵɵtext(103, \" \\u0639\\u0631\\u0636 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0645\\u0642\\u0627\\u0644\\u0627\\u062A \");\n        i0.ɵɵelement(104, \"i\", 34);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(105, \"section\", 44)(106, \"div\", 3)(107, \"div\", 45)(108, \"div\", 5)(109, \"div\", 46)(110, \"h3\", 47);\n        i0.ɵɵtext(111, \"\\u0627\\u0634\\u062A\\u0631\\u0643 \\u0641\\u064A \\u0627\\u0644\\u0646\\u0634\\u0631\\u0629 \\u0627\\u0644\\u0625\\u062E\\u0628\\u0627\\u0631\\u064A\\u0629\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(112, \"p\", 48);\n        i0.ɵɵtext(113, \" \\u0627\\u062D\\u0635\\u0644 \\u0639\\u0644\\u0649 \\u0623\\u062D\\u062F\\u062B \\u0627\\u0644\\u0639\\u0631\\u0648\\u0636 \\u0648\\u0627\\u0644\\u0645\\u0642\\u0627\\u0644\\u0627\\u062A \\u0627\\u0644\\u0639\\u0642\\u0627\\u0631\\u064A\\u0629 \\u0645\\u0628\\u0627\\u0634\\u0631\\u0629 \\u0641\\u064A \\u0628\\u0631\\u064A\\u062F\\u0643 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(114, \"div\", 5)(115, \"div\", 49)(116, \"div\", 50);\n        i0.ɵɵelement(117, \"input\", 51);\n        i0.ɵɵelementStart(118, \"button\", 52);\n        i0.ɵɵelement(119, \"i\", 53);\n        i0.ɵɵtext(120, \" \\u0627\\u0634\\u062A\\u0631\\u0643 \");\n        i0.ɵɵelementEnd()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(60);\n        i0.ɵɵproperty(\"ngForOf\", ctx.heroCards);\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"ngForOf\", ctx.featuredProperties);\n        i0.ɵɵadvance(17);\n        i0.ɵɵproperty(\"ngForOf\", ctx.cities);\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"ngForOf\", ctx.blogArticles);\n      }\n    },\n    dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule],\n    styles: [\".hero-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  min-height: 100vh;\\n  overflow: hidden;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);\\n  background-image: url(\\\"~src/assets/media/bg/hero-bg.jpg\\\");\\n  background-size: cover;\\n  background-position: center;\\n  background-attachment: fixed;\\n  min-height: 100vh;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .hero-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(30, 60, 114, 0.8);\\n  z-index: 1;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%] {\\n  color: white;\\n  padding: 2rem 0;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n  font-size: 3.5rem;\\n  font-weight: 700;\\n  line-height: 1.2;\\n  margin-bottom: 1.5rem;\\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  margin-bottom: 2rem;\\n  opacity: 0.9;\\n  line-height: 1.6;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-tabs[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 1.5rem;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  background: #f8f9fa;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-tabs[_ngcontent-%COMP%]   .search-tab[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0.75rem 1rem;\\n  border: none;\\n  background: transparent;\\n  color: #6c757d;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-tabs[_ngcontent-%COMP%]   .search-tab.active[_ngcontent-%COMP%] {\\n  background: var(--bs-primary);\\n  color: white;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-tabs[_ngcontent-%COMP%]   .search-tab[_ngcontent-%COMP%]:hover:not(.active) {\\n  background: #e9ecef;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr 1fr auto;\\n  gap: 1rem;\\n  align-items: end;\\n}\\n@media (max-width: 768px) {\\n  .hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 1rem;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: #6c757d;\\n  z-index: 3;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%] {\\n  padding-left: 3rem;\\n  border: 2px solid #e9ecef;\\n  border-radius: 10px;\\n  height: 50px;\\n  font-weight: 500;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]:focus {\\n  border-color: var(--bs-primary);\\n  box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%] {\\n  height: 50px;\\n  padding: 0 2rem;\\n  border-radius: 10px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  white-space: nowrap;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr;\\n  gap: 2rem;\\n  padding: 2rem 0;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card.hero-card-1[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInRight 0.6s ease-out 0.2s both;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card.hero-card-2[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInRight 0.6s ease-out 0.4s both;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card.hero-card-3[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInRight 0.6s ease-out 0.6s both;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card[_ngcontent-%COMP%]   .hero-card-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card[_ngcontent-%COMP%]   .hero-card-content[_ngcontent-%COMP%]   .hero-card-icon[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto 1.5rem;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card[_ngcontent-%COMP%]   .hero-card-content[_ngcontent-%COMP%]   .hero-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  margin-bottom: 1rem;\\n  color: #2c3e50;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card[_ngcontent-%COMP%]   .hero-card-content[_ngcontent-%COMP%]   .hero-card-description[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  line-height: 1.6;\\n  margin: 0;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInRight {\\n  from {\\n    opacity: 0;\\n    transform: translateX(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 1rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n@media (max-width: 768px) {\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n}\\n\\n.section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  color: #6c757d;\\n  margin-bottom: 0;\\n}\\n\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  height: 100%;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-badges[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  z-index: 2;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-image[_ngcontent-%COMP%]   .property-rating[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 1rem;\\n  left: 1rem;\\n  background: rgba(255, 255, 255, 0.9);\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 20px;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-title[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n  color: #2c3e50;\\n  line-height: 1.4;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-location[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-bottom: 1rem;\\n  display: flex;\\n  align-items: center;\\n  font-size: 0.875rem;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n  flex-wrap: wrap;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-details[_ngcontent-%COMP%]   .property-detail[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-details[_ngcontent-%COMP%]   .property-detail[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: baseline;\\n  gap: 0.5rem;\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: var(--bs-primary);\\n}\\n.featured-properties-section[_ngcontent-%COMP%]   .property-card[_ngcontent-%COMP%]   .property-content[_ngcontent-%COMP%]   .property-price[_ngcontent-%COMP%]   .currency[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.875rem;\\n}\\n\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%] {\\n  border-radius: 15px;\\n  overflow: hidden;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  height: 200px;\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]   .city-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]   .city-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]   .city-image[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]   .city-image[_ngcontent-%COMP%]   .city-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);\\n  display: flex;\\n  align-items: end;\\n  padding: 1.5rem;\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]   .city-image[_ngcontent-%COMP%]   .city-overlay[_ngcontent-%COMP%]   .city-content[_ngcontent-%COMP%] {\\n  color: white;\\n  text-align: center;\\n  width: 100%;\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]   .city-image[_ngcontent-%COMP%]   .city-overlay[_ngcontent-%COMP%]   .city-content[_ngcontent-%COMP%]   .city-name[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  margin-bottom: 0.5rem;\\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\\n}\\n.cities-section[_ngcontent-%COMP%]   .city-card[_ngcontent-%COMP%]   .city-image[_ngcontent-%COMP%]   .city-overlay[_ngcontent-%COMP%]   .city-content[_ngcontent-%COMP%]   .city-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  opacity: 0.9;\\n  margin: 0;\\n}\\n\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  height: 100%;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-image[_ngcontent-%COMP%]   .article-category[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  z-index: 2;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   .article-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  margin-bottom: 1rem;\\n  color: #2c3e50;\\n  line-height: 1.4;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   .article-excerpt[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-bottom: 1.5rem;\\n  line-height: 1.6;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   .article-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n}\\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   .article-meta[_ngcontent-%COMP%]   .article-author[_ngcontent-%COMP%], \\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   .article-meta[_ngcontent-%COMP%]   .article-date[_ngcontent-%COMP%], \\n.blog-section[_ngcontent-%COMP%]   .article-card[_ngcontent-%COMP%]   .article-content[_ngcontent-%COMP%]   .article-meta[_ngcontent-%COMP%]   .article-read-time[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #6c757d;\\n}\\n\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-content[_ngcontent-%COMP%]   .newsletter-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  margin-bottom: 1rem;\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-content[_ngcontent-%COMP%]   .newsletter-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  opacity: 0.9;\\n  margin: 0;\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 10px 0 0 10px;\\n  padding: 1rem 1.5rem;\\n  font-size: 1rem;\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: transparent;\\n}\\n.newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 0 10px 10px 0;\\n  padding: 1rem 2rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  white-space: nowrap;\\n}\\n\\n@media (max-width: 768px) {\\n  .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 1rem;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-cards[_ngcontent-%COMP%]   .hero-card[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n  .newsletter-section[_ngcontent-%COMP%]   .newsletter-content[_ngcontent-%COMP%] {\\n    text-align: center;\\n    margin-bottom: 2rem;\\n  }\\n  .newsletter-section[_ngcontent-%COMP%]   .newsletter-content[_ngcontent-%COMP%]   .newsletter-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    border-radius: 10px;\\n    margin-bottom: 1rem;\\n  }\\n  .newsletter-section[_ngcontent-%COMP%]   .newsletter-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    border-radius: 10px;\\n    justify-content: center;\\n  }\\n}\\n.fade-in[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.6s ease-out;\\n}\\n\\n.slide-up[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideUp 0.6s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.bg-light-dark-blue[_ngcontent-%COMP%] {\\n  background-color: rgba(var(--bs-primary-rgb), 0.1) !important;\\n}\\n\\n.text-dark-blue[_ngcontent-%COMP%] {\\n  color: var(--bs-primary) !important;\\n}\\n\\n.btn-dark-blue[_ngcontent-%COMP%] {\\n  background-color: var(--bs-primary);\\n  border-color: var(--bs-primary);\\n  color: white;\\n}\\n.btn-dark-blue[_ngcontent-%COMP%]:hover, .btn-dark-blue[_ngcontent-%COMP%]:focus, .btn-dark-blue[_ngcontent-%COMP%]:active {\\n  background-color: rgba(var(--bs-primary-rgb), 0.9);\\n  border-color: rgba(var(--bs-primary-rgb), 0.9);\\n  color: white;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG9tZS9ob21lLmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vLi4vLi4vLi4vLi4vLi4vY29tcHUlMjB6b25lL0Rlc2t0b3AvdGFza2VzL05ldyUyMGZvbGRlci9lYXN5ZGVhbC1mcm9udGVuZC9zcmMvYXBwL3BhZ2VzL2hvbWUvaG9tZS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDQTtFQUNFLGtCQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtBQ0FGO0FERUU7RUFDRSxrQkFBQTtFQUNBLDZEQUFBO0VBQ0EseURBQUE7RUFDQSxzQkFBQTtFQUNBLDJCQUFBO0VBQ0EsNEJBQUE7RUFDQSxpQkFBQTtBQ0FKO0FERUk7RUFDRSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxrQ0FBQTtFQUNBLFVBQUE7QUNBTjtBREdJO0VBQ0Usa0JBQUE7RUFDQSxVQUFBO0FDRE47QURLRTtFQUNFLFlBQUE7RUFDQSxlQUFBO0FDSEo7QURLSTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLHFCQUFBO0VBQ0EsMkNBQUE7QUNITjtBREtNO0VBUEY7SUFRSSxpQkFBQTtFQ0ZOO0FBQ0Y7QURLSTtFQUNFLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7QUNITjtBRE9FO0VBQ0UscUNBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSwwQ0FBQTtFQUNBLG1DQUFBO1VBQUEsMkJBQUE7QUNMSjtBRE9JO0VBQ0UsYUFBQTtFQUNBLHFCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0FDTE47QURPTTtFQUNFLE9BQUE7RUFDQSxxQkFBQTtFQUNBLFlBQUE7RUFDQSx1QkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0EsZUFBQTtBQ0xSO0FET1E7RUFDRSw2QkFBQTtFQUNBLFlBQUE7QUNMVjtBRFFRO0VBQ0UsbUJBQUE7QUNOVjtBRFdJO0VBQ0UsYUFBQTtFQUNBLHVDQUFBO0VBQ0EsU0FBQTtFQUNBLGdCQUFBO0FDVE47QURXTTtFQU5GO0lBT0ksMEJBQUE7RUNSTjtBQUNGO0FEVU07RUFDRSxrQkFBQTtBQ1JSO0FEVVE7RUFDRSxrQkFBQTtFQUNBLFVBQUE7RUFDQSxRQUFBO0VBQ0EsMkJBQUE7RUFDQSxjQUFBO0VBQ0EsVUFBQTtBQ1JWO0FEV1E7RUFDRSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7QUNUVjtBRFdVO0VBQ0UsK0JBQUE7RUFDQSwwREFBQTtBQ1RaO0FEY007RUFDRSxZQUFBO0VBQ0EsZUFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7QUNaUjtBRGlCRTtFQUNFLGFBQUE7RUFDQSwwQkFBQTtFQUNBLFNBQUE7RUFDQSxlQUFBO0FDZko7QURpQkk7RUFDRSxxQ0FBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLDBDQUFBO0VBQ0EsbUNBQUE7VUFBQSwyQkFBQTtFQUNBLHlCQUFBO0VBQ0EsZUFBQTtBQ2ZOO0FEaUJNO0VBQ0UsMkJBQUE7RUFDQSwyQ0FBQTtBQ2ZSO0FEa0JNO0VBQ0UsK0NBQUE7QUNoQlI7QURtQk07RUFDRSwrQ0FBQTtBQ2pCUjtBRG9CTTtFQUNFLCtDQUFBO0FDbEJSO0FEcUJNO0VBQ0Usa0JBQUE7QUNuQlI7QURxQlE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxxQkFBQTtBQ25CVjtBRHNCUTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLGNBQUE7QUNwQlY7QUR1QlE7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxTQUFBO0FDckJWOztBRDRCQTtFQUNFO0lBQ0UsVUFBQTtJQUNBLDJCQUFBO0VDekJGO0VEMkJBO0lBQ0UsVUFBQTtJQUNBLHdCQUFBO0VDekJGO0FBQ0Y7QUQ2QkE7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUMzQkY7QUQ2QkU7RUFURjtJQVVJLGVBQUE7SUFDQSxzQkFBQTtJQUNBLFdBQUE7RUMxQkY7QUFDRjs7QUQ2QkE7RUFDRSxtQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQzFCRjs7QUQrQkU7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSwwQ0FBQTtFQUNBLHlCQUFBO0VBQ0EsZUFBQTtFQUNBLFlBQUE7QUM1Qko7QUQ4Qkk7RUFDRSwyQkFBQTtFQUNBLDJDQUFBO0FDNUJOO0FEK0JJO0VBQ0Usa0JBQUE7RUFDQSxhQUFBO0VBQ0EsZ0JBQUE7QUM3Qk47QUQrQk07RUFDRSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSwrQkFBQTtBQzdCUjtBRGdDTTtFQUNFLHNCQUFBO0FDOUJSO0FEaUNNO0VBQ0Usa0JBQUE7RUFDQSxTQUFBO0VBQ0EsV0FBQTtFQUNBLFVBQUE7QUMvQlI7QURrQ007RUFDRSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxVQUFBO0VBQ0Esb0NBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7QUNoQ1I7QURvQ0k7RUFDRSxlQUFBO0FDbENOO0FEb0NNO0VBQ0UsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHFCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FDbENSO0FEcUNNO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7QUNuQ1I7QURzQ007RUFDRSxhQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtBQ3BDUjtBRHNDUTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGNBQUE7QUNwQ1Y7QURzQ1U7RUFDRSxlQUFBO0FDcENaO0FEeUNNO0VBQ0UsYUFBQTtFQUNBLHFCQUFBO0VBQ0EsV0FBQTtBQ3ZDUjtBRHlDUTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx3QkFBQTtBQ3ZDVjtBRDBDUTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtBQ3hDVjs7QURpREU7RUFDRSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0VBQ0EsYUFBQTtBQzlDSjtBRGdESTtFQUNFLDJCQUFBO0VBQ0EsMkNBQUE7QUM5Q047QURpREk7RUFDRSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtBQy9DTjtBRGlETTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSwrQkFBQTtBQy9DUjtBRGtETTtFQUNFLHFCQUFBO0FDaERSO0FEbURNO0VBQ0Usa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0EsK0VBQUE7RUFDQSxhQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0FDakRSO0FEbURRO0VBQ0UsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtBQ2pEVjtBRG1EVTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxxQkFBQTtFQUNBLDJDQUFBO0FDakRaO0FEb0RVO0VBQ0UsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsU0FBQTtBQ2xEWjs7QUQ0REU7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSwwQ0FBQTtFQUNBLHlCQUFBO0VBQ0EsZUFBQTtFQUNBLFlBQUE7QUN6REo7QUQyREk7RUFDRSwyQkFBQTtFQUNBLDJDQUFBO0FDekROO0FENERJO0VBQ0Usa0JBQUE7RUFDQSxhQUFBO0VBQ0EsZ0JBQUE7QUMxRE47QUQ0RE07RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGlCQUFBO0VBQ0EsK0JBQUE7QUMxRFI7QUQ2RE07RUFDRSxzQkFBQTtBQzNEUjtBRDhETTtFQUNFLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFdBQUE7RUFDQSxVQUFBO0FDNURSO0FEZ0VJO0VBQ0UsZUFBQTtBQzlETjtBRGdFTTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQzlEUjtBRGlFTTtFQUNFLGNBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0FDL0RSO0FEa0VNO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0FDaEVSO0FEa0VROzs7RUFHRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxjQUFBO0FDaEVWOztBRDBFSTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0FDdkVOO0FEMEVJO0VBQ0UsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsU0FBQTtBQ3hFTjtBRDhFTTtFQUNFLFlBQUE7RUFDQSw0QkFBQTtFQUNBLG9CQUFBO0VBQ0EsZUFBQTtBQzVFUjtBRDhFUTtFQUNFLGdCQUFBO0VBQ0EseUJBQUE7QUM1RVY7QURnRk07RUFDRSw0QkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7QUM5RVI7O0FEcUZBO0VBR007SUFDRSxlQUFBO0VDcEZOO0VEdUZJO0lBQ0UsZUFBQTtFQ3JGTjtFRHlGRTtJQUNFLGVBQUE7RUN2Rko7RUR5Rkk7SUFDRSwwQkFBQTtJQUNBLFNBQUE7RUN2Rk47RUQyRkU7SUFDRSxTQUFBO0VDekZKO0VEMkZJO0lBQ0UsZUFBQTtFQ3pGTjtFRDhGQTtJQUNFLGtCQUFBO0VDNUZGO0VEZ0dFO0lBQ0Usa0JBQUE7SUFDQSxtQkFBQTtFQzlGSjtFRGdHSTtJQUNFLGlCQUFBO0VDOUZOO0VEbUdJO0lBQ0Usc0JBQUE7RUNqR047RURtR007SUFDRSxtQkFBQTtJQUNBLG1CQUFBO0VDakdSO0VEb0dNO0lBQ0UsbUJBQUE7SUFDQSx1QkFBQTtFQ2xHUjtBQUNGO0FEeUdBO0VBQ0UsK0JBQUE7QUN2R0Y7O0FEMEdBO0VBQ0UsZ0NBQUE7QUN2R0Y7O0FEMEdBO0VBQ0U7SUFDRSxVQUFBO0VDdkdGO0VEeUdBO0lBQ0UsVUFBQTtFQ3ZHRjtBQUNGO0FEMEdBO0VBQ0U7SUFDRSxVQUFBO0lBQ0EsMkJBQUE7RUN4R0Y7RUQwR0E7SUFDRSxVQUFBO0lBQ0Esd0JBQUE7RUN4R0Y7QUFDRjtBRDRHQTtFQUNFLDZEQUFBO0FDMUdGOztBRDZHQTtFQUNFLG1DQUFBO0FDMUdGOztBRDZHQTtFQUNFLG1DQUFBO0VBQ0EsK0JBQUE7RUFDQSxZQUFBO0FDMUdGO0FENEdFO0VBR0Usa0RBQUE7RUFDQSw4Q0FBQTtFQUNBLFlBQUE7QUM1R0oiLCJzb3VyY2VzQ29udGVudCI6WyIvLyBIZXJvIFNlY3Rpb25cclxuLmhlcm8tc2VjdGlvbiB7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcblxyXG4gIC5oZXJvLWJhY2tncm91bmQge1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFlM2M3MiAwJSwgIzJhNTI5OCAxMDAlKTtcclxuICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgnfnNyYy9hc3NldHMvbWVkaWEvYmcvaGVyby1iZy5qcGcnKTtcclxuICAgIGJhY2tncm91bmQtc2l6ZTogY292ZXI7XHJcbiAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiBjZW50ZXI7XHJcbiAgICBiYWNrZ3JvdW5kLWF0dGFjaG1lbnQ6IGZpeGVkO1xyXG4gICAgbWluLWhlaWdodDogMTAwdmg7XHJcblxyXG4gICAgLmhlcm8tb3ZlcmxheSB7XHJcbiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgICAgdG9wOiAwO1xyXG4gICAgICBsZWZ0OiAwO1xyXG4gICAgICByaWdodDogMDtcclxuICAgICAgYm90dG9tOiAwO1xyXG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDMwLCA2MCwgMTE0LCAwLjgpO1xyXG4gICAgICB6LWluZGV4OiAxO1xyXG4gICAgfVxyXG5cclxuICAgIC5jb250YWluZXIge1xyXG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICAgIHotaW5kZXg6IDI7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuaGVyby1jb250ZW50IHtcclxuICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgIHBhZGRpbmc6IDJyZW0gMDtcclxuXHJcbiAgICAuaGVyby10aXRsZSB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMy41cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNzAwO1xyXG4gICAgICBsaW5lLWhlaWdodDogMS4yO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XHJcbiAgICAgIHRleHQtc2hhZG93OiAycHggMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMyk7XHJcblxyXG4gICAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcclxuICAgICAgICBmb250LXNpemU6IDIuNXJlbTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5oZXJvLXN1YnRpdGxlIHtcclxuICAgICAgZm9udC1zaXplOiAxLjI1cmVtO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAycmVtO1xyXG4gICAgICBvcGFjaXR5OiAwLjk7XHJcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuaGVyby1zZWFyY2gtZm9ybSB7XHJcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOTUpO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTVweDtcclxuICAgIHBhZGRpbmc6IDJyZW07XHJcbiAgICBib3gtc2hhZG93OiAwIDIwcHggNDBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7XHJcblxyXG4gICAgLnNlYXJjaC10YWJzIHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xyXG4gICAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xyXG5cclxuICAgICAgLnNlYXJjaC10YWIge1xyXG4gICAgICAgIGZsZXg6IDE7XHJcbiAgICAgICAgcGFkZGluZzogMC43NXJlbSAxcmVtO1xyXG4gICAgICAgIGJvcmRlcjogbm9uZTtcclxuICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxuICAgICAgICBjb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbiAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG5cclxuICAgICAgICAmLmFjdGl2ZSB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1icy1wcmltYXJ5KTtcclxuICAgICAgICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgICY6aG92ZXI6bm90KC5hY3RpdmUpIHtcclxuICAgICAgICAgIGJhY2tncm91bmQ6ICNlOWVjZWY7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLnNlYXJjaC1pbnB1dHMge1xyXG4gICAgICBkaXNwbGF5OiBncmlkO1xyXG4gICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnIgMWZyIGF1dG87XHJcbiAgICAgIGdhcDogMXJlbTtcclxuICAgICAgYWxpZ24taXRlbXM6IGVuZDtcclxuXHJcbiAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xyXG4gICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAuc2VhcmNoLWlucHV0LWdyb3VwIHtcclxuICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcblxyXG4gICAgICAgIGkge1xyXG4gICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgbGVmdDogMXJlbTtcclxuICAgICAgICAgIHRvcDogNTAlO1xyXG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpO1xyXG4gICAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgICB6LWluZGV4OiAzO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmZvcm0tc2VsZWN0IHtcclxuICAgICAgICAgIHBhZGRpbmctbGVmdDogM3JlbTtcclxuICAgICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICNlOWVjZWY7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xyXG4gICAgICAgICAgaGVpZ2h0OiA1MHB4O1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuXHJcbiAgICAgICAgICAmOmZvY3VzIHtcclxuICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiB2YXIoLS1icy1wcmltYXJ5KTtcclxuICAgICAgICAgICAgYm94LXNoYWRvdzogMCAwIDAgMC4ycmVtIHJnYmEodmFyKC0tYnMtcHJpbWFyeS1yZ2IpLCAwLjI1KTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5zZWFyY2gtYnRuIHtcclxuICAgICAgICBoZWlnaHQ6IDUwcHg7XHJcbiAgICAgICAgcGFkZGluZzogMCAycmVtO1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgZ2FwOiAwLjVyZW07XHJcbiAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmhlcm8tY2FyZHMge1xyXG4gICAgZGlzcGxheTogZ3JpZDtcclxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xyXG4gICAgZ2FwOiAycmVtO1xyXG4gICAgcGFkZGluZzogMnJlbSAwO1xyXG5cclxuICAgIC5oZXJvLWNhcmQge1xyXG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOTUpO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiAxNXB4O1xyXG4gICAgICBwYWRkaW5nOiAycmVtO1xyXG4gICAgICBib3gtc2hhZG93OiAwIDEwcHggMzBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcclxuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG5cclxuICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xyXG4gICAgICAgIGJveC1zaGFkb3c6IDAgMjBweCA0MHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgICYuaGVyby1jYXJkLTEge1xyXG4gICAgICAgIGFuaW1hdGlvbjogc2xpZGVJblJpZ2h0IDAuNnMgZWFzZS1vdXQgMC4ycyBib3RoO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmLmhlcm8tY2FyZC0yIHtcclxuICAgICAgICBhbmltYXRpb246IHNsaWRlSW5SaWdodCAwLjZzIGVhc2Utb3V0IDAuNHMgYm90aDtcclxuICAgICAgfVxyXG5cclxuICAgICAgJi5oZXJvLWNhcmQtMyB7XHJcbiAgICAgICAgYW5pbWF0aW9uOiBzbGlkZUluUmlnaHQgMC42cyBlYXNlLW91dCAwLjZzIGJvdGg7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5oZXJvLWNhcmQtY29udGVudCB7XHJcbiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG5cclxuICAgICAgICAuaGVyby1jYXJkLWljb24ge1xyXG4gICAgICAgICAgd2lkdGg6IDgwcHg7XHJcbiAgICAgICAgICBoZWlnaHQ6IDgwcHg7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICAgICAgbWFyZ2luOiAwIGF1dG8gMS41cmVtO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmhlcm8tY2FyZC10aXRsZSB7XHJcbiAgICAgICAgICBmb250LXNpemU6IDEuNXJlbTtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XHJcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG4gICAgICAgICAgY29sb3I6ICMyYzNlNTA7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuaGVyby1jYXJkLWRlc2NyaXB0aW9uIHtcclxuICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNjtcclxuICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbkBrZXlmcmFtZXMgc2xpZGVJblJpZ2h0IHtcclxuICBmcm9tIHtcclxuICAgIG9wYWNpdHk6IDA7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMzBweCk7XHJcbiAgfVxyXG4gIHRvIHtcclxuICAgIG9wYWNpdHk6IDE7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMCk7XHJcbiAgfVxyXG59XHJcblxyXG4vLyBTZWN0aW9uIFN0eWxlc1xyXG4uc2VjdGlvbi10aXRsZSB7XHJcbiAgZm9udC1zaXplOiAyLjVyZW07XHJcbiAgZm9udC13ZWlnaHQ6IDcwMDtcclxuICBjb2xvcjogIzJjM2U1MDtcclxuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuXHJcbiAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgICBmb250LXNpemU6IDJyZW07XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgZ2FwOiAwLjVyZW07XHJcbiAgfVxyXG59XHJcblxyXG4uc2VjdGlvbi1zdWJ0aXRsZSB7XHJcbiAgZm9udC1zaXplOiAxLjEyNXJlbTtcclxuICBjb2xvcjogIzZjNzU3ZDtcclxuICBtYXJnaW4tYm90dG9tOiAwO1xyXG59XHJcblxyXG4vLyBGZWF0dXJlZCBQcm9wZXJ0aWVzIFNlY3Rpb25cclxuLmZlYXR1cmVkLXByb3BlcnRpZXMtc2VjdGlvbiB7XHJcbiAgLnByb3BlcnR5LWNhcmQge1xyXG4gICAgYmFja2dyb3VuZDogd2hpdGU7XHJcbiAgICBib3JkZXItcmFkaXVzOiAxNXB4O1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgIGJveC1zaGFkb3c6IDAgNXB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjA4KTtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbiAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICBoZWlnaHQ6IDEwMCU7XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTtcclxuICAgICAgYm94LXNoYWRvdzogMCAxNXB4IDM1cHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcclxuICAgIH1cclxuXHJcbiAgICAucHJvcGVydHktaW1hZ2Uge1xyXG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICAgIGhlaWdodDogMjAwcHg7XHJcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcblxyXG4gICAgICBpbWcge1xyXG4gICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjtcclxuICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmOmhvdmVyIGltZyB7XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLnByb3BlcnR5LWJhZGdlcyB7XHJcbiAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgIHRvcDogMXJlbTtcclxuICAgICAgICByaWdodDogMXJlbTtcclxuICAgICAgICB6LWluZGV4OiAyO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAucHJvcGVydHktcmF0aW5nIHtcclxuICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgdG9wOiAxcmVtO1xyXG4gICAgICAgIGxlZnQ6IDFyZW07XHJcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpO1xyXG4gICAgICAgIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtO1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDIwcHg7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgIGdhcDogMC4yNXJlbTtcclxuICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAucHJvcGVydHktY29udGVudCB7XHJcbiAgICAgIHBhZGRpbmc6IDEuNXJlbTtcclxuXHJcbiAgICAgIC5wcm9wZXJ0eS10aXRsZSB7XHJcbiAgICAgICAgZm9udC1zaXplOiAxLjEyNXJlbTtcclxuICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcclxuICAgICAgICBjb2xvcjogIzJjM2U1MDtcclxuICAgICAgICBsaW5lLWhlaWdodDogMS40O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAucHJvcGVydHktbG9jYXRpb24ge1xyXG4gICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5wcm9wZXJ0eS1kZXRhaWxzIHtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGdhcDogMXJlbTtcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG4gICAgICAgIGZsZXgtd3JhcDogd3JhcDtcclxuXHJcbiAgICAgICAgLnByb3BlcnR5LWRldGFpbCB7XHJcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgIGdhcDogMC4yNXJlbTtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICBjb2xvcjogIzZjNzU3ZDtcclxuXHJcbiAgICAgICAgICBpIHtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAxcmVtO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLnByb3BlcnR5LXByaWNlIHtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBiYXNlbGluZTtcclxuICAgICAgICBnYXA6IDAuNXJlbTtcclxuXHJcbiAgICAgICAgLnByaWNlIHtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMS41cmVtO1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcclxuICAgICAgICAgIGNvbG9yOiB2YXIoLS1icy1wcmltYXJ5KTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5jdXJyZW5jeSB7XHJcbiAgICAgICAgICBjb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBDaXRpZXMgU2VjdGlvblxyXG4uY2l0aWVzLXNlY3Rpb24ge1xyXG4gIC5jaXR5LWNhcmQge1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTVweDtcclxuICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gICAgaGVpZ2h0OiAyMDBweDtcclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xyXG4gICAgICBib3gtc2hhZG93OiAwIDE1cHggMzVweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xyXG4gICAgfVxyXG5cclxuICAgIC5jaXR5LWltYWdlIHtcclxuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcblxyXG4gICAgICBpbWcge1xyXG4gICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjtcclxuICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmOmhvdmVyIGltZyB7XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAuY2l0eS1vdmVybGF5IHtcclxuICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgdG9wOiAwO1xyXG4gICAgICAgIGxlZnQ6IDA7XHJcbiAgICAgICAgcmlnaHQ6IDA7XHJcbiAgICAgICAgYm90dG9tOiAwO1xyXG4gICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byBib3R0b20sIHRyYW5zcGFyZW50IDAlLCByZ2JhKDAsIDAsIDAsIDAuNykgMTAwJSk7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBhbGlnbi1pdGVtczogZW5kO1xyXG4gICAgICAgIHBhZGRpbmc6IDEuNXJlbTtcclxuXHJcbiAgICAgICAgLmNpdHktY29udGVudCB7XHJcbiAgICAgICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgICAgICB3aWR0aDogMTAwJTtcclxuXHJcbiAgICAgICAgICAuY2l0eS1uYW1lIHtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAxLjI1cmVtO1xyXG4gICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XHJcbiAgICAgICAgICAgIHRleHQtc2hhZG93OiAycHggMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuNSk7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgLmNpdHktY291bnQge1xyXG4gICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICBvcGFjaXR5OiAwLjk7XHJcbiAgICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIEJsb2cgU2VjdGlvblxyXG4uYmxvZy1zZWN0aW9uIHtcclxuICAuYXJ0aWNsZS1jYXJkIHtcclxuICAgIGJhY2tncm91bmQ6IHdoaXRlO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTVweDtcclxuICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICBib3gtc2hhZG93OiAwIDVweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgaGVpZ2h0OiAxMDAlO1xyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTVweCk7XHJcbiAgICAgIGJveC1zaGFkb3c6IDAgMTVweCAzNXB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XHJcbiAgICB9XHJcblxyXG4gICAgLmFydGljbGUtaW1hZ2Uge1xyXG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICAgIGhlaWdodDogMjAwcHg7XHJcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcblxyXG4gICAgICBpbWcge1xyXG4gICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjtcclxuICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmOmhvdmVyIGltZyB7XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLmFydGljbGUtY2F0ZWdvcnkge1xyXG4gICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgICAgICB0b3A6IDFyZW07XHJcbiAgICAgICAgcmlnaHQ6IDFyZW07XHJcbiAgICAgICAgei1pbmRleDogMjtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5hcnRpY2xlLWNvbnRlbnQge1xyXG4gICAgICBwYWRkaW5nOiAxLjVyZW07XHJcblxyXG4gICAgICAuYXJ0aWNsZS10aXRsZSB7XHJcbiAgICAgICAgZm9udC1zaXplOiAxLjI1cmVtO1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxuICAgICAgICBjb2xvcjogIzJjM2U1MDtcclxuICAgICAgICBsaW5lLWhlaWdodDogMS40O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAuYXJ0aWNsZS1leGNlcnB0IHtcclxuICAgICAgICBjb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XHJcbiAgICAgICAgbGluZS1oZWlnaHQ6IDEuNjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLmFydGljbGUtbWV0YSB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgIGdhcDogMC41cmVtO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcblxyXG4gICAgICAgIC5hcnRpY2xlLWF1dGhvcixcclxuICAgICAgICAuYXJ0aWNsZS1kYXRlLFxyXG4gICAgICAgIC5hcnRpY2xlLXJlYWQtdGltZSB7XHJcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gTmV3c2xldHRlciBTZWN0aW9uXHJcbi5uZXdzbGV0dGVyLXNlY3Rpb24ge1xyXG4gIC5uZXdzbGV0dGVyLWNvbnRlbnQge1xyXG4gICAgLm5ld3NsZXR0ZXItdGl0bGUge1xyXG4gICAgICBmb250LXNpemU6IDJyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbiAgICB9XHJcblxyXG4gICAgLm5ld3NsZXR0ZXItc3VidGl0bGUge1xyXG4gICAgICBmb250LXNpemU6IDEuMTI1cmVtO1xyXG4gICAgICBvcGFjaXR5OiAwLjk7XHJcbiAgICAgIG1hcmdpbjogMDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5uZXdzbGV0dGVyLWZvcm0ge1xyXG4gICAgLmlucHV0LWdyb3VwIHtcclxuICAgICAgLmZvcm0tY29udHJvbCB7XHJcbiAgICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDEwcHggMCAwIDEwcHg7XHJcbiAgICAgICAgcGFkZGluZzogMXJlbSAxLjVyZW07XHJcbiAgICAgICAgZm9udC1zaXplOiAxcmVtO1xyXG5cclxuICAgICAgICAmOmZvY3VzIHtcclxuICAgICAgICAgIGJveC1zaGFkb3c6IG5vbmU7XHJcbiAgICAgICAgICBib3JkZXItY29sb3I6IHRyYW5zcGFyZW50O1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLmJ0biB7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMCAxMHB4IDEwcHggMDtcclxuICAgICAgICBwYWRkaW5nOiAxcmVtIDJyZW07XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgZ2FwOiAwLjVyZW07XHJcbiAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gUmVzcG9uc2l2ZSBEZXNpZ25cclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgLmhlcm8tc2VjdGlvbiB7XHJcbiAgICAuaGVyby1jb250ZW50IHtcclxuICAgICAgLmhlcm8tdGl0bGUge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMnJlbTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLmhlcm8tc3VidGl0bGUge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMXJlbTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5oZXJvLXNlYXJjaC1mb3JtIHtcclxuICAgICAgcGFkZGluZzogMS41cmVtO1xyXG5cclxuICAgICAgLnNlYXJjaC1pbnB1dHMge1xyXG4gICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xyXG4gICAgICAgIGdhcDogMXJlbTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5oZXJvLWNhcmRzIHtcclxuICAgICAgZ2FwOiAxcmVtO1xyXG5cclxuICAgICAgLmhlcm8tY2FyZCB7XHJcbiAgICAgICAgcGFkZGluZzogMS41cmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuc2VjdGlvbi10aXRsZSB7XHJcbiAgICBmb250LXNpemU6IDEuNzVyZW07XHJcbiAgfVxyXG5cclxuICAubmV3c2xldHRlci1zZWN0aW9uIHtcclxuICAgIC5uZXdzbGV0dGVyLWNvbnRlbnQge1xyXG4gICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDJyZW07XHJcblxyXG4gICAgICAubmV3c2xldHRlci10aXRsZSB7XHJcbiAgICAgICAgZm9udC1zaXplOiAxLjVyZW07XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAubmV3c2xldHRlci1mb3JtIHtcclxuICAgICAgLmlucHV0LWdyb3VwIHtcclxuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG5cclxuICAgICAgICAuZm9ybS1jb250cm9sIHtcclxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7XHJcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmJ0biB7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xyXG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBBbmltYXRpb24gQ2xhc3Nlc1xyXG4uZmFkZS1pbiB7XHJcbiAgYW5pbWF0aW9uOiBmYWRlSW4gMC42cyBlYXNlLW91dDtcclxufVxyXG5cclxuLnNsaWRlLXVwIHtcclxuICBhbmltYXRpb246IHNsaWRlVXAgMC42cyBlYXNlLW91dDtcclxufVxyXG5cclxuQGtleWZyYW1lcyBmYWRlSW4ge1xyXG4gIGZyb20ge1xyXG4gICAgb3BhY2l0eTogMDtcclxuICB9XHJcbiAgdG8ge1xyXG4gICAgb3BhY2l0eTogMTtcclxuICB9XHJcbn1cclxuXHJcbkBrZXlmcmFtZXMgc2xpZGVVcCB7XHJcbiAgZnJvbSB7XHJcbiAgICBvcGFjaXR5OiAwO1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDMwcHgpO1xyXG4gIH1cclxuICB0byB7XHJcbiAgICBvcGFjaXR5OiAxO1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xyXG4gIH1cclxufVxyXG5cclxuLy8gQ3VzdG9tIFV0aWxpdGllc1xyXG4uYmctbGlnaHQtZGFyay1ibHVlIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKHZhcigtLWJzLXByaW1hcnktcmdiKSwgMC4xKSAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4udGV4dC1kYXJrLWJsdWUge1xyXG4gIGNvbG9yOiB2YXIoLS1icy1wcmltYXJ5KSAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4uYnRuLWRhcmstYmx1ZSB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tYnMtcHJpbWFyeSk7XHJcbiAgYm9yZGVyLWNvbG9yOiB2YXIoLS1icy1wcmltYXJ5KTtcclxuICBjb2xvcjogd2hpdGU7XHJcblxyXG4gICY6aG92ZXIsXHJcbiAgJjpmb2N1cyxcclxuICAmOmFjdGl2ZSB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKHZhcigtLWJzLXByaW1hcnktcmdiKSwgMC45KTtcclxuICAgIGJvcmRlci1jb2xvcjogcmdiYSh2YXIoLS1icy1wcmltYXJ5LXJnYiksIDAuOSk7XHJcbiAgICBjb2xvcjogd2hpdGU7XHJcbiAgfVxyXG59XHJcbiIsIi5oZXJvLXNlY3Rpb24ge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICBvdmVyZmxvdzogaGlkZGVuO1xufVxuLmhlcm8tc2VjdGlvbiAuaGVyby1iYWNrZ3JvdW5kIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMWUzYzcyIDAlLCAjMmE1Mjk4IDEwMCUpO1xuICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoXCJ+c3JjL2Fzc2V0cy9tZWRpYS9iZy9oZXJvLWJnLmpwZ1wiKTtcbiAgYmFja2dyb3VuZC1zaXplOiBjb3ZlcjtcbiAgYmFja2dyb3VuZC1wb3NpdGlvbjogY2VudGVyO1xuICBiYWNrZ3JvdW5kLWF0dGFjaG1lbnQ6IGZpeGVkO1xuICBtaW4taGVpZ2h0OiAxMDB2aDtcbn1cbi5oZXJvLXNlY3Rpb24gLmhlcm8tYmFja2dyb3VuZCAuaGVyby1vdmVybGF5IHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDA7XG4gIGxlZnQ6IDA7XG4gIHJpZ2h0OiAwO1xuICBib3R0b206IDA7XG4gIGJhY2tncm91bmQ6IHJnYmEoMzAsIDYwLCAxMTQsIDAuOCk7XG4gIHotaW5kZXg6IDE7XG59XG4uaGVyby1zZWN0aW9uIC5oZXJvLWJhY2tncm91bmQgLmNvbnRhaW5lciB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgei1pbmRleDogMjtcbn1cbi5oZXJvLXNlY3Rpb24gLmhlcm8tY29udGVudCB7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgcGFkZGluZzogMnJlbSAwO1xufVxuLmhlcm8tc2VjdGlvbiAuaGVyby1jb250ZW50IC5oZXJvLXRpdGxlIHtcbiAgZm9udC1zaXplOiAzLjVyZW07XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGxpbmUtaGVpZ2h0OiAxLjI7XG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcbiAgdGV4dC1zaGFkb3c6IDJweCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4zKTtcbn1cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuaGVyby1zZWN0aW9uIC5oZXJvLWNvbnRlbnQgLmhlcm8tdGl0bGUge1xuICAgIGZvbnQtc2l6ZTogMi41cmVtO1xuICB9XG59XG4uaGVyby1zZWN0aW9uIC5oZXJvLWNvbnRlbnQgLmhlcm8tc3VidGl0bGUge1xuICBmb250LXNpemU6IDEuMjVyZW07XG4gIG1hcmdpbi1ib3R0b206IDJyZW07XG4gIG9wYWNpdHk6IDAuOTtcbiAgbGluZS1oZWlnaHQ6IDEuNjtcbn1cbi5oZXJvLXNlY3Rpb24gLmhlcm8tc2VhcmNoLWZvcm0ge1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOTUpO1xuICBib3JkZXItcmFkaXVzOiAxNXB4O1xuICBwYWRkaW5nOiAycmVtO1xuICBib3gtc2hhZG93OiAwIDIwcHggNDBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcbn1cbi5oZXJvLXNlY3Rpb24gLmhlcm8tc2VhcmNoLWZvcm0gLnNlYXJjaC10YWJzIHtcbiAgZGlzcGxheTogZmxleDtcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xufVxuLmhlcm8tc2VjdGlvbiAuaGVyby1zZWFyY2gtZm9ybSAuc2VhcmNoLXRhYnMgLnNlYXJjaC10YWIge1xuICBmbGV4OiAxO1xuICBwYWRkaW5nOiAwLjc1cmVtIDFyZW07XG4gIGJvcmRlcjogbm9uZTtcbiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gIGNvbG9yOiAjNmM3NTdkO1xuICBmb250LXdlaWdodDogNjAwO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBjdXJzb3I6IHBvaW50ZXI7XG59XG4uaGVyby1zZWN0aW9uIC5oZXJvLXNlYXJjaC1mb3JtIC5zZWFyY2gtdGFicyAuc2VhcmNoLXRhYi5hY3RpdmUge1xuICBiYWNrZ3JvdW5kOiB2YXIoLS1icy1wcmltYXJ5KTtcbiAgY29sb3I6IHdoaXRlO1xufVxuLmhlcm8tc2VjdGlvbiAuaGVyby1zZWFyY2gtZm9ybSAuc2VhcmNoLXRhYnMgLnNlYXJjaC10YWI6aG92ZXI6bm90KC5hY3RpdmUpIHtcbiAgYmFja2dyb3VuZDogI2U5ZWNlZjtcbn1cbi5oZXJvLXNlY3Rpb24gLmhlcm8tc2VhcmNoLWZvcm0gLnNlYXJjaC1pbnB1dHMge1xuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnIgMWZyIGF1dG87XG4gIGdhcDogMXJlbTtcbiAgYWxpZ24taXRlbXM6IGVuZDtcbn1cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuaGVyby1zZWN0aW9uIC5oZXJvLXNlYXJjaC1mb3JtIC5zZWFyY2gtaW5wdXRzIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgfVxufVxuLmhlcm8tc2VjdGlvbiAuaGVyby1zZWFyY2gtZm9ybSAuc2VhcmNoLWlucHV0cyAuc2VhcmNoLWlucHV0LWdyb3VwIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xufVxuLmhlcm8tc2VjdGlvbiAuaGVyby1zZWFyY2gtZm9ybSAuc2VhcmNoLWlucHV0cyAuc2VhcmNoLWlucHV0LWdyb3VwIGkge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIGxlZnQ6IDFyZW07XG4gIHRvcDogNTAlO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSk7XG4gIGNvbG9yOiAjNmM3NTdkO1xuICB6LWluZGV4OiAzO1xufVxuLmhlcm8tc2VjdGlvbiAuaGVyby1zZWFyY2gtZm9ybSAuc2VhcmNoLWlucHV0cyAuc2VhcmNoLWlucHV0LWdyb3VwIC5mb3JtLXNlbGVjdCB7XG4gIHBhZGRpbmctbGVmdDogM3JlbTtcbiAgYm9yZGVyOiAycHggc29saWQgI2U5ZWNlZjtcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgaGVpZ2h0OiA1MHB4O1xuICBmb250LXdlaWdodDogNTAwO1xufVxuLmhlcm8tc2VjdGlvbiAuaGVyby1zZWFyY2gtZm9ybSAuc2VhcmNoLWlucHV0cyAuc2VhcmNoLWlucHV0LWdyb3VwIC5mb3JtLXNlbGVjdDpmb2N1cyB7XG4gIGJvcmRlci1jb2xvcjogdmFyKC0tYnMtcHJpbWFyeSk7XG4gIGJveC1zaGFkb3c6IDAgMCAwIDAuMnJlbSByZ2JhKHZhcigtLWJzLXByaW1hcnktcmdiKSwgMC4yNSk7XG59XG4uaGVyby1zZWN0aW9uIC5oZXJvLXNlYXJjaC1mb3JtIC5zZWFyY2gtaW5wdXRzIC5zZWFyY2gtYnRuIHtcbiAgaGVpZ2h0OiA1MHB4O1xuICBwYWRkaW5nOiAwIDJyZW07XG4gIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC41cmVtO1xuICB3aGl0ZS1zcGFjZTogbm93cmFwO1xufVxuLmhlcm8tc2VjdGlvbiAuaGVyby1jYXJkcyB7XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICBnYXA6IDJyZW07XG4gIHBhZGRpbmc6IDJyZW0gMDtcbn1cbi5oZXJvLXNlY3Rpb24gLmhlcm8tY2FyZHMgLmhlcm8tY2FyZCB7XG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45NSk7XG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XG4gIHBhZGRpbmc6IDJyZW07XG4gIGJveC1zaGFkb3c6IDAgMTBweCAzMHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBjdXJzb3I6IHBvaW50ZXI7XG59XG4uaGVyby1zZWN0aW9uIC5oZXJvLWNhcmRzIC5oZXJvLWNhcmQ6aG92ZXIge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTVweCk7XG4gIGJveC1zaGFkb3c6IDAgMjBweCA0MHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XG59XG4uaGVyby1zZWN0aW9uIC5oZXJvLWNhcmRzIC5oZXJvLWNhcmQuaGVyby1jYXJkLTEge1xuICBhbmltYXRpb246IHNsaWRlSW5SaWdodCAwLjZzIGVhc2Utb3V0IDAuMnMgYm90aDtcbn1cbi5oZXJvLXNlY3Rpb24gLmhlcm8tY2FyZHMgLmhlcm8tY2FyZC5oZXJvLWNhcmQtMiB7XG4gIGFuaW1hdGlvbjogc2xpZGVJblJpZ2h0IDAuNnMgZWFzZS1vdXQgMC40cyBib3RoO1xufVxuLmhlcm8tc2VjdGlvbiAuaGVyby1jYXJkcyAuaGVyby1jYXJkLmhlcm8tY2FyZC0zIHtcbiAgYW5pbWF0aW9uOiBzbGlkZUluUmlnaHQgMC42cyBlYXNlLW91dCAwLjZzIGJvdGg7XG59XG4uaGVyby1zZWN0aW9uIC5oZXJvLWNhcmRzIC5oZXJvLWNhcmQgLmhlcm8tY2FyZC1jb250ZW50IHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xufVxuLmhlcm8tc2VjdGlvbiAuaGVyby1jYXJkcyAuaGVyby1jYXJkIC5oZXJvLWNhcmQtY29udGVudCAuaGVyby1jYXJkLWljb24ge1xuICB3aWR0aDogODBweDtcbiAgaGVpZ2h0OiA4MHB4O1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBtYXJnaW46IDAgYXV0byAxLjVyZW07XG59XG4uaGVyby1zZWN0aW9uIC5oZXJvLWNhcmRzIC5oZXJvLWNhcmQgLmhlcm8tY2FyZC1jb250ZW50IC5oZXJvLWNhcmQtdGl0bGUge1xuICBmb250LXNpemU6IDEuNXJlbTtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbiAgY29sb3I6ICMyYzNlNTA7XG59XG4uaGVyby1zZWN0aW9uIC5oZXJvLWNhcmRzIC5oZXJvLWNhcmQgLmhlcm8tY2FyZC1jb250ZW50IC5oZXJvLWNhcmQtZGVzY3JpcHRpb24ge1xuICBjb2xvcjogIzZjNzU3ZDtcbiAgbGluZS1oZWlnaHQ6IDEuNjtcbiAgbWFyZ2luOiAwO1xufVxuXG5Aa2V5ZnJhbWVzIHNsaWRlSW5SaWdodCB7XG4gIGZyb20ge1xuICAgIG9wYWNpdHk6IDA7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDMwcHgpO1xuICB9XG4gIHRvIHtcbiAgICBvcGFjaXR5OiAxO1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKTtcbiAgfVxufVxuLnNlY3Rpb24tdGl0bGUge1xuICBmb250LXNpemU6IDIuNXJlbTtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgY29sb3I6ICMyYzNlNTA7XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xufVxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5zZWN0aW9uLXRpdGxlIHtcbiAgICBmb250LXNpemU6IDJyZW07XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBnYXA6IDAuNXJlbTtcbiAgfVxufVxuXG4uc2VjdGlvbi1zdWJ0aXRsZSB7XG4gIGZvbnQtc2l6ZTogMS4xMjVyZW07XG4gIGNvbG9yOiAjNmM3NTdkO1xuICBtYXJnaW4tYm90dG9tOiAwO1xufVxuXG4uZmVhdHVyZWQtcHJvcGVydGllcy1zZWN0aW9uIC5wcm9wZXJ0eS1jYXJkIHtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIGJveC1zaGFkb3c6IDAgNXB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjA4KTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICBoZWlnaHQ6IDEwMCU7XG59XG4uZmVhdHVyZWQtcHJvcGVydGllcy1zZWN0aW9uIC5wcm9wZXJ0eS1jYXJkOmhvdmVyIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xuICBib3gtc2hhZG93OiAwIDE1cHggMzVweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xufVxuLmZlYXR1cmVkLXByb3BlcnRpZXMtc2VjdGlvbiAucHJvcGVydHktY2FyZCAucHJvcGVydHktaW1hZ2Uge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIGhlaWdodDogMjAwcHg7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59XG4uZmVhdHVyZWQtcHJvcGVydGllcy1zZWN0aW9uIC5wcm9wZXJ0eS1jYXJkIC5wcm9wZXJ0eS1pbWFnZSBpbWcge1xuICBoZWlnaHQ6IDEwMCU7XG4gIG9iamVjdC1maXQ6IGNvdmVyO1xuICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xufVxuLmZlYXR1cmVkLXByb3BlcnRpZXMtc2VjdGlvbiAucHJvcGVydHktY2FyZCAucHJvcGVydHktaW1hZ2U6aG92ZXIgaW1nIHtcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTtcbn1cbi5mZWF0dXJlZC1wcm9wZXJ0aWVzLXNlY3Rpb24gLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWltYWdlIC5wcm9wZXJ0eS1iYWRnZXMge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMXJlbTtcbiAgcmlnaHQ6IDFyZW07XG4gIHotaW5kZXg6IDI7XG59XG4uZmVhdHVyZWQtcHJvcGVydGllcy1zZWN0aW9uIC5wcm9wZXJ0eS1jYXJkIC5wcm9wZXJ0eS1pbWFnZSAucHJvcGVydHktcmF0aW5nIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDFyZW07XG4gIGxlZnQ6IDFyZW07XG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTtcbiAgcGFkZGluZzogMC4yNXJlbSAwLjVyZW07XG4gIGJvcmRlci1yYWRpdXM6IDIwcHg7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC4yNXJlbTtcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbn1cbi5mZWF0dXJlZC1wcm9wZXJ0aWVzLXNlY3Rpb24gLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWNvbnRlbnQge1xuICBwYWRkaW5nOiAxLjVyZW07XG59XG4uZmVhdHVyZWQtcHJvcGVydGllcy1zZWN0aW9uIC5wcm9wZXJ0eS1jYXJkIC5wcm9wZXJ0eS1jb250ZW50IC5wcm9wZXJ0eS10aXRsZSB7XG4gIGZvbnQtc2l6ZTogMS4xMjVyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcbiAgY29sb3I6ICMyYzNlNTA7XG4gIGxpbmUtaGVpZ2h0OiAxLjQ7XG59XG4uZmVhdHVyZWQtcHJvcGVydGllcy1zZWN0aW9uIC5wcm9wZXJ0eS1jYXJkIC5wcm9wZXJ0eS1jb250ZW50IC5wcm9wZXJ0eS1sb2NhdGlvbiB7XG4gIGNvbG9yOiAjNmM3NTdkO1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xufVxuLmZlYXR1cmVkLXByb3BlcnRpZXMtc2VjdGlvbiAucHJvcGVydHktY2FyZCAucHJvcGVydHktY29udGVudCAucHJvcGVydHktZGV0YWlscyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbiAgZmxleC13cmFwOiB3cmFwO1xufVxuLmZlYXR1cmVkLXByb3BlcnRpZXMtc2VjdGlvbiAucHJvcGVydHktY2FyZCAucHJvcGVydHktY29udGVudCAucHJvcGVydHktZGV0YWlscyAucHJvcGVydHktZGV0YWlsIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiAwLjI1cmVtO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xuICBjb2xvcjogIzZjNzU3ZDtcbn1cbi5mZWF0dXJlZC1wcm9wZXJ0aWVzLXNlY3Rpb24gLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWNvbnRlbnQgLnByb3BlcnR5LWRldGFpbHMgLnByb3BlcnR5LWRldGFpbCBpIHtcbiAgZm9udC1zaXplOiAxcmVtO1xufVxuLmZlYXR1cmVkLXByb3BlcnRpZXMtc2VjdGlvbiAucHJvcGVydHktY2FyZCAucHJvcGVydHktY29udGVudCAucHJvcGVydHktcHJpY2Uge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogYmFzZWxpbmU7XG4gIGdhcDogMC41cmVtO1xufVxuLmZlYXR1cmVkLXByb3BlcnRpZXMtc2VjdGlvbiAucHJvcGVydHktY2FyZCAucHJvcGVydHktY29udGVudCAucHJvcGVydHktcHJpY2UgLnByaWNlIHtcbiAgZm9udC1zaXplOiAxLjVyZW07XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGNvbG9yOiB2YXIoLS1icy1wcmltYXJ5KTtcbn1cbi5mZWF0dXJlZC1wcm9wZXJ0aWVzLXNlY3Rpb24gLnByb3BlcnR5LWNhcmQgLnByb3BlcnR5LWNvbnRlbnQgLnByb3BlcnR5LXByaWNlIC5jdXJyZW5jeSB7XG4gIGNvbG9yOiAjNmM3NTdkO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xufVxuXG4uY2l0aWVzLXNlY3Rpb24gLmNpdHktY2FyZCB7XG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgaGVpZ2h0OiAyMDBweDtcbn1cbi5jaXRpZXMtc2VjdGlvbiAuY2l0eS1jYXJkOmhvdmVyIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xuICBib3gtc2hhZG93OiAwIDE1cHggMzVweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xufVxuLmNpdGllcy1zZWN0aW9uIC5jaXR5LWNhcmQgLmNpdHktaW1hZ2Uge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIGhlaWdodDogMTAwJTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cbi5jaXRpZXMtc2VjdGlvbiAuY2l0eS1jYXJkIC5jaXR5LWltYWdlIGltZyB7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIG9iamVjdC1maXQ6IGNvdmVyO1xuICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xufVxuLmNpdGllcy1zZWN0aW9uIC5jaXR5LWNhcmQgLmNpdHktaW1hZ2U6aG92ZXIgaW1nIHtcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xufVxuLmNpdGllcy1zZWN0aW9uIC5jaXR5LWNhcmQgLmNpdHktaW1hZ2UgLmNpdHktb3ZlcmxheSB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICByaWdodDogMDtcbiAgYm90dG9tOiAwO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQodG8gYm90dG9tLCB0cmFuc3BhcmVudCAwJSwgcmdiYSgwLCAwLCAwLCAwLjcpIDEwMCUpO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogZW5kO1xuICBwYWRkaW5nOiAxLjVyZW07XG59XG4uY2l0aWVzLXNlY3Rpb24gLmNpdHktY2FyZCAuY2l0eS1pbWFnZSAuY2l0eS1vdmVybGF5IC5jaXR5LWNvbnRlbnQge1xuICBjb2xvcjogd2hpdGU7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgd2lkdGg6IDEwMCU7XG59XG4uY2l0aWVzLXNlY3Rpb24gLmNpdHktY2FyZCAuY2l0eS1pbWFnZSAuY2l0eS1vdmVybGF5IC5jaXR5LWNvbnRlbnQgLmNpdHktbmFtZSB7XG4gIGZvbnQtc2l6ZTogMS4yNXJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xuICB0ZXh0LXNoYWRvdzogMnB4IDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjUpO1xufVxuLmNpdGllcy1zZWN0aW9uIC5jaXR5LWNhcmQgLmNpdHktaW1hZ2UgLmNpdHktb3ZlcmxheSAuY2l0eS1jb250ZW50IC5jaXR5LWNvdW50IHtcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgb3BhY2l0eTogMC45O1xuICBtYXJnaW46IDA7XG59XG5cbi5ibG9nLXNlY3Rpb24gLmFydGljbGUtY2FyZCB7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBib3JkZXItcmFkaXVzOiAxNXB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBib3gtc2hhZG93OiAwIDVweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgaGVpZ2h0OiAxMDAlO1xufVxuLmJsb2ctc2VjdGlvbiAuYXJ0aWNsZS1jYXJkOmhvdmVyIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xuICBib3gtc2hhZG93OiAwIDE1cHggMzVweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xufVxuLmJsb2ctc2VjdGlvbiAuYXJ0aWNsZS1jYXJkIC5hcnRpY2xlLWltYWdlIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBoZWlnaHQ6IDIwMHB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xufVxuLmJsb2ctc2VjdGlvbiAuYXJ0aWNsZS1jYXJkIC5hcnRpY2xlLWltYWdlIGltZyB7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIG9iamVjdC1maXQ6IGNvdmVyO1xuICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xufVxuLmJsb2ctc2VjdGlvbiAuYXJ0aWNsZS1jYXJkIC5hcnRpY2xlLWltYWdlOmhvdmVyIGltZyB7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7XG59XG4uYmxvZy1zZWN0aW9uIC5hcnRpY2xlLWNhcmQgLmFydGljbGUtaW1hZ2UgLmFydGljbGUtY2F0ZWdvcnkge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMXJlbTtcbiAgcmlnaHQ6IDFyZW07XG4gIHotaW5kZXg6IDI7XG59XG4uYmxvZy1zZWN0aW9uIC5hcnRpY2xlLWNhcmQgLmFydGljbGUtY29udGVudCB7XG4gIHBhZGRpbmc6IDEuNXJlbTtcbn1cbi5ibG9nLXNlY3Rpb24gLmFydGljbGUtY2FyZCAuYXJ0aWNsZS1jb250ZW50IC5hcnRpY2xlLXRpdGxlIHtcbiAgZm9udC1zaXplOiAxLjI1cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICBjb2xvcjogIzJjM2U1MDtcbiAgbGluZS1oZWlnaHQ6IDEuNDtcbn1cbi5ibG9nLXNlY3Rpb24gLmFydGljbGUtY2FyZCAuYXJ0aWNsZS1jb250ZW50IC5hcnRpY2xlLWV4Y2VycHQge1xuICBjb2xvcjogIzZjNzU3ZDtcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xuICBsaW5lLWhlaWdodDogMS42O1xufVxuLmJsb2ctc2VjdGlvbiAuYXJ0aWNsZS1jYXJkIC5hcnRpY2xlLWNvbnRlbnQgLmFydGljbGUtbWV0YSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogMC41cmVtO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xufVxuLmJsb2ctc2VjdGlvbiAuYXJ0aWNsZS1jYXJkIC5hcnRpY2xlLWNvbnRlbnQgLmFydGljbGUtbWV0YSAuYXJ0aWNsZS1hdXRob3IsXG4uYmxvZy1zZWN0aW9uIC5hcnRpY2xlLWNhcmQgLmFydGljbGUtY29udGVudCAuYXJ0aWNsZS1tZXRhIC5hcnRpY2xlLWRhdGUsXG4uYmxvZy1zZWN0aW9uIC5hcnRpY2xlLWNhcmQgLmFydGljbGUtY29udGVudCAuYXJ0aWNsZS1tZXRhIC5hcnRpY2xlLXJlYWQtdGltZSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGNvbG9yOiAjNmM3NTdkO1xufVxuXG4ubmV3c2xldHRlci1zZWN0aW9uIC5uZXdzbGV0dGVyLWNvbnRlbnQgLm5ld3NsZXR0ZXItdGl0bGUge1xuICBmb250LXNpemU6IDJyZW07XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIG1hcmdpbi1ib3R0b206IDFyZW07XG59XG4ubmV3c2xldHRlci1zZWN0aW9uIC5uZXdzbGV0dGVyLWNvbnRlbnQgLm5ld3NsZXR0ZXItc3VidGl0bGUge1xuICBmb250LXNpemU6IDEuMTI1cmVtO1xuICBvcGFjaXR5OiAwLjk7XG4gIG1hcmdpbjogMDtcbn1cbi5uZXdzbGV0dGVyLXNlY3Rpb24gLm5ld3NsZXR0ZXItZm9ybSAuaW5wdXQtZ3JvdXAgLmZvcm0tY29udHJvbCB7XG4gIGJvcmRlcjogbm9uZTtcbiAgYm9yZGVyLXJhZGl1czogMTBweCAwIDAgMTBweDtcbiAgcGFkZGluZzogMXJlbSAxLjVyZW07XG4gIGZvbnQtc2l6ZTogMXJlbTtcbn1cbi5uZXdzbGV0dGVyLXNlY3Rpb24gLm5ld3NsZXR0ZXItZm9ybSAuaW5wdXQtZ3JvdXAgLmZvcm0tY29udHJvbDpmb2N1cyB7XG4gIGJveC1zaGFkb3c6IG5vbmU7XG4gIGJvcmRlci1jb2xvcjogdHJhbnNwYXJlbnQ7XG59XG4ubmV3c2xldHRlci1zZWN0aW9uIC5uZXdzbGV0dGVyLWZvcm0gLmlucHV0LWdyb3VwIC5idG4ge1xuICBib3JkZXItcmFkaXVzOiAwIDEwcHggMTBweCAwO1xuICBwYWRkaW5nOiAxcmVtIDJyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMC41cmVtO1xuICB3aGl0ZS1zcGFjZTogbm93cmFwO1xufVxuXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLmhlcm8tc2VjdGlvbiAuaGVyby1jb250ZW50IC5oZXJvLXRpdGxlIHtcbiAgICBmb250LXNpemU6IDJyZW07XG4gIH1cbiAgLmhlcm8tc2VjdGlvbiAuaGVyby1jb250ZW50IC5oZXJvLXN1YnRpdGxlIHtcbiAgICBmb250LXNpemU6IDFyZW07XG4gIH1cbiAgLmhlcm8tc2VjdGlvbiAuaGVyby1zZWFyY2gtZm9ybSB7XG4gICAgcGFkZGluZzogMS41cmVtO1xuICB9XG4gIC5oZXJvLXNlY3Rpb24gLmhlcm8tc2VhcmNoLWZvcm0gLnNlYXJjaC1pbnB1dHMge1xuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICAgIGdhcDogMXJlbTtcbiAgfVxuICAuaGVyby1zZWN0aW9uIC5oZXJvLWNhcmRzIHtcbiAgICBnYXA6IDFyZW07XG4gIH1cbiAgLmhlcm8tc2VjdGlvbiAuaGVyby1jYXJkcyAuaGVyby1jYXJkIHtcbiAgICBwYWRkaW5nOiAxLjVyZW07XG4gIH1cbiAgLnNlY3Rpb24tdGl0bGUge1xuICAgIGZvbnQtc2l6ZTogMS43NXJlbTtcbiAgfVxuICAubmV3c2xldHRlci1zZWN0aW9uIC5uZXdzbGV0dGVyLWNvbnRlbnQge1xuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICBtYXJnaW4tYm90dG9tOiAycmVtO1xuICB9XG4gIC5uZXdzbGV0dGVyLXNlY3Rpb24gLm5ld3NsZXR0ZXItY29udGVudCAubmV3c2xldHRlci10aXRsZSB7XG4gICAgZm9udC1zaXplOiAxLjVyZW07XG4gIH1cbiAgLm5ld3NsZXR0ZXItc2VjdGlvbiAubmV3c2xldHRlci1mb3JtIC5pbnB1dC1ncm91cCB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgfVxuICAubmV3c2xldHRlci1zZWN0aW9uIC5uZXdzbGV0dGVyLWZvcm0gLmlucHV0LWdyb3VwIC5mb3JtLWNvbnRyb2wge1xuICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbiAgfVxuICAubmV3c2xldHRlci1zZWN0aW9uIC5uZXdzbGV0dGVyLWZvcm0gLmlucHV0LWdyb3VwIC5idG4ge1xuICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIH1cbn1cbi5mYWRlLWluIHtcbiAgYW5pbWF0aW9uOiBmYWRlSW4gMC42cyBlYXNlLW91dDtcbn1cblxuLnNsaWRlLXVwIHtcbiAgYW5pbWF0aW9uOiBzbGlkZVVwIDAuNnMgZWFzZS1vdXQ7XG59XG5cbkBrZXlmcmFtZXMgZmFkZUluIHtcbiAgZnJvbSB7XG4gICAgb3BhY2l0eTogMDtcbiAgfVxuICB0byB7XG4gICAgb3BhY2l0eTogMTtcbiAgfVxufVxuQGtleWZyYW1lcyBzbGlkZVVwIHtcbiAgZnJvbSB7XG4gICAgb3BhY2l0eTogMDtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMzBweCk7XG4gIH1cbiAgdG8ge1xuICAgIG9wYWNpdHk6IDE7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xuICB9XG59XG4uYmctbGlnaHQtZGFyay1ibHVlIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSh2YXIoLS1icy1wcmltYXJ5LXJnYiksIDAuMSkgIWltcG9ydGFudDtcbn1cblxuLnRleHQtZGFyay1ibHVlIHtcbiAgY29sb3I6IHZhcigtLWJzLXByaW1hcnkpICFpbXBvcnRhbnQ7XG59XG5cbi5idG4tZGFyay1ibHVlIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tYnMtcHJpbWFyeSk7XG4gIGJvcmRlci1jb2xvcjogdmFyKC0tYnMtcHJpbWFyeSk7XG4gIGNvbG9yOiB3aGl0ZTtcbn1cbi5idG4tZGFyay1ibHVlOmhvdmVyLCAuYnRuLWRhcmstYmx1ZTpmb2N1cywgLmJ0bi1kYXJrLWJsdWU6YWN0aXZlIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSh2YXIoLS1icy1wcmltYXJ5LXJnYiksIDAuOSk7XG4gIGJvcmRlci1jb2xvcjogcmdiYSh2YXIoLS1icy1wcmltYXJ5LXJnYiksIDAuOSk7XG4gIGNvbG9yOiB3aGl0ZTtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵclassMap", "i_r2", "ɵɵadvance", "card_r1", "color", "icon", "ɵɵtextInterpolate", "title", "description", "ɵɵtextInterpolate1", "property_r4", "bedrooms", "bathrooms", "ɵɵlistener", "HomeComponent_div_71_Template_div_click_1_listener", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "viewProperty", "id", "ɵɵtemplate", "HomeComponent_div_71_span_5_Template", "HomeComponent_div_71_div_17_Template", "HomeComponent_div_71_div_18_Template", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "isNew", "rating", "location", "area", "price", "HomeComponent_div_88_Template_div_click_1_listener", "city_r7", "_r6", "viewCity", "name", "propertiesCount", "HomeComponent_div_99_Template_div_click_1_listener", "article_r9", "_r8", "readArticle", "category", "excerpt", "author", "date", "readTime", "HomeComponent", "heroCards", "featuredProperties", "cities", "blogArticles", "constructor", "ngOnInit", "searchProperties", "console", "log", "propertyId", "cityName", "articleId", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "HomeComponent_Template_button_click_55_listener", "HomeComponent_div_60_Template", "HomeComponent_div_71_Template", "HomeComponent_div_88_Template", "HomeComponent_div_99_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule],\r\n  templateUrl: './home.component.html',\r\n  styleUrl: './home.component.scss'\r\n})\r\nexport class HomeComponent implements OnInit {\r\n\r\n  // Hero section data\r\n  heroCards = [\r\n    {\r\n      title: 'سهولة',\r\n      description: 'سهولة في التعامل والوصول للعقارات',\r\n      icon: 'ki-outline ki-check-circle',\r\n      color: 'success'\r\n    },\r\n    {\r\n      title: 'سرعة',\r\n      description: 'سرعة في إنجاز المعاملات',\r\n      icon: 'ki-outline ki-flash',\r\n      color: 'warning'\r\n    },\r\n    {\r\n      title: 'ثقة',\r\n      description: 'ثقة في التعامل مع أفضل المطورين',\r\n      icon: 'ki-outline ki-shield-tick',\r\n      color: 'primary'\r\n    }\r\n  ];\r\n\r\n  // Featured properties\r\n  featuredProperties = [\r\n    {\r\n      id: 1,\r\n      title: 'شقة للبيع في التجمع الخامس',\r\n      location: 'التجمع الخامس، القاهرة الجديدة',\r\n      price: '2,500,000',\r\n      area: '120',\r\n      bedrooms: 3,\r\n      bathrooms: 2,\r\n      image: 'assets/media/properties/property-1.jpg',\r\n      rating: 4.5,\r\n      isNew: true\r\n    },\r\n    {\r\n      id: 2,\r\n      title: 'فيلا للبيع في الشيخ زايد',\r\n      location: 'الشيخ زايد، الجيزة',\r\n      price: '8,500,000',\r\n      area: '350',\r\n      bedrooms: 5,\r\n      bathrooms: 4,\r\n      image: 'assets/media/properties/property-2.jpg',\r\n      rating: 4.8,\r\n      isNew: false\r\n    },\r\n    {\r\n      id: 3,\r\n      title: 'شقة للإيجار في المعادي',\r\n      location: 'المعادي، القاهرة',\r\n      price: '15,000',\r\n      area: '90',\r\n      bedrooms: 2,\r\n      bathrooms: 1,\r\n      image: 'assets/media/properties/property-3.jpg',\r\n      rating: 4.2,\r\n      isNew: true\r\n    },\r\n    {\r\n      id: 4,\r\n      title: 'مكتب للبيع في وسط البلد',\r\n      location: 'وسط البلد، القاهرة',\r\n      price: '1,200,000',\r\n      area: '80',\r\n      bedrooms: 0,\r\n      bathrooms: 1,\r\n      image: 'assets/media/properties/property-4.jpg',\r\n      rating: 4.0,\r\n      isNew: false\r\n    }\r\n  ];\r\n\r\n  // Cities carousel\r\n  cities = [\r\n    {\r\n      name: 'القاهرة الجديدة',\r\n      image: 'assets/media/cities/new-cairo.jpg',\r\n      propertiesCount: 1250\r\n    },\r\n    {\r\n      name: 'الشيخ زايد',\r\n      image: 'assets/media/cities/sheikh-zayed.jpg',\r\n      propertiesCount: 890\r\n    },\r\n    {\r\n      name: 'العاصمة الإدارية',\r\n      image: 'assets/media/cities/new-capital.jpg',\r\n      propertiesCount: 650\r\n    },\r\n    {\r\n      name: 'الساحل الشمالي',\r\n      image: 'assets/media/cities/north-coast.jpg',\r\n      propertiesCount: 420\r\n    },\r\n    {\r\n      name: 'العين السخنة',\r\n      image: 'assets/media/cities/ain-sokhna.jpg',\r\n      propertiesCount: 320\r\n    }\r\n  ];\r\n\r\n  // Blog articles\r\n  blogArticles = [\r\n    {\r\n      id: 1,\r\n      title: 'نصائح لشراء العقار المناسب',\r\n      excerpt: 'دليل شامل لاختيار العقار المناسب لاحتياجاتك وميزانيتك',\r\n      image: 'assets/media/blog/article-1.jpg',\r\n      author: 'أحمد محمد',\r\n      date: '2024-01-15',\r\n      readTime: '5 دقائق',\r\n      category: 'نصائح عقارية'\r\n    },\r\n    {\r\n      id: 2,\r\n      title: 'استثمار العقارات في مصر',\r\n      excerpt: 'كيفية الاستثمار الناجح في العقارات والحصول على عائد مجزي',\r\n      image: 'assets/media/blog/article-2.jpg',\r\n      author: 'سارة أحمد',\r\n      date: '2024-01-10',\r\n      readTime: '7 دقائق',\r\n      category: 'استثمار'\r\n    },\r\n    {\r\n      id: 3,\r\n      title: 'أحدث المشاريع العقارية',\r\n      excerpt: 'تعرف على أحدث المشاريع العقارية في القاهرة الجديدة والعاصمة الإدارية',\r\n      image: 'assets/media/blog/article-3.jpg',\r\n      author: 'محمد علي',\r\n      date: '2024-01-05',\r\n      readTime: '4 دقائق',\r\n      category: 'مشاريع جديدة'\r\n    }\r\n  ];\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n    // Initialize any required data or services\r\n  }\r\n\r\n  // Navigation methods\r\n  searchProperties(): void {\r\n    // Navigate to properties search page\r\n    console.log('Searching properties...');\r\n  }\r\n\r\n  viewProperty(propertyId: number): void {\r\n    // Navigate to property details\r\n    console.log('Viewing property:', propertyId);\r\n  }\r\n\r\n  viewCity(cityName: string): void {\r\n    // Navigate to city properties\r\n    console.log('Viewing city:', cityName);\r\n  }\r\n\r\n  readArticle(articleId: number): void {\r\n    // Navigate to blog article\r\n    console.log('Reading article:', articleId);\r\n  }\r\n}\r\n", "<!-- Hero Section -->\r\n<section class=\"hero-section\">\r\n  <div class=\"hero-background\">\r\n    <div class=\"hero-overlay\"></div>\r\n    <div class=\"container\">\r\n      <div class=\"row align-items-center min-vh-100\">\r\n        <!-- Hero Content -->\r\n        <div class=\"col-lg-6\">\r\n          <div class=\"hero-content\">\r\n            <h1 class=\"hero-title\">\r\n              ابحث عن العقار المثالي\r\n              <span class=\"text-primary\">بسهولة وثقة</span>\r\n            </h1>\r\n            <p class=\"hero-subtitle\">\r\n              منصة إيزي ديل تقدم لك أفضل العقارات في مصر مع خدمات متميزة وأسعار تنافسية\r\n            </p>\r\n\r\n            <!-- Search Form -->\r\n            <div class=\"hero-search-form\">\r\n              <div class=\"search-tabs\">\r\n                <button class=\"search-tab active\">شراء</button>\r\n                <button class=\"search-tab\">إيجار</button>\r\n                <button class=\"search-tab\">استثمار</button>\r\n              </div>\r\n\r\n              <div class=\"search-inputs\">\r\n                <div class=\"search-input-group\">\r\n                  <i class=\"ki-outline ki-geolocation fs-2\"></i>\r\n                  <select class=\"form-select\">\r\n                    <option>اختر المدينة</option>\r\n                    <option>القاهرة الجديدة</option>\r\n                    <option>الشيخ زايد</option>\r\n                    <option>العاصمة الإدارية</option>\r\n                  </select>\r\n                </div>\r\n\r\n                <div class=\"search-input-group\">\r\n                  <i class=\"ki-outline ki-home fs-2\"></i>\r\n                  <select class=\"form-select\">\r\n                    <option>نوع العقار</option>\r\n                    <option>شقة</option>\r\n                    <option>فيلا</option>\r\n                    <option>مكتب</option>\r\n                  </select>\r\n                </div>\r\n\r\n                <div class=\"search-input-group\">\r\n                  <i class=\"ki-outline ki-dollar fs-2\"></i>\r\n                  <select class=\"form-select\">\r\n                    <option>الميزانية</option>\r\n                    <option>أقل من مليون</option>\r\n                    <option>1-3 مليون</option>\r\n                    <option>أكثر من 3 مليون</option>\r\n                  </select>\r\n                </div>\r\n\r\n                <button class=\"btn btn-primary search-btn\" (click)=\"searchProperties()\">\r\n                  <i class=\"ki-outline ki-magnifier fs-2\"></i>\r\n                  ابحث\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Hero Cards -->\r\n        <div class=\"col-lg-6\">\r\n          <div class=\"hero-cards\">\r\n            <div class=\"hero-card\" *ngFor=\"let card of heroCards; let i = index\" [class]=\"'hero-card-' + (i + 1)\">\r\n              <div class=\"hero-card-content\">\r\n                <div class=\"hero-card-icon\" [class]=\"'bg-light-' + card.color\">\r\n                  <i [class]=\"card.icon + ' fs-1 text-' + card.color\"></i>\r\n                </div>\r\n                <h3 class=\"hero-card-title\">{{ card.title }}</h3>\r\n                <p class=\"hero-card-description\">{{ card.description }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Featured Properties Section -->\r\n<section class=\"featured-properties-section py-15\">\r\n  <div class=\"container\">\r\n    <!-- Section Header -->\r\n    <div class=\"row mb-10\">\r\n      <div class=\"col-12 text-center\">\r\n        <h2 class=\"section-title\">\r\n          <i class=\"ki-outline ki-star fs-1 text-warning me-3\"></i>\r\n          العقارات المميزة\r\n        </h2>\r\n        <p class=\"section-subtitle\">اكتشف أفضل العقارات المتاحة حالياً</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Properties Grid -->\r\n    <div class=\"row g-6\">\r\n      <div class=\"col-lg-3 col-md-6\" *ngFor=\"let property of featuredProperties\">\r\n        <div class=\"property-card\" (click)=\"viewProperty(property.id)\">\r\n          <!-- Property Image -->\r\n          <div class=\"property-image\">\r\n            <img [src]=\"property.image\" [alt]=\"property.title\" class=\"w-100\">\r\n            <div class=\"property-badges\">\r\n              <span class=\"badge badge-success\" *ngIf=\"property.isNew\">جديد</span>\r\n            </div>\r\n            <div class=\"property-rating\">\r\n              <i class=\"ki-solid ki-star text-warning\"></i>\r\n              <span>{{ property.rating }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Property Content -->\r\n          <div class=\"property-content\">\r\n            <h4 class=\"property-title\">{{ property.title }}</h4>\r\n            <p class=\"property-location\">\r\n              <i class=\"ki-outline ki-geolocation text-muted me-2\"></i>\r\n              {{ property.location }}\r\n            </p>\r\n\r\n            <div class=\"property-details\">\r\n              <div class=\"property-detail\" *ngIf=\"property.bedrooms > 0\">\r\n                <i class=\"ki-outline ki-home text-muted\"></i>\r\n                <span>{{ property.bedrooms }} غرف</span>\r\n              </div>\r\n              <div class=\"property-detail\" *ngIf=\"property.bathrooms > 0\">\r\n                <i class=\"ki-outline ki-droplet text-muted\"></i>\r\n                <span>{{ property.bathrooms }} حمام</span>\r\n              </div>\r\n              <div class=\"property-detail\">\r\n                <i class=\"ki-outline ki-resize text-muted\"></i>\r\n                <span>{{ property.area }} م²</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"property-price\">\r\n              <span class=\"price\">{{ property.price }}</span>\r\n              <span class=\"currency\">جنيه</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- View All Button -->\r\n    <div class=\"row mt-10\">\r\n      <div class=\"col-12 text-center\">\r\n        <button class=\"btn btn-outline-primary btn-lg\">\r\n          عرض جميع العقارات\r\n          <i class=\"ki-outline ki-arrow-left ms-2\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Cities Carousel Section -->\r\n<section class=\"cities-section py-15 bg-light\">\r\n  <div class=\"container\">\r\n    <!-- Section Header -->\r\n    <div class=\"row mb-10\">\r\n      <div class=\"col-12 text-center\">\r\n        <h2 class=\"section-title\">\r\n          <i class=\"ki-outline ki-map fs-1 text-primary me-3\"></i>\r\n          استكشف المدن\r\n        </h2>\r\n        <p class=\"section-subtitle\">اختر المدينة التي تناسبك</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Cities Carousel -->\r\n    <div class=\"cities-carousel\">\r\n      <div class=\"row g-4\">\r\n        <div class=\"col-lg-2 col-md-4 col-6\" *ngFor=\"let city of cities\">\r\n          <div class=\"city-card\" (click)=\"viewCity(city.name)\">\r\n            <div class=\"city-image\">\r\n              <img [src]=\"city.image\" [alt]=\"city.name\" class=\"w-100\">\r\n              <div class=\"city-overlay\">\r\n                <div class=\"city-content\">\r\n                  <h4 class=\"city-name\">{{ city.name }}</h4>\r\n                  <p class=\"city-count\">{{ city.propertiesCount }} عقار</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Blog Articles Section -->\r\n<section class=\"blog-section py-15\">\r\n  <div class=\"container\">\r\n    <!-- Section Header -->\r\n    <div class=\"row mb-10\">\r\n      <div class=\"col-12 text-center\">\r\n        <h2 class=\"section-title\">\r\n          <i class=\"ki-outline ki-book fs-1 text-info me-3\"></i>\r\n          مقالات تهمك\r\n        </h2>\r\n        <p class=\"section-subtitle\">اقرأ أحدث المقالات والنصائح العقارية</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Articles Grid -->\r\n    <div class=\"row g-6\">\r\n      <div class=\"col-lg-4 col-md-6\" *ngFor=\"let article of blogArticles\">\r\n        <div class=\"article-card\" (click)=\"readArticle(article.id)\">\r\n          <!-- Article Image -->\r\n          <div class=\"article-image\">\r\n            <img [src]=\"article.image\" [alt]=\"article.title\" class=\"w-100\">\r\n            <div class=\"article-category\">\r\n              <span class=\"badge badge-primary\">{{ article.category }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Article Content -->\r\n          <div class=\"article-content\">\r\n            <h4 class=\"article-title\">{{ article.title }}</h4>\r\n            <p class=\"article-excerpt\">{{ article.excerpt }}</p>\r\n\r\n            <div class=\"article-meta\">\r\n              <div class=\"article-author\">\r\n                <i class=\"ki-outline ki-profile-user text-muted me-2\"></i>\r\n                <span>{{ article.author }}</span>\r\n              </div>\r\n              <div class=\"article-date\">\r\n                <i class=\"ki-outline ki-calendar text-muted me-2\"></i>\r\n                <span>{{ article.date }}</span>\r\n              </div>\r\n              <div class=\"article-read-time\">\r\n                <i class=\"ki-outline ki-time text-muted me-2\"></i>\r\n                <span>{{ article.readTime }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- View All Articles Button -->\r\n    <div class=\"row mt-10\">\r\n      <div class=\"col-12 text-center\">\r\n        <button class=\"btn btn-outline-info btn-lg\">\r\n          عرض جميع المقالات\r\n          <i class=\"ki-outline ki-arrow-left ms-2\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Newsletter Section -->\r\n<section class=\"newsletter-section py-15 bg-primary\">\r\n  <div class=\"container\">\r\n    <div class=\"row align-items-center\">\r\n      <div class=\"col-lg-6\">\r\n        <div class=\"newsletter-content text-white\">\r\n          <h3 class=\"newsletter-title\">اشترك في النشرة الإخبارية</h3>\r\n          <p class=\"newsletter-subtitle\">\r\n            احصل على أحدث العروض والمقالات العقارية مباشرة في بريدك الإلكتروني\r\n          </p>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-lg-6\">\r\n        <div class=\"newsletter-form\">\r\n          <div class=\"input-group\">\r\n            <input type=\"email\" class=\"form-control form-control-lg\" placeholder=\"أدخل بريدك الإلكتروني\">\r\n            <button class=\"btn btn-light btn-lg\" type=\"button\">\r\n              <i class=\"ki-outline ki-send fs-2\"></i>\r\n              اشترك\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;;;ICoE9BC,EAFJ,CAAAC,cAAA,cAAsG,cACrE,cACkC;IAC7DD,EAAA,CAAAE,SAAA,QAAwD;IAC1DF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAI,MAAA,GAAsB;IAE3DJ,EAF2D,CAAAG,YAAA,EAAI,EACvD,EACF;;;;;IAR+DH,EAAA,CAAAK,UAAA,iBAAAC,IAAA,MAAgC;IAErEN,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAK,UAAA,eAAAG,OAAA,CAAAC,KAAA,CAAkC;IACzDT,EAAA,CAAAO,SAAA,EAAgD;IAAhDP,EAAA,CAAAK,UAAA,CAAAG,OAAA,CAAAE,IAAA,mBAAAF,OAAA,CAAAC,KAAA,CAAgD;IAEzBT,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAW,iBAAA,CAAAH,OAAA,CAAAI,KAAA,CAAgB;IACXZ,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAW,iBAAA,CAAAH,OAAA,CAAAK,WAAA,CAAsB;;;;;IAgCzDb,EAAA,CAAAC,cAAA,eAAyD;IAAAD,EAAA,CAAAI,MAAA,+BAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAiBpEH,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,SAAA,YAA6C;IAC7CF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAA2B;IACnCJ,EADmC,CAAAG,YAAA,EAAO,EACpC;;;;IADEH,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAc,kBAAA,KAAAC,WAAA,CAAAC,QAAA,wBAA2B;;;;;IAEnChB,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAA6B;IACrCJ,EADqC,CAAAG,YAAA,EAAO,EACtC;;;;IADEH,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAc,kBAAA,KAAAC,WAAA,CAAAE,SAAA,8BAA6B;;;;;;IA5B3CjB,EADF,CAAAC,cAAA,cAA2E,cACV;IAApCD,EAAA,CAAAkB,UAAA,mBAAAC,mDAAA;MAAA,MAAAJ,WAAA,GAAAf,EAAA,CAAAoB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAX,WAAA,CAAAY,EAAA,CAAyB;IAAA,EAAC;IAE5D3B,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAE,SAAA,cAAiE;IACjEF,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAA4B,UAAA,IAAAC,oCAAA,mBAAyD;IAC3D7B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAE,SAAA,YAA6C;IAC7CF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAAqB;IAE/BJ,EAF+B,CAAAG,YAAA,EAAO,EAC9B,EACF;IAIJH,EADF,CAAAC,cAAA,eAA8B,cACD;IAAAD,EAAA,CAAAI,MAAA,IAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAE,SAAA,aAAyD;IACzDF,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAC,cAAA,eAA8B;IAK5BD,EAJA,CAAA4B,UAAA,KAAAE,oCAAA,kBAA2D,KAAAC,oCAAA,kBAIC;IAI5D/B,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAE,SAAA,aAA+C;IAC/CF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAAsB;IAEhCJ,EAFgC,CAAAG,YAAA,EAAO,EAC/B,EACF;IAGJH,EADF,CAAAC,cAAA,eAA4B,gBACN;IAAAD,EAAA,CAAAI,MAAA,IAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAI,MAAA,gCAAI;IAInCJ,EAJmC,CAAAG,YAAA,EAAO,EAC9B,EACF,EACF,EACF;;;;IAvCKH,EAAA,CAAAO,SAAA,GAAsB;IAACP,EAAvB,CAAAgC,UAAA,QAAAjB,WAAA,CAAAkB,KAAA,EAAAjC,EAAA,CAAAkC,aAAA,CAAsB,QAAAnB,WAAA,CAAAH,KAAA,CAAuB;IAEbZ,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAgC,UAAA,SAAAjB,WAAA,CAAAoB,KAAA,CAAoB;IAIjDnC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAW,iBAAA,CAAAI,WAAA,CAAAqB,MAAA,CAAqB;IAMFpC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAW,iBAAA,CAAAI,WAAA,CAAAH,KAAA,CAAoB;IAG7CZ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAC,WAAA,CAAAsB,QAAA,MACF;IAGgCrC,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAgC,UAAA,SAAAjB,WAAA,CAAAC,QAAA,KAA2B;IAI3BhB,EAAA,CAAAO,SAAA,EAA4B;IAA5BP,EAAA,CAAAgC,UAAA,SAAAjB,WAAA,CAAAE,SAAA,KAA4B;IAMlDjB,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAc,kBAAA,KAAAC,WAAA,CAAAuB,IAAA,kBAAsB;IAKVtC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAW,iBAAA,CAAAI,WAAA,CAAAwB,KAAA,CAAoB;;;;;;IAsC5CvC,EADF,CAAAC,cAAA,cAAiE,cACV;IAA9BD,EAAA,CAAAkB,UAAA,mBAAAsB,mDAAA;MAAA,MAAAC,OAAA,GAAAzC,EAAA,CAAAoB,aAAA,CAAAsB,GAAA,EAAApB,SAAA;MAAA,MAAAC,MAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASF,MAAA,CAAAoB,QAAA,CAAAF,OAAA,CAAAG,IAAA,CAAmB;IAAA,EAAC;IAClD5C,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,SAAA,cAAwD;IAGpDF,EAFJ,CAAAC,cAAA,cAA0B,cACE,aACF;IAAAD,EAAA,CAAAI,MAAA,GAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAI,MAAA,GAA+B;IAK/DJ,EAL+D,CAAAG,YAAA,EAAI,EACrD,EACF,EACF,EACF,EACF;;;;IATKH,EAAA,CAAAO,SAAA,GAAkB;IAACP,EAAnB,CAAAgC,UAAA,QAAAS,OAAA,CAAAR,KAAA,EAAAjC,EAAA,CAAAkC,aAAA,CAAkB,QAAAO,OAAA,CAAAG,IAAA,CAAkB;IAGf5C,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAW,iBAAA,CAAA8B,OAAA,CAAAG,IAAA,CAAe;IACf5C,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAc,kBAAA,KAAA2B,OAAA,CAAAI,eAAA,8BAA+B;;;;;;IA4B/D7C,EADF,CAAAC,cAAA,cAAoE,cACN;IAAlCD,EAAA,CAAAkB,UAAA,mBAAA4B,mDAAA;MAAA,MAAAC,UAAA,GAAA/C,EAAA,CAAAoB,aAAA,CAAA4B,GAAA,EAAA1B,SAAA;MAAA,MAAAC,MAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASF,MAAA,CAAA0B,WAAA,CAAAF,UAAA,CAAApB,EAAA,CAAuB;IAAA,EAAC;IAEzD3B,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAE,SAAA,cAA+D;IAE7DF,EADF,CAAAC,cAAA,cAA8B,eACM;IAAAD,EAAA,CAAAI,MAAA,GAAsB;IAE5DJ,EAF4D,CAAAG,YAAA,EAAO,EAC3D,EACF;IAIJH,EADF,CAAAC,cAAA,cAA6B,aACD;IAAAD,EAAA,CAAAI,MAAA,GAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAI,MAAA,IAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAGlDH,EADF,CAAAC,cAAA,eAA0B,eACI;IAC1BD,EAAA,CAAAE,SAAA,aAA0D;IAC1DF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAAoB;IAC5BJ,EAD4B,CAAAG,YAAA,EAAO,EAC7B;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,SAAA,cAAsD;IACtDF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAAkB;IAC1BJ,EAD0B,CAAAG,YAAA,EAAO,EAC3B;IACNH,EAAA,CAAAC,cAAA,gBAA+B;IAC7BD,EAAA,CAAAE,SAAA,cAAkD;IAClDF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAAsB;IAKtCJ,EALsC,CAAAG,YAAA,EAAO,EAC/B,EACF,EACF,EACF,EACF;;;;IA3BKH,EAAA,CAAAO,SAAA,GAAqB;IAACP,EAAtB,CAAAgC,UAAA,QAAAe,UAAA,CAAAd,KAAA,EAAAjC,EAAA,CAAAkC,aAAA,CAAqB,QAAAa,UAAA,CAAAnC,KAAA,CAAsB;IAEZZ,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAW,iBAAA,CAAAoC,UAAA,CAAAG,QAAA,CAAsB;IAMhClD,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAW,iBAAA,CAAAoC,UAAA,CAAAnC,KAAA,CAAmB;IAClBZ,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAW,iBAAA,CAAAoC,UAAA,CAAAI,OAAA,CAAqB;IAKtCnD,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAW,iBAAA,CAAAoC,UAAA,CAAAK,MAAA,CAAoB;IAIpBpD,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAW,iBAAA,CAAAoC,UAAA,CAAAM,IAAA,CAAkB;IAIlBrD,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAW,iBAAA,CAAAoC,UAAA,CAAAO,QAAA,CAAsB;;;ADhO5C,OAAM,MAAOC,aAAa;EAExB;EACAC,SAAS,GAAG,CACV;IACE5C,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,mCAAmC;IAChDH,IAAI,EAAE,4BAA4B;IAClCD,KAAK,EAAE;GACR,EACD;IACEG,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,yBAAyB;IACtCH,IAAI,EAAE,qBAAqB;IAC3BD,KAAK,EAAE;GACR,EACD;IACEG,KAAK,EAAE,KAAK;IACZC,WAAW,EAAE,iCAAiC;IAC9CH,IAAI,EAAE,2BAA2B;IACjCD,KAAK,EAAE;GACR,CACF;EAED;EACAgD,kBAAkB,GAAG,CACnB;IACE9B,EAAE,EAAE,CAAC;IACLf,KAAK,EAAE,4BAA4B;IACnCyB,QAAQ,EAAE,gCAAgC;IAC1CE,KAAK,EAAE,WAAW;IAClBD,IAAI,EAAE,KAAK;IACXtB,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,CAAC;IACZgB,KAAK,EAAE,wCAAwC;IAC/CG,MAAM,EAAE,GAAG;IACXD,KAAK,EAAE;GACR,EACD;IACER,EAAE,EAAE,CAAC;IACLf,KAAK,EAAE,0BAA0B;IACjCyB,QAAQ,EAAE,oBAAoB;IAC9BE,KAAK,EAAE,WAAW;IAClBD,IAAI,EAAE,KAAK;IACXtB,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,CAAC;IACZgB,KAAK,EAAE,wCAAwC;IAC/CG,MAAM,EAAE,GAAG;IACXD,KAAK,EAAE;GACR,EACD;IACER,EAAE,EAAE,CAAC;IACLf,KAAK,EAAE,wBAAwB;IAC/ByB,QAAQ,EAAE,kBAAkB;IAC5BE,KAAK,EAAE,QAAQ;IACfD,IAAI,EAAE,IAAI;IACVtB,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,CAAC;IACZgB,KAAK,EAAE,wCAAwC;IAC/CG,MAAM,EAAE,GAAG;IACXD,KAAK,EAAE;GACR,EACD;IACER,EAAE,EAAE,CAAC;IACLf,KAAK,EAAE,yBAAyB;IAChCyB,QAAQ,EAAE,oBAAoB;IAC9BE,KAAK,EAAE,WAAW;IAClBD,IAAI,EAAE,IAAI;IACVtB,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,CAAC;IACZgB,KAAK,EAAE,wCAAwC;IAC/CG,MAAM,EAAE,GAAG;IACXD,KAAK,EAAE;GACR,CACF;EAED;EACAuB,MAAM,GAAG,CACP;IACEd,IAAI,EAAE,iBAAiB;IACvBX,KAAK,EAAE,mCAAmC;IAC1CY,eAAe,EAAE;GAClB,EACD;IACED,IAAI,EAAE,YAAY;IAClBX,KAAK,EAAE,sCAAsC;IAC7CY,eAAe,EAAE;GAClB,EACD;IACED,IAAI,EAAE,kBAAkB;IACxBX,KAAK,EAAE,qCAAqC;IAC5CY,eAAe,EAAE;GAClB,EACD;IACED,IAAI,EAAE,gBAAgB;IACtBX,KAAK,EAAE,qCAAqC;IAC5CY,eAAe,EAAE;GAClB,EACD;IACED,IAAI,EAAE,cAAc;IACpBX,KAAK,EAAE,oCAAoC;IAC3CY,eAAe,EAAE;GAClB,CACF;EAED;EACAc,YAAY,GAAG,CACb;IACEhC,EAAE,EAAE,CAAC;IACLf,KAAK,EAAE,4BAA4B;IACnCuC,OAAO,EAAE,uDAAuD;IAChElB,KAAK,EAAE,iCAAiC;IACxCmB,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,YAAY;IAClBC,QAAQ,EAAE,SAAS;IACnBJ,QAAQ,EAAE;GACX,EACD;IACEvB,EAAE,EAAE,CAAC;IACLf,KAAK,EAAE,yBAAyB;IAChCuC,OAAO,EAAE,0DAA0D;IACnElB,KAAK,EAAE,iCAAiC;IACxCmB,MAAM,EAAE,WAAW;IACnBC,IAAI,EAAE,YAAY;IAClBC,QAAQ,EAAE,SAAS;IACnBJ,QAAQ,EAAE;GACX,EACD;IACEvB,EAAE,EAAE,CAAC;IACLf,KAAK,EAAE,wBAAwB;IAC/BuC,OAAO,EAAE,sEAAsE;IAC/ElB,KAAK,EAAE,iCAAiC;IACxCmB,MAAM,EAAE,UAAU;IAClBC,IAAI,EAAE,YAAY;IAClBC,QAAQ,EAAE,SAAS;IACnBJ,QAAQ,EAAE;GACX,CACF;EAEDU,YAAA,GAAgB;EAEhBC,QAAQA,CAAA;IACN;EAAA;EAGF;EACAC,gBAAgBA,CAAA;IACd;IACAC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;EACxC;EAEAtC,YAAYA,CAACuC,UAAkB;IAC7B;IACAF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,UAAU,CAAC;EAC9C;EAEAtB,QAAQA,CAACuB,QAAgB;IACvB;IACAH,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEE,QAAQ,CAAC;EACxC;EAEAjB,WAAWA,CAACkB,SAAiB;IAC3B;IACAJ,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEG,SAAS,CAAC;EAC5C;;qCApKWZ,aAAa;EAAA;;UAAbA,aAAa;IAAAa,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAtE,EAAA,CAAAuE,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTxB7E,EADF,CAAAC,cAAA,iBAA8B,aACC;QAC3BD,EAAA,CAAAE,SAAA,aAAgC;QAMxBF,EALR,CAAAC,cAAA,aAAuB,aAC0B,aAEvB,aACM,YACD;QACrBD,EAAA,CAAAI,MAAA,8HACA;QAAAJ,EAAA,CAAAC,cAAA,cAA2B;QAAAD,EAAA,CAAAI,MAAA,qEAAW;QACxCJ,EADwC,CAAAG,YAAA,EAAO,EAC1C;QACLH,EAAA,CAAAC,cAAA,YAAyB;QACvBD,EAAA,CAAAI,MAAA,+XACF;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAKAH,EAFJ,CAAAC,cAAA,eAA8B,eACH,kBACW;QAAAD,EAAA,CAAAI,MAAA,gCAAI;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAC/CH,EAAA,CAAAC,cAAA,kBAA2B;QAAAD,EAAA,CAAAI,MAAA,sCAAK;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QACzCH,EAAA,CAAAC,cAAA,kBAA2B;QAAAD,EAAA,CAAAI,MAAA,kDAAO;QACpCJ,EADoC,CAAAG,YAAA,EAAS,EACvC;QAGJH,EADF,CAAAC,cAAA,eAA2B,eACO;QAC9BD,EAAA,CAAAE,SAAA,aAA8C;QAE5CF,EADF,CAAAC,cAAA,kBAA4B,cAClB;QAAAD,EAAA,CAAAI,MAAA,2EAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAC7BH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,6FAAe;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAChCH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,+DAAU;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAC3BH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,mGAAgB;QAE5BJ,EAF4B,CAAAG,YAAA,EAAS,EAC1B,EACL;QAENH,EAAA,CAAAC,cAAA,eAAgC;QAC9BD,EAAA,CAAAE,SAAA,aAAuC;QAErCF,EADF,CAAAC,cAAA,kBAA4B,cAClB;QAAAD,EAAA,CAAAI,MAAA,+DAAU;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAC3BH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,0BAAG;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QACpBH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,gCAAI;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QACrBH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,gCAAI;QAEhBJ,EAFgB,CAAAG,YAAA,EAAS,EACd,EACL;QAENH,EAAA,CAAAC,cAAA,eAAgC;QAC9BD,EAAA,CAAAE,SAAA,aAAyC;QAEvCF,EADF,CAAAC,cAAA,kBAA4B,cAClB;QAAAD,EAAA,CAAAI,MAAA,8DAAS;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAC1BH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,sEAAY;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAC7BH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,0CAAS;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAC1BH,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAI,MAAA,8EAAe;QAE3BJ,EAF2B,CAAAG,YAAA,EAAS,EACzB,EACL;QAENH,EAAA,CAAAC,cAAA,kBAAwE;QAA7BD,EAAA,CAAAkB,UAAA,mBAAA6D,gDAAA;UAAA,OAASD,GAAA,CAAAhB,gBAAA,EAAkB;QAAA,EAAC;QACrE9D,EAAA,CAAAE,SAAA,aAA4C;QAC5CF,EAAA,CAAAI,MAAA,kCACF;QAIRJ,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;QAIJH,EADF,CAAAC,cAAA,cAAsB,eACI;QACtBD,EAAA,CAAA4B,UAAA,KAAAoD,6BAAA,kBAAsG;QAclHhF,EALU,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF,EACE;QAQFH,EALR,CAAAC,cAAA,mBAAmD,cAC1B,eAEE,eACW,cACJ;QACxBD,EAAA,CAAAE,SAAA,aAAyD;QACzDF,EAAA,CAAAI,MAAA,qGACF;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,aAA4B;QAAAD,EAAA,CAAAI,MAAA,gMAAkC;QAElEJ,EAFkE,CAAAG,YAAA,EAAI,EAC9D,EACF;QAGNH,EAAA,CAAAC,cAAA,eAAqB;QACnBD,EAAA,CAAA4B,UAAA,KAAAqD,6BAAA,oBAA2E;QA4C7EjF,EAAA,CAAAG,YAAA,EAAM;QAKFH,EAFJ,CAAAC,cAAA,eAAuB,eACW,kBACiB;QAC7CD,EAAA,CAAAI,MAAA,sGACA;QAAAJ,EAAA,CAAAE,SAAA,aAA6C;QAKvDF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACE;QAQFH,EALR,CAAAC,cAAA,mBAA+C,cACtB,eAEE,eACW,cACJ;QACxBD,EAAA,CAAAE,SAAA,aAAwD;QACxDF,EAAA,CAAAI,MAAA,6EACF;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,aAA4B;QAAAD,EAAA,CAAAI,MAAA,yIAAwB;QAExDJ,EAFwD,CAAAG,YAAA,EAAI,EACpD,EACF;QAIJH,EADF,CAAAC,cAAA,eAA6B,eACN;QACnBD,EAAA,CAAA4B,UAAA,KAAAsD,6BAAA,mBAAiE;QAgBzElF,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACE;QAQFH,EALR,CAAAC,cAAA,mBAAoC,cACX,eAEE,eACW,cACJ;QACxBD,EAAA,CAAAE,SAAA,aAAsD;QACtDF,EAAA,CAAAI,MAAA,uEACF;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,aAA4B;QAAAD,EAAA,CAAAI,MAAA,4MAAoC;QAEpEJ,EAFoE,CAAAG,YAAA,EAAI,EAChE,EACF;QAGNH,EAAA,CAAAC,cAAA,eAAqB;QACnBD,EAAA,CAAA4B,UAAA,KAAAuD,6BAAA,mBAAoE;QAgCtEnF,EAAA,CAAAG,YAAA,EAAM;QAKFH,EAFJ,CAAAC,cAAA,gBAAuB,gBACW,mBACc;QAC1CD,EAAA,CAAAI,MAAA,uGACA;QAAAJ,EAAA,CAAAE,SAAA,cAA6C;QAKvDF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACE;QAQAH,EALV,CAAAC,cAAA,oBAAqD,eAC5B,gBACe,eACZ,gBACuB,eACZ;QAAAD,EAAA,CAAAI,MAAA,gJAAyB;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QAC3DH,EAAA,CAAAC,cAAA,cAA+B;QAC7BD,EAAA,CAAAI,MAAA,0WACF;QAEJJ,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;QAGFH,EAFJ,CAAAC,cAAA,eAAsB,gBACS,gBACF;QACvBD,EAAA,CAAAE,SAAA,kBAA6F;QAC7FF,EAAA,CAAAC,cAAA,mBAAmD;QACjDD,EAAA,CAAAE,SAAA,cAAuC;QACvCF,EAAA,CAAAI,MAAA,yCACF;QAMZJ,EANY,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF,EACE;;;QApN0CH,EAAA,CAAAO,SAAA,IAAc;QAAdP,EAAA,CAAAgC,UAAA,YAAA8C,GAAA,CAAAtB,SAAA,CAAc;QAgCRxD,EAAA,CAAAO,SAAA,IAAqB;QAArBP,EAAA,CAAAgC,UAAA,YAAA8C,GAAA,CAAArB,kBAAA,CAAqB;QA2EjBzD,EAAA,CAAAO,SAAA,IAAS;QAATP,EAAA,CAAAgC,UAAA,YAAA8C,GAAA,CAAApB,MAAA,CAAS;QAkCd1D,EAAA,CAAAO,SAAA,IAAe;QAAfP,EAAA,CAAAgC,UAAA,YAAA8C,GAAA,CAAAnB,YAAA,CAAe;;;mBD1M5D7D,YAAY,EAAAsF,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEvF,YAAY;IAAAwF,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}