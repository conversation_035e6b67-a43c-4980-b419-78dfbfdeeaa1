{"ast": null, "code": "import { BaseConfigService } from './base-config.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./inside-compound/sell-config.service\";\nimport * as i2 from \"./inside-compound/purchasing-config.service\";\nimport * as i3 from \"./inside-compound/rental-config.service\";\nimport * as i4 from \"./outside-compound/rental-outside-compound-config.service\";\nimport * as i5 from \"./outside-compound/sell-outside-compound-config.service\";\nimport * as i6 from \"./outside-compound/purchase-outside-compound-config.service\";\nexport class ConfigFactoryService extends BaseConfigService {\n  sellConfigService;\n  purchasingConfigService;\n  rentalConfigService;\n  rentalOutsideCompoundConfigService;\n  sellOutsideCompoundConfigService;\n  purchaseOutsideCompoundConfigService;\n  constructor(sellConfigService, purchasingConfigService, rentalConfigService, rentalOutsideCompoundConfigService, sellOutsideCompoundConfigService, purchaseOutsideCompoundConfigService) {\n    super();\n    this.sellConfigService = sellConfigService;\n    this.purchasingConfigService = purchasingConfigService;\n    this.rentalConfigService = rentalConfigService;\n    this.rentalOutsideCompoundConfigService = rentalOutsideCompoundConfigService;\n    this.sellOutsideCompoundConfigService = sellOutsideCompoundConfigService;\n    this.purchaseOutsideCompoundConfigService = purchaseOutsideCompoundConfigService;\n  }\n  /**\n   * Get input configurations from all services\n   */\n  getInputConfigs(stepperModal) {\n    return [...this.sellConfigService.getInputConfigs(stepperModal), ...this.purchasingConfigService.getInputConfigs(stepperModal), ...this.rentalConfigService.getInputConfigs(stepperModal), ...this.rentalOutsideCompoundConfigService.getInputConfigs(stepperModal), ...this.sellOutsideCompoundConfigService.getInputConfigs(stepperModal), ...this.purchaseOutsideCompoundConfigService.getInputConfigs(stepperModal)];\n  }\n  /**\n  * Get inputs for a specific configuration key and step\n  */\n  getInputsForKey(key, step, stepperModal) {\n    // Check sell outside compound configurations first (more specific)\n    if (key.includes('purchase_sell_outside_compound_sell_')) {\n      return this.sellOutsideCompoundConfigService.getInputsForKey(key, step, stepperModal);\n    }\n    // Check purchase outside compound configurations (more specific)\n    if (key.includes('purchase_sell_outside_compound_purchasing_')) {\n      return this.purchaseOutsideCompoundConfigService.getInputsForKey(key, step, stepperModal);\n    }\n    // Check sell configurations (inside compound)\n    if (key.includes('_sell_')) {\n      return this.sellConfigService.getInputsForKey(key, step, stepperModal);\n    }\n    // Check purchase configurations\n    if (key.includes('_purchasing_')) {\n      return this.purchasingConfigService.getInputsForKey(key, step, stepperModal);\n    }\n    // Check rental outside compound configurations (more specific)\n    if (key.includes('rentals_outside_compound_rent_out_') || key.includes('rentals_outside_compound_rent_in_')) {\n      return this.rentalOutsideCompoundConfigService.getInputsForKey(key, step, stepperModal);\n    }\n    // Check rental configurations (general)\n    if (key.includes('_rent_')) {\n      return this.rentalConfigService.getInputsForKey(key, step, stepperModal);\n    }\n    // Default fallback\n    return this.getDefaultInputsForStep(step);\n  }\n  /**\n   * Check if a configuration key exists in any service\n   */\n  hasConfiguration(key, stepperModal) {\n    return this.sellConfigService.hasConfiguration(key, stepperModal) || this.purchasingConfigService.hasConfiguration(key, stepperModal) || this.rentalConfigService.hasConfiguration(key, stepperModal) || this.rentalOutsideCompoundConfigService.hasConfiguration(key, stepperModal) || this.sellOutsideCompoundConfigService.hasConfiguration(key, stepperModal) || this.purchaseOutsideCompoundConfigService.hasConfiguration(key, stepperModal);\n  }\n  /**\n   * Get all available configuration keys from all services\n   */\n  getAllAvailableConfigKeys(stepperModal) {\n    const allConfigs = this.getInputConfigs(stepperModal);\n    return allConfigs.map(config => config.key);\n  }\n  /**\n   * Get configuration type based on key\n   */\n  getConfigurationType(key) {\n    if (key.includes('purchase_sell_outside_compound_sell_')) return 'sell_outside_compound';\n    if (key.includes('purchase_sell_outside_compound_purchasing_')) return 'purchase_outside_compound';\n    if (key.includes('_sell_')) return 'sell';\n    if (key.includes('_purchasing_')) return 'purchase';\n    if (key.includes('rentals_outside_compound_rent_out_') || key.includes('rentals_outside_compound_rent_in_')) return 'rentals_outside_compound';\n    if (key.includes('_rent_')) return 'rental';\n    return 'unknown';\n  }\n  static ɵfac = function ConfigFactoryService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ConfigFactoryService)(i0.ɵɵinject(i1.SellConfigService), i0.ɵɵinject(i2.PurchasingConfigService), i0.ɵɵinject(i3.RentalConfigService), i0.ɵɵinject(i4.RentalOutsideCompoundConfigService), i0.ɵɵinject(i5.SellOutsideCompoundConfigService), i0.ɵɵinject(i6.PurchaseOutsideCompoundConfigService));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ConfigFactoryService,\n    factory: ConfigFactoryService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BaseConfigService", "ConfigFactoryService", "sellConfigService", "purchasingConfigService", "rentalConfigService", "rentalOutsideCompoundConfigService", "sellOutsideCompoundConfigService", "purchaseOutsideCompoundConfigService", "constructor", "getInputConfigs", "stepperModal", "getInputsForKey", "key", "step", "includes", "getDefaultInputsForStep", "hasConfiguration", "getAllAvailableConfigKeys", "allConfigs", "map", "config", "getConfigurationType", "i0", "ɵɵinject", "i1", "SellConfigService", "i2", "PurchasingConfigService", "i3", "RentalConfigService", "i4", "RentalOutsideCompoundConfigService", "i5", "SellOutsideCompoundConfigService", "i6", "PurchaseOutsideCompoundConfigService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\shared\\stepper-modal\\services\\config-factory.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BaseConfigService, InputConfig, StepperConfiguration } from './base-config.service';\r\nimport { SellConfigService } from './inside-compound/sell-config.service';\r\nimport { PurchasingConfigService } from './inside-compound/purchasing-config.service';\r\nimport { RentalConfigService } from './inside-compound/rental-config.service';\r\nimport { RentalOutsideCompoundConfigService } from './outside-compound/rental-outside-compound-config.service';\r\nimport { SellOutsideCompoundConfigService } from './outside-compound/sell-outside-compound-config.service';\r\nimport { PurchaseOutsideCompoundConfigService } from './outside-compound/purchase-outside-compound-config.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ConfigFactoryService extends BaseConfigService {\r\n\r\n  constructor(\r\n\r\n    private sellConfigService: SellConfigService,\r\n    private purchasingConfigService: PurchasingConfigService,\r\n    private rentalConfigService: RentalConfigService,\r\n    private rentalOutsideCompoundConfigService: RentalOutsideCompoundConfigService,\r\n    private sellOutsideCompoundConfigService: SellOutsideCompoundConfigService,\r\n    private purchaseOutsideCompoundConfigService: PurchaseOutsideCompoundConfigService\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  /**\r\n   * Get input configurations from all services\r\n   */\r\n  getInputConfigs(stepperModal: any): StepperConfiguration[] {\r\n    return [\r\n      ...this.sellConfigService.getInputConfigs(stepperModal),\r\n      ...this.purchasingConfigService.getInputConfigs(stepperModal),\r\n      ...this.rentalConfigService.getInputConfigs(stepperModal),\r\n      ...this.rentalOutsideCompoundConfigService.getInputConfigs(stepperModal),\r\n      ...this.sellOutsideCompoundConfigService.getInputConfigs(stepperModal),\r\n      ...this.purchaseOutsideCompoundConfigService.getInputConfigs(stepperModal),\r\n    ];\r\n  }\r\n\r\n    /**\r\n   * Get inputs for a specific configuration key and step\r\n   */\r\n  getInputsForKey(key: string, step: number, stepperModal: any): InputConfig[] {\r\n    // Check sell outside compound configurations first (more specific)\r\n    if (key.includes('purchase_sell_outside_compound_sell_')) {\r\n      return this.sellOutsideCompoundConfigService.getInputsForKey(key, step, stepperModal);\r\n    }\r\n\r\n    // Check purchase outside compound configurations (more specific)\r\n    if (key.includes('purchase_sell_outside_compound_purchasing_')) {\r\n      return this.purchaseOutsideCompoundConfigService.getInputsForKey(key, step, stepperModal);\r\n    }\r\n\r\n    // Check sell configurations (inside compound)\r\n    if (key.includes('_sell_')) {\r\n      return this.sellConfigService.getInputsForKey(key, step, stepperModal);\r\n    }\r\n\r\n    // Check purchase configurations\r\n    if (key.includes('_purchasing_')) {\r\n      return this.purchasingConfigService.getInputsForKey(key, step, stepperModal);\r\n    }\r\n\r\n    // Check rental outside compound configurations (more specific)\r\n    if (key.includes('rentals_outside_compound_rent_out_') || key.includes('rentals_outside_compound_rent_in_')) {\r\n      return this.rentalOutsideCompoundConfigService.getInputsForKey(key, step, stepperModal);\r\n    }\r\n\r\n    // Check rental configurations (general)\r\n    if (key.includes('_rent_')) {\r\n      return this.rentalConfigService.getInputsForKey(key, step, stepperModal);\r\n    }\r\n\r\n    // Default fallback\r\n    return this.getDefaultInputsForStep(step);\r\n  }\r\n\r\n  /**\r\n   * Check if a configuration key exists in any service\r\n   */\r\n  hasConfiguration(key: string, stepperModal: any): boolean {\r\n      return this.sellConfigService.hasConfiguration(key, stepperModal) ||\r\n            this.purchasingConfigService.hasConfiguration(key, stepperModal) ||\r\n            this.rentalConfigService.hasConfiguration(key, stepperModal)  ||\r\n            this.rentalOutsideCompoundConfigService.hasConfiguration(key, stepperModal) ||\r\n            this.sellOutsideCompoundConfigService.hasConfiguration(key, stepperModal) ||\r\n            this.purchaseOutsideCompoundConfigService.hasConfiguration(key, stepperModal);\r\n  }\r\n\r\n  /**\r\n   * Get all available configuration keys from all services\r\n   */\r\n  getAllAvailableConfigKeys(stepperModal: any): string[] {\r\n    const allConfigs = this.getInputConfigs(stepperModal);\r\n    return allConfigs.map(config => config.key);\r\n  }\r\n\r\n  /**\r\n   * Get configuration type based on key\r\n   */\r\n  getConfigurationType(key: string): 'sell' | 'purchase' | 'rental' | 'rentals_outside_compound' | 'sell_outside_compound' | 'purchase_outside_compound' | 'unknown' {\r\n    if (key.includes('purchase_sell_outside_compound_sell_')) return 'sell_outside_compound';\r\n    if (key.includes('purchase_sell_outside_compound_purchasing_')) return 'purchase_outside_compound';\r\n    if (key.includes('_sell_')) return 'sell';\r\n    if (key.includes('_purchasing_')) return 'purchase';\r\n    if (key.includes('rentals_outside_compound_rent_out_') || key.includes('rentals_outside_compound_rent_in_')) return 'rentals_outside_compound';\r\n    if (key.includes('_rent_')) return 'rental';\r\n    return 'unknown';\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,iBAAiB,QAA2C,uBAAuB;;;;;;;;AAW5F,OAAM,MAAOC,oBAAqB,SAAQD,iBAAiB;EAI/CE,iBAAA;EACAC,uBAAA;EACAC,mBAAA;EACAC,kCAAA;EACAC,gCAAA;EACAC,oCAAA;EAPVC,YAEUN,iBAAoC,EACpCC,uBAAgD,EAChDC,mBAAwC,EACxCC,kCAAsE,EACtEC,gCAAkE,EAClEC,oCAA0E;IAElF,KAAK,EAAE;IAPC,KAAAL,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,uBAAuB,GAAvBA,uBAAuB;IACvB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,kCAAkC,GAAlCA,kCAAkC;IAClC,KAAAC,gCAAgC,GAAhCA,gCAAgC;IAChC,KAAAC,oCAAoC,GAApCA,oCAAoC;EAG9C;EAEA;;;EAGAE,eAAeA,CAACC,YAAiB;IAC/B,OAAO,CACL,GAAG,IAAI,CAACR,iBAAiB,CAACO,eAAe,CAACC,YAAY,CAAC,EACvD,GAAG,IAAI,CAACP,uBAAuB,CAACM,eAAe,CAACC,YAAY,CAAC,EAC7D,GAAG,IAAI,CAACN,mBAAmB,CAACK,eAAe,CAACC,YAAY,CAAC,EACzD,GAAG,IAAI,CAACL,kCAAkC,CAACI,eAAe,CAACC,YAAY,CAAC,EACxE,GAAG,IAAI,CAACJ,gCAAgC,CAACG,eAAe,CAACC,YAAY,CAAC,EACtE,GAAG,IAAI,CAACH,oCAAoC,CAACE,eAAe,CAACC,YAAY,CAAC,CAC3E;EACH;EAEE;;;EAGFC,eAAeA,CAACC,GAAW,EAAEC,IAAY,EAAEH,YAAiB;IAC1D;IACA,IAAIE,GAAG,CAACE,QAAQ,CAAC,sCAAsC,CAAC,EAAE;MACxD,OAAO,IAAI,CAACR,gCAAgC,CAACK,eAAe,CAACC,GAAG,EAAEC,IAAI,EAAEH,YAAY,CAAC;IACvF;IAEA;IACA,IAAIE,GAAG,CAACE,QAAQ,CAAC,4CAA4C,CAAC,EAAE;MAC9D,OAAO,IAAI,CAACP,oCAAoC,CAACI,eAAe,CAACC,GAAG,EAAEC,IAAI,EAAEH,YAAY,CAAC;IAC3F;IAEA;IACA,IAAIE,GAAG,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC1B,OAAO,IAAI,CAACZ,iBAAiB,CAACS,eAAe,CAACC,GAAG,EAAEC,IAAI,EAAEH,YAAY,CAAC;IACxE;IAEA;IACA,IAAIE,GAAG,CAACE,QAAQ,CAAC,cAAc,CAAC,EAAE;MAChC,OAAO,IAAI,CAACX,uBAAuB,CAACQ,eAAe,CAACC,GAAG,EAAEC,IAAI,EAAEH,YAAY,CAAC;IAC9E;IAEA;IACA,IAAIE,GAAG,CAACE,QAAQ,CAAC,oCAAoC,CAAC,IAAIF,GAAG,CAACE,QAAQ,CAAC,mCAAmC,CAAC,EAAE;MAC3G,OAAO,IAAI,CAACT,kCAAkC,CAACM,eAAe,CAACC,GAAG,EAAEC,IAAI,EAAEH,YAAY,CAAC;IACzF;IAEA;IACA,IAAIE,GAAG,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC1B,OAAO,IAAI,CAACV,mBAAmB,CAACO,eAAe,CAACC,GAAG,EAAEC,IAAI,EAAEH,YAAY,CAAC;IAC1E;IAEA;IACA,OAAO,IAAI,CAACK,uBAAuB,CAACF,IAAI,CAAC;EAC3C;EAEA;;;EAGAG,gBAAgBA,CAACJ,GAAW,EAAEF,YAAiB;IAC3C,OAAO,IAAI,CAACR,iBAAiB,CAACc,gBAAgB,CAACJ,GAAG,EAAEF,YAAY,CAAC,IAC3D,IAAI,CAACP,uBAAuB,CAACa,gBAAgB,CAACJ,GAAG,EAAEF,YAAY,CAAC,IAChE,IAAI,CAACN,mBAAmB,CAACY,gBAAgB,CAACJ,GAAG,EAAEF,YAAY,CAAC,IAC5D,IAAI,CAACL,kCAAkC,CAACW,gBAAgB,CAACJ,GAAG,EAAEF,YAAY,CAAC,IAC3E,IAAI,CAACJ,gCAAgC,CAACU,gBAAgB,CAACJ,GAAG,EAAEF,YAAY,CAAC,IACzE,IAAI,CAACH,oCAAoC,CAACS,gBAAgB,CAACJ,GAAG,EAAEF,YAAY,CAAC;EACvF;EAEA;;;EAGAO,yBAAyBA,CAACP,YAAiB;IACzC,MAAMQ,UAAU,GAAG,IAAI,CAACT,eAAe,CAACC,YAAY,CAAC;IACrD,OAAOQ,UAAU,CAACC,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACR,GAAG,CAAC;EAC7C;EAEA;;;EAGAS,oBAAoBA,CAACT,GAAW;IAC9B,IAAIA,GAAG,CAACE,QAAQ,CAAC,sCAAsC,CAAC,EAAE,OAAO,uBAAuB;IACxF,IAAIF,GAAG,CAACE,QAAQ,CAAC,4CAA4C,CAAC,EAAE,OAAO,2BAA2B;IAClG,IAAIF,GAAG,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,MAAM;IACzC,IAAIF,GAAG,CAACE,QAAQ,CAAC,cAAc,CAAC,EAAE,OAAO,UAAU;IACnD,IAAIF,GAAG,CAACE,QAAQ,CAAC,oCAAoC,CAAC,IAAIF,GAAG,CAACE,QAAQ,CAAC,mCAAmC,CAAC,EAAE,OAAO,0BAA0B;IAC9I,IAAIF,GAAG,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,QAAQ;IAC3C,OAAO,SAAS;EAClB;;qCAjGWb,oBAAoB,EAAAqB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,uBAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,mBAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,kCAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,gCAAA,GAAAX,EAAA,CAAAC,QAAA,CAAAW,EAAA,CAAAC,oCAAA;EAAA;;WAApBlC,oBAAoB;IAAAmC,OAAA,EAApBnC,oBAAoB,CAAAoC,IAAA;IAAAC,UAAA,EAFnB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}