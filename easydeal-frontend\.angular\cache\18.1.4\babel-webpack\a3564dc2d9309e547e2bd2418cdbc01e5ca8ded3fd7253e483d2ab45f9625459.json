{"ast": null, "code": "import { Modal } from 'bootstrap';\nimport { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/unit.service\";\nimport * as i2 from \"@angular/platform-browser\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../../../shared/view-apartment-model/view-apartment-model.component\";\nimport * as i6 from \"../../../../../pagination/pagination.component\";\nconst _c0 = (a0, a1) => ({\n  \"text-success\": a0,\n  \"text-primary\": a1\n});\nconst _c1 = (a0, a1, a2, a3, a4) => ({\n  \"badge-light-success\": a0,\n  \"badge-light-danger\": a1,\n  \"badge-light-warning\": a2,\n  \"badge-light-info\": a3,\n  \"badge-light-primary\": a4\n});\nfunction PropertiestableComponent_th_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 12);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_4_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"owner_name\"));\n    });\n    i0.ɵɵtext(1, \" Owner Name \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"owner_name\"));\n  }\n}\nfunction PropertiestableComponent_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_5_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"ownerPhone\"));\n    });\n    i0.ɵɵtext(1, \" Owner Phone \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"ownerPhone\"));\n  }\n}\nfunction PropertiestableComponent_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_6_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"type\"));\n    });\n    i0.ɵɵtext(1, \" Unit Type \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"type\"));\n  }\n}\nfunction PropertiestableComponent_th_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_7_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"city_id\"));\n    });\n    i0.ɵɵtext(1, \" City \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"city_id\"));\n  }\n}\nfunction PropertiestableComponent_th_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_8_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"area_id\"));\n    });\n    i0.ɵɵtext(1, \" Area \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"area_id\"));\n  }\n}\nfunction PropertiestableComponent_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_9_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"sub_area\"));\n    });\n    i0.ɵɵtext(1, \" Sub Area \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"sub_area\"));\n  }\n}\nfunction PropertiestableComponent_th_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_10_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"detailedAddress\"));\n    });\n    i0.ɵɵtext(1, \" Detailed Address \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"detailedAddress\"));\n  }\n}\nfunction PropertiestableComponent_th_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 15);\n    i0.ɵɵtext(1, \" Location on map \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PropertiestableComponent_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_12_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"compound_name\"));\n    });\n    i0.ɵɵtext(1, \" Compound Name \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"compound_name\"));\n  }\n}\nfunction PropertiestableComponent_th_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_13_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"mall_name\"));\n    });\n    i0.ɵɵtext(1, \" Mall Name \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"mall_name\"));\n  }\n}\nfunction PropertiestableComponent_th_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_14_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"building_number\"));\n    });\n    i0.ɵɵtext(1, \" Building Number \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"building_number\"));\n  }\n}\nfunction PropertiestableComponent_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_15_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"unit_number\"));\n    });\n    i0.ɵɵtext(1, \" Unit Number \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"unit_number\"));\n  }\n}\nfunction PropertiestableComponent_th_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_16_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"floor\"));\n    });\n    i0.ɵɵtext(1, \" Floor \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"floor\"));\n  }\n}\nfunction PropertiestableComponent_th_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_17_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"unit_area\"));\n    });\n    i0.ɵɵtext(1, \" Unit Area \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"unit_area\"));\n  }\n}\nfunction PropertiestableComponent_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_18_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"ground_area\"));\n    });\n    i0.ɵɵtext(1, \" Ground Area \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"ground_area\"));\n  }\n}\nfunction PropertiestableComponent_th_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_19_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"building_area\"));\n    });\n    i0.ɵɵtext(1, \" Building Area \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"building_area\"));\n  }\n}\nfunction PropertiestableComponent_th_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_20_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"number_of_rooms\"));\n    });\n    i0.ɵɵtext(1, \" Rooms \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"number_of_rooms\"));\n  }\n}\nfunction PropertiestableComponent_th_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_21_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"number_of_bathrooms\"));\n    });\n    i0.ɵɵtext(1, \" Bathrooms \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"number_of_bathrooms\"));\n  }\n}\nfunction PropertiestableComponent_th_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_22_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"number_of_floors\"));\n    });\n    i0.ɵɵtext(1, \" number of Floors \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"number_of_floors\"));\n  }\n}\nfunction PropertiestableComponent_th_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_23_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"view\"));\n    });\n    i0.ɵɵtext(1, \" View \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"view\"));\n  }\n}\nfunction PropertiestableComponent_th_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_24_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"finishing_type\"));\n    });\n    i0.ɵɵtext(1, \" Finishing Type \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"finishing_type\"));\n  }\n}\nfunction PropertiestableComponent_th_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_25_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"unit_description\"));\n    });\n    i0.ɵɵtext(1, \" Unit Description \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"unit_description\"));\n  }\n}\nfunction PropertiestableComponent_th_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_26_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"unit_design\"));\n    });\n    i0.ɵɵtext(1, \" Unit Design \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"unit_design\"));\n  }\n}\nfunction PropertiestableComponent_th_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_27_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"unit_facing\"));\n    });\n    i0.ɵɵtext(1, \" Unit Facing \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"unit_facing\"));\n  }\n}\nfunction PropertiestableComponent_th_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_28_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"delivery_status\"));\n    });\n    i0.ɵɵtext(1, \" Delivery Status \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"delivery_status\"));\n  }\n}\nfunction PropertiestableComponent_th_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_29_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"fit_out_condition\"));\n    });\n    i0.ɵɵtext(1, \" Fit Out Condition \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"fit_out_condition\"));\n  }\n}\nfunction PropertiestableComponent_th_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_30_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"building_deadline\"));\n    });\n    i0.ɵɵtext(1, \" Building Deadline \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"building_deadline\"));\n  }\n}\nfunction PropertiestableComponent_th_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_31_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"requested_over\"));\n    });\n    i0.ɵɵtext(1, \" Requested Over \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"requested_over\"));\n  }\n}\nfunction PropertiestableComponent_th_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_32_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"furnishing_status\"));\n    });\n    i0.ɵɵtext(1, \" Furnishing Status \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"furnishing_status\"));\n  }\n}\nfunction PropertiestableComponent_th_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_33_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"legal_status\"));\n    });\n    i0.ɵɵtext(1, \" Legal Status \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"legal_status\"));\n  }\n}\nfunction PropertiestableComponent_th_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_34_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"financial_status\"));\n    });\n    i0.ɵɵtext(1, \" Financial Status \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"financial_status\"));\n  }\n}\nfunction PropertiestableComponent_th_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_35_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"activity\"));\n    });\n    i0.ɵɵtext(1, \" Activity \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"activity\"));\n  }\n}\nfunction PropertiestableComponent_th_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_36_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"payment_system\"));\n    });\n    i0.ɵɵtext(1, \" Payment System \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"payment_system\"));\n  }\n}\nfunction PropertiestableComponent_th_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_37_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"total_price\"));\n    });\n    i0.ɵɵtext(1, \" Total Price \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"total_price\"));\n  }\n}\nfunction PropertiestableComponent_th_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_38_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"rent_recurrence\"));\n    });\n    i0.ɵɵtext(1, \" Rent Recurrence \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"rent_recurrence\"));\n  }\n}\nfunction PropertiestableComponent_th_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_39_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"daily_rent\"));\n    });\n    i0.ɵɵtext(1, \" Daily Rent \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"daily_rent\"));\n  }\n}\nfunction PropertiestableComponent_th_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_40_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"monthly_rent\"));\n    });\n    i0.ɵɵtext(1, \" Monthly Rent \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"monthly_rent\"));\n  }\n}\nfunction PropertiestableComponent_th_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 16);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_41_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"other_accessories\"));\n    });\n    i0.ɵɵtext(1, \" Other Accessories \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"other_accessories\"));\n  }\n}\nfunction PropertiestableComponent_th_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 15);\n    i0.ɵɵtext(1, \" Unit Plan \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PropertiestableComponent_th_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 14);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_th_43_Template_th_click_0_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sortData(\"status\"));\n    });\n    i0.ɵɵtext(1, \" Status \");\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getSortArrow(\"status\"));\n  }\n}\nfunction PropertiestableComponent_th_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 17);\n    i0.ɵɵtext(1, \" Actions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PropertiestableComponent_tr_46_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 20);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.ownerName, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.ownerPhone, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.type, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.city == null ? null : property_r40.city.name_en, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.area == null ? null : property_r40.area.name_en, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (property_r40 == null ? null : property_r40.subArea == null ? null : property_r40.subArea.name_en) || \"Unknown\", \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.detailedAddress, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_tr_46_td_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const property_r40 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showImageModal(property_r40.location));\n    });\n    i0.ɵɵelement(2, \"i\", 23);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PropertiestableComponent_tr_46_td_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.compoundName, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.additionalDetails == null ? null : property_r40.additionalDetails.mallName, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.buildingNumber, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.unitNumber, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.floor, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.unitArea, \" m\\u00B2 \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.additionalDetails == null ? null : property_r40.additionalDetails.groundArea, \" m\\u00B2 \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.additionalDetails == null ? null : property_r40.additionalDetails.buildingArea, \" m\\u00B2 \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.numberOfRooms, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.numberOfBathrooms, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.additionalDetails == null ? null : property_r40.additionalDetails.numberOfFloors, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.view, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.finishingType, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.additionalDetails == null ? null : property_r40.additionalDetails.unitDescription, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.additionalDetails == null ? null : property_r40.additionalDetails.unitDesign, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.additionalDetails == null ? null : property_r40.additionalDetails.unitFacing, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(property_r40.deliveryStatus);\n  }\n}\nfunction PropertiestableComponent_tr_46_td_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.additionalDetails == null ? null : property_r40.additionalDetails.fitOutCondition, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.additionalDetails == null ? null : property_r40.additionalDetails.buildingDeadline, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.requestedOver, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(property_r40.additionalDetails == null ? null : property_r40.additionalDetails.furnishingStatus);\n  }\n}\nfunction PropertiestableComponent_tr_46_td_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(property_r40.additionalDetails == null ? null : property_r40.additionalDetails.legalStatus);\n  }\n}\nfunction PropertiestableComponent_tr_46_td_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(property_r40.additionalDetails == null ? null : property_r40.additionalDetails.financialStatus);\n  }\n}\nfunction PropertiestableComponent_tr_46_td_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.additionalDetails == null ? null : property_r40.additionalDetails.activity, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.paymentSystem, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_34_span_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"currency\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind4(2, 1, property_r40.totalPriceInCash, \"EGP\", \"symbol\", \"1.0-0\"), \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_34_span_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"currency\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind4(2, 1, property_r40.totalPriceInInstallment, \"EGP\", \"symbol\", \"1.0-0\"), \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_34_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtemplate(1, PropertiestableComponent_tr_46_td_34_span_1_ng_container_1_Template, 3, 6, \"ng-container\", 18)(2, PropertiestableComponent_tr_46_td_34_span_1_ng_container_2_Template, 3, 6, \"ng-container\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c0, property_r40.paymentSystem === \"cash\", property_r40.paymentSystem === \"installment\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", property_r40.paymentSystem === \"cash\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", property_r40.paymentSystem === \"installment\");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_34_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"span\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 32);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Cash: \", i0.ɵɵpipeBind4(3, 2, property_r40.totalPriceInCash, \"EGP\", \"symbol\", \"1.0-0\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Installment: \", i0.ɵɵpipeBind4(6, 7, property_r40.totalPriceInInstallment, \"EGP\", \"symbol\", \"1.0-0\"), \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtemplate(1, PropertiestableComponent_tr_46_td_34_span_1_Template, 3, 6, \"span\", 27)(2, PropertiestableComponent_tr_46_td_34_div_2_Template, 7, 12, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", property_r40.paymentSystem !== \"all_of_the_above_are_suitable\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", property_r40.paymentSystem === \"all_of_the_above_are_suitable\");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 33);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(property_r40.additionalDetails == null ? null : property_r40.additionalDetails.rentRecurrence);\n  }\n}\nfunction PropertiestableComponent_tr_46_td_36_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"currency\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind4(2, 1, property_r40.dailyRent, \"EGP\", \"symbol\", \"1.0-0\"), \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_36_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PropertiestableComponent_tr_46_td_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtemplate(1, PropertiestableComponent_tr_46_td_36_span_1_Template, 3, 6, \"span\", 34)(2, PropertiestableComponent_tr_46_td_36_span_2_Template, 2, 0, \"span\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (property_r40.additionalDetails == null ? null : property_r40.additionalDetails.rentRecurrence) === \"daily\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (property_r40.additionalDetails == null ? null : property_r40.additionalDetails.rentRecurrence) !== \"daily\");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_37_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"currency\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind4(2, 1, property_r40.monthlyRent, \"EGP\", \"symbol\", \"1.0-0\"), \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_37_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PropertiestableComponent_tr_46_td_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtemplate(1, PropertiestableComponent_tr_46_td_37_span_1_Template, 3, 6, \"span\", 34)(2, PropertiestableComponent_tr_46_td_37_span_2_Template, 2, 0, \"span\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (property_r40.additionalDetails == null ? null : property_r40.additionalDetails.rentRecurrence) === \"monthly\" || (property_r40.additionalDetails == null ? null : property_r40.additionalDetails.rentRecurrence) === \"annually\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (property_r40.additionalDetails == null ? null : property_r40.additionalDetails.rentRecurrence) !== \"monthly\" && (property_r40.additionalDetails == null ? null : property_r40.additionalDetails.rentRecurrence) !== \"annually\");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", property_r40.otherAccessories, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_tr_46_td_39_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const property_r40 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showUnitPlanModal(property_r40.diagram));\n    });\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵtext(3, \" View Plan \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PropertiestableComponent_tr_46_td_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"span\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const property_r40 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(2, _c1, property_r40.status === \"available\", property_r40.status === \"sold\", property_r40.status === \"reserved\", property_r40.status === \"pending\", property_r40.status === \"new\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", property_r40.status, \" \");\n  }\n}\nfunction PropertiestableComponent_tr_46_td_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 41)(1, \"div\", 42)(2, \"button\", 43);\n    i0.ɵɵelement(3, \"i\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 45)(5, \"li\")(6, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function PropertiestableComponent_tr_46_td_41_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const property_r40 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.viewProperty(property_r40));\n    });\n    i0.ɵɵelement(7, \"i\", 47);\n    i0.ɵɵtext(8, \" View unit Details \");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction PropertiestableComponent_tr_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵtemplate(1, PropertiestableComponent_tr_46_td_1_Template, 3, 1, \"td\", 18)(2, PropertiestableComponent_tr_46_td_2_Template, 3, 1, \"td\", 18)(3, PropertiestableComponent_tr_46_td_3_Template, 3, 1, \"td\", 18)(4, PropertiestableComponent_tr_46_td_4_Template, 3, 1, \"td\", 18)(5, PropertiestableComponent_tr_46_td_5_Template, 3, 1, \"td\", 18)(6, PropertiestableComponent_tr_46_td_6_Template, 3, 1, \"td\", 18)(7, PropertiestableComponent_tr_46_td_7_Template, 3, 1, \"td\", 18)(8, PropertiestableComponent_tr_46_td_8_Template, 3, 0, \"td\", 18)(9, PropertiestableComponent_tr_46_td_9_Template, 3, 1, \"td\", 18)(10, PropertiestableComponent_tr_46_td_10_Template, 3, 1, \"td\", 18)(11, PropertiestableComponent_tr_46_td_11_Template, 3, 1, \"td\", 18)(12, PropertiestableComponent_tr_46_td_12_Template, 3, 1, \"td\", 18)(13, PropertiestableComponent_tr_46_td_13_Template, 3, 1, \"td\", 18)(14, PropertiestableComponent_tr_46_td_14_Template, 3, 1, \"td\", 18)(15, PropertiestableComponent_tr_46_td_15_Template, 3, 1, \"td\", 18)(16, PropertiestableComponent_tr_46_td_16_Template, 3, 1, \"td\", 18)(17, PropertiestableComponent_tr_46_td_17_Template, 3, 1, \"td\", 18)(18, PropertiestableComponent_tr_46_td_18_Template, 3, 1, \"td\", 18)(19, PropertiestableComponent_tr_46_td_19_Template, 3, 1, \"td\", 18)(20, PropertiestableComponent_tr_46_td_20_Template, 3, 1, \"td\", 18)(21, PropertiestableComponent_tr_46_td_21_Template, 3, 1, \"td\", 18)(22, PropertiestableComponent_tr_46_td_22_Template, 3, 1, \"td\", 18)(23, PropertiestableComponent_tr_46_td_23_Template, 3, 1, \"td\", 18)(24, PropertiestableComponent_tr_46_td_24_Template, 3, 1, \"td\", 18)(25, PropertiestableComponent_tr_46_td_25_Template, 3, 1, \"td\", 18)(26, PropertiestableComponent_tr_46_td_26_Template, 3, 1, \"td\", 18)(27, PropertiestableComponent_tr_46_td_27_Template, 3, 1, \"td\", 18)(28, PropertiestableComponent_tr_46_td_28_Template, 3, 1, \"td\", 18)(29, PropertiestableComponent_tr_46_td_29_Template, 3, 1, \"td\", 18)(30, PropertiestableComponent_tr_46_td_30_Template, 3, 1, \"td\", 18)(31, PropertiestableComponent_tr_46_td_31_Template, 3, 1, \"td\", 18)(32, PropertiestableComponent_tr_46_td_32_Template, 3, 1, \"td\", 18)(33, PropertiestableComponent_tr_46_td_33_Template, 3, 1, \"td\", 18)(34, PropertiestableComponent_tr_46_td_34_Template, 3, 2, \"td\", 18)(35, PropertiestableComponent_tr_46_td_35_Template, 3, 1, \"td\", 18)(36, PropertiestableComponent_tr_46_td_36_Template, 3, 2, \"td\", 18)(37, PropertiestableComponent_tr_46_td_37_Template, 3, 2, \"td\", 18)(38, PropertiestableComponent_tr_46_td_38_Template, 3, 1, \"td\", 18)(39, PropertiestableComponent_tr_46_td_39_Template, 4, 0, \"td\", 18)(40, PropertiestableComponent_tr_46_td_40_Template, 3, 8, \"td\", 18)(41, PropertiestableComponent_tr_46_td_41_Template, 9, 0, \"td\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"ownerName\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"ownerPhone\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"type\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"city\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"area\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"subArea\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"detailedAddress\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"location\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"compoundName\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"mallName\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"buildingNumber\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"unitNumber\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"floor\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"unitArea\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"groundArea\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"buildingArea\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"numberOfRooms\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"numberOfBathrooms\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"numberOfFloors\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"view\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"finishingType\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"unitDescription\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"unitDesign\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"unitFacing\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"deliveryStatus\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"fitOutCondition\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"buildingDeadline\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"requestedOver\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"furnishingStatus\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"legalStatus\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"financialStatus\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"activity\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"paymentSystem\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"totalPrice\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"rentRecurrence\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"dailyRent\") && ctx_r1.hasPropertiesWithDailyRent());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"monthlyRent\") && ctx_r1.hasPropertiesWithMonthlyRent());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"otherAccessories\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"unitPlan\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"status\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shouldShowColumn(\"actions\"));\n  }\n}\nexport class PropertiestableComponent extends BaseGridComponent {\n  cd;\n  unitService;\n  sanitizer;\n  router;\n  //session\n  brokerId;\n  appliedFilters;\n  selectedImage = null;\n  selectedLocation = null;\n  // Dynamic column visibility\n  visibleColumns = [];\n  constructor(cd, unitService, sanitizer, router) {\n    super(cd);\n    this.cd = cd;\n    this.unitService = unitService;\n    this.sanitizer = sanitizer;\n    this.router = router;\n    const userJson = localStorage.getItem('currentUser');\n    let user = userJson ? JSON.parse(userJson) : null;\n    this.brokerId = user?.brokerId;\n    this.setService(unitService);\n    this.orderBy = 'id';\n    this.orderDir = 'desc';\n    this.page.filters = {\n      brokerId: this.brokerId\n    };\n  }\n  ngOnChanges(changes) {\n    if (changes.appliedFilters && !changes.appliedFilters.firstChange) {\n      this.page.filters = {\n        brokerId: this.brokerId,\n        ...this.appliedFilters\n      };\n      this.updateVisibleColumns();\n      this.reloadTable(this.page);\n    }\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    this.updateVisibleColumns();\n  }\n  updateVisibleColumns() {\n    if (this.appliedFilters?.compoundType && this.appliedFilters?.unitType) {\n      this.visibleColumns = this.getFieldsToShow();\n    } else {\n      // Show all columns by default\n      this.visibleColumns = ['ownerName', 'ownerPhone', 'type', 'city', 'area', 'detailedAddress', 'location', 'unitPlan', 'status', 'otherAccessories', 'actions'];\n    }\n  }\n  // Get fields to show based on compound type and property type\n  getFieldsToShow() {\n    const compoundType = this.appliedFilters?.compoundType;\n    const type = this.appliedFilters?.unitType;\n    // Base columns that are always shown\n    const baseColumns = ['ownerName', 'ownerPhone', 'type', 'city', 'area', 'subArea', 'detailedAddress', 'location'];\n    const actionColumns = ['unitPlan', 'status', 'actions'];\n    let specificColumns = [];\n    if (compoundType === 'outside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'roofs' || type === 'basement')) {\n      specificColumns = ['buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'unitFacing', 'view', 'finishingType', 'deliveryStatus', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'pricePerMeterInInstallment', 'totalPrice'];\n    } else if (compoundType === 'outside_compound' && (type == 'villas' || type == 'full_buildings')) {\n      specificColumns = ['buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'unitDescription', 'unitDesign', 'unitFacing', 'view', 'finishingType', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'pricePerMeterInInstallment', 'totalPrice'];\n    } else if (compoundType === 'outside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\n      specificColumns = ['mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'fitOutCondition', 'deliveryStatus', 'activity', 'financialStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'pricePerMeterInInstallment', 'totalPrice'];\n    } else if (compoundType === 'outside_compound' && (type === 'warehouses' || type === 'factories')) {\n      specificColumns = ['buildingNumber', 'numberOfFloors', 'groundArea', 'buildingArea', 'activity', 'finishingType', 'unitDescription', 'legalStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'pricePerMeterInInstallment', 'totalPrice'];\n    } else if (compoundType === 'outside_compound' && (type === 'residential_villa_lands' || type === 'residential_lands' || type === 'administrative_lands' || type === 'commercial_administrative_lands' || type === 'commercial_lands' || type === 'medical_lands' || type === 'mixed_lands' || type === 'warehouses_land' || type === 'industrial_lands')) {\n      specificColumns = ['unitNumber', 'groundArea', 'fitOutCondition', 'unitDescription', 'buildingDeadline', 'view', 'legalStatus', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'paymentSystem', 'pricePerMeterInCash', 'pricePerMeterInInstallment', 'totalPrice'];\n    } else if (compoundType === 'inside_compound' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'i_villa')) {\n      specificColumns = ['compoundName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'pricePerMeterInInstallment', 'totalPrice'];\n    } else if (compoundType === 'inside_compound' && (type === 'standalone_villas' || type === 'twin_houses' || type === 'town_houses')) {\n      specificColumns = ['compoundName', 'buildingNumber', 'numberOfFloors', 'buildingArea', 'groundArea', 'numberOfRooms', 'numberOfBathrooms', 'view', 'finishingType', 'deliveryStatus', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'pricePerMeterInInstallment', 'totalPrice'];\n    } else if (compoundType === 'inside_compound' && (type === 'pharmacies' || type === 'medical_clinics' || type === 'administrative_units' || type === 'commercial_stores')) {\n      specificColumns = ['compoundName', 'mallName', 'buildingNumber', 'unitNumber', 'floor', 'unitArea', 'view', 'finishingType', 'deliveryStatus', 'fitOutCondition', 'financialStatus', 'otherAccessories', 'requestedOver', 'paymentSystem', 'pricePerMeterInCash', 'pricePerMeterInInstallment', 'totalPrice'];\n    } else if (compoundType === 'village' && (type === 'apartments' || type === 'duplexes' || type === 'studios' || type === 'penthouses' || type === 'basement' || type === 'roofs' || type === 'i_villa' || type === 'villas' || type === 'standalone_villas' || type === 'full_buildings' || type === 'twin_houses' || type === 'town_houses' || type === 'chalets')) {\n      specificColumns = ['buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'dailyRent', 'monthlyRent', 'annualRent'];\n    } else if (compoundType === 'village' && (type === 'warehouses' || type === 'factories' || type === 'administrative_units' || type === 'medical_clinics' || type === 'commercial_stores' || type === 'pharmacies')) {\n      specificColumns = ['buildingNumber', 'unitNumber', 'unitArea', 'floor', 'view', 'numberOfRooms', 'numberOfBathrooms', 'finishingType', 'furnishingStatus', 'otherAccessories', 'rentRecurrence', 'activity', 'dailyRent', 'monthlyRent'];\n    }\n    return [...baseColumns, ...specificColumns, ...actionColumns];\n  }\n  // Check if a specific column should be shown\n  shouldShowColumn(columnName) {\n    return this.visibleColumns.includes(columnName);\n  }\n  // Check if there are properties with daily rent\n  hasPropertiesWithDailyRent() {\n    return this.rows.some(property => property.additionalDetails?.rentRecurrence === 'daily' && property.dailyRent);\n  }\n  // Check if there are properties with monthly/annually rent\n  hasPropertiesWithMonthlyRent() {\n    return this.rows.some(property => (property.additionalDetails?.rentRecurrence === 'monthly' || property.additionalDetails?.rentRecurrence === 'annually') && property.monthlyRent);\n  }\n  showImageModal(location) {\n    if (!location || location.trim() === '') {\n      Swal.fire({\n        title: 'Warning',\n        text: 'No location available',\n        icon: 'warning',\n        confirmButtonText: 'OK'\n      });\n      return;\n    }\n    if (location.includes('maps.google.com') || location.includes('maps.app.goo.gl')) {\n      window.open(location, '_blank');\n      return;\n    }\n    const mapUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(location)}`;\n    window.open(mapUrl, '_blank');\n  }\n  //****************************** */\n  selectedUnitPlanImage = null;\n  showUnitPlanModal(imgPath) {\n    this.selectedUnitPlanImage = imgPath;\n    const modalElement = document.getElementById('viewUnitPlanModal');\n    if (modalElement) {\n      const modal = new Modal(modalElement);\n      modal.show();\n    }\n  }\n  viewProperty(unitService) {\n    this.router.navigate(['/developer/projects/models/units/details'], {\n      queryParams: {\n        unitId: unitService.id\n      }\n    });\n  }\n  static ɵfac = function PropertiestableComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PropertiestableComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.UnitService), i0.ɵɵdirectiveInject(i2.DomSanitizer), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PropertiestableComponent,\n    selectors: [[\"app-propertiestable\"]],\n    inputs: {\n      appliedFilters: \"appliedFilters\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature],\n    decls: 50,\n    vars: 46,\n    consts: [[1, \"table-responsive\", \"mb-5\"], [1, \"table\", \"table-row-bordered\", \"table-row-gray-100\", \"align-middle\", \"gs-0\", \"gy-3\", \"mt-5\"], [1, \"fw-bold\", \"bg-light-dark-blue\", \"text-dark-blue\", \"me-1\", \"ms-1\"], [\"class\", \"min-w-150px cursor-pointer ps-4 rounded-start\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"min-w-150px cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"min-w-150px\", 4, \"ngIf\"], [\"class\", \"min-w-200px cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"min-w-50px text-end rounded-end pe-4\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"m-2\"], [3, \"pageChange\", \"totalItems\", \"itemsPerPage\", \"currentPage\"], [3, \"selectedUnitPlanImage\"], [1, \"min-w-150px\", \"cursor-pointer\", \"ps-4\", \"rounded-start\", 3, \"click\"], [1, \"ms-1\", \"text-primary\", \"fw-bold\"], [1, \"min-w-150px\", \"cursor-pointer\", 3, \"click\"], [1, \"min-w-150px\"], [1, \"min-w-200px\", \"cursor-pointer\", 3, \"click\"], [1, \"min-w-50px\", \"text-end\", \"rounded-end\", \"pe-4\"], [4, \"ngIf\"], [\"class\", \"text-end pe-4\", 4, \"ngIf\"], [1, \"text-gray-900\", \"fw-bold\", \"d-block\", \"mb-1\", \"fs-6\", \"ps-4\"], [1, \"text-gray-900\", \"fw-bold\", \"d-block\", \"mb-1\", \"fs-6\"], [\"data-bs-toggle\", \"tooltip\", \"title\", \"View on map\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"btn-light-primary\", 3, \"click\"], [1, \"fa-solid\", \"fa-map-location-dot\"], [1, \"badge\", \"badge-light-success\"], [1, \"badge\", \"badge-light-info\"], [1, \"badge\", \"badge-light-warning\"], [\"class\", \"fw-bold d-block mb-1 fs-6\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"d-flex flex-column gap-1\", 4, \"ngIf\"], [1, \"fw-bold\", \"d-block\", \"mb-1\", \"fs-6\", 3, \"ngClass\"], [1, \"d-flex\", \"flex-column\", \"gap-1\"], [1, \"text-success\", \"fw-bold\", \"fs-6\"], [1, \"text-primary\", \"fw-bold\", \"fs-6\"], [1, \"badge\", \"badge-light-primary\"], [\"class\", \"text-success fw-bold d-block mb-1 fs-6\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"text-success\", \"fw-bold\", \"d-block\", \"mb-1\", \"fs-6\"], [1, \"text-muted\"], [1, \"btn\", \"btn-sm\", \"btn-light-info\", 3, \"click\"], [1, \"fa-solid\", \"fa-file-image\", \"me-1\"], [1, \"badge\", \"fw-bold\", 3, \"ngClass\"], [1, \"text-end\", \"pe-4\"], [1, \"dropdown\"], [\"type\", \"button\", \"data-bs-toggle\", \"dropdown\", 1, \"btn\", \"btn-sm\", \"btn-icon\", \"btn-color-primary\", \"btn-active-light-primary\"], [1, \"fa-solid\", \"fa-ellipsis-vertical\"], [1, \"dropdown-menu\"], [1, \"dropdown-item\", 3, \"click\"], [1, \"fa-solid\", \"fa-eye\", \"me-2\"]],\n    template: function PropertiestableComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"table\", 1)(2, \"thead\")(3, \"tr\", 2);\n        i0.ɵɵtemplate(4, PropertiestableComponent_th_4_Template, 4, 1, \"th\", 3)(5, PropertiestableComponent_th_5_Template, 4, 1, \"th\", 4)(6, PropertiestableComponent_th_6_Template, 4, 1, \"th\", 4)(7, PropertiestableComponent_th_7_Template, 4, 1, \"th\", 4)(8, PropertiestableComponent_th_8_Template, 4, 1, \"th\", 4)(9, PropertiestableComponent_th_9_Template, 4, 1, \"th\", 4)(10, PropertiestableComponent_th_10_Template, 4, 1, \"th\", 4)(11, PropertiestableComponent_th_11_Template, 2, 0, \"th\", 5)(12, PropertiestableComponent_th_12_Template, 4, 1, \"th\", 4)(13, PropertiestableComponent_th_13_Template, 4, 1, \"th\", 4)(14, PropertiestableComponent_th_14_Template, 4, 1, \"th\", 4)(15, PropertiestableComponent_th_15_Template, 4, 1, \"th\", 4)(16, PropertiestableComponent_th_16_Template, 4, 1, \"th\", 4)(17, PropertiestableComponent_th_17_Template, 4, 1, \"th\", 4)(18, PropertiestableComponent_th_18_Template, 4, 1, \"th\", 4)(19, PropertiestableComponent_th_19_Template, 4, 1, \"th\", 4)(20, PropertiestableComponent_th_20_Template, 4, 1, \"th\", 4)(21, PropertiestableComponent_th_21_Template, 4, 1, \"th\", 4)(22, PropertiestableComponent_th_22_Template, 4, 1, \"th\", 4)(23, PropertiestableComponent_th_23_Template, 4, 1, \"th\", 4)(24, PropertiestableComponent_th_24_Template, 4, 1, \"th\", 4)(25, PropertiestableComponent_th_25_Template, 4, 1, \"th\", 4)(26, PropertiestableComponent_th_26_Template, 4, 1, \"th\", 4)(27, PropertiestableComponent_th_27_Template, 4, 1, \"th\", 4)(28, PropertiestableComponent_th_28_Template, 4, 1, \"th\", 4)(29, PropertiestableComponent_th_29_Template, 4, 1, \"th\", 4)(30, PropertiestableComponent_th_30_Template, 4, 1, \"th\", 4)(31, PropertiestableComponent_th_31_Template, 4, 1, \"th\", 4)(32, PropertiestableComponent_th_32_Template, 4, 1, \"th\", 4)(33, PropertiestableComponent_th_33_Template, 4, 1, \"th\", 4)(34, PropertiestableComponent_th_34_Template, 4, 1, \"th\", 4)(35, PropertiestableComponent_th_35_Template, 4, 1, \"th\", 4)(36, PropertiestableComponent_th_36_Template, 4, 1, \"th\", 4)(37, PropertiestableComponent_th_37_Template, 4, 1, \"th\", 4)(38, PropertiestableComponent_th_38_Template, 4, 1, \"th\", 4)(39, PropertiestableComponent_th_39_Template, 4, 1, \"th\", 4)(40, PropertiestableComponent_th_40_Template, 4, 1, \"th\", 4)(41, PropertiestableComponent_th_41_Template, 4, 1, \"th\", 6)(42, PropertiestableComponent_th_42_Template, 2, 0, \"th\", 5)(43, PropertiestableComponent_th_43_Template, 4, 1, \"th\", 4)(44, PropertiestableComponent_th_44_Template, 2, 0, \"th\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(45, \"tbody\");\n        i0.ɵɵtemplate(46, PropertiestableComponent_tr_46_Template, 42, 41, \"tr\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(47, \"div\", 9)(48, \"app-pagination\", 10);\n        i0.ɵɵlistener(\"pageChange\", function PropertiestableComponent_Template_app_pagination_pageChange_48_listener($event) {\n          return ctx.onPageChange($event);\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(49, \"app-view-apartment-model\", 11);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"ownerName\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"ownerPhone\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"type\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"city\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"area\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"subArea\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"detailedAddress\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"location\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"compoundName\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"mallName\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"buildingNumber\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"unitNumber\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"floor\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"unitArea\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"groundArea\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"buildingArea\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"numberOfRooms\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"numberOfBathrooms\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"numberOfFloors\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"view\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"finishingType\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"unitDescription\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"unitDesign\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"unitFacing\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"deliveryStatus\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"fitOutCondition\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"buildingDeadline\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"requestedOver\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"furnishingStatus\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"legalStatus\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"financialStatus\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"activity\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"paymentSystem\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"totalPrice\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"rentRecurrence\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"dailyRent\") && ctx.hasPropertiesWithDailyRent());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"monthlyRent\") && ctx.hasPropertiesWithMonthlyRent());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"otherAccessories\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"unitPlan\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"status\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.shouldShowColumn(\"actions\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.rows);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"totalItems\", ctx.page.totalElements)(\"itemsPerPage\", ctx.page.size)(\"currentPage\", ctx.page.pageNumber);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"selectedUnitPlanImage\", ctx.selectedUnitPlanImage);\n      }\n    },\n    dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.ViewApartmentModelComponent, i6.PaginationComponent, i4.CurrencyPipe],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["Modal", "BaseGridComponent", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵlistener", "PropertiestableComponent_th_4_Template_th_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "sortData", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "getSortArrow", "PropertiestableComponent_th_5_Template_th_click_0_listener", "_r3", "PropertiestableComponent_th_6_Template_th_click_0_listener", "_r4", "PropertiestableComponent_th_7_Template_th_click_0_listener", "_r5", "PropertiestableComponent_th_8_Template_th_click_0_listener", "_r6", "PropertiestableComponent_th_9_Template_th_click_0_listener", "_r7", "PropertiestableComponent_th_10_Template_th_click_0_listener", "_r8", "PropertiestableComponent_th_12_Template_th_click_0_listener", "_r9", "PropertiestableComponent_th_13_Template_th_click_0_listener", "_r10", "PropertiestableComponent_th_14_Template_th_click_0_listener", "_r11", "PropertiestableComponent_th_15_Template_th_click_0_listener", "_r12", "PropertiestableComponent_th_16_Template_th_click_0_listener", "_r13", "PropertiestableComponent_th_17_Template_th_click_0_listener", "_r14", "PropertiestableComponent_th_18_Template_th_click_0_listener", "_r15", "PropertiestableComponent_th_19_Template_th_click_0_listener", "_r16", "PropertiestableComponent_th_20_Template_th_click_0_listener", "_r17", "PropertiestableComponent_th_21_Template_th_click_0_listener", "_r18", "PropertiestableComponent_th_22_Template_th_click_0_listener", "_r19", "PropertiestableComponent_th_23_Template_th_click_0_listener", "_r20", "PropertiestableComponent_th_24_Template_th_click_0_listener", "_r21", "PropertiestableComponent_th_25_Template_th_click_0_listener", "_r22", "PropertiestableComponent_th_26_Template_th_click_0_listener", "_r23", "PropertiestableComponent_th_27_Template_th_click_0_listener", "_r24", "PropertiestableComponent_th_28_Template_th_click_0_listener", "_r25", "PropertiestableComponent_th_29_Template_th_click_0_listener", "_r26", "PropertiestableComponent_th_30_Template_th_click_0_listener", "_r27", "PropertiestableComponent_th_31_Template_th_click_0_listener", "_r28", "PropertiestableComponent_th_32_Template_th_click_0_listener", "_r29", "PropertiestableComponent_th_33_Template_th_click_0_listener", "_r30", "PropertiestableComponent_th_34_Template_th_click_0_listener", "_r31", "PropertiestableComponent_th_35_Template_th_click_0_listener", "_r32", "PropertiestableComponent_th_36_Template_th_click_0_listener", "_r33", "PropertiestableComponent_th_37_Template_th_click_0_listener", "_r34", "PropertiestableComponent_th_38_Template_th_click_0_listener", "_r35", "PropertiestableComponent_th_39_Template_th_click_0_listener", "_r36", "PropertiestableComponent_th_40_Template_th_click_0_listener", "_r37", "PropertiestableComponent_th_41_Template_th_click_0_listener", "_r38", "PropertiestableComponent_th_43_Template_th_click_0_listener", "_r39", "ɵɵtextInterpolate1", "property_r40", "ownerName", "ownerPhone", "type", "city", "name_en", "area", "subArea", "detailed<PERSON>ddress", "PropertiestableComponent_tr_46_td_8_Template_button_click_1_listener", "_r41", "$implicit", "showImageModal", "location", "ɵɵelement", "compoundName", "additionalDetails", "mallName", "buildingNumber", "unitNumber", "floor", "unitArea", "groundArea", "buildingArea", "numberOfRooms", "numberOfBathrooms", "numberOfFloors", "view", "finishingType", "unitDescription", "unitDesign", "unitFacing", "deliveryStatus", "fitOutCondition", "buildingDeadline", "requestedOver", "furnishingStatus", "legalStatus", "financialStatus", "activity", "paymentSystem", "ɵɵelementContainerStart", "ɵɵpipeBind4", "totalPriceInCash", "totalPriceInInstallment", "ɵɵtemplate", "PropertiestableComponent_tr_46_td_34_span_1_ng_container_1_Template", "PropertiestableComponent_tr_46_td_34_span_1_ng_container_2_Template", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "PropertiestableComponent_tr_46_td_34_span_1_Template", "PropertiestableComponent_tr_46_td_34_div_2_Template", "rentRecurrence", "dailyRent", "PropertiestableComponent_tr_46_td_36_span_1_Template", "PropertiestableComponent_tr_46_td_36_span_2_Template", "monthlyRent", "PropertiestableComponent_tr_46_td_37_span_1_Template", "PropertiestableComponent_tr_46_td_37_span_2_Template", "otherAccessories", "PropertiestableComponent_tr_46_td_39_Template_button_click_1_listener", "_r42", "showUnitPlanModal", "diagram", "ɵɵpureFunction5", "_c1", "status", "PropertiestableComponent_tr_46_td_41_Template_button_click_6_listener", "_r43", "viewProperty", "PropertiestableComponent_tr_46_td_1_Template", "PropertiestableComponent_tr_46_td_2_Template", "PropertiestableComponent_tr_46_td_3_Template", "PropertiestableComponent_tr_46_td_4_Template", "PropertiestableComponent_tr_46_td_5_Template", "PropertiestableComponent_tr_46_td_6_Template", "PropertiestableComponent_tr_46_td_7_Template", "PropertiestableComponent_tr_46_td_8_Template", "PropertiestableComponent_tr_46_td_9_Template", "PropertiestableComponent_tr_46_td_10_Template", "PropertiestableComponent_tr_46_td_11_Template", "PropertiestableComponent_tr_46_td_12_Template", "PropertiestableComponent_tr_46_td_13_Template", "PropertiestableComponent_tr_46_td_14_Template", "PropertiestableComponent_tr_46_td_15_Template", "PropertiestableComponent_tr_46_td_16_Template", "PropertiestableComponent_tr_46_td_17_Template", "PropertiestableComponent_tr_46_td_18_Template", "PropertiestableComponent_tr_46_td_19_Template", "PropertiestableComponent_tr_46_td_20_Template", "PropertiestableComponent_tr_46_td_21_Template", "PropertiestableComponent_tr_46_td_22_Template", "PropertiestableComponent_tr_46_td_23_Template", "PropertiestableComponent_tr_46_td_24_Template", "PropertiestableComponent_tr_46_td_25_Template", "PropertiestableComponent_tr_46_td_26_Template", "PropertiestableComponent_tr_46_td_27_Template", "PropertiestableComponent_tr_46_td_28_Template", "PropertiestableComponent_tr_46_td_29_Template", "PropertiestableComponent_tr_46_td_30_Template", "PropertiestableComponent_tr_46_td_31_Template", "PropertiestableComponent_tr_46_td_32_Template", "PropertiestableComponent_tr_46_td_33_Template", "PropertiestableComponent_tr_46_td_34_Template", "PropertiestableComponent_tr_46_td_35_Template", "PropertiestableComponent_tr_46_td_36_Template", "PropertiestableComponent_tr_46_td_37_Template", "PropertiestableComponent_tr_46_td_38_Template", "PropertiestableComponent_tr_46_td_39_Template", "PropertiestableComponent_tr_46_td_40_Template", "PropertiestableComponent_tr_46_td_41_Template", "shouldShowColumn", "hasPropertiesWithDailyRent", "hasPropertiesWithMonthlyRent", "PropertiestableComponent", "cd", "unitService", "sanitizer", "router", "brokerId", "appliedFilters", "selectedImage", "selectedLocation", "visibleColumns", "constructor", "userJson", "localStorage", "getItem", "user", "JSON", "parse", "setService", "orderBy", "orderDir", "page", "filters", "ngOnChanges", "changes", "firstChange", "updateVisibleColumns", "reloadTable", "ngOnInit", "compoundType", "unitType", "getFieldsToShow", "baseColumns", "actionColumns", "specificColumns", "columnName", "includes", "rows", "some", "property", "trim", "fire", "title", "text", "icon", "confirmButtonText", "window", "open", "mapUrl", "encodeURIComponent", "selectedUnitPlanImage", "imgPath", "modalElement", "document", "getElementById", "modal", "show", "navigate", "queryParams", "unitId", "id", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "UnitService", "i2", "Dom<PERSON><PERSON><PERSON>zer", "i3", "Router", "selectors", "inputs", "features", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "PropertiestableComponent_Template", "rf", "ctx", "PropertiestableComponent_th_4_Template", "PropertiestableComponent_th_5_Template", "PropertiestableComponent_th_6_Template", "PropertiestableComponent_th_7_Template", "PropertiestableComponent_th_8_Template", "PropertiestableComponent_th_9_Template", "PropertiestableComponent_th_10_Template", "PropertiestableComponent_th_11_Template", "PropertiestableComponent_th_12_Template", "PropertiestableComponent_th_13_Template", "PropertiestableComponent_th_14_Template", "PropertiestableComponent_th_15_Template", "PropertiestableComponent_th_16_Template", "PropertiestableComponent_th_17_Template", "PropertiestableComponent_th_18_Template", "PropertiestableComponent_th_19_Template", "PropertiestableComponent_th_20_Template", "PropertiestableComponent_th_21_Template", "PropertiestableComponent_th_22_Template", "PropertiestableComponent_th_23_Template", "PropertiestableComponent_th_24_Template", "PropertiestableComponent_th_25_Template", "PropertiestableComponent_th_26_Template", "PropertiestableComponent_th_27_Template", "PropertiestableComponent_th_28_Template", "PropertiestableComponent_th_29_Template", "PropertiestableComponent_th_30_Template", "PropertiestableComponent_th_31_Template", "PropertiestableComponent_th_32_Template", "PropertiestableComponent_th_33_Template", "PropertiestableComponent_th_34_Template", "PropertiestableComponent_th_35_Template", "PropertiestableComponent_th_36_Template", "PropertiestableComponent_th_37_Template", "PropertiestableComponent_th_38_Template", "PropertiestableComponent_th_39_Template", "PropertiestableComponent_th_40_Template", "PropertiestableComponent_th_41_Template", "PropertiestableComponent_th_42_Template", "PropertiestableComponent_th_43_Template", "PropertiestableComponent_th_44_Template", "PropertiestableComponent_tr_46_Template", "PropertiestableComponent_Template_app_pagination_pageChange_48_listener", "$event", "onPageChange", "totalElements", "size", "pageNumber"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\dataandproperties\\components\\propertiestable\\propertiestable.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\dataandproperties\\components\\propertiestable\\propertiestable.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, Input, SimpleChanges, OnChanges } from '@angular/core';\r\nimport { Modal } from 'bootstrap';\r\nimport { BaseGridComponent } from 'src/app/pages/shared/base-grid/base-grid.component';\r\nimport { UnitService } from '../../../services/unit.service';\r\nimport { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';\r\nimport Swal from 'sweetalert2';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-propertiestable',\r\n  templateUrl: './propertiestable.component.html',\r\n  styleUrl: './propertiestable.component.scss',\r\n})\r\nexport class PropertiestableComponent extends BaseGridComponent implements OnChanges {\r\n\r\n  //session\r\n  brokerId: number;\r\n\r\n  @Input() appliedFilters: any;\r\n  selectedImage: string | null = null;\r\n  selectedLocation: SafeResourceUrl | null = null;\r\n\r\n  // Dynamic column visibility\r\n  visibleColumns: string[] = [];\r\n\r\n  constructor(\r\n    protected cd: ChangeDetectorRef,\r\n    protected unitService: UnitService,\r\n    private sanitizer: DomSanitizer,\r\n    private router: Router\r\n  ) {\r\n    super(cd);\r\n    const userJson = localStorage.getItem('currentUser');\r\n    let user = userJson ? JSON.parse(userJson) : null;\r\n    this.brokerId = user?.brokerId;\r\n    this.setService(unitService);\r\n    this.orderBy = 'id';\r\n    this.orderDir = 'desc';\r\n    this.page.filters = { brokerId: this.brokerId };\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes.appliedFilters && !changes.appliedFilters.firstChange) {\r\n      this.page.filters = { brokerId: this.brokerId, ...this.appliedFilters };\r\n      this.updateVisibleColumns();\r\n      this.reloadTable(this.page);\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    super.ngOnInit();\r\n    this.updateVisibleColumns();\r\n  }\r\n\r\n  updateVisibleColumns(): void {\r\n    if (this.appliedFilters?.compoundType && this.appliedFilters?.unitType) {\r\n      this.visibleColumns = this.getFieldsToShow();\r\n    } else {\r\n      // Show all columns by default\r\n      this.visibleColumns = [\r\n        'ownerName',\r\n        'ownerPhone',\r\n        'type',\r\n        'city',\r\n        'area',\r\n        'detailedAddress',\r\n        'location',\r\n        'unitPlan',\r\n        'status',\r\n        'otherAccessories',\r\n        'actions',\r\n      ];\r\n    }\r\n  }\r\n\r\n  // Get fields to show based on compound type and property type\r\n  getFieldsToShow(): string[] {\r\n    const compoundType = this.appliedFilters?.compoundType;\r\n    const type = this.appliedFilters?.unitType;\r\n\r\n    // Base columns that are always shown\r\n    const baseColumns = [\r\n      'ownerName',\r\n      'ownerPhone',\r\n      'type',\r\n      'city',\r\n      'area',\r\n      'subArea',\r\n      'detailedAddress',\r\n      'location',\r\n    ];\r\n    const actionColumns = ['unitPlan', 'status', 'actions'];\r\n\r\n    let specificColumns: string[] = [];\r\n\r\n    if (\r\n      compoundType === 'outside_compound' &&\r\n      (type === 'apartments' ||\r\n        type === 'duplexes' ||\r\n        type === 'studios' ||\r\n        type === 'penthouses' ||\r\n        type === 'roofs' ||\r\n        type === 'basement')\r\n    ) {\r\n      specificColumns = [\r\n        'buildingNumber',\r\n        'unitNumber',\r\n        'floor',\r\n        'unitArea',\r\n        'numberOfRooms',\r\n        'numberOfBathrooms',\r\n        'unitFacing',\r\n        'view',\r\n        'finishingType',\r\n        'deliveryStatus',\r\n        'legalStatus',\r\n        'otherAccessories',\r\n        'paymentSystem',\r\n        'pricePerMeterInCash',\r\n        'pricePerMeterInInstallment',\r\n        'totalPrice',\r\n      ];\r\n    } else if (\r\n      compoundType === 'outside_compound' &&\r\n      (type == 'villas' || type == 'full_buildings')\r\n    ) {\r\n      specificColumns = [\r\n        'buildingNumber',\r\n        'numberOfFloors',\r\n        'buildingArea',\r\n        'groundArea',\r\n        'unitDescription',\r\n        'unitDesign',\r\n        'unitFacing',\r\n        'view',\r\n        'finishingType',\r\n        'legalStatus',\r\n        'otherAccessories',\r\n        'paymentSystem',\r\n        'pricePerMeterInCash',\r\n        'pricePerMeterInInstallment',\r\n        'totalPrice',\r\n      ];\r\n    } else if (\r\n      compoundType === 'outside_compound' &&\r\n      (type === 'pharmacies' ||\r\n        type === 'medical_clinics' ||\r\n        type === 'administrative_units' ||\r\n        type === 'commercial_stores')\r\n    ) {\r\n      specificColumns = [\r\n        'mallName',\r\n        'buildingNumber',\r\n        'unitNumber',\r\n        'floor',\r\n        'unitArea',\r\n        'view',\r\n        'finishingType',\r\n        'fitOutCondition',\r\n        'deliveryStatus',\r\n        'activity',\r\n        'financialStatus',\r\n        'otherAccessories',\r\n        'paymentSystem',\r\n        'pricePerMeterInCash',\r\n        'pricePerMeterInInstallment',\r\n        'totalPrice',\r\n      ];\r\n    } else if (\r\n      compoundType === 'outside_compound' &&\r\n      (type === 'warehouses' || type === 'factories')\r\n    ) {\r\n      specificColumns = [\r\n        'buildingNumber',\r\n        'numberOfFloors',\r\n        'groundArea',\r\n        'buildingArea',\r\n        'activity',\r\n        'finishingType',\r\n        'unitDescription',\r\n        'legalStatus',\r\n        'otherAccessories',\r\n        'paymentSystem',\r\n        'pricePerMeterInCash',\r\n        'pricePerMeterInInstallment',\r\n        'totalPrice',\r\n      ];\r\n    } else if (\r\n      compoundType === 'outside_compound' &&\r\n      (type === 'residential_villa_lands' ||\r\n        type === 'residential_lands' ||\r\n        type === 'administrative_lands' ||\r\n        type === 'commercial_administrative_lands' ||\r\n        type === 'commercial_lands' ||\r\n        type === 'medical_lands' ||\r\n        type === 'mixed_lands' ||\r\n        type === 'warehouses_land' ||\r\n        type === 'industrial_lands')\r\n    ) {\r\n      specificColumns = [\r\n        'unitNumber',\r\n        'groundArea',\r\n        'fitOutCondition',\r\n        'unitDescription',\r\n        'buildingDeadline',\r\n        'view',\r\n        'legalStatus',\r\n        'deliveryStatus',\r\n        'financialStatus',\r\n        'otherAccessories',\r\n        'paymentSystem',\r\n        'pricePerMeterInCash',\r\n        'pricePerMeterInInstallment',\r\n        'totalPrice',\r\n      ];\r\n    } else if (\r\n      compoundType === 'inside_compound' &&\r\n      (type === 'apartments' ||\r\n        type === 'duplexes' ||\r\n        type === 'studios' ||\r\n        type === 'penthouses' ||\r\n        type === 'i_villa')\r\n    ) {\r\n      specificColumns = [\r\n        'compoundName',\r\n        'buildingNumber',\r\n        'unitNumber',\r\n        'floor',\r\n        'unitArea',\r\n        'numberOfRooms',\r\n        'numberOfBathrooms',\r\n        'view',\r\n        'finishingType',\r\n        'deliveryStatus',\r\n        'financialStatus',\r\n        'otherAccessories',\r\n        'requestedOver',\r\n        'paymentSystem',\r\n        'pricePerMeterInCash',\r\n        'pricePerMeterInInstallment',\r\n        'totalPrice',\r\n      ];\r\n    } else if (\r\n      compoundType === 'inside_compound' &&\r\n      (type === 'standalone_villas' ||\r\n        type === 'twin_houses' ||\r\n        type === 'town_houses')\r\n    ) {\r\n      specificColumns = [\r\n        'compoundName',\r\n        'buildingNumber',\r\n        'numberOfFloors',\r\n        'buildingArea',\r\n        'groundArea',\r\n        'numberOfRooms',\r\n        'numberOfBathrooms',\r\n        'view',\r\n        'finishingType',\r\n        'deliveryStatus',\r\n        'financialStatus',\r\n        'otherAccessories',\r\n        'requestedOver',\r\n        'paymentSystem',\r\n        'pricePerMeterInCash',\r\n        'pricePerMeterInInstallment',\r\n        'totalPrice',\r\n      ];\r\n    } else if (\r\n      compoundType === 'inside_compound' &&\r\n      (type === 'pharmacies' ||\r\n        type === 'medical_clinics' ||\r\n        type === 'administrative_units' ||\r\n        type === 'commercial_stores')\r\n    ) {\r\n      specificColumns = [\r\n        'compoundName',\r\n        'mallName',\r\n        'buildingNumber',\r\n        'unitNumber',\r\n        'floor',\r\n        'unitArea',\r\n        'view',\r\n        'finishingType',\r\n        'deliveryStatus',\r\n        'fitOutCondition',\r\n        'financialStatus',\r\n        'otherAccessories',\r\n        'requestedOver',\r\n        'paymentSystem',\r\n        'pricePerMeterInCash',\r\n        'pricePerMeterInInstallment',\r\n        'totalPrice',\r\n      ];\r\n    } else if (\r\n      compoundType === 'village' &&\r\n      (type === 'apartments' ||\r\n        type === 'duplexes' ||\r\n        type === 'studios' ||\r\n        type === 'penthouses' ||\r\n        type === 'basement' ||\r\n        type === 'roofs' ||\r\n        type === 'i_villa' ||\r\n        type === 'villas' ||\r\n        type === 'standalone_villas' ||\r\n        type === 'full_buildings' ||\r\n        type === 'twin_houses' ||\r\n        type === 'town_houses' ||\r\n        type === 'chalets')\r\n    ) {\r\n      specificColumns = [\r\n        'buildingNumber',\r\n        'unitNumber',\r\n        'unitArea',\r\n        'floor',\r\n        'view',\r\n        'numberOfRooms',\r\n        'numberOfBathrooms',\r\n        'finishingType',\r\n        'furnishingStatus',\r\n        'otherAccessories',\r\n        'rentRecurrence',\r\n        'dailyRent',\r\n        'monthlyRent',\r\n        'annualRent',\r\n      ];\r\n    } else if (\r\n      compoundType === 'village' &&\r\n      (type === 'warehouses' ||\r\n        type === 'factories' ||\r\n        type === 'administrative_units' ||\r\n        type === 'medical_clinics' ||\r\n        type === 'commercial_stores' ||\r\n        type === 'pharmacies')\r\n    ) {\r\n      specificColumns = [\r\n        'buildingNumber',\r\n        'unitNumber',\r\n        'unitArea',\r\n        'floor',\r\n        'view',\r\n        'numberOfRooms',\r\n        'numberOfBathrooms',\r\n        'finishingType',\r\n        'furnishingStatus',\r\n        'otherAccessories',\r\n        'rentRecurrence',\r\n        'activity',\r\n        'dailyRent',\r\n        'monthlyRent',\r\n      ];\r\n    }\r\n\r\n    return [...baseColumns, ...specificColumns, ...actionColumns];\r\n  }\r\n\r\n  // Check if a specific column should be shown\r\n  shouldShowColumn(columnName: string): boolean {\r\n    return this.visibleColumns.includes(columnName);\r\n  }\r\n\r\n  // Check if there are properties with daily rent\r\n  hasPropertiesWithDailyRent(): boolean {\r\n    return this.rows.some((property: any) =>\r\n      property.additionalDetails?.rentRecurrence === 'daily' && property.dailyRent\r\n    );\r\n  }\r\n\r\n  // Check if there are properties with monthly/annually rent\r\n  hasPropertiesWithMonthlyRent(): boolean {\r\n    return this.rows.some((property: any) =>\r\n      (property.additionalDetails?.rentRecurrence === 'monthly' ||\r\n       property.additionalDetails?.rentRecurrence === 'annually') &&\r\n      property.monthlyRent\r\n    );\r\n  }\r\n\r\n  showImageModal(location: string) {\r\n    if (!location || location.trim() === '') {\r\n      Swal.fire({\r\n        title: 'Warning',\r\n        text: 'No location available',\r\n        icon: 'warning',\r\n        confirmButtonText: 'OK',\r\n      });\r\n      return;\r\n    }\r\n    if (\r\n      location.includes('maps.google.com') ||\r\n      location.includes('maps.app.goo.gl')\r\n    ) {\r\n      window.open(location, '_blank');\r\n      return;\r\n    }\r\n\r\n    const mapUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(\r\n      location\r\n    )}`;\r\n    window.open(mapUrl, '_blank');\r\n  }\r\n\r\n  //****************************** */\r\n\r\n  selectedUnitPlanImage: string | null = null;\r\n\r\n  showUnitPlanModal(imgPath: string) {\r\n    this.selectedUnitPlanImage = imgPath;\r\n\r\n    const modalElement = document.getElementById('viewUnitPlanModal');\r\n    if (modalElement) {\r\n      const modal = new Modal(modalElement);\r\n      modal.show();\r\n    }\r\n  }\r\n\r\n  viewProperty(unitService: any) {\r\n    this.router.navigate(['/developer/projects/models/units/details'], {\r\n      queryParams: { unitId: unitService.id },\r\n    });\r\n  }\r\n  //************************************** */\r\n\r\n  //  sortData(column: string) {\r\n  //    if (this.orderBy === column) {\r\n  //     this.orderDir = this.orderDir === 'asc' ? 'desc' : 'asc';\r\n  //   } else {\r\n  //     this.orderBy = column;\r\n  //     this.orderDir = 'asc';\r\n  //   }\r\n\r\n  //    this.page.orderBy = this.orderBy;\r\n  //   this.page.orderDir = this.orderDir;\r\n  //   this.page.pageNumber = 0;\r\n  //   this.reloadTable(this.page);\r\n  // }\r\n\r\n  //  getSortArrow(column: string): string {\r\n  //   if (this.orderBy !== column) {\r\n  //     return '';\r\n  //   }\r\n  //   return this.orderDir === 'asc' ? '↑' : '↓';\r\n  // }\r\n}\r\n", "<div class=\"table-responsive mb-5\">\r\n  <table class=\"table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5\">\r\n    <thead>\r\n      <tr class=\"fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1\">\r\n        <!-- Base columns - always shown -->\r\n        <th class=\"min-w-150px cursor-pointer ps-4 rounded-start\" (click)=\"sortData('owner_name')\"\r\n          *ngIf=\"shouldShowColumn('ownerName')\">\r\n          Owner Name\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"owner_name\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('ownerPhone')\" *ngIf=\"shouldShowColumn('ownerPhone')\">\r\n          Owner Phone\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"ownerPhone\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('type')\" *ngIf=\"shouldShowColumn('type')\">\r\n          Unit Type\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"type\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('city_id')\" *ngIf=\"shouldShowColumn('city')\">\r\n          City\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"city_id\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('area_id')\" *ngIf=\"shouldShowColumn('area')\">\r\n          Area\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"area_id\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('sub_area')\" *ngIf=\"shouldShowColumn('subArea')\">\r\n          Sub Area\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"sub_area\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('detailedAddress')\"\r\n          *ngIf=\"shouldShowColumn('detailedAddress')\">\r\n          Detailed Address\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"detailedAddress\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px\" *ngIf=\"shouldShowColumn('location')\">\r\n          Location on map\r\n        </th>\r\n\r\n        <!-- Dynamic columns based on filter -->\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('compound_name')\"\r\n          *ngIf=\"shouldShowColumn('compoundName')\">\r\n          Compound Name\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"compound_name\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('mall_name')\" *ngIf=\"shouldShowColumn('mallName')\">\r\n          Mall Name\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"mall_name\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('building_number')\"\r\n          *ngIf=\"shouldShowColumn('buildingNumber')\">\r\n          Building Number\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"building_number\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('unit_number')\" *ngIf=\"shouldShowColumn('unitNumber')\">\r\n          Unit Number\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"unit_number\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('floor')\" *ngIf=\"shouldShowColumn('floor')\">\r\n          Floor\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"floor\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('unit_area')\" *ngIf=\"shouldShowColumn('unitArea')\">\r\n          Unit Area\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"unit_area\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('ground_area')\" *ngIf=\"shouldShowColumn('groundArea')\">\r\n          Ground Area\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"ground_area\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('building_area')\"\r\n          *ngIf=\"shouldShowColumn('buildingArea')\">\r\n          Building Area\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"building_area\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('number_of_rooms')\"\r\n          *ngIf=\"shouldShowColumn('numberOfRooms')\">\r\n          Rooms\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"number_of_rooms\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('number_of_bathrooms')\"\r\n          *ngIf=\"shouldShowColumn('numberOfBathrooms')\">\r\n          Bathrooms\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"number_of_bathrooms\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('number_of_floors')\"\r\n          *ngIf=\"shouldShowColumn('numberOfFloors')\">\r\n          number of Floors\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"number_of_floors\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('view')\" *ngIf=\"shouldShowColumn('view')\">\r\n          View\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"view\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('finishing_type')\"\r\n          *ngIf=\"shouldShowColumn('finishingType')\">\r\n          Finishing Type\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"finishing_type\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('unit_description')\"\r\n          *ngIf=\"shouldShowColumn('unitDescription')\">\r\n          Unit Description\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"unit_description\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('unit_design')\" *ngIf=\"shouldShowColumn('unitDesign')\">\r\n          Unit Design\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"unit_design\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('unit_facing')\" *ngIf=\"shouldShowColumn('unitFacing')\">\r\n          Unit Facing\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"unit_facing\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('delivery_status')\"\r\n          *ngIf=\"shouldShowColumn('deliveryStatus')\">\r\n          Delivery Status\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"delivery_status\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('fit_out_condition')\"\r\n          *ngIf=\"shouldShowColumn('fitOutCondition')\">\r\n          Fit Out Condition\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"fit_out_condition\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('building_deadline')\"\r\n          *ngIf=\"shouldShowColumn('buildingDeadline')\">\r\n          Building Deadline\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"building_deadline\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('requested_over')\"\r\n          *ngIf=\"shouldShowColumn('requestedOver')\">\r\n          Requested Over\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"requested_over\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('furnishing_status')\"\r\n          *ngIf=\"shouldShowColumn('furnishingStatus')\">\r\n          Furnishing Status\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"furnishing_status\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('legal_status')\"\r\n          *ngIf=\"shouldShowColumn('legalStatus')\">\r\n          Legal Status\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"legal_status\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('financial_status')\"\r\n          *ngIf=\"shouldShowColumn('financialStatus')\">\r\n          Financial Status\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"financial_status\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('activity')\" *ngIf=\"shouldShowColumn('activity')\">\r\n          Activity\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"activity\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('payment_system')\"\r\n          *ngIf=\"shouldShowColumn('paymentSystem')\">\r\n          Payment System\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"payment_system\")\r\n            }}</span>\r\n        </th>\r\n\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('total_price')\" *ngIf=\"shouldShowColumn('totalPrice')\">\r\n          Total Price\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"total_price\")\r\n            }}</span>\r\n        </th>\r\n\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('rent_recurrence')\"\r\n          *ngIf=\"shouldShowColumn('rentRecurrence')\">\r\n          Rent Recurrence\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"rent_recurrence\")\r\n            }}</span>\r\n        </th>\r\n        <!-- Show Daily Rent header only when there are properties with daily rent -->\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('daily_rent')\"\r\n          *ngIf=\"shouldShowColumn('dailyRent') && hasPropertiesWithDailyRent()\">\r\n          Daily Rent\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"daily_rent\")\r\n            }}</span>\r\n        </th>\r\n        <!-- Show Monthly Rent header when there are properties with monthly/annually rent -->\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('monthly_rent')\"\r\n          *ngIf=\"shouldShowColumn('monthlyRent') && hasPropertiesWithMonthlyRent()\">\r\n          Monthly Rent\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"monthly_rent\")\r\n            }}</span>\r\n        </th>\r\n\r\n        <th class=\"min-w-200px cursor-pointer\" (click)=\"sortData('other_accessories')\"\r\n          *ngIf=\"shouldShowColumn('otherAccessories')\">\r\n          Other Accessories\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"other_accessories\")\r\n            }}</span>\r\n        </th>\r\n\r\n        <!-- Always show unit plan and actions -->\r\n        <th class=\"min-w-150px\" *ngIf=\"shouldShowColumn('unitPlan')\">\r\n          Unit Plan\r\n        </th>\r\n        <th class=\"min-w-150px cursor-pointer\" (click)=\"sortData('status')\" *ngIf=\"shouldShowColumn('status')\">\r\n          Status\r\n          <span class=\"ms-1 text-primary fw-bold\">{{\r\n            getSortArrow(\"status\")\r\n            }}</span>\r\n        </th>\r\n        <th class=\"min-w-50px text-end rounded-end pe-4\" *ngIf=\"shouldShowColumn('actions')\">\r\n          Actions\r\n        </th>\r\n      </tr>\r\n    </thead>\r\n    <tbody>\r\n      <tr *ngFor=\"let property of rows\">\r\n        <!-- Base columns - always shown -->\r\n        <td *ngIf=\"shouldShowColumn('ownerName')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6 ps-4\">\r\n            {{ property.ownerName }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('ownerPhone')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.ownerPhone }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('type')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.type }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('city')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.city?.name_en }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('area')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.area?.name_en }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('subArea')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property?.subArea?.name_en || \"Unknown\" }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('detailedAddress')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.detailedAddress }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('location')\">\r\n          <button class=\"btn btn-icon btn-sm btn-light-primary\" data-bs-toggle=\"tooltip\" title=\"View on map\"\r\n            (click)=\"showImageModal(property.location)\">\r\n            <i class=\"fa-solid fa-map-location-dot\"></i>\r\n          </button>\r\n        </td>\r\n\r\n        <!-- Dynamic columns based on filter -->\r\n        <td *ngIf=\"shouldShowColumn('compoundName')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.compoundName }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('mallName')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.additionalDetails?.mallName }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('buildingNumber')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.buildingNumber }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('unitNumber')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.unitNumber }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('floor')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.floor }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('unitArea')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.unitArea }} m²\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('groundArea')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.additionalDetails?.groundArea }} m²\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('buildingArea')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.additionalDetails?.buildingArea }} m²\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('numberOfRooms')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.numberOfRooms }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('numberOfBathrooms')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.numberOfBathrooms }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('numberOfFloors')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.additionalDetails?.numberOfFloors }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('view')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.view }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('finishingType')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.finishingType }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('unitDescription')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.additionalDetails?.unitDescription }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('unitDesign')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.additionalDetails?.unitDesign }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('unitFacing')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.additionalDetails?.unitFacing }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('deliveryStatus')\">\r\n          <span class=\"badge badge-light-success\">{{\r\n            property.deliveryStatus\r\n            }}</span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('fitOutCondition')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.additionalDetails?.fitOutCondition }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('buildingDeadline')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.additionalDetails?.buildingDeadline }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('requestedOver')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.requestedOver }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('furnishingStatus')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">{{\r\n            property.additionalDetails?.furnishingStatus\r\n            }}</span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('legalStatus')\">\r\n          <span class=\"badge badge-light-info\">{{ property.additionalDetails?.legalStatus }}</span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('financialStatus')\">\r\n          <span class=\"badge badge-light-warning\">{{\r\n            property.additionalDetails?.financialStatus\r\n            }}</span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('activity')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.additionalDetails?.activity }}\r\n          </span>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('paymentSystem')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.paymentSystem }}\r\n          </span>\r\n        </td>\r\n\r\n        <td *ngIf=\"shouldShowColumn('totalPrice')\">\r\n          <!-- Single payment system -->\r\n          <span *ngIf=\"property.paymentSystem !== 'all_of_the_above_are_suitable'\" class=\"fw-bold d-block mb-1 fs-6\"\r\n            [ngClass]=\"{\r\n              'text-success': property.paymentSystem === 'cash',\r\n              'text-primary': property.paymentSystem === 'installment'\r\n            }\">\r\n            <ng-container *ngIf=\"property.paymentSystem === 'cash'\">\r\n              {{\r\n              property.totalPriceInCash\r\n              | currency : \"EGP\" : \"symbol\" : \"1.0-0\"\r\n              }}\r\n            </ng-container>\r\n            <ng-container *ngIf=\"property.paymentSystem === 'installment'\">\r\n              {{\r\n              property.totalPriceInInstallment\r\n              | currency : \"EGP\" : \"symbol\" : \"1.0-0\"\r\n              }}\r\n            </ng-container>\r\n          </span>\r\n\r\n          <!-- Both payment systems -->\r\n          <div *ngIf=\"property.paymentSystem === 'all_of_the_above_are_suitable'\" class=\"d-flex flex-column gap-1\">\r\n            <span class=\"text-success fw-bold fs-6\">\r\n              Cash:\r\n              {{\r\n              property.totalPriceInCash\r\n              | currency : \"EGP\" : \"symbol\" : \"1.0-0\"\r\n              }}\r\n            </span>\r\n            <span class=\"text-primary fw-bold fs-6\">\r\n              Installment:\r\n              {{\r\n              property.totalPriceInInstallment\r\n              | currency : \"EGP\" : \"symbol\" : \"1.0-0\"\r\n              }}\r\n            </span>\r\n          </div>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('rentRecurrence')\">\r\n          <span class=\"badge badge-light-primary\">{{\r\n            property.additionalDetails?.rentRecurrence\r\n            }}</span>\r\n        </td>\r\n        <!-- Show Daily Rent column when there are properties with daily rent -->\r\n        <td *ngIf=\"shouldShowColumn('dailyRent') && hasPropertiesWithDailyRent()\">\r\n          <span *ngIf=\"property.additionalDetails?.rentRecurrence === 'daily'\"\r\n            class=\"text-success fw-bold d-block mb-1 fs-6\">\r\n            {{ property.dailyRent | currency : \"EGP\" : \"symbol\" : \"1.0-0\" }}\r\n          </span>\r\n          <span *ngIf=\"property.additionalDetails?.rentRecurrence !== 'daily'\" class=\"text-muted\">-</span>\r\n        </td>\r\n        <!-- Show Monthly Rent column when there are properties with monthly/annually rent -->\r\n        <td *ngIf=\"shouldShowColumn('monthlyRent') && hasPropertiesWithMonthlyRent()\">\r\n          <span\r\n            *ngIf=\"property.additionalDetails?.rentRecurrence === 'monthly' || property.additionalDetails?.rentRecurrence === 'annually'\"\r\n            class=\"text-success fw-bold d-block mb-1 fs-6\">\r\n            {{ property.monthlyRent | currency : \"EGP\" : \"symbol\" : \"1.0-0\" }}\r\n          </span>\r\n          <span\r\n            *ngIf=\"property.additionalDetails?.rentRecurrence !== 'monthly' && property.additionalDetails?.rentRecurrence !== 'annually'\"\r\n            class=\"text-muted\">-</span>\r\n        </td>\r\n\r\n        <td *ngIf=\"shouldShowColumn('otherAccessories')\">\r\n          <span class=\"text-gray-900 fw-bold d-block mb-1 fs-6\">\r\n            {{ property.otherAccessories }}\r\n          </span>\r\n        </td>\r\n\r\n        <!-- Always show unit plan and actions -->\r\n        <td *ngIf=\"shouldShowColumn('unitPlan')\">\r\n          <button class=\"btn btn-sm btn-light-info\" (click)=\"showUnitPlanModal(property.diagram)\">\r\n            <i class=\"fa-solid fa-file-image me-1\"></i> View Plan\r\n          </button>\r\n        </td>\r\n        <td *ngIf=\"shouldShowColumn('status')\">\r\n          <span class=\"badge fw-bold\" [ngClass]=\"{\r\n              'badge-light-success': property.status === 'available',\r\n              'badge-light-danger': property.status === 'sold',\r\n              'badge-light-warning': property.status === 'reserved',\r\n              'badge-light-info': property.status === 'pending',\r\n              'badge-light-primary': property.status === 'new'\r\n            }\">\r\n            {{ property.status }}\r\n          </span>\r\n        </td>\r\n        <td class=\"text-end pe-4\" *ngIf=\"shouldShowColumn('actions')\">\r\n          <div class=\"dropdown\">\r\n            <button class=\"btn btn-sm btn-icon btn-color-primary btn-active-light-primary\" type=\"button\"\r\n              data-bs-toggle=\"dropdown\">\r\n              <i class=\"fa-solid fa-ellipsis-vertical\"></i>\r\n            </button>\r\n            <ul class=\"dropdown-menu\">\r\n              <li>\r\n                <button class=\"dropdown-item\" (click)=\"viewProperty(property)\">\r\n                  <i class=\"fa-solid fa-eye me-2\"></i> View unit Details\r\n                </button>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </td>\r\n      </tr>\r\n    </tbody>\r\n  </table>\r\n  <div class=\"m-2\">\r\n    <app-pagination [totalItems]=\"page.totalElements\" [itemsPerPage]=\"page.size\" [currentPage]=\"page.pageNumber\"\r\n      (pageChange)=\"onPageChange($event)\">\r\n    </app-pagination>\r\n  </div>\r\n</div>\r\n\r\n<app-view-apartment-model [selectedUnitPlanImage]=\"selectedUnitPlanImage\"></app-view-apartment-model>\r\n"], "mappings": "AACA,SAASA,KAAK,QAAQ,WAAW;AACjC,SAASC,iBAAiB,QAAQ,oDAAoD;AAGtF,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;ICAtBC,EAAA,CAAAC,cAAA,aACwC;IADkBD,EAAA,CAAAE,UAAA,mBAAAC,2DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,YAAY,CAAC;IAAA,EAAC;IAExFT,EAAA,CAAAU,MAAA,mBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,eAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAA+G;IAAxED,EAAA,CAAAE,UAAA,mBAAAa,2DAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,YAAY,CAAC;IAAA,EAAC;IACrET,EAAA,CAAAU,MAAA,oBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,eAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAAmG;IAA5DD,EAAA,CAAAE,UAAA,mBAAAe,2DAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAc,GAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,MAAM,CAAC;IAAA,EAAC;IAC/DT,EAAA,CAAAU,MAAA,kBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,SAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAAsG;IAA/DD,EAAA,CAAAE,UAAA,mBAAAiB,2DAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,SAAS,CAAC;IAAA,EAAC;IAClET,EAAA,CAAAU,MAAA,aACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,YAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAAsG;IAA/DD,EAAA,CAAAE,UAAA,mBAAAmB,2DAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,SAAS,CAAC;IAAA,EAAC;IAClET,EAAA,CAAAU,MAAA,aACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,YAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAA0G;IAAnED,EAAA,CAAAE,UAAA,mBAAAqB,2DAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,UAAU,CAAC;IAAA,EAAC;IACnET,EAAA,CAAAU,MAAA,iBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,aAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC8C;IADPD,EAAA,CAAAE,UAAA,mBAAAuB,4DAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,iBAAiB,CAAC;IAAA,EAAC;IAE1ET,EAAA,CAAAU,MAAA,yBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,oBAEpC;;;;;IAENd,EAAA,CAAAC,cAAA,aAA6D;IAC3DD,EAAA,CAAAU,MAAA,wBACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;;IAGLX,EAAA,CAAAC,cAAA,aAC2C;IADJD,EAAA,CAAAE,UAAA,mBAAAyB,4DAAA;MAAA3B,EAAA,CAAAI,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,eAAe,CAAC;IAAA,EAAC;IAExET,EAAA,CAAAU,MAAA,sBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,kBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAA4G;IAArED,EAAA,CAAAE,UAAA,mBAAA2B,4DAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAA0B,IAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,WAAW,CAAC;IAAA,EAAC;IACpET,EAAA,CAAAU,MAAA,kBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,cAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC6C;IADND,EAAA,CAAAE,UAAA,mBAAA6B,4DAAA;MAAA/B,EAAA,CAAAI,aAAA,CAAA4B,IAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,iBAAiB,CAAC;IAAA,EAAC;IAE1ET,EAAA,CAAAU,MAAA,wBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,oBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAAgH;IAAzED,EAAA,CAAAE,UAAA,mBAAA+B,4DAAA;MAAAjC,EAAA,CAAAI,aAAA,CAAA8B,IAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,aAAa,CAAC;IAAA,EAAC;IACtET,EAAA,CAAAU,MAAA,oBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,gBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAAqG;IAA9DD,EAAA,CAAAE,UAAA,mBAAAiC,4DAAA;MAAAnC,EAAA,CAAAI,aAAA,CAAAgC,IAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,OAAO,CAAC;IAAA,EAAC;IAChET,EAAA,CAAAU,MAAA,cACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,UAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAA4G;IAArED,EAAA,CAAAE,UAAA,mBAAAmC,4DAAA;MAAArC,EAAA,CAAAI,aAAA,CAAAkC,IAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,WAAW,CAAC;IAAA,EAAC;IACpET,EAAA,CAAAU,MAAA,kBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,cAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAAgH;IAAzED,EAAA,CAAAE,UAAA,mBAAAqC,4DAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAoC,IAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,aAAa,CAAC;IAAA,EAAC;IACtET,EAAA,CAAAU,MAAA,oBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,gBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC2C;IADJD,EAAA,CAAAE,UAAA,mBAAAuC,4DAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAsC,IAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,eAAe,CAAC;IAAA,EAAC;IAExET,EAAA,CAAAU,MAAA,sBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,kBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC4C;IADLD,EAAA,CAAAE,UAAA,mBAAAyC,4DAAA;MAAA3C,EAAA,CAAAI,aAAA,CAAAwC,IAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,iBAAiB,CAAC;IAAA,EAAC;IAE1ET,EAAA,CAAAU,MAAA,cACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,oBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aACgD;IADTD,EAAA,CAAAE,UAAA,mBAAA2C,4DAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAA0C,IAAA;MAAA,MAAAxC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,qBAAqB,CAAC;IAAA,EAAC;IAE9ET,EAAA,CAAAU,MAAA,kBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,wBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC6C;IADND,EAAA,CAAAE,UAAA,mBAAA6C,4DAAA;MAAA/C,EAAA,CAAAI,aAAA,CAAA4C,IAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,kBAAkB,CAAC;IAAA,EAAC;IAE3ET,EAAA,CAAAU,MAAA,yBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,qBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAAmG;IAA5DD,EAAA,CAAAE,UAAA,mBAAA+C,4DAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,MAAM,CAAC;IAAA,EAAC;IAC/DT,EAAA,CAAAU,MAAA,aACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,SAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC4C;IADLD,EAAA,CAAAE,UAAA,mBAAAiD,4DAAA;MAAAnD,EAAA,CAAAI,aAAA,CAAAgD,IAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,gBAAgB,CAAC;IAAA,EAAC;IAEzET,EAAA,CAAAU,MAAA,uBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,mBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC8C;IADPD,EAAA,CAAAE,UAAA,mBAAAmD,4DAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,kBAAkB,CAAC;IAAA,EAAC;IAE3ET,EAAA,CAAAU,MAAA,yBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,qBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAAgH;IAAzED,EAAA,CAAAE,UAAA,mBAAAqD,4DAAA;MAAAvD,EAAA,CAAAI,aAAA,CAAAoD,IAAA;MAAA,MAAAlD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,aAAa,CAAC;IAAA,EAAC;IACtET,EAAA,CAAAU,MAAA,oBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,gBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAAgH;IAAzED,EAAA,CAAAE,UAAA,mBAAAuD,4DAAA;MAAAzD,EAAA,CAAAI,aAAA,CAAAsD,IAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,aAAa,CAAC;IAAA,EAAC;IACtET,EAAA,CAAAU,MAAA,oBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,gBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC6C;IADND,EAAA,CAAAE,UAAA,mBAAAyD,4DAAA;MAAA3D,EAAA,CAAAI,aAAA,CAAAwD,IAAA;MAAA,MAAAtD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,iBAAiB,CAAC;IAAA,EAAC;IAE1ET,EAAA,CAAAU,MAAA,wBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,oBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC8C;IADPD,EAAA,CAAAE,UAAA,mBAAA2D,4DAAA;MAAA7D,EAAA,CAAAI,aAAA,CAAA0D,IAAA;MAAA,MAAAxD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,mBAAmB,CAAC;IAAA,EAAC;IAE5ET,EAAA,CAAAU,MAAA,0BACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,sBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC+C;IADRD,EAAA,CAAAE,UAAA,mBAAA6D,4DAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAA4D,IAAA;MAAA,MAAA1D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,mBAAmB,CAAC;IAAA,EAAC;IAE5ET,EAAA,CAAAU,MAAA,0BACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,sBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC4C;IADLD,EAAA,CAAAE,UAAA,mBAAA+D,4DAAA;MAAAjE,EAAA,CAAAI,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,gBAAgB,CAAC;IAAA,EAAC;IAEzET,EAAA,CAAAU,MAAA,uBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,mBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC+C;IADRD,EAAA,CAAAE,UAAA,mBAAAiE,4DAAA;MAAAnE,EAAA,CAAAI,aAAA,CAAAgE,IAAA;MAAA,MAAA9D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,mBAAmB,CAAC;IAAA,EAAC;IAE5ET,EAAA,CAAAU,MAAA,0BACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,sBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC0C;IADHD,EAAA,CAAAE,UAAA,mBAAAmE,4DAAA;MAAArE,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,cAAc,CAAC;IAAA,EAAC;IAEvET,EAAA,CAAAU,MAAA,qBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,iBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC8C;IADPD,EAAA,CAAAE,UAAA,mBAAAqE,4DAAA;MAAAvE,EAAA,CAAAI,aAAA,CAAAoE,IAAA;MAAA,MAAAlE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,kBAAkB,CAAC;IAAA,EAAC;IAE3ET,EAAA,CAAAU,MAAA,yBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,qBAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAA2G;IAApED,EAAA,CAAAE,UAAA,mBAAAuE,4DAAA;MAAAzE,EAAA,CAAAI,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,UAAU,CAAC;IAAA,EAAC;IACnET,EAAA,CAAAU,MAAA,iBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,aAEpC;;;;;;IAENd,EAAA,CAAAC,cAAA,aAC4C;IADLD,EAAA,CAAAE,UAAA,mBAAAyE,4DAAA;MAAA3E,EAAA,CAAAI,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,gBAAgB,CAAC;IAAA,EAAC;IAEzET,EAAA,CAAAU,MAAA,uBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,mBAEpC;;;;;;IAGNd,EAAA,CAAAC,cAAA,aAAgH;IAAzED,EAAA,CAAAE,UAAA,mBAAA2E,4DAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAA0E,IAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,aAAa,CAAC;IAAA,EAAC;IACtET,EAAA,CAAAU,MAAA,oBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,gBAEpC;;;;;;IAGNd,EAAA,CAAAC,cAAA,aAC6C;IADND,EAAA,CAAAE,UAAA,mBAAA6E,4DAAA;MAAA/E,EAAA,CAAAI,aAAA,CAAA4E,IAAA;MAAA,MAAA1E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,iBAAiB,CAAC;IAAA,EAAC;IAE1ET,EAAA,CAAAU,MAAA,wBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,oBAEpC;;;;;;IAGNd,EAAA,CAAAC,cAAA,aACwE;IADjCD,EAAA,CAAAE,UAAA,mBAAA+E,4DAAA;MAAAjF,EAAA,CAAAI,aAAA,CAAA8E,IAAA;MAAA,MAAA5E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,YAAY,CAAC;IAAA,EAAC;IAErET,EAAA,CAAAU,MAAA,mBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,eAEpC;;;;;;IAGNd,EAAA,CAAAC,cAAA,aAC4E;IADrCD,EAAA,CAAAE,UAAA,mBAAAiF,4DAAA;MAAAnF,EAAA,CAAAI,aAAA,CAAAgF,IAAA;MAAA,MAAA9E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,cAAc,CAAC;IAAA,EAAC;IAEvET,EAAA,CAAAU,MAAA,qBACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,iBAEpC;;;;;;IAGNd,EAAA,CAAAC,cAAA,aAC+C;IADRD,EAAA,CAAAE,UAAA,mBAAAmF,4DAAA;MAAArF,EAAA,CAAAI,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,mBAAmB,CAAC;IAAA,EAAC;IAE5ET,EAAA,CAAAU,MAAA,0BACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,sBAEpC;;;;;IAINd,EAAA,CAAAC,cAAA,aAA6D;IAC3DD,EAAA,CAAAU,MAAA,kBACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;;IACLX,EAAA,CAAAC,cAAA,aAAuG;IAAhED,EAAA,CAAAE,UAAA,mBAAAqF,4DAAA;MAAAvF,EAAA,CAAAI,aAAA,CAAAoF,IAAA;MAAA,MAAAlF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAS,QAAQ,CAAC;IAAA,EAAC;IACjET,EAAA,CAAAU,MAAA,eACA;IAAAV,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,WAEpC;;;;;IAENd,EAAA,CAAAC,cAAA,aAAqF;IACnFD,EAAA,CAAAU,MAAA,gBACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;IAOHX,EADF,CAAAC,cAAA,SAA0C,eACmB;IACzDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAC,SAAA,MACF;;;;;IAGA3F,EADF,CAAAC,cAAA,SAA2C,eACa;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAE,UAAA,MACF;;;;;IAGA5F,EADF,CAAAC,cAAA,SAAqC,eACmB;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAG,IAAA,MACF;;;;;IAGA7F,EADF,CAAAC,cAAA,SAAqC,eACmB;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAI,IAAA,kBAAAJ,YAAA,CAAAI,IAAA,CAAAC,OAAA,MACF;;;;;IAGA/F,EADF,CAAAC,cAAA,SAAqC,eACmB;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAM,IAAA,kBAAAN,YAAA,CAAAM,IAAA,CAAAD,OAAA,MACF;;;;;IAGA/F,EADF,CAAAC,cAAA,SAAwC,eACgB;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,OAAAC,YAAA,kBAAAA,YAAA,CAAAO,OAAA,kBAAAP,YAAA,CAAAO,OAAA,CAAAF,OAAA,oBACF;;;;;IAGA/F,EADF,CAAAC,cAAA,SAAgD,eACQ;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAQ,eAAA,MACF;;;;;;IAGAlG,EADF,CAAAC,cAAA,SAAyC,iBAEO;IAA5CD,EAAA,CAAAE,UAAA,mBAAAiG,qEAAA;MAAAnG,EAAA,CAAAI,aAAA,CAAAgG,IAAA;MAAA,MAAAV,YAAA,GAAA1F,EAAA,CAAAO,aAAA,GAAA8F,SAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgG,cAAA,CAAAZ,YAAA,CAAAa,QAAA,CAAiC;IAAA,EAAC;IAC3CvG,EAAA,CAAAwG,SAAA,YAA4C;IAEhDxG,EADE,CAAAW,YAAA,EAAS,EACN;;;;;IAIHX,EADF,CAAAC,cAAA,SAA6C,eACW;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAe,YAAA,MACF;;;;;IAGAzG,EADF,CAAAC,cAAA,SAAyC,eACe;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAC,QAAA,MACF;;;;;IAGA3G,EADF,CAAAC,cAAA,SAA+C,eACS;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAkB,cAAA,MACF;;;;;IAGA5G,EADF,CAAAC,cAAA,SAA2C,eACa;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAmB,UAAA,MACF;;;;;IAGA7G,EADF,CAAAC,cAAA,SAAsC,eACkB;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAoB,KAAA,MACF;;;;;IAGA9G,EADF,CAAAC,cAAA,SAAyC,eACe;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAqB,QAAA,cACF;;;;;IAGA/G,EADF,CAAAC,cAAA,SAA2C,eACa;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAM,UAAA,cACF;;;;;IAGAhH,EADF,CAAAC,cAAA,SAA6C,eACW;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAO,YAAA,cACF;;;;;IAGAjH,EADF,CAAAC,cAAA,SAA8C,eACU;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAwB,aAAA,MACF;;;;;IAGAlH,EADF,CAAAC,cAAA,SAAkD,eACM;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAyB,iBAAA,MACF;;;;;IAGAnH,EADF,CAAAC,cAAA,SAA+C,eACS;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAU,cAAA,MACF;;;;;IAGApH,EADF,CAAAC,cAAA,SAAqC,eACmB;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAA2B,IAAA,MACF;;;;;IAGArH,EADF,CAAAC,cAAA,SAA8C,eACU;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAA4B,aAAA,MACF;;;;;IAGAtH,EADF,CAAAC,cAAA,SAAgD,eACQ;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAa,eAAA,MACF;;;;;IAGAvH,EADF,CAAAC,cAAA,SAA2C,eACa;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAc,UAAA,MACF;;;;;IAGAxH,EADF,CAAAC,cAAA,SAA2C,eACa;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAe,UAAA,MACF;;;;;IAGAzH,EADF,CAAAC,cAAA,SAA+C,eACL;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAA6E,YAAA,CAAAgC,cAAA,CAEpC;;;;;IAGJ1H,EADF,CAAAC,cAAA,SAAgD,eACQ;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAiB,eAAA,MACF;;;;;IAGA3H,EADF,CAAAC,cAAA,SAAiD,eACO;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAkB,gBAAA,MACF;;;;;IAGA5H,EADF,CAAAC,cAAA,SAA8C,eACU;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAmC,aAAA,MACF;;;;;IAGA7H,EADF,CAAAC,cAAA,SAAiD,eACO;IAAAD,EAAA,CAAAU,MAAA,GAElD;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHmDX,EAAA,CAAAY,SAAA,GAElD;IAFkDZ,EAAA,CAAAa,iBAAA,CAAA6E,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAoB,gBAAA,CAElD;;;;;IAGJ9H,EADF,CAAAC,cAAA,SAA4C,eACL;IAAAD,EAAA,CAAAU,MAAA,GAA6C;IACpFV,EADoF,CAAAW,YAAA,EAAO,EACtF;;;;IADkCX,EAAA,CAAAY,SAAA,GAA6C;IAA7CZ,EAAA,CAAAa,iBAAA,CAAA6E,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAqB,WAAA,CAA6C;;;;;IAGlF/H,EADF,CAAAC,cAAA,SAAgD,eACN;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAA6E,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAsB,eAAA,CAEpC;;;;;IAGJhI,EADF,CAAAC,cAAA,SAAyC,eACe;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAuB,QAAA,MACF;;;;;IAGAjI,EADF,CAAAC,cAAA,SAA8C,eACU;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAwC,aAAA,MACF;;;;;IAUElI,EAAA,CAAAmI,uBAAA,GAAwD;IACtDnI,EAAA,CAAAU,MAAA,GAIF;;;;;;IAJEV,EAAA,CAAAY,SAAA,EAIF;IAJEZ,EAAA,CAAAyF,kBAAA,MAAAzF,EAAA,CAAAoI,WAAA,OAAA1C,YAAA,CAAA2C,gBAAA,iCAIF;;;;;IACArI,EAAA,CAAAmI,uBAAA,GAA+D;IAC7DnI,EAAA,CAAAU,MAAA,GAIF;;;;;;IAJEV,EAAA,CAAAY,SAAA,EAIF;IAJEZ,EAAA,CAAAyF,kBAAA,MAAAzF,EAAA,CAAAoI,WAAA,OAAA1C,YAAA,CAAA4C,uBAAA,iCAIF;;;;;IAhBFtI,EAAA,CAAAC,cAAA,eAIK;IAOHD,EANA,CAAAuI,UAAA,IAAAC,mEAAA,2BAAwD,IAAAC,mEAAA,2BAMO;IAMjEzI,EAAA,CAAAW,YAAA,EAAO;;;;IAhBLX,EAAA,CAAA0I,UAAA,YAAA1I,EAAA,CAAA2I,eAAA,IAAAC,GAAA,EAAAlD,YAAA,CAAAwC,aAAA,aAAAxC,YAAA,CAAAwC,aAAA,oBAGE;IACalI,EAAA,CAAAY,SAAA,EAAuC;IAAvCZ,EAAA,CAAA0I,UAAA,SAAAhD,YAAA,CAAAwC,aAAA,YAAuC;IAMvClI,EAAA,CAAAY,SAAA,EAA8C;IAA9CZ,EAAA,CAAA0I,UAAA,SAAAhD,YAAA,CAAAwC,aAAA,mBAA8C;;;;;IAU7DlI,EADF,CAAAC,cAAA,cAAyG,eAC/D;IACtCD,EAAA,CAAAU,MAAA,GAKF;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAAU,MAAA,GAKF;;IACFV,EADE,CAAAW,YAAA,EAAO,EACH;;;;IAbFX,EAAA,CAAAY,SAAA,GAKF;IALEZ,EAAA,CAAAyF,kBAAA,YAAAzF,EAAA,CAAAoI,WAAA,OAAA1C,YAAA,CAAA2C,gBAAA,iCAKF;IAEErI,EAAA,CAAAY,SAAA,GAKF;IALEZ,EAAA,CAAAyF,kBAAA,mBAAAzF,EAAA,CAAAoI,WAAA,OAAA1C,YAAA,CAAA4C,uBAAA,iCAKF;;;;;IApCJtI,EAAA,CAAAC,cAAA,SAA2C;IAsBzCD,EApBA,CAAAuI,UAAA,IAAAM,oDAAA,mBAIK,IAAAC,mDAAA,mBAgBoG;IAgB3G9I,EAAA,CAAAW,YAAA,EAAK;;;;IApCIX,EAAA,CAAAY,SAAA,EAAgE;IAAhEZ,EAAA,CAAA0I,UAAA,SAAAhD,YAAA,CAAAwC,aAAA,qCAAgE;IAoBjElI,EAAA,CAAAY,SAAA,EAAgE;IAAhEZ,EAAA,CAAA0I,UAAA,SAAAhD,YAAA,CAAAwC,aAAA,qCAAgE;;;;;IAkBtElI,EADF,CAAAC,cAAA,SAA+C,eACL;IAAAD,EAAA,CAAAU,MAAA,GAEpC;IACNV,EADM,CAAAW,YAAA,EAAO,EACR;;;;IAHqCX,EAAA,CAAAY,SAAA,GAEpC;IAFoCZ,EAAA,CAAAa,iBAAA,CAAA6E,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAqC,cAAA,CAEpC;;;;;IAIJ/I,EAAA,CAAAC,cAAA,eACiD;IAC/CD,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAzF,EAAA,CAAAoI,WAAA,OAAA1C,YAAA,CAAAsD,SAAA,iCACF;;;;;IACAhJ,EAAA,CAAAC,cAAA,eAAwF;IAAAD,EAAA,CAAAU,MAAA,QAAC;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IALlGX,EAAA,CAAAC,cAAA,SAA0E;IAKxED,EAJA,CAAAuI,UAAA,IAAAU,oDAAA,mBACiD,IAAAC,oDAAA,mBAGuC;IAC1FlJ,EAAA,CAAAW,YAAA,EAAK;;;;IALIX,EAAA,CAAAY,SAAA,EAA4D;IAA5DZ,EAAA,CAAA0I,UAAA,UAAAhD,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAqC,cAAA,cAA4D;IAI5D/I,EAAA,CAAAY,SAAA,EAA4D;IAA5DZ,EAAA,CAAA0I,UAAA,UAAAhD,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAqC,cAAA,cAA4D;;;;;IAInE/I,EAAA,CAAAC,cAAA,eAEiD;IAC/CD,EAAA,CAAAU,MAAA,GACF;;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAzF,EAAA,CAAAoI,WAAA,OAAA1C,YAAA,CAAAyD,WAAA,iCACF;;;;;IACAnJ,EAAA,CAAAC,cAAA,eAEqB;IAAAD,EAAA,CAAAU,MAAA,QAAC;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAR/BX,EAAA,CAAAC,cAAA,SAA8E;IAM5ED,EALA,CAAAuI,UAAA,IAAAa,oDAAA,mBAEiD,IAAAC,oDAAA,mBAK5B;IACvBrJ,EAAA,CAAAW,YAAA,EAAK;;;;IAPAX,EAAA,CAAAY,SAAA,EAA2H;IAA3HZ,EAAA,CAAA0I,UAAA,UAAAhD,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAqC,cAAA,oBAAArD,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAqC,cAAA,iBAA2H;IAK3H/I,EAAA,CAAAY,SAAA,EAA2H;IAA3HZ,EAAA,CAAA0I,UAAA,UAAAhD,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAqC,cAAA,oBAAArD,YAAA,CAAAgB,iBAAA,kBAAAhB,YAAA,CAAAgB,iBAAA,CAAAqC,cAAA,iBAA2H;;;;;IAK9H/I,EADF,CAAAC,cAAA,SAAiD,eACO;IACpDD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IAFDX,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAA4D,gBAAA,MACF;;;;;;IAKAtJ,EADF,CAAAC,cAAA,SAAyC,iBACiD;IAA9CD,EAAA,CAAAE,UAAA,mBAAAqJ,sEAAA;MAAAvJ,EAAA,CAAAI,aAAA,CAAAoJ,IAAA;MAAA,MAAA9D,YAAA,GAAA1F,EAAA,CAAAO,aAAA,GAAA8F,SAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAmJ,iBAAA,CAAA/D,YAAA,CAAAgE,OAAA,CAAmC;IAAA,EAAC;IACrF1J,EAAA,CAAAwG,SAAA,YAA2C;IAACxG,EAAA,CAAAU,MAAA,kBAC9C;IACFV,EADE,CAAAW,YAAA,EAAS,EACN;;;;;IAEHX,EADF,CAAAC,cAAA,SAAuC,eAOhC;IACHD,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAW,YAAA,EAAO,EACJ;;;;IATyBX,EAAA,CAAAY,SAAA,EAMxB;IANwBZ,EAAA,CAAA0I,UAAA,YAAA1I,EAAA,CAAA2J,eAAA,IAAAC,GAAA,EAAAlE,YAAA,CAAAmE,MAAA,kBAAAnE,YAAA,CAAAmE,MAAA,aAAAnE,YAAA,CAAAmE,MAAA,iBAAAnE,YAAA,CAAAmE,MAAA,gBAAAnE,YAAA,CAAAmE,MAAA,YAMxB;IACF7J,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAyF,kBAAA,MAAAC,YAAA,CAAAmE,MAAA,MACF;;;;;;IAIE7J,EAFJ,CAAAC,cAAA,aAA8D,cACtC,iBAEQ;IAC1BD,EAAA,CAAAwG,SAAA,YAA6C;IAC/CxG,EAAA,CAAAW,YAAA,EAAS;IAGLX,EAFJ,CAAAC,cAAA,aAA0B,SACpB,iBAC6D;IAAjCD,EAAA,CAAAE,UAAA,mBAAA4J,sEAAA;MAAA9J,EAAA,CAAAI,aAAA,CAAA2J,IAAA;MAAA,MAAArE,YAAA,GAAA1F,EAAA,CAAAO,aAAA,GAAA8F,SAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA0J,YAAA,CAAAtE,YAAA,CAAsB;IAAA,EAAC;IAC5D1F,EAAA,CAAAwG,SAAA,YAAoC;IAACxG,EAAA,CAAAU,MAAA,0BACvC;IAIRV,EAJQ,CAAAW,YAAA,EAAS,EACN,EACF,EACD,EACH;;;;;IA9QPX,EAAA,CAAAC,cAAA,SAAkC;IAgQhCD,EA9PA,CAAAuI,UAAA,IAAA0B,4CAAA,iBAA0C,IAAAC,4CAAA,iBAKC,IAAAC,4CAAA,iBAKN,IAAAC,4CAAA,iBAKA,IAAAC,4CAAA,iBAKA,IAAAC,4CAAA,iBAKG,IAAAC,4CAAA,iBAKQ,IAAAC,4CAAA,iBAKP,IAAAC,4CAAA,iBAQI,KAAAC,6CAAA,iBAKJ,KAAAC,6CAAA,iBAKM,KAAAC,6CAAA,iBAKJ,KAAAC,6CAAA,iBAKL,KAAAC,6CAAA,iBAKG,KAAAC,6CAAA,iBAKE,KAAAC,6CAAA,iBAKE,KAAAC,6CAAA,iBAKC,KAAAC,6CAAA,iBAKI,KAAAC,6CAAA,iBAKH,KAAAC,6CAAA,iBAKV,KAAAC,6CAAA,iBAKS,KAAAC,6CAAA,iBAKE,KAAAC,6CAAA,iBAKL,KAAAC,6CAAA,iBAKA,KAAAC,6CAAA,iBAKI,KAAAC,6CAAA,iBAKC,KAAAC,6CAAA,iBAKC,KAAAC,6CAAA,iBAKH,KAAAC,6CAAA,iBAKG,KAAAC,6CAAA,iBAKL,KAAAC,6CAAA,iBAGI,KAAAC,6CAAA,iBAKP,KAAAC,6CAAA,iBAKK,KAAAC,6CAAA,iBAMH,KAAAC,6CAAA,iBAuCI,KAAAC,6CAAA,iBAM2B,KAAAC,6CAAA,iBAQI,KAAAC,6CAAA,iBAW7B,KAAAC,6CAAA,iBAOR,KAAAC,6CAAA,iBAKF,KAAAC,6CAAA,iBAWuB;IAehEzM,EAAA,CAAAW,YAAA,EAAK;;;;IA7QEX,EAAA,CAAAY,SAAA,EAAmC;IAAnCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,cAAmC;IAKnC1M,EAAA,CAAAY,SAAA,EAAoC;IAApCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,eAAoC;IAKpC1M,EAAA,CAAAY,SAAA,EAA8B;IAA9BZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,SAA8B;IAK9B1M,EAAA,CAAAY,SAAA,EAA8B;IAA9BZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,SAA8B;IAK9B1M,EAAA,CAAAY,SAAA,EAA8B;IAA9BZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,SAA8B;IAK9B1M,EAAA,CAAAY,SAAA,EAAiC;IAAjCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,YAAiC;IAKjC1M,EAAA,CAAAY,SAAA,EAAyC;IAAzCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,oBAAyC;IAKzC1M,EAAA,CAAAY,SAAA,EAAkC;IAAlCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,aAAkC;IAQlC1M,EAAA,CAAAY,SAAA,EAAsC;IAAtCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,iBAAsC;IAKtC1M,EAAA,CAAAY,SAAA,EAAkC;IAAlCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,aAAkC;IAKlC1M,EAAA,CAAAY,SAAA,EAAwC;IAAxCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,mBAAwC;IAKxC1M,EAAA,CAAAY,SAAA,EAAoC;IAApCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,eAAoC;IAKpC1M,EAAA,CAAAY,SAAA,EAA+B;IAA/BZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,UAA+B;IAK/B1M,EAAA,CAAAY,SAAA,EAAkC;IAAlCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,aAAkC;IAKlC1M,EAAA,CAAAY,SAAA,EAAoC;IAApCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,eAAoC;IAKpC1M,EAAA,CAAAY,SAAA,EAAsC;IAAtCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,iBAAsC;IAKtC1M,EAAA,CAAAY,SAAA,EAAuC;IAAvCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,kBAAuC;IAKvC1M,EAAA,CAAAY,SAAA,EAA2C;IAA3CZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,sBAA2C;IAK3C1M,EAAA,CAAAY,SAAA,EAAwC;IAAxCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,mBAAwC;IAKxC1M,EAAA,CAAAY,SAAA,EAA8B;IAA9BZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,SAA8B;IAK9B1M,EAAA,CAAAY,SAAA,EAAuC;IAAvCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,kBAAuC;IAKvC1M,EAAA,CAAAY,SAAA,EAAyC;IAAzCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,oBAAyC;IAKzC1M,EAAA,CAAAY,SAAA,EAAoC;IAApCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,eAAoC;IAKpC1M,EAAA,CAAAY,SAAA,EAAoC;IAApCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,eAAoC;IAKpC1M,EAAA,CAAAY,SAAA,EAAwC;IAAxCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,mBAAwC;IAKxC1M,EAAA,CAAAY,SAAA,EAAyC;IAAzCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,oBAAyC;IAKzC1M,EAAA,CAAAY,SAAA,EAA0C;IAA1CZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,qBAA0C;IAK1C1M,EAAA,CAAAY,SAAA,EAAuC;IAAvCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,kBAAuC;IAKvC1M,EAAA,CAAAY,SAAA,EAA0C;IAA1CZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,qBAA0C;IAK1C1M,EAAA,CAAAY,SAAA,EAAqC;IAArCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,gBAAqC;IAGrC1M,EAAA,CAAAY,SAAA,EAAyC;IAAzCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,oBAAyC;IAKzC1M,EAAA,CAAAY,SAAA,EAAkC;IAAlCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,aAAkC;IAKlC1M,EAAA,CAAAY,SAAA,EAAuC;IAAvCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,kBAAuC;IAMvC1M,EAAA,CAAAY,SAAA,EAAoC;IAApCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,eAAoC;IAuCpC1M,EAAA,CAAAY,SAAA,EAAwC;IAAxCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,mBAAwC;IAMxC1M,EAAA,CAAAY,SAAA,EAAmE;IAAnEZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,iBAAApM,MAAA,CAAAqM,0BAAA,GAAmE;IAQnE3M,EAAA,CAAAY,SAAA,EAAuE;IAAvEZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,mBAAApM,MAAA,CAAAsM,4BAAA,GAAuE;IAWvE5M,EAAA,CAAAY,SAAA,EAA0C;IAA1CZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,qBAA0C;IAO1C1M,EAAA,CAAAY,SAAA,EAAkC;IAAlCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,aAAkC;IAKlC1M,EAAA,CAAAY,SAAA,EAAgC;IAAhCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,WAAgC;IAWV1M,EAAA,CAAAY,SAAA,EAAiC;IAAjCZ,EAAA,CAAA0I,UAAA,SAAApI,MAAA,CAAAoM,gBAAA,YAAiC;;;ADvgBpE,OAAM,MAAOG,wBAAyB,SAAQ/M,iBAAiB;EAajDgN,EAAA;EACAC,WAAA;EACFC,SAAA;EACAC,MAAA;EAdV;EACAC,QAAQ;EAECC,cAAc;EACvBC,aAAa,GAAkB,IAAI;EACnCC,gBAAgB,GAA2B,IAAI;EAE/C;EACAC,cAAc,GAAa,EAAE;EAE7BC,YACYT,EAAqB,EACrBC,WAAwB,EAC1BC,SAAuB,EACvBC,MAAc;IAEtB,KAAK,CAACH,EAAE,CAAC;IALC,KAAAA,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACb,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,MAAM,GAANA,MAAM;IAGd,MAAMO,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACpD,IAAIC,IAAI,GAAGH,QAAQ,GAAGI,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC,GAAG,IAAI;IACjD,IAAI,CAACN,QAAQ,GAAGS,IAAI,EAAET,QAAQ;IAC9B,IAAI,CAACY,UAAU,CAACf,WAAW,CAAC;IAC5B,IAAI,CAACgB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,QAAQ,GAAG,MAAM;IACtB,IAAI,CAACC,IAAI,CAACC,OAAO,GAAG;MAAEhB,QAAQ,EAAE,IAAI,CAACA;IAAQ,CAAE;EACjD;EAEAiB,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAACjB,cAAc,IAAI,CAACiB,OAAO,CAACjB,cAAc,CAACkB,WAAW,EAAE;MACjE,IAAI,CAACJ,IAAI,CAACC,OAAO,GAAG;QAAEhB,QAAQ,EAAE,IAAI,CAACA,QAAQ;QAAE,GAAG,IAAI,CAACC;MAAc,CAAE;MACvE,IAAI,CAACmB,oBAAoB,EAAE;MAC3B,IAAI,CAACC,WAAW,CAAC,IAAI,CAACN,IAAI,CAAC;IAC7B;EACF;EAEAO,QAAQA,CAAA;IACN,KAAK,CAACA,QAAQ,EAAE;IAChB,IAAI,CAACF,oBAAoB,EAAE;EAC7B;EAEAA,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACnB,cAAc,EAAEsB,YAAY,IAAI,IAAI,CAACtB,cAAc,EAAEuB,QAAQ,EAAE;MACtE,IAAI,CAACpB,cAAc,GAAG,IAAI,CAACqB,eAAe,EAAE;IAC9C,CAAC,MAAM;MACL;MACA,IAAI,CAACrB,cAAc,GAAG,CACpB,WAAW,EACX,YAAY,EACZ,MAAM,EACN,MAAM,EACN,MAAM,EACN,iBAAiB,EACjB,UAAU,EACV,UAAU,EACV,QAAQ,EACR,kBAAkB,EAClB,SAAS,CACV;IACH;EACF;EAEA;EACAqB,eAAeA,CAAA;IACb,MAAMF,YAAY,GAAG,IAAI,CAACtB,cAAc,EAAEsB,YAAY;IACtD,MAAM5I,IAAI,GAAG,IAAI,CAACsH,cAAc,EAAEuB,QAAQ;IAE1C;IACA,MAAME,WAAW,GAAG,CAClB,WAAW,EACX,YAAY,EACZ,MAAM,EACN,MAAM,EACN,MAAM,EACN,SAAS,EACT,iBAAiB,EACjB,UAAU,CACX;IACD,MAAMC,aAAa,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,SAAS,CAAC;IAEvD,IAAIC,eAAe,GAAa,EAAE;IAElC,IACEL,YAAY,KAAK,kBAAkB,KAClC5I,IAAI,KAAK,YAAY,IACpBA,IAAI,KAAK,UAAU,IACnBA,IAAI,KAAK,SAAS,IAClBA,IAAI,KAAK,YAAY,IACrBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,UAAU,CAAC,EACtB;MACAiJ,eAAe,GAAG,CAChB,gBAAgB,EAChB,YAAY,EACZ,OAAO,EACP,UAAU,EACV,eAAe,EACf,mBAAmB,EACnB,YAAY,EACZ,MAAM,EACN,eAAe,EACf,gBAAgB,EAChB,aAAa,EACb,kBAAkB,EAClB,eAAe,EACf,qBAAqB,EACrB,4BAA4B,EAC5B,YAAY,CACb;IACH,CAAC,MAAM,IACLL,YAAY,KAAK,kBAAkB,KAClC5I,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,gBAAgB,CAAC,EAC9C;MACAiJ,eAAe,GAAG,CAChB,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EACd,YAAY,EACZ,iBAAiB,EACjB,YAAY,EACZ,YAAY,EACZ,MAAM,EACN,eAAe,EACf,aAAa,EACb,kBAAkB,EAClB,eAAe,EACf,qBAAqB,EACrB,4BAA4B,EAC5B,YAAY,CACb;IACH,CAAC,MAAM,IACLL,YAAY,KAAK,kBAAkB,KAClC5I,IAAI,KAAK,YAAY,IACpBA,IAAI,KAAK,iBAAiB,IAC1BA,IAAI,KAAK,sBAAsB,IAC/BA,IAAI,KAAK,mBAAmB,CAAC,EAC/B;MACAiJ,eAAe,GAAG,CAChB,UAAU,EACV,gBAAgB,EAChB,YAAY,EACZ,OAAO,EACP,UAAU,EACV,MAAM,EACN,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,UAAU,EACV,iBAAiB,EACjB,kBAAkB,EAClB,eAAe,EACf,qBAAqB,EACrB,4BAA4B,EAC5B,YAAY,CACb;IACH,CAAC,MAAM,IACLL,YAAY,KAAK,kBAAkB,KAClC5I,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,WAAW,CAAC,EAC/C;MACAiJ,eAAe,GAAG,CAChB,gBAAgB,EAChB,gBAAgB,EAChB,YAAY,EACZ,cAAc,EACd,UAAU,EACV,eAAe,EACf,iBAAiB,EACjB,aAAa,EACb,kBAAkB,EAClB,eAAe,EACf,qBAAqB,EACrB,4BAA4B,EAC5B,YAAY,CACb;IACH,CAAC,MAAM,IACLL,YAAY,KAAK,kBAAkB,KAClC5I,IAAI,KAAK,yBAAyB,IACjCA,IAAI,KAAK,mBAAmB,IAC5BA,IAAI,KAAK,sBAAsB,IAC/BA,IAAI,KAAK,iCAAiC,IAC1CA,IAAI,KAAK,kBAAkB,IAC3BA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,aAAa,IACtBA,IAAI,KAAK,iBAAiB,IAC1BA,IAAI,KAAK,kBAAkB,CAAC,EAC9B;MACAiJ,eAAe,GAAG,CAChB,YAAY,EACZ,YAAY,EACZ,iBAAiB,EACjB,iBAAiB,EACjB,kBAAkB,EAClB,MAAM,EACN,aAAa,EACb,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,EAClB,eAAe,EACf,qBAAqB,EACrB,4BAA4B,EAC5B,YAAY,CACb;IACH,CAAC,MAAM,IACLL,YAAY,KAAK,iBAAiB,KACjC5I,IAAI,KAAK,YAAY,IACpBA,IAAI,KAAK,UAAU,IACnBA,IAAI,KAAK,SAAS,IAClBA,IAAI,KAAK,YAAY,IACrBA,IAAI,KAAK,SAAS,CAAC,EACrB;MACAiJ,eAAe,GAAG,CAChB,cAAc,EACd,gBAAgB,EAChB,YAAY,EACZ,OAAO,EACP,UAAU,EACV,eAAe,EACf,mBAAmB,EACnB,MAAM,EACN,eAAe,EACf,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,EAClB,eAAe,EACf,eAAe,EACf,qBAAqB,EACrB,4BAA4B,EAC5B,YAAY,CACb;IACH,CAAC,MAAM,IACLL,YAAY,KAAK,iBAAiB,KACjC5I,IAAI,KAAK,mBAAmB,IAC3BA,IAAI,KAAK,aAAa,IACtBA,IAAI,KAAK,aAAa,CAAC,EACzB;MACAiJ,eAAe,GAAG,CAChB,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EACd,YAAY,EACZ,eAAe,EACf,mBAAmB,EACnB,MAAM,EACN,eAAe,EACf,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,EAClB,eAAe,EACf,eAAe,EACf,qBAAqB,EACrB,4BAA4B,EAC5B,YAAY,CACb;IACH,CAAC,MAAM,IACLL,YAAY,KAAK,iBAAiB,KACjC5I,IAAI,KAAK,YAAY,IACpBA,IAAI,KAAK,iBAAiB,IAC1BA,IAAI,KAAK,sBAAsB,IAC/BA,IAAI,KAAK,mBAAmB,CAAC,EAC/B;MACAiJ,eAAe,GAAG,CAChB,cAAc,EACd,UAAU,EACV,gBAAgB,EAChB,YAAY,EACZ,OAAO,EACP,UAAU,EACV,MAAM,EACN,eAAe,EACf,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EACjB,kBAAkB,EAClB,eAAe,EACf,eAAe,EACf,qBAAqB,EACrB,4BAA4B,EAC5B,YAAY,CACb;IACH,CAAC,MAAM,IACLL,YAAY,KAAK,SAAS,KACzB5I,IAAI,KAAK,YAAY,IACpBA,IAAI,KAAK,UAAU,IACnBA,IAAI,KAAK,SAAS,IAClBA,IAAI,KAAK,YAAY,IACrBA,IAAI,KAAK,UAAU,IACnBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,SAAS,IAClBA,IAAI,KAAK,QAAQ,IACjBA,IAAI,KAAK,mBAAmB,IAC5BA,IAAI,KAAK,gBAAgB,IACzBA,IAAI,KAAK,aAAa,IACtBA,IAAI,KAAK,aAAa,IACtBA,IAAI,KAAK,SAAS,CAAC,EACrB;MACAiJ,eAAe,GAAG,CAChB,gBAAgB,EAChB,YAAY,EACZ,UAAU,EACV,OAAO,EACP,MAAM,EACN,eAAe,EACf,mBAAmB,EACnB,eAAe,EACf,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,WAAW,EACX,aAAa,EACb,YAAY,CACb;IACH,CAAC,MAAM,IACLL,YAAY,KAAK,SAAS,KACzB5I,IAAI,KAAK,YAAY,IACpBA,IAAI,KAAK,WAAW,IACpBA,IAAI,KAAK,sBAAsB,IAC/BA,IAAI,KAAK,iBAAiB,IAC1BA,IAAI,KAAK,mBAAmB,IAC5BA,IAAI,KAAK,YAAY,CAAC,EACxB;MACAiJ,eAAe,GAAG,CAChB,gBAAgB,EAChB,YAAY,EACZ,UAAU,EACV,OAAO,EACP,MAAM,EACN,eAAe,EACf,mBAAmB,EACnB,eAAe,EACf,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EAChB,UAAU,EACV,WAAW,EACX,aAAa,CACd;IACH;IAEA,OAAO,CAAC,GAAGF,WAAW,EAAE,GAAGE,eAAe,EAAE,GAAGD,aAAa,CAAC;EAC/D;EAEA;EACAnC,gBAAgBA,CAACqC,UAAkB;IACjC,OAAO,IAAI,CAACzB,cAAc,CAAC0B,QAAQ,CAACD,UAAU,CAAC;EACjD;EAEA;EACApC,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAACsC,IAAI,CAACC,IAAI,CAAEC,QAAa,IAClCA,QAAQ,CAACzI,iBAAiB,EAAEqC,cAAc,KAAK,OAAO,IAAIoG,QAAQ,CAACnG,SAAS,CAC7E;EACH;EAEA;EACA4D,4BAA4BA,CAAA;IAC1B,OAAO,IAAI,CAACqC,IAAI,CAACC,IAAI,CAAEC,QAAa,IAClC,CAACA,QAAQ,CAACzI,iBAAiB,EAAEqC,cAAc,KAAK,SAAS,IACxDoG,QAAQ,CAACzI,iBAAiB,EAAEqC,cAAc,KAAK,UAAU,KAC1DoG,QAAQ,CAAChG,WAAW,CACrB;EACH;EAEA7C,cAAcA,CAACC,QAAgB;IAC7B,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAAC6I,IAAI,EAAE,KAAK,EAAE,EAAE;MACvCrP,IAAI,CAACsP,IAAI,CAAC;QACRC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,uBAAuB;QAC7BC,IAAI,EAAE,SAAS;QACfC,iBAAiB,EAAE;OACpB,CAAC;MACF;IACF;IACA,IACElJ,QAAQ,CAACyI,QAAQ,CAAC,iBAAiB,CAAC,IACpCzI,QAAQ,CAACyI,QAAQ,CAAC,iBAAiB,CAAC,EACpC;MACAU,MAAM,CAACC,IAAI,CAACpJ,QAAQ,EAAE,QAAQ,CAAC;MAC/B;IACF;IAEA,MAAMqJ,MAAM,GAAG,mDAAmDC,kBAAkB,CAClFtJ,QAAQ,CACT,EAAE;IACHmJ,MAAM,CAACC,IAAI,CAACC,MAAM,EAAE,QAAQ,CAAC;EAC/B;EAEA;EAEAE,qBAAqB,GAAkB,IAAI;EAE3CrG,iBAAiBA,CAACsG,OAAe;IAC/B,IAAI,CAACD,qBAAqB,GAAGC,OAAO;IAEpC,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC;IACjE,IAAIF,YAAY,EAAE;MAChB,MAAMG,KAAK,GAAG,IAAItQ,KAAK,CAACmQ,YAAY,CAAC;MACrCG,KAAK,CAACC,IAAI,EAAE;IACd;EACF;EAEApG,YAAYA,CAAC+C,WAAgB;IAC3B,IAAI,CAACE,MAAM,CAACoD,QAAQ,CAAC,CAAC,0CAA0C,CAAC,EAAE;MACjEC,WAAW,EAAE;QAAEC,MAAM,EAAExD,WAAW,CAACyD;MAAE;KACtC,CAAC;EACJ;;qCArZW3D,wBAAwB,EAAA7M,EAAA,CAAAyQ,iBAAA,CAAAzQ,EAAA,CAAA0Q,iBAAA,GAAA1Q,EAAA,CAAAyQ,iBAAA,CAAAE,EAAA,CAAAC,WAAA,GAAA5Q,EAAA,CAAAyQ,iBAAA,CAAAI,EAAA,CAAAC,YAAA,GAAA9Q,EAAA,CAAAyQ,iBAAA,CAAAM,EAAA,CAAAC,MAAA;EAAA;;UAAxBnE,wBAAwB;IAAAoE,SAAA;IAAAC,MAAA;MAAA/D,cAAA;IAAA;IAAAgE,QAAA,GAAAnR,EAAA,CAAAoR,0BAAA,EAAApR,EAAA,CAAAqR,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCV/B3R,EAHN,CAAAC,cAAA,aAAmC,eACsD,YAC9E,YAC2D;QA2Q9DD,EAzQA,CAAAuI,UAAA,IAAAsJ,sCAAA,gBACwC,IAAAC,sCAAA,gBAMuE,IAAAC,sCAAA,gBAMZ,IAAAC,sCAAA,gBAMG,IAAAC,sCAAA,gBAMA,IAAAC,sCAAA,gBAMI,KAAAC,uCAAA,gBAO5D,KAAAC,uCAAA,gBAMe,KAAAC,uCAAA,gBAMlB,KAAAC,uCAAA,gBAMiE,KAAAC,uCAAA,gBAO/D,KAAAC,uCAAA,gBAMmE,KAAAC,uCAAA,gBAMX,KAAAC,uCAAA,gBAMO,KAAAC,uCAAA,gBAMI,KAAAC,uCAAA,gBAOrE,KAAAC,uCAAA,gBAOC,KAAAC,uCAAA,gBAOI,KAAAC,uCAAA,gBAOH,KAAAC,uCAAA,gBAMsD,KAAAC,uCAAA,gBAOvD,KAAAC,uCAAA,gBAOE,KAAAC,uCAAA,gBAMkE,KAAAC,uCAAA,gBAMA,KAAAC,uCAAA,gBAOnE,KAAAC,uCAAA,gBAOC,KAAAC,uCAAA,gBAOC,KAAAC,uCAAA,gBAOH,KAAAC,uCAAA,gBAOG,KAAAC,uCAAA,gBAOL,KAAAC,uCAAA,gBAOI,KAAAC,uCAAA,gBAM6D,KAAAC,uCAAA,gBAO/D,KAAAC,uCAAA,gBAOoE,KAAAC,uCAAA,gBAQnE,KAAAC,uCAAA,gBAQ2B,KAAAC,uCAAA,gBAQI,KAAAC,uCAAA,gBAQ7B,KAAAC,uCAAA,gBAQc,KAAAC,uCAAA,gBAG0C,KAAAC,uCAAA,gBAMlB;QAIzFrU,EADE,CAAAW,YAAA,EAAK,EACC;QACRX,EAAA,CAAAC,cAAA,aAAO;QACLD,EAAA,CAAAuI,UAAA,KAAA+L,uCAAA,kBAAkC;QAiRtCtU,EADE,CAAAW,YAAA,EAAQ,EACF;QAENX,EADF,CAAAC,cAAA,cAAiB,0BAEuB;QAApCD,EAAA,CAAAE,UAAA,wBAAAqU,wEAAAC,MAAA;UAAA,OAAc5C,GAAA,CAAA6C,YAAA,CAAAD,MAAA,CAAoB;QAAA,EAAC;QAGzCxU,EAFI,CAAAW,YAAA,EAAiB,EACb,EACF;QAENX,EAAA,CAAAwG,SAAA,oCAAqG;;;QAviB1FxG,EAAA,CAAAY,SAAA,GAAmC;QAAnCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,cAAmC;QAMmC1M,EAAA,CAAAY,SAAA,EAAoC;QAApCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,eAAoC;QAM1C1M,EAAA,CAAAY,SAAA,EAA8B;QAA9BZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,SAA8B;QAM3B1M,EAAA,CAAAY,SAAA,EAA8B;QAA9BZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,SAA8B;QAM9B1M,EAAA,CAAAY,SAAA,EAA8B;QAA9BZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,SAA8B;QAM7B1M,EAAA,CAAAY,SAAA,EAAiC;QAAjCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,YAAiC;QAOrG1M,EAAA,CAAAY,SAAA,EAAyC;QAAzCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,oBAAyC;QAMnB1M,EAAA,CAAAY,SAAA,EAAkC;QAAlCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,aAAkC;QAMxD1M,EAAA,CAAAY,SAAA,EAAsC;QAAtCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,iBAAsC;QAM+B1M,EAAA,CAAAY,SAAA,EAAkC;QAAlCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,aAAkC;QAOvG1M,EAAA,CAAAY,SAAA,EAAwC;QAAxCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,mBAAwC;QAM+B1M,EAAA,CAAAY,SAAA,EAAoC;QAApCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,eAAoC;QAM1C1M,EAAA,CAAAY,SAAA,EAA+B;QAA/BZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,UAA+B;QAM3B1M,EAAA,CAAAY,SAAA,EAAkC;QAAlCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,aAAkC;QAMhC1M,EAAA,CAAAY,SAAA,EAAoC;QAApCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,eAAoC;QAO3G1M,EAAA,CAAAY,SAAA,EAAsC;QAAtCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,iBAAsC;QAOtC1M,EAAA,CAAAY,SAAA,EAAuC;QAAvCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,kBAAuC;QAOvC1M,EAAA,CAAAY,SAAA,EAA2C;QAA3CZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,sBAA2C;QAO3C1M,EAAA,CAAAY,SAAA,EAAwC;QAAxCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,mBAAwC;QAMwB1M,EAAA,CAAAY,SAAA,EAA8B;QAA9BZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,SAA8B;QAO9F1M,EAAA,CAAAY,SAAA,EAAuC;QAAvCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,kBAAuC;QAOvC1M,EAAA,CAAAY,SAAA,EAAyC;QAAzCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,oBAAyC;QAM8B1M,EAAA,CAAAY,SAAA,EAAoC;QAApCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,eAAoC;QAMpC1M,EAAA,CAAAY,SAAA,EAAoC;QAApCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,eAAoC;QAO3G1M,EAAA,CAAAY,SAAA,EAAwC;QAAxCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,mBAAwC;QAOxC1M,EAAA,CAAAY,SAAA,EAAyC;QAAzCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,oBAAyC;QAOzC1M,EAAA,CAAAY,SAAA,EAA0C;QAA1CZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,qBAA0C;QAO1C1M,EAAA,CAAAY,SAAA,EAAuC;QAAvCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,kBAAuC;QAOvC1M,EAAA,CAAAY,SAAA,EAA0C;QAA1CZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,qBAA0C;QAO1C1M,EAAA,CAAAY,SAAA,EAAqC;QAArCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,gBAAqC;QAOrC1M,EAAA,CAAAY,SAAA,EAAyC;QAAzCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,oBAAyC;QAM2B1M,EAAA,CAAAY,SAAA,EAAkC;QAAlCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,aAAkC;QAOtG1M,EAAA,CAAAY,SAAA,EAAuC;QAAvCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,kBAAuC;QAOgC1M,EAAA,CAAAY,SAAA,EAAoC;QAApCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,eAAoC;QAQ3G1M,EAAA,CAAAY,SAAA,EAAwC;QAAxCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,mBAAwC;QAQxC1M,EAAA,CAAAY,SAAA,EAAmE;QAAnEZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,iBAAAkF,GAAA,CAAAjF,0BAAA,GAAmE;QAQnE3M,EAAA,CAAAY,SAAA,EAAuE;QAAvEZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,mBAAAkF,GAAA,CAAAhF,4BAAA,GAAuE;QAQvE5M,EAAA,CAAAY,SAAA,EAA0C;QAA1CZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,qBAA0C;QAQpB1M,EAAA,CAAAY,SAAA,EAAkC;QAAlCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,aAAkC;QAGU1M,EAAA,CAAAY,SAAA,EAAgC;QAAhCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,WAAgC;QAMnD1M,EAAA,CAAAY,SAAA,EAAiC;QAAjCZ,EAAA,CAAA0I,UAAA,SAAAkJ,GAAA,CAAAlF,gBAAA,YAAiC;QAM5D1M,EAAA,CAAAY,SAAA,GAAO;QAAPZ,EAAA,CAAA0I,UAAA,YAAAkJ,GAAA,CAAA3C,IAAA,CAAO;QAmRlBjP,EAAA,CAAAY,SAAA,GAAiC;QAA4BZ,EAA7D,CAAA0I,UAAA,eAAAkJ,GAAA,CAAA3D,IAAA,CAAAyG,aAAA,CAAiC,iBAAA9C,GAAA,CAAA3D,IAAA,CAAA0G,IAAA,CAA2B,gBAAA/C,GAAA,CAAA3D,IAAA,CAAA2G,UAAA,CAAgC;QAMtF5U,EAAA,CAAAY,SAAA,EAA+C;QAA/CZ,EAAA,CAAA0I,UAAA,0BAAAkJ,GAAA,CAAA9B,qBAAA,CAA+C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}