{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { BaseConfigService } from '../base-config.service';\nimport { FLOOR_TYPES_OPTIONS, UNIT_VIEW_TYPES_OPTIONS, FINISHING_STATUS_TYPES_OPTIONS, DELIVERY_STATUS_TYPES_OPTIONS, OTHER_ACCESSORIES_OPTIONS, FINANCIAL_STATUS_TYPES_OPTIONS } from '../../stepper-modal.constants';\nimport * as i0 from \"@angular/core\";\nexport class SellConfigService extends BaseConfigService {\n  /**\n   * Create sell-specific location inputs with additional fields for selling\n   */\n  createSellLocationInputs(stepperModal) {\n    return [{\n      step: 2,\n      name: 'compoundName',\n      type: 'text',\n      label: 'Compound Name',\n      validators: [Validators.required],\n      visibility: () => stepperModal.getInsideCompoundPrivilege()\n    }, {\n      step: 2,\n      name: 'detailedAddress',\n      type: 'text',\n      label: 'Detailed Address',\n      validators: [Validators.required],\n      visibility: () => stepperModal.getSellInsideCompoundInputs()\n    }, {\n      step: 2,\n      name: 'addressLink',\n      type: 'url',\n      label: 'Detailed Address Link',\n      validators: [Validators.pattern(/^(https?:\\/\\/)?([\\da-z.-]+)\\.([a-z.]{2,6})([\\/\\w .-]*)*\\/?$/)],\n      visibility: () => stepperModal.getSellInsideCompoundInputs()\n    }, {\n      step: 2,\n      name: 'projectManagement',\n      type: 'text',\n      label: 'Project Management',\n      validators: [Validators.required],\n      visibility: () => stepperModal.getSellInsideCompoundInputs()\n    }, {\n      step: 2,\n      name: 'projectConstructor',\n      type: 'text',\n      label: 'Project Constructor',\n      validators: [Validators.required],\n      visibility: () => stepperModal.getSellInsideCompoundInputs()\n    }, {\n      step: 2,\n      name: 'locationSuggestions',\n      type: 'checkbox',\n      label: 'Location Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 2,\n      name: 'cityId',\n      type: 'select',\n      label: 'City',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'areaId',\n      type: 'select',\n      label: 'Area',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'subAreaId',\n      type: 'select',\n      label: 'Sub Area',\n      options: [],\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell-specific unit information for apartments and similar units\n   */\n  createSellApartmentUnitInformationInputs(stepperModal, includeRooms = true) {\n    const inputs = [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true,\n      isDynamic: true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true,\n      isDynamic: true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n    if (includeRooms) {\n      inputs.push({\n        step: 3,\n        name: 'rooms',\n        type: 'number',\n        label: 'Number of Rooms',\n        validators: [Validators.required, Validators.min(0)],\n        visibility: () => true\n      }, {\n        step: 3,\n        name: 'bathRooms',\n        type: 'number',\n        label: 'Number of Bathrooms',\n        validators: [Validators.required, Validators.min(0)],\n        visibility: () => true\n      });\n    }\n    inputs.push({\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'financialStatus',\n      type: 'select',\n      label: 'Financial Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'clubStatus',\n      type: 'select',\n      label: 'Club Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'parkingStatus',\n      type: 'select',\n      label: 'Parking Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    });\n    return inputs;\n  }\n  /**\n   * Create sell-specific unit information for duplexes and similar units\n   */\n  createSellDuplexUnitInformationInputs(stepperModal, includeRooms = true) {\n    const inputs = [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true,\n      isDynamic: true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true,\n      isDynamic: true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n    if (includeRooms) {\n      inputs.push({\n        step: 3,\n        name: 'rooms',\n        type: 'number',\n        label: 'Number of Rooms',\n        validators: [Validators.required, Validators.min(0)],\n        visibility: () => true\n      }, {\n        step: 3,\n        name: 'bathRooms',\n        type: 'number',\n        label: 'Number of Bathrooms',\n        validators: [Validators.required, Validators.min(0)],\n        visibility: () => true\n      });\n    }\n    inputs.push({\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'financialStatus',\n      type: 'select',\n      label: 'Financial Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'clubStatus',\n      type: 'select',\n      label: 'Club Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'parkingStatus',\n      type: 'select',\n      label: 'Parking Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    });\n    return inputs;\n  }\n  createSellVillasUnitInformationInputs(stepperModal, includeRooms = true) {\n    const inputs = [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true,\n      isDynamic: true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }];\n    if (includeRooms) {\n      inputs.push({\n        step: 3,\n        name: 'rooms',\n        type: 'number',\n        label: 'Number of Rooms',\n        validators: [Validators.required, Validators.min(0)],\n        visibility: () => true\n      }, {\n        step: 3,\n        name: 'bathRooms',\n        type: 'number',\n        label: 'Number of Bathrooms',\n        validators: [Validators.required, Validators.min(0)],\n        visibility: () => true\n      });\n    }\n    inputs.push({\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'financialStatus',\n      type: 'select',\n      label: 'Financial Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'clubStatus',\n      type: 'select',\n      label: 'Club Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'parkingStatus',\n      type: 'select',\n      label: 'Parking Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    });\n    return inputs;\n  }\n  createSellTwinHousesUnitInformationInputs(stepperModal, includeRooms = true) {\n    const inputs = [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true,\n      isDynamic: true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }];\n    if (includeRooms) {\n      inputs.push({\n        step: 3,\n        name: 'rooms',\n        type: 'number',\n        label: 'Number of Rooms',\n        validators: [Validators.required, Validators.min(0)],\n        visibility: () => true\n      }, {\n        step: 3,\n        name: 'bathRooms',\n        type: 'number',\n        label: 'Number of Bathrooms',\n        validators: [Validators.required, Validators.min(0)],\n        visibility: () => true\n      });\n    }\n    inputs.push({\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'financialStatus',\n      type: 'select',\n      label: 'Financial Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'clubStatus',\n      type: 'select',\n      label: 'Club Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'parkingStatus',\n      type: 'select',\n      label: 'Parking Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    });\n    return inputs;\n  }\n  createSellPenthousesUnitInformationInputs(stepperModal, includeRooms = true) {\n    const inputs = [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true,\n      isDynamic: true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n    if (includeRooms) {\n      inputs.push({\n        step: 3,\n        name: 'rooms',\n        type: 'number',\n        label: 'Number of Rooms',\n        validators: [Validators.required, Validators.min(0)],\n        visibility: () => true\n      }, {\n        step: 3,\n        name: 'bathRooms',\n        type: 'number',\n        label: 'Number of Bathrooms',\n        validators: [Validators.required, Validators.min(0)],\n        visibility: () => true\n      });\n    }\n    inputs.push({\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'financialStatus',\n      type: 'select',\n      label: 'Financial Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'clubStatus',\n      type: 'select',\n      label: 'Club Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'parkingStatus',\n      type: 'select',\n      label: 'Parking Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    });\n    return inputs;\n  }\n  createSellPAdministrativeUnitsUnitInformationInputs(stepperModal, includeRooms = true) {\n    const inputs = [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true,\n      isDynamic: true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n    if (includeRooms) {\n      inputs.push({\n        step: 3,\n        name: 'rooms',\n        type: 'number',\n        label: 'Number of Rooms',\n        validators: [Validators.required, Validators.min(0)],\n        visibility: () => true\n      }, {\n        step: 3,\n        name: 'bathRooms',\n        type: 'number',\n        label: 'Number of Bathrooms',\n        validators: [Validators.required, Validators.min(0)],\n        visibility: () => true\n      });\n    }\n    inputs.push({\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'financialStatus',\n      type: 'select',\n      label: 'Financial Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'clubStatus',\n      type: 'select',\n      label: 'Club Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'parkingStatus',\n      type: 'select',\n      label: 'Parking Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    });\n    return inputs;\n  }\n  createSellCommercialAdministrativeBuildingsInformationInputs(stepperModal, includeRooms = true) {\n    const inputs = [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true,\n      isDynamic: true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }];\n    if (includeRooms) {\n      inputs.push({\n        step: 3,\n        name: 'rooms',\n        type: 'number',\n        label: 'Number of Rooms',\n        validators: [Validators.required, Validators.min(0)],\n        visibility: () => true\n      }, {\n        step: 3,\n        name: 'bathRooms',\n        type: 'number',\n        label: 'Number of Bathrooms',\n        validators: [Validators.required, Validators.min(0)],\n        visibility: () => true\n      });\n    }\n    inputs.push({\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'deliveryStatus',\n      type: 'select',\n      label: 'Delivery Status',\n      options: DELIVERY_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'financialStatus',\n      type: 'select',\n      label: 'Financial Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'clubStatus',\n      type: 'select',\n      label: 'Club Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'parkingStatus',\n      type: 'select',\n      label: 'Parking Status',\n      options: FINANCIAL_STATUS_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Other Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Notes',\n      validators: [],\n      visibility: () => true\n    });\n    return inputs;\n  }\n  /**\n   * Create sell-specific document inputs\n   */\n  createSellDocumentInputs() {\n    return [{\n      step: 4,\n      name: 'mainImage',\n      type: 'file',\n      label: 'Unit Main Image',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 4,\n      name: 'unitInMasterPlanImage',\n      type: 'file',\n      label: 'Unit in Master Plan Image',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 4,\n      name: 'galleryImages',\n      type: 'file',\n      label: 'Unit Gallery Images',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 4,\n      name: 'video',\n      type: 'file',\n      label: 'Videos',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create sell-specific financial inputs\n   */\n  createSellFinancialInputs(stepperModal) {\n    return [{\n      step: 5,\n      name: 'askingPrice',\n      type: 'number',\n      label: 'Asking Price',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'unitPriceSuggestions',\n      type: 'checkbox',\n      label: 'Price Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 5,\n      name: 'requestedOver',\n      type: 'number',\n      label: 'Request Over',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Configuration builder for primary inside compound sell apartments\n   */\n  createPrimaryInsideCompoundSellApartmentsConfig(stepperModal) {\n    return [...this.createSellLocationInputs(stepperModal), ...this.createSellApartmentUnitInformationInputs(stepperModal, true), ...this.createSellDocumentInputs(), ...this.createSellFinancialInputs(stepperModal)];\n  }\n  /**\n   * Configuration builder for primary and resale inside compound sell duplexes\n   */\n  createInsideCompoundSellDuplexesConfig(stepperModal, options = {}) {\n    return [...this.createSellLocationInputs(stepperModal), ...this.createSellDuplexUnitInformationInputs(stepperModal, true), ...this.createSellDocumentInputs(), ...this.createSellFinancialInputs(stepperModal)];\n  }\n  createInsideCompoundSellVillasConfig(stepperModal, options = {}) {\n    return [...this.createSellLocationInputs(stepperModal), ...this.createSellVillasUnitInformationInputs(stepperModal, true), ...this.createSellDocumentInputs(), ...this.createSellFinancialInputs(stepperModal)];\n  }\n  createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal, options = {}) {\n    return [...this.createSellLocationInputs(stepperModal), ...this.createSellTwinHousesUnitInformationInputs(stepperModal, true), ...this.createSellDocumentInputs(), ...this.createSellFinancialInputs(stepperModal)];\n  }\n  createPrimaryInsideCompoundPurchasingPenthouses(stepperModal, options = {}) {\n    return [...this.createSellLocationInputs(stepperModal), ...this.createSellPenthousesUnitInformationInputs(stepperModal, true), ...this.createSellDocumentInputs(), ...this.createSellFinancialInputs(stepperModal)];\n  }\n  createPrimaryInsideCompoundPurchasingAdministrativeUnits(stepperModal, options = {}) {\n    return [...this.createSellLocationInputs(stepperModal), ...this.createSellPAdministrativeUnitsUnitInformationInputs(stepperModal, true), ...this.createSellDocumentInputs(), ...this.createSellFinancialInputs(stepperModal)];\n  }\n  createPrimaryInsideCompoundPurchasingCommercialAdministrativeBuildings(stepperModal, options = {}) {\n    return [...this.createSellLocationInputs(stepperModal), ...this.createSellCommercialAdministrativeBuildingsInformationInputs(stepperModal, true), ...this.createSellDocumentInputs(), ...this.createSellFinancialInputs(stepperModal)];\n  }\n  /**\n   * Get input configurations for sell cases\n   */\n  getInputConfigs(stepperModal) {\n    return [{\n      key: 'primary_inside_compound_sell_apartments',\n      value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal)\n    }, {\n      key: 'resale_inside_compound_sell_apartments',\n      value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal)\n    }, {\n      key: 'primary_inside_compound_sell_duplexes',\n      value: this.createInsideCompoundSellDuplexesConfig(stepperModal)\n    }, {\n      key: 'resale_inside_compound_sell_duplexes',\n      value: this.createInsideCompoundSellDuplexesConfig(stepperModal)\n    }, {\n      key: 'primary_inside_compound_sell_studios',\n      value: this.createInsideCompoundSellDuplexesConfig(stepperModal, {\n        includeRooms: false,\n        includeDocuments: true\n      })\n    }, {\n      key: 'resale_inside_compound_sell_studios',\n      value: this.createInsideCompoundSellDuplexesConfig(stepperModal, {\n        includeRooms: false,\n        includeDocuments: true\n      })\n    }, {\n      key: 'primary_inside_compound_sell_villas',\n      value: this.createInsideCompoundSellVillasConfig(stepperModal, {\n        includeRooms: false,\n        includeDocuments: true\n      })\n    }, {\n      key: 'resale_inside_compound_sell_villas',\n      value: this.createInsideCompoundSellVillasConfig(stepperModal, {\n        includeRooms: false,\n        includeDocuments: true\n      })\n    }, {\n      key: 'primary_inside_compound_sell_administrative_units',\n      value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal)\n    }, {\n      key: 'resale_inside_compound_sell_administrative_units',\n      value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal)\n    }, {\n      key: 'primary_inside_compound_sell_medical_clinics',\n      value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal)\n    }, {\n      key: 'resale_inside_compound_sell_medical_clinics',\n      value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal)\n    }, {\n      key: 'primary_inside_compound_sell_pharmacies',\n      value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal)\n    }, {\n      key: 'resale_inside_compound_sell_pharmacies',\n      value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal)\n    }, {\n      key: 'primary_inside_compound_sell_shops',\n      value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal)\n    }, {\n      key: 'resale_inside_compound_sell_shops',\n      value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal)\n    }, {\n      key: 'primary_inside_compound_sell_twin_houses',\n      value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal)\n    }, {\n      key: 'resale_inside_compound_sell_twin_houses',\n      value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal)\n    }, {\n      key: 'primary_inside_compound_sell_town_houses',\n      value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal)\n    }, {\n      key: 'resale_inside_compound_sell_town_houses',\n      value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal)\n    }, {\n      key: 'primary_inside_compound_sell_standalone_villas',\n      value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal)\n    }, {\n      key: 'resale_inside_compound_sell_standalone_villas',\n      value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal)\n    }, {\n      key: 'primary_inside_compound_sell_penthouses',\n      value: this.createPrimaryInsideCompoundPurchasingPenthouses(stepperModal)\n    }, {\n      key: 'resale_inside_compound_sell_penthouses',\n      value: this.createPrimaryInsideCompoundPurchasingPenthouses(stepperModal)\n    }, {\n      key: 'primary_inside_compound_sell_administrative_units',\n      value: this.createPrimaryInsideCompoundPurchasingAdministrativeUnits(stepperModal)\n    }, {\n      key: 'resale_inside_compound_sell_administrative_units',\n      value: this.createPrimaryInsideCompoundPurchasingAdministrativeUnits(stepperModal)\n    }, {\n      key: 'primary_inside_compound_sell_commercial_administrative_buildings',\n      value: this.createPrimaryInsideCompoundPurchasingCommercialAdministrativeBuildings(stepperModal)\n    }, {\n      key: 'resale_inside_compound_sell_commercial_administrative_buildings',\n      value: this.createPrimaryInsideCompoundPurchasingCommercialAdministrativeBuildings(stepperModal)\n    }];\n  }\n  static ɵfac = /*@__PURE__*/(() => {\n    let ɵSellConfigService_BaseFactory;\n    return function SellConfigService_Factory(__ngFactoryType__) {\n      return (ɵSellConfigService_BaseFactory || (ɵSellConfigService_BaseFactory = i0.ɵɵgetInheritedFactory(SellConfigService)))(__ngFactoryType__ || SellConfigService);\n    };\n  })();\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: SellConfigService,\n    factory: SellConfigService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["Validators", "BaseConfigService", "FLOOR_TYPES_OPTIONS", "UNIT_VIEW_TYPES_OPTIONS", "FINISHING_STATUS_TYPES_OPTIONS", "DELIVERY_STATUS_TYPES_OPTIONS", "OTHER_ACCESSORIES_OPTIONS", "FINANCIAL_STATUS_TYPES_OPTIONS", "SellConfigService", "createSellLocationInputs", "stepperModal", "step", "name", "type", "label", "validators", "required", "visibility", "getInsideCompoundPrivilege", "getSellInsideCompoundInputs", "pattern", "isClient", "options", "createSellApartmentUnitInformationInputs", "includeRooms", "inputs", "isDynamic", "min", "push", "createSellDuplexUnitInformationInputs", "createSellVillasUnitInformationInputs", "createSellTwinHousesUnitInformationInputs", "createSellPenthousesUnitInformationInputs", "createSellPAdministrativeUnitsUnitInformationInputs", "createSellCommercialAdministrativeBuildingsInformationInputs", "createSellDocumentInputs", "createSellFinancialInputs", "createPrimaryInsideCompoundSellApartmentsConfig", "createInsideCompoundSellDuplexesConfig", "createInsideCompoundSellVillasConfig", "createPrimaryInsideCompoundPurchasingTwinHousesConfig", "createPrimaryInsideCompoundPurchasingPenthouses", "createPrimaryInsideCompoundPurchasingAdministrativeUnits", "createPrimaryInsideCompoundPurchasingCommercialAdministrativeBuildings", "getInputConfigs", "key", "value", "includeDocuments", "__ngFactoryType__", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\shared\\stepper-modal\\services\\inside-compound\\sell-config.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Validators } from '@angular/forms';\r\nimport { BaseConfigService, InputConfig, StepperConfiguration } from '../base-config.service';\r\nimport {\r\n  FLOOR_TYPES_OPTIONS,\r\n  UNIT_VIEW_TYPES_OPTIONS,\r\n  FINISHING_STATUS_TYPES_OPTIONS,\r\n  DELIVERY_STATUS_TYPES_OPTIONS,\r\n  OTHER_ACCESSORIES_OPTIONS,\r\n  FINANCIAL_STATUS_TYPES_OPTIONS,\r\n} from '../../stepper-modal.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class SellConfigService extends BaseConfigService {\r\n\r\n  /**\r\n   * Create sell-specific location inputs with additional fields for selling\r\n   */\r\n  private createSellLocationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 2,\r\n        name: 'compoundName',\r\n        type: 'text',\r\n        label: 'Compound Name',\r\n        validators: [Validators.required],\r\n        visibility: () => stepperModal.getInsideCompoundPrivilege(),\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'detailedAddress',\r\n        type: 'text',\r\n        label: 'Detailed Address',\r\n        validators: [Validators.required],\r\n        visibility: () => stepperModal.getSellInsideCompoundInputs(),\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'addressLink',\r\n        type: 'url',\r\n        label: 'Detailed Address Link',\r\n        validators: [\r\n          Validators.pattern(\r\n            /^(https?:\\/\\/)?([\\da-z.-]+)\\.([a-z.]{2,6})([\\/\\w .-]*)*\\/?$/\r\n          )\r\n        ],\r\n        visibility: () => stepperModal.getSellInsideCompoundInputs(),\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'projectManagement',\r\n        type: 'text',\r\n        label: 'Project Management',\r\n        validators: [Validators.required],\r\n        visibility: () => stepperModal.getSellInsideCompoundInputs(),\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'projectConstructor',\r\n        type: 'text',\r\n        label: 'Project Constructor',\r\n        validators: [Validators.required],\r\n        visibility: () => stepperModal.getSellInsideCompoundInputs(),\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'locationSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Location Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'cityId',\r\n        type: 'select',\r\n        label: 'City',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'areaId',\r\n        type: 'select',\r\n        label: 'Area',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'subAreaId',\r\n        type: 'select',\r\n        label: 'Sub Area',\r\n        options: [],\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell-specific unit information for apartments and similar units\r\n   */\r\n  private createSellApartmentUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {\r\n    const inputs: InputConfig[] = [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n        isDynamic: true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n        isDynamic: true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n\r\n    if (includeRooms) {\r\n      inputs.push(\r\n        {\r\n          step: 3,\r\n          name: 'rooms',\r\n          type: 'number',\r\n          label: 'Number of Rooms',\r\n          validators: [Validators.required, Validators.min(0)],\r\n          visibility: () => true,\r\n        },\r\n        {\r\n          step: 3,\r\n          name: 'bathRooms',\r\n          type: 'number',\r\n          label: 'Number of Bathrooms',\r\n          validators: [Validators.required, Validators.min(0)],\r\n          visibility: () => true,\r\n        }\r\n      );\r\n    }\r\n\r\n    inputs.push(\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'financialStatus',\r\n        type: 'select',\r\n        label: 'Financial Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'clubStatus',\r\n        type: 'select',\r\n        label: 'Club Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'parkingStatus',\r\n        type: 'select',\r\n        label: 'Parking Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      }\r\n    );\r\n\r\n    return inputs;\r\n  }\r\n\r\n  /**\r\n   * Create sell-specific unit information for duplexes and similar units\r\n   */\r\n  private createSellDuplexUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {\r\n    const inputs: InputConfig[] = [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n        isDynamic: true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n        isDynamic: true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n\r\n    if (includeRooms) {\r\n      inputs.push(\r\n        {\r\n          step: 3,\r\n          name: 'rooms',\r\n          type: 'number',\r\n          label: 'Number of Rooms',\r\n          validators: [Validators.required, Validators.min(0)],\r\n          visibility: () => true,\r\n        },\r\n        {\r\n          step: 3,\r\n          name: 'bathRooms',\r\n          type: 'number',\r\n          label: 'Number of Bathrooms',\r\n          validators: [Validators.required, Validators.min(0)],\r\n          visibility: () => true,\r\n        }\r\n      );\r\n    }\r\n\r\n    inputs.push(\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'financialStatus',\r\n        type: 'select',\r\n        label: 'Financial Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'clubStatus',\r\n        type: 'select',\r\n        label: 'Club Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'parkingStatus',\r\n        type: 'select',\r\n        label: 'Parking Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      }\r\n    );\r\n\r\n    return inputs;\r\n  }\r\n\r\n  private createSellVillasUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {\r\n    const inputs: InputConfig[] = [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n        isDynamic: true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n\r\n    if (includeRooms) {\r\n      inputs.push(\r\n        {\r\n          step: 3,\r\n          name: 'rooms',\r\n          type: 'number',\r\n          label: 'Number of Rooms',\r\n          validators: [Validators.required, Validators.min(0)],\r\n          visibility: () => true,\r\n        },\r\n        {\r\n          step: 3,\r\n          name: 'bathRooms',\r\n          type: 'number',\r\n          label: 'Number of Bathrooms',\r\n          validators: [Validators.required, Validators.min(0)],\r\n          visibility: () => true,\r\n        }\r\n      );\r\n    }\r\n\r\n    inputs.push(\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'financialStatus',\r\n        type: 'select',\r\n        label: 'Financial Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'clubStatus',\r\n        type: 'select',\r\n        label: 'Club Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'parkingStatus',\r\n        type: 'select',\r\n        label: 'Parking Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      }\r\n    );\r\n\r\n    return inputs;\r\n  }\r\n\r\n  private createSellTwinHousesUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {\r\n    const inputs: InputConfig[] = [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n        isDynamic: true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n\r\n    if (includeRooms) {\r\n      inputs.push(\r\n        {\r\n          step: 3,\r\n          name: 'rooms',\r\n          type: 'number',\r\n          label: 'Number of Rooms',\r\n          validators: [Validators.required, Validators.min(0)],\r\n          visibility: () => true,\r\n        },\r\n        {\r\n          step: 3,\r\n          name: 'bathRooms',\r\n          type: 'number',\r\n          label: 'Number of Bathrooms',\r\n          validators: [Validators.required, Validators.min(0)],\r\n          visibility: () => true,\r\n        }\r\n      );\r\n    }\r\n\r\n    inputs.push(\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'financialStatus',\r\n        type: 'select',\r\n        label: 'Financial Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'clubStatus',\r\n        type: 'select',\r\n        label: 'Club Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'parkingStatus',\r\n        type: 'select',\r\n        label: 'Parking Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      }\r\n    );\r\n\r\n    return inputs;\r\n  }\r\n\r\n  private createSellPenthousesUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {\r\n    const inputs: InputConfig[] = [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n        isDynamic: true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n\r\n    if (includeRooms) {\r\n      inputs.push(\r\n        {\r\n          step: 3,\r\n          name: 'rooms',\r\n          type: 'number',\r\n          label: 'Number of Rooms',\r\n          validators: [Validators.required, Validators.min(0)],\r\n          visibility: () => true,\r\n        },\r\n        {\r\n          step: 3,\r\n          name: 'bathRooms',\r\n          type: 'number',\r\n          label: 'Number of Bathrooms',\r\n          validators: [Validators.required, Validators.min(0)],\r\n          visibility: () => true,\r\n        }\r\n      );\r\n    }\r\n\r\n    inputs.push(\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'financialStatus',\r\n        type: 'select',\r\n        label: 'Financial Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'clubStatus',\r\n        type: 'select',\r\n        label: 'Club Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'parkingStatus',\r\n        type: 'select',\r\n        label: 'Parking Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      }\r\n    );\r\n\r\n    return inputs;\r\n  }\r\n\r\n  private createSellPAdministrativeUnitsUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {\r\n    const inputs: InputConfig[] = [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n        isDynamic: true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n\r\n    if (includeRooms) {\r\n      inputs.push(\r\n        {\r\n          step: 3,\r\n          name: 'rooms',\r\n          type: 'number',\r\n          label: 'Number of Rooms',\r\n          validators: [Validators.required, Validators.min(0)],\r\n          visibility: () => true,\r\n        },\r\n        {\r\n          step: 3,\r\n          name: 'bathRooms',\r\n          type: 'number',\r\n          label: 'Number of Bathrooms',\r\n          validators: [Validators.required, Validators.min(0)],\r\n          visibility: () => true,\r\n        }\r\n      );\r\n    }\r\n\r\n    inputs.push(\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'financialStatus',\r\n        type: 'select',\r\n        label: 'Financial Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'clubStatus',\r\n        type: 'select',\r\n        label: 'Club Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'parkingStatus',\r\n        type: 'select',\r\n        label: 'Parking Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      }\r\n    );\r\n\r\n    return inputs;\r\n  }\r\n\r\n  private createSellCommercialAdministrativeBuildingsInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {\r\n    const inputs: InputConfig[] = [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n        isDynamic: true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n\r\n    if (includeRooms) {\r\n      inputs.push(\r\n        {\r\n          step: 3,\r\n          name: 'rooms',\r\n          type: 'number',\r\n          label: 'Number of Rooms',\r\n          validators: [Validators.required, Validators.min(0)],\r\n          visibility: () => true,\r\n        },\r\n        {\r\n          step: 3,\r\n          name: 'bathRooms',\r\n          type: 'number',\r\n          label: 'Number of Bathrooms',\r\n          validators: [Validators.required, Validators.min(0)],\r\n          visibility: () => true,\r\n        }\r\n      );\r\n    }\r\n\r\n    inputs.push(\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'deliveryStatus',\r\n        type: 'select',\r\n        label: 'Delivery Status',\r\n        options: DELIVERY_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'financialStatus',\r\n        type: 'select',\r\n        label: 'Financial Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'clubStatus',\r\n        type: 'select',\r\n        label: 'Club Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'parkingStatus',\r\n        type: 'select',\r\n        label: 'Parking Status',\r\n        options: FINANCIAL_STATUS_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Other Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Notes',\r\n        validators: [],\r\n        visibility: () => true,\r\n      }\r\n    );\r\n\r\n    return inputs;\r\n  }\r\n  /**\r\n   * Create sell-specific document inputs\r\n   */\r\n  private createSellDocumentInputs(): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 4,\r\n        name: 'mainImage',\r\n        type: 'file',\r\n        label: 'Unit Main Image',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 4,\r\n        name: 'unitInMasterPlanImage',\r\n        type: 'file',\r\n        label: 'Unit in Master Plan Image',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 4,\r\n        name: 'galleryImages',\r\n        type: 'file',\r\n        label: 'Unit Gallery Images',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 4,\r\n        name: 'video',\r\n        type: 'file',\r\n        label: 'Videos',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create sell-specific financial inputs\r\n   */\r\n  private createSellFinancialInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 5,\r\n        name: 'askingPrice',\r\n        type: 'number',\r\n        label: 'Asking Price',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'unitPriceSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Price Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'requestedOver',\r\n        type: 'number',\r\n        label: 'Request Over',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Configuration builder for primary inside compound sell apartments\r\n   */\r\n  private createPrimaryInsideCompoundSellApartmentsConfig(stepperModal: any): InputConfig[] {\r\n    return [\r\n      ...this.createSellLocationInputs(stepperModal),\r\n      ...this.createSellApartmentUnitInformationInputs(stepperModal, true),\r\n      ...this.createSellDocumentInputs(),\r\n      ...this.createSellFinancialInputs(stepperModal),\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Configuration builder for primary and resale inside compound sell duplexes\r\n   */\r\n  private createInsideCompoundSellDuplexesConfig(stepperModal: any,\r\n    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}): InputConfig[] {\r\n    return [\r\n      ...this.createSellLocationInputs(stepperModal),\r\n      ...this.createSellDuplexUnitInformationInputs(stepperModal, true),\r\n      ...this.createSellDocumentInputs(),\r\n      ...this.createSellFinancialInputs(stepperModal),\r\n    ];\r\n  }\r\n\r\n  private createInsideCompoundSellVillasConfig(stepperModal: any,\r\n    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}): InputConfig[] {\r\n    return [\r\n      ...this.createSellLocationInputs(stepperModal),\r\n      ...this.createSellVillasUnitInformationInputs(stepperModal, true),\r\n      ...this.createSellDocumentInputs(),\r\n      ...this.createSellFinancialInputs(stepperModal),\r\n    ];\r\n  }\r\n\r\n  private createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal: any,\r\n    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}): InputConfig[] {\r\n    return [\r\n      ...this.createSellLocationInputs(stepperModal),\r\n      ...this.createSellTwinHousesUnitInformationInputs(stepperModal, true),\r\n      ...this.createSellDocumentInputs(),\r\n      ...this.createSellFinancialInputs(stepperModal),\r\n    ];\r\n  }\r\n\r\n  private createPrimaryInsideCompoundPurchasingPenthouses(stepperModal: any,\r\n    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}): InputConfig[] {\r\n    return [\r\n      ...this.createSellLocationInputs(stepperModal),\r\n      ...this.createSellPenthousesUnitInformationInputs(stepperModal, true),\r\n      ...this.createSellDocumentInputs(),\r\n      ...this.createSellFinancialInputs(stepperModal),\r\n    ];\r\n  }\r\n\r\n  private createPrimaryInsideCompoundPurchasingAdministrativeUnits(stepperModal: any,\r\n    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}): InputConfig[] {\r\n    return [\r\n      ...this.createSellLocationInputs(stepperModal),\r\n      ...this.createSellPAdministrativeUnitsUnitInformationInputs(stepperModal, true),\r\n      ...this.createSellDocumentInputs(),\r\n      ...this.createSellFinancialInputs(stepperModal),\r\n    ];\r\n  }\r\n\r\n  private createPrimaryInsideCompoundPurchasingCommercialAdministrativeBuildings(stepperModal: any,\r\n    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}): InputConfig[] {\r\n    return [\r\n      ...this.createSellLocationInputs(stepperModal),\r\n      ...this.createSellCommercialAdministrativeBuildingsInformationInputs(stepperModal, true),\r\n      ...this.createSellDocumentInputs(),\r\n      ...this.createSellFinancialInputs(stepperModal),\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get input configurations for sell cases\r\n   */\r\n  getInputConfigs(stepperModal: any): StepperConfiguration[] {\r\n    return [\r\n      {\r\n        key: 'primary_inside_compound_sell_apartments',\r\n        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'resale_inside_compound_sell_apartments',\r\n        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),\r\n      },\r\n\r\n      {\r\n        key: 'primary_inside_compound_sell_duplexes',\r\n        value: this.createInsideCompoundSellDuplexesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'resale_inside_compound_sell_duplexes',\r\n        value: this.createInsideCompoundSellDuplexesConfig(stepperModal),\r\n      },\r\n\r\n      {\r\n        key: 'primary_inside_compound_sell_studios',\r\n        value: this.createInsideCompoundSellDuplexesConfig(stepperModal, {\r\n          includeRooms: false,\r\n          includeDocuments: true\r\n        }),\r\n      },\r\n      {\r\n        key: 'resale_inside_compound_sell_studios',\r\n        value: this.createInsideCompoundSellDuplexesConfig(stepperModal, {\r\n          includeRooms: false,\r\n          includeDocuments: true\r\n        }),\r\n      },\r\n      {\r\n        key: 'primary_inside_compound_sell_villas',\r\n        value: this.createInsideCompoundSellVillasConfig(stepperModal, {\r\n          includeRooms: false,\r\n          includeDocuments: true\r\n        }),\r\n      },\r\n      {\r\n        key: 'resale_inside_compound_sell_villas',\r\n        value: this.createInsideCompoundSellVillasConfig(stepperModal, {\r\n          includeRooms: false,\r\n          includeDocuments: true\r\n        }),\r\n      },\r\n      {\r\n        key: 'primary_inside_compound_sell_administrative_units',\r\n        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'resale_inside_compound_sell_administrative_units',\r\n        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),\r\n      },\r\n\r\n      {\r\n        key: 'primary_inside_compound_sell_medical_clinics',\r\n        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'resale_inside_compound_sell_medical_clinics',\r\n        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),\r\n      },\r\n\r\n      {\r\n        key: 'primary_inside_compound_sell_pharmacies',\r\n        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'resale_inside_compound_sell_pharmacies',\r\n        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),\r\n      },\r\n\r\n      {\r\n        key: 'primary_inside_compound_sell_shops',\r\n        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'resale_inside_compound_sell_shops',\r\n        value: this.createPrimaryInsideCompoundSellApartmentsConfig(stepperModal),\r\n      },\r\n\r\n      {\r\n        key: 'primary_inside_compound_sell_twin_houses',\r\n        value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'resale_inside_compound_sell_twin_houses',\r\n        value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal),\r\n      },\r\n\r\n      {\r\n        key: 'primary_inside_compound_sell_town_houses',\r\n        value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'resale_inside_compound_sell_town_houses',\r\n        value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal),\r\n      },\r\n\r\n      {\r\n        key: 'primary_inside_compound_sell_standalone_villas',\r\n        value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'resale_inside_compound_sell_standalone_villas',\r\n        value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal),\r\n      },\r\n\r\n      {\r\n        key: 'primary_inside_compound_sell_penthouses',\r\n        value: this.createPrimaryInsideCompoundPurchasingPenthouses(stepperModal),\r\n      },\r\n      {\r\n        key: 'resale_inside_compound_sell_penthouses',\r\n        value: this.createPrimaryInsideCompoundPurchasingPenthouses(stepperModal),\r\n      },\r\n\r\n      {\r\n        key: 'primary_inside_compound_sell_administrative_units',\r\n        value: this.createPrimaryInsideCompoundPurchasingAdministrativeUnits(stepperModal),\r\n      },\r\n      {\r\n        key: 'resale_inside_compound_sell_administrative_units',\r\n        value: this.createPrimaryInsideCompoundPurchasingAdministrativeUnits(stepperModal),\r\n      },\r\n\r\n       {\r\n        key: 'primary_inside_compound_sell_commercial_administrative_buildings',\r\n        value: this.createPrimaryInsideCompoundPurchasingCommercialAdministrativeBuildings(stepperModal),\r\n      },\r\n      {\r\n        key: 'resale_inside_compound_sell_commercial_administrative_buildings',\r\n        value: this.createPrimaryInsideCompoundPurchasingCommercialAdministrativeBuildings(stepperModal),\r\n      },\r\n\r\n\r\n    ];\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,iBAAiB,QAA2C,wBAAwB;AAC7F,SACEC,mBAAmB,EACnBC,uBAAuB,EACvBC,8BAA8B,EAC9BC,6BAA6B,EAC7BC,yBAAyB,EACzBC,8BAA8B,QACzB,+BAA+B;;AAKtC,OAAM,MAAOC,iBAAkB,SAAQP,iBAAiB;EAEtD;;;EAGQQ,wBAAwBA,CAACC,YAAiB;IAChD,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,eAAe;MACtBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACQ,0BAA0B;KAC1D,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,2BAA2B;KAC3D,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,uBAAuB;MAC9BC,UAAU,EAAE,CACVf,UAAU,CAACoB,OAAO,CAChB,6DAA6D,CAC9D,CACF;MACDH,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,2BAA2B;KAC3D,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,mBAAmB;MACzBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,2BAA2B;KAC3D,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,2BAA2B;KAC3D,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACW,QAAQ;KACxC,EACD;MACEV,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbQ,OAAO,EAAE,EAAE;MACXP,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbQ,OAAO,EAAE,EAAE;MACXP,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBQ,OAAO,EAAE,EAAE;MACXP,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQM,wCAAwCA,CAACb,YAAiB,EAAEc,YAAA,GAAwB,IAAI;IAC9F,MAAMC,MAAM,GAAkB,CAC5B;MACEd,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM,IAAI;MACtBS,SAAS,EAAE;KACZ,EACD;MACEf,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM,IAAI;MACtBS,SAAS,EAAE;KACZ,EACD;MACEf,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDV,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdQ,OAAO,EAAEpB,mBAAmB;MAC5Ba,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,IAAIO,YAAY,EAAE;MAChBC,MAAM,CAACG,IAAI,CACT;QACEjB,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,iBAAiB;QACxBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;QACpDV,UAAU,EAAEA,CAAA,KAAM;OACnB,EACD;QACEN,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,qBAAqB;QAC5BC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;QACpDV,UAAU,EAAEA,CAAA,KAAM;OACnB,CACF;IACH;IAEAQ,MAAM,CAACG,IAAI,CACT;MACEjB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbQ,OAAO,EAAEnB,uBAAuB;MAChCY,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBQ,OAAO,EAAElB,8BAA8B;MACvCW,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBQ,OAAO,EAAEjB,6BAA6B;MACtCU,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BQ,OAAO,EAAEhB,yBAAyB;MAClCS,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,OAAOQ,MAAM;EACf;EAEA;;;EAGQI,qCAAqCA,CAACnB,YAAiB,EAAEc,YAAA,GAAwB,IAAI;IAC3F,MAAMC,MAAM,GAAkB,CAC5B;MACEd,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM,IAAI;MACtBS,SAAS,EAAE;KACZ,EACD;MACEf,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM,IAAI;MACtBS,SAAS,EAAE;KACZ,EACD;MACEf,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDV,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdQ,OAAO,EAAEpB,mBAAmB;MAC5Ba,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,IAAIO,YAAY,EAAE;MAChBC,MAAM,CAACG,IAAI,CACT;QACEjB,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,iBAAiB;QACxBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;QACpDV,UAAU,EAAEA,CAAA,KAAM;OACnB,EACD;QACEN,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,qBAAqB;QAC5BC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;QACpDV,UAAU,EAAEA,CAAA,KAAM;OACnB,CACF;IACH;IAEAQ,MAAM,CAACG,IAAI,CACT;MACEjB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbQ,OAAO,EAAEnB,uBAAuB;MAChCY,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBQ,OAAO,EAAElB,8BAA8B;MACvCW,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBQ,OAAO,EAAEjB,6BAA6B;MACtCU,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BQ,OAAO,EAAEhB,yBAAyB;MAClCS,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,OAAOQ,MAAM;EACf;EAEQK,qCAAqCA,CAACpB,YAAiB,EAAEc,YAAA,GAAwB,IAAI;IAC3F,MAAMC,MAAM,GAAkB,CAC5B;MACEd,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM,IAAI;MACtBS,SAAS,EAAE;KACZ,EACD;MACEf,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDV,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDV,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,IAAIO,YAAY,EAAE;MAChBC,MAAM,CAACG,IAAI,CACT;QACEjB,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,iBAAiB;QACxBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;QACpDV,UAAU,EAAEA,CAAA,KAAM;OACnB,EACD;QACEN,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,qBAAqB;QAC5BC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;QACpDV,UAAU,EAAEA,CAAA,KAAM;OACnB,CACF;IACH;IAEAQ,MAAM,CAACG,IAAI,CACT;MACEjB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbQ,OAAO,EAAEnB,uBAAuB;MAChCY,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBQ,OAAO,EAAElB,8BAA8B;MACvCW,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBQ,OAAO,EAAEjB,6BAA6B;MACtCU,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BQ,OAAO,EAAEhB,yBAAyB;MAClCS,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,OAAOQ,MAAM;EACf;EAEQM,yCAAyCA,CAACrB,YAAiB,EAAEc,YAAA,GAAwB,IAAI;IAC/F,MAAMC,MAAM,GAAkB,CAC5B;MACEd,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM,IAAI;MACtBS,SAAS,EAAE;KACZ,EACD;MACEf,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDV,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDV,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDV,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,IAAIO,YAAY,EAAE;MAChBC,MAAM,CAACG,IAAI,CACT;QACEjB,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,iBAAiB;QACxBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;QACpDV,UAAU,EAAEA,CAAA,KAAM;OACnB,EACD;QACEN,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,qBAAqB;QAC5BC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;QACpDV,UAAU,EAAEA,CAAA,KAAM;OACnB,CACF;IACH;IAEAQ,MAAM,CAACG,IAAI,CACT;MACEjB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbQ,OAAO,EAAEnB,uBAAuB;MAChCY,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBQ,OAAO,EAAElB,8BAA8B;MACvCW,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBQ,OAAO,EAAEjB,6BAA6B;MACtCU,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BQ,OAAO,EAAEhB,yBAAyB;MAClCS,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,OAAOQ,MAAM;EACf;EAEQO,yCAAyCA,CAACtB,YAAiB,EAAEc,YAAA,GAAwB,IAAI;IAC/F,MAAMC,MAAM,GAAkB,CAC5B;MACEd,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM,IAAI;MACtBS,SAAS,EAAE;KACZ,EACD;MACEf,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDV,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,IAAIO,YAAY,EAAE;MAChBC,MAAM,CAACG,IAAI,CACT;QACEjB,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,iBAAiB;QACxBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;QACpDV,UAAU,EAAEA,CAAA,KAAM;OACnB,EACD;QACEN,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,qBAAqB;QAC5BC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;QACpDV,UAAU,EAAEA,CAAA,KAAM;OACnB,CACF;IACH;IAEAQ,MAAM,CAACG,IAAI,CACT;MACEjB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbQ,OAAO,EAAEnB,uBAAuB;MAChCY,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBQ,OAAO,EAAElB,8BAA8B;MACvCW,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBQ,OAAO,EAAEjB,6BAA6B;MACtCU,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BQ,OAAO,EAAEhB,yBAAyB;MAClCS,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,OAAOQ,MAAM;EACf;EAEQQ,mDAAmDA,CAACvB,YAAiB,EAAEc,YAAA,GAAwB,IAAI;IACzG,MAAMC,MAAM,GAAkB,CAC5B;MACEd,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM,IAAI;MACtBS,SAAS,EAAE;KACZ,EACD;MACEf,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDV,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,OAAO;MACdQ,OAAO,EAAEpB,mBAAmB;MAC5Ba,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,IAAIO,YAAY,EAAE;MAChBC,MAAM,CAACG,IAAI,CACT;QACEjB,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,iBAAiB;QACxBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;QACpDV,UAAU,EAAEA,CAAA,KAAM;OACnB,EACD;QACEN,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,qBAAqB;QAC5BC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;QACpDV,UAAU,EAAEA,CAAA,KAAM;OACnB,CACF;IACH;IAEAQ,MAAM,CAACG,IAAI,CACT;MACEjB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbQ,OAAO,EAAEnB,uBAAuB;MAChCY,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBQ,OAAO,EAAElB,8BAA8B;MACvCW,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBQ,OAAO,EAAEjB,6BAA6B;MACtCU,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BQ,OAAO,EAAEhB,yBAAyB;MAClCS,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,OAAOQ,MAAM;EACf;EAEQS,4DAA4DA,CAACxB,YAAiB,EAAEc,YAAA,GAAwB,IAAI;IAClH,MAAMC,MAAM,GAAkB,CAC5B;MACEd,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM,IAAI;MACtBS,SAAS,EAAE;KACZ,EACD;MACEf,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACf,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BV,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDV,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDV,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,IAAIO,YAAY,EAAE;MAChBC,MAAM,CAACG,IAAI,CACT;QACEjB,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,iBAAiB;QACxBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;QACpDV,UAAU,EAAEA,CAAA,KAAM;OACnB,EACD;QACEN,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,QAAQ;QACdC,KAAK,EAAE,qBAAqB;QAC5BC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;QACpDV,UAAU,EAAEA,CAAA,KAAM;OACnB,CACF;IACH;IAEAQ,MAAM,CAACG,IAAI,CACT;MACEjB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbQ,OAAO,EAAEnB,uBAAuB;MAChCY,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBQ,OAAO,EAAElB,8BAA8B;MACvCW,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBQ,OAAO,EAAEjB,6BAA6B;MACtCU,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,aAAa;MACpBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBQ,OAAO,EAAEf,8BAA8B;MACvCQ,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,mBAAmB;MAC1BQ,OAAO,EAAEhB,yBAAyB;MAClCS,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,OAAOQ,MAAM;EACf;EACA;;;EAGQU,wBAAwBA,CAAA;IAC9B,OAAO,CACL;MACExB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,uBAAuB;MAC7BC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,2BAA2B;MAClCC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,QAAQ;MACfC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQmB,yBAAyBA,CAAC1B,YAAiB;IACjD,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,cAAc;MACrBC,UAAU,EAAE,CAACf,UAAU,CAACgB,QAAQ,EAAEhB,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDV,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,mBAAmB;MAC1BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACW,QAAQ;KACxC,EACD;MACEV,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,cAAc;MACrBC,UAAU,EAAE,CAACf,UAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BV,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQoB,+CAA+CA,CAAC3B,YAAiB;IACvE,OAAO,CACL,GAAG,IAAI,CAACD,wBAAwB,CAACC,YAAY,CAAC,EAC9C,GAAG,IAAI,CAACa,wCAAwC,CAACb,YAAY,EAAE,IAAI,CAAC,EACpE,GAAG,IAAI,CAACyB,wBAAwB,EAAE,EAClC,GAAG,IAAI,CAACC,yBAAyB,CAAC1B,YAAY,CAAC,CAChD;EACH;EAEA;;;EAGQ4B,sCAAsCA,CAAC5B,YAAiB,EAC9DY,OAAA,GAAkE,EAAE;IACpE,OAAO,CACL,GAAG,IAAI,CAACb,wBAAwB,CAACC,YAAY,CAAC,EAC9C,GAAG,IAAI,CAACmB,qCAAqC,CAACnB,YAAY,EAAE,IAAI,CAAC,EACjE,GAAG,IAAI,CAACyB,wBAAwB,EAAE,EAClC,GAAG,IAAI,CAACC,yBAAyB,CAAC1B,YAAY,CAAC,CAChD;EACH;EAEQ6B,oCAAoCA,CAAC7B,YAAiB,EAC5DY,OAAA,GAAkE,EAAE;IACpE,OAAO,CACL,GAAG,IAAI,CAACb,wBAAwB,CAACC,YAAY,CAAC,EAC9C,GAAG,IAAI,CAACoB,qCAAqC,CAACpB,YAAY,EAAE,IAAI,CAAC,EACjE,GAAG,IAAI,CAACyB,wBAAwB,EAAE,EAClC,GAAG,IAAI,CAACC,yBAAyB,CAAC1B,YAAY,CAAC,CAChD;EACH;EAEQ8B,qDAAqDA,CAAC9B,YAAiB,EAC7EY,OAAA,GAAkE,EAAE;IACpE,OAAO,CACL,GAAG,IAAI,CAACb,wBAAwB,CAACC,YAAY,CAAC,EAC9C,GAAG,IAAI,CAACqB,yCAAyC,CAACrB,YAAY,EAAE,IAAI,CAAC,EACrE,GAAG,IAAI,CAACyB,wBAAwB,EAAE,EAClC,GAAG,IAAI,CAACC,yBAAyB,CAAC1B,YAAY,CAAC,CAChD;EACH;EAEQ+B,+CAA+CA,CAAC/B,YAAiB,EACvEY,OAAA,GAAkE,EAAE;IACpE,OAAO,CACL,GAAG,IAAI,CAACb,wBAAwB,CAACC,YAAY,CAAC,EAC9C,GAAG,IAAI,CAACsB,yCAAyC,CAACtB,YAAY,EAAE,IAAI,CAAC,EACrE,GAAG,IAAI,CAACyB,wBAAwB,EAAE,EAClC,GAAG,IAAI,CAACC,yBAAyB,CAAC1B,YAAY,CAAC,CAChD;EACH;EAEQgC,wDAAwDA,CAAChC,YAAiB,EAChFY,OAAA,GAAkE,EAAE;IACpE,OAAO,CACL,GAAG,IAAI,CAACb,wBAAwB,CAACC,YAAY,CAAC,EAC9C,GAAG,IAAI,CAACuB,mDAAmD,CAACvB,YAAY,EAAE,IAAI,CAAC,EAC/E,GAAG,IAAI,CAACyB,wBAAwB,EAAE,EAClC,GAAG,IAAI,CAACC,yBAAyB,CAAC1B,YAAY,CAAC,CAChD;EACH;EAEQiC,sEAAsEA,CAACjC,YAAiB,EAC9FY,OAAA,GAAkE,EAAE;IACpE,OAAO,CACL,GAAG,IAAI,CAACb,wBAAwB,CAACC,YAAY,CAAC,EAC9C,GAAG,IAAI,CAACwB,4DAA4D,CAACxB,YAAY,EAAE,IAAI,CAAC,EACxF,GAAG,IAAI,CAACyB,wBAAwB,EAAE,EAClC,GAAG,IAAI,CAACC,yBAAyB,CAAC1B,YAAY,CAAC,CAChD;EACH;EAEA;;;EAGAkC,eAAeA,CAAClC,YAAiB;IAC/B,OAAO,CACL;MACEmC,GAAG,EAAE,yCAAyC;MAC9CC,KAAK,EAAE,IAAI,CAACT,+CAA+C,CAAC3B,YAAY;KACzE,EACD;MACEmC,GAAG,EAAE,wCAAwC;MAC7CC,KAAK,EAAE,IAAI,CAACT,+CAA+C,CAAC3B,YAAY;KACzE,EAED;MACEmC,GAAG,EAAE,uCAAuC;MAC5CC,KAAK,EAAE,IAAI,CAACR,sCAAsC,CAAC5B,YAAY;KAChE,EACD;MACEmC,GAAG,EAAE,sCAAsC;MAC3CC,KAAK,EAAE,IAAI,CAACR,sCAAsC,CAAC5B,YAAY;KAChE,EAED;MACEmC,GAAG,EAAE,sCAAsC;MAC3CC,KAAK,EAAE,IAAI,CAACR,sCAAsC,CAAC5B,YAAY,EAAE;QAC/Dc,YAAY,EAAE,KAAK;QACnBuB,gBAAgB,EAAE;OACnB;KACF,EACD;MACEF,GAAG,EAAE,qCAAqC;MAC1CC,KAAK,EAAE,IAAI,CAACR,sCAAsC,CAAC5B,YAAY,EAAE;QAC/Dc,YAAY,EAAE,KAAK;QACnBuB,gBAAgB,EAAE;OACnB;KACF,EACD;MACEF,GAAG,EAAE,qCAAqC;MAC1CC,KAAK,EAAE,IAAI,CAACP,oCAAoC,CAAC7B,YAAY,EAAE;QAC7Dc,YAAY,EAAE,KAAK;QACnBuB,gBAAgB,EAAE;OACnB;KACF,EACD;MACEF,GAAG,EAAE,oCAAoC;MACzCC,KAAK,EAAE,IAAI,CAACP,oCAAoC,CAAC7B,YAAY,EAAE;QAC7Dc,YAAY,EAAE,KAAK;QACnBuB,gBAAgB,EAAE;OACnB;KACF,EACD;MACEF,GAAG,EAAE,mDAAmD;MACxDC,KAAK,EAAE,IAAI,CAACT,+CAA+C,CAAC3B,YAAY;KACzE,EACD;MACEmC,GAAG,EAAE,kDAAkD;MACvDC,KAAK,EAAE,IAAI,CAACT,+CAA+C,CAAC3B,YAAY;KACzE,EAED;MACEmC,GAAG,EAAE,8CAA8C;MACnDC,KAAK,EAAE,IAAI,CAACT,+CAA+C,CAAC3B,YAAY;KACzE,EACD;MACEmC,GAAG,EAAE,6CAA6C;MAClDC,KAAK,EAAE,IAAI,CAACT,+CAA+C,CAAC3B,YAAY;KACzE,EAED;MACEmC,GAAG,EAAE,yCAAyC;MAC9CC,KAAK,EAAE,IAAI,CAACT,+CAA+C,CAAC3B,YAAY;KACzE,EACD;MACEmC,GAAG,EAAE,wCAAwC;MAC7CC,KAAK,EAAE,IAAI,CAACT,+CAA+C,CAAC3B,YAAY;KACzE,EAED;MACEmC,GAAG,EAAE,oCAAoC;MACzCC,KAAK,EAAE,IAAI,CAACT,+CAA+C,CAAC3B,YAAY;KACzE,EACD;MACEmC,GAAG,EAAE,mCAAmC;MACxCC,KAAK,EAAE,IAAI,CAACT,+CAA+C,CAAC3B,YAAY;KACzE,EAED;MACEmC,GAAG,EAAE,0CAA0C;MAC/CC,KAAK,EAAE,IAAI,CAACN,qDAAqD,CAAC9B,YAAY;KAC/E,EACD;MACEmC,GAAG,EAAE,yCAAyC;MAC9CC,KAAK,EAAE,IAAI,CAACN,qDAAqD,CAAC9B,YAAY;KAC/E,EAED;MACEmC,GAAG,EAAE,0CAA0C;MAC/CC,KAAK,EAAE,IAAI,CAACN,qDAAqD,CAAC9B,YAAY;KAC/E,EACD;MACEmC,GAAG,EAAE,yCAAyC;MAC9CC,KAAK,EAAE,IAAI,CAACN,qDAAqD,CAAC9B,YAAY;KAC/E,EAED;MACEmC,GAAG,EAAE,gDAAgD;MACrDC,KAAK,EAAE,IAAI,CAACN,qDAAqD,CAAC9B,YAAY;KAC/E,EACD;MACEmC,GAAG,EAAE,+CAA+C;MACpDC,KAAK,EAAE,IAAI,CAACN,qDAAqD,CAAC9B,YAAY;KAC/E,EAED;MACEmC,GAAG,EAAE,yCAAyC;MAC9CC,KAAK,EAAE,IAAI,CAACL,+CAA+C,CAAC/B,YAAY;KACzE,EACD;MACEmC,GAAG,EAAE,wCAAwC;MAC7CC,KAAK,EAAE,IAAI,CAACL,+CAA+C,CAAC/B,YAAY;KACzE,EAED;MACEmC,GAAG,EAAE,mDAAmD;MACxDC,KAAK,EAAE,IAAI,CAACJ,wDAAwD,CAAChC,YAAY;KAClF,EACD;MACEmC,GAAG,EAAE,kDAAkD;MACvDC,KAAK,EAAE,IAAI,CAACJ,wDAAwD,CAAChC,YAAY;KAClF,EAEA;MACCmC,GAAG,EAAE,kEAAkE;MACvEC,KAAK,EAAE,IAAI,CAACH,sEAAsE,CAACjC,YAAY;KAChG,EACD;MACEmC,GAAG,EAAE,iEAAiE;MACtEC,KAAK,EAAE,IAAI,CAACH,sEAAsE,CAACjC,YAAY;KAChG,CAGF;EACH;;;;2GAtyCWF,iBAAiB,IAAAwC,iBAAA,IAAjBxC,iBAAiB;IAAA;EAAA;;WAAjBA,iBAAiB;IAAAyC,OAAA,EAAjBzC,iBAAiB,CAAA0C,IAAA;IAAAC,UAAA,EAFhB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}