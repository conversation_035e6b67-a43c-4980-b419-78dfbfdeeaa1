{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { BaseConfigService } from '../base-config.service';\nimport { UNIT_VIEW_TYPES_OPTIONS, FINISHING_STATUS_TYPES_OPTIONS, OTHER_ACCESSORIES_OPTIONS, FURNISHING_STATUS_OPTIONS, RENT_RECURRENCE_OPTIONS, REQUIRED_INSURANCE_TYPES_OPTIONS, OTHER_EXPENSES_OPTIONS, FLOOR_TYPES_OPTIONS, FIT_OUT_CONDITION_TYPES_OPTIONS, ACTIVITY_TYPES_OPTIONS } from '../../stepper-modal.constants';\nimport * as i0 from \"@angular/core\";\nexport class RentalConfigService extends BaseConfigService {\n  // ============================================================================\n  // RENT-IN CONFIGURATIONS (Tenant looking for property)\n  // ============================================================================\n  /**\n   * Create rental-specific location inputs for rent-in scenarios\n   */\n  createRentInLocationInputs(stepperModal) {\n    return [{\n      step: 2,\n      name: 'compoundName',\n      type: 'text',\n      label: 'Preferred Compound Name',\n      validators: [Validators.required],\n      visibility: () => stepperModal.getInsideCompoundPrivilege()\n    }, {\n      step: 2,\n      name: 'locationSuggestions',\n      type: 'checkbox',\n      label: 'Location Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 2,\n      name: 'cityId',\n      type: 'select',\n      label: 'Preferred City',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'areaId',\n      type: 'select',\n      label: 'Preferred Area',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'subAreaId',\n      type: 'select',\n      label: 'Preferred Sub Area',\n      options: [],\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-in unit information inputs\n   */\n  createRentInUnitInformationInputs(stepperModal, includeRooms = true) {\n    const baseInputs = [{\n      step: 3,\n      name: 'favoriteFloor',\n      type: 'number',\n      label: 'Preferred Floor',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n    const roomInputs = includeRooms ? [{\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required],\n      visibility: () => true\n    }] : [];\n    const commonInputs = [{\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Preferred View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Preferred Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Preferred Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Desired Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Additional Requirements',\n      validators: [],\n      visibility: () => true\n    }];\n    return [...baseInputs, ...roomInputs, ...commonInputs];\n  }\n  /**\n   * Create rent-in penthouses unit information inputs\n   */\n  createRentInPenthousesUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Preferred View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Preferred Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Preferred Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Desired Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Additional Requirements',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-in villas unit information inputs\n   */\n  createRentInVillasUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Preferred View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Preferred Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Preferred Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Desired Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Additional Requirements',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-in house unit information inputs (for twin houses, town houses, standalone villas)\n   */\n  createRentInHouseUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundAreaMin',\n      type: 'number',\n      label: 'Minimum Ground Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundAreaMax',\n      type: 'number',\n      label: 'Maximum Ground Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Preferred View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Preferred Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Preferred Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Desired Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Additional Requirements',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-in commercial unit information inputs (for administrative units, medical clinics)\n   */\n  createRentInCommercialUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'favoriteFloor',\n      type: 'number',\n      label: 'Preferred Floor',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Preferred View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Preferred Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Preferred Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Desired Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Additional Requirements',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-in pharmacies unit information inputs\n   */\n  createRentInPharmaciesUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'favoriteFloor',\n      type: 'number',\n      label: 'Preferred Floor',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Preferred View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Preferred Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Preferred Fit-Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Desired Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Additional Requirements',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-in commercial stores unit information inputs\n   */\n  createRentInCommercialStoresUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'favoriteFloor',\n      type: 'number',\n      label: 'Preferred Floor',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Preferred View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Preferred Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Preferred Fit-Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Preferred Activity Type',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Desired Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Additional Requirements',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-in commercial administrative buildings unit information inputs\n   */\n  createRentInCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitAreaMin',\n      type: 'number',\n      label: 'Minimum Unit Area (m²)',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitAreaMax',\n      type: 'number',\n      label: 'Maximum Unit Area (m²)',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Preferred View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Desired Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Additional Requirements',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-in financial inputs\n   */\n  createRentInFinancialInputs(stepperModal) {\n    return [{\n      step: 5,\n      name: 'averageUnitPriceMin',\n      type: 'number',\n      label: 'Minimum Average Unit Price',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'averageUnitPriceMax',\n      type: 'number',\n      label: 'Maximum Average Unit Price',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'unitPriceSuggestions',\n      type: 'checkbox',\n      label: 'Unit Price Suggestions',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'rentRecurrence',\n      type: 'select',\n      label: 'Rent Recurrence',\n      options: RENT_RECURRENCE_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n  }\n  // ============================================================================\n  // RENT-OUT CONFIGURATIONS\n  // ============================================================================\n  /**\n   * Create rental-specific location inputs for rent-out scenarios\n   */\n  createRentOutLocationInputs(stepperModal) {\n    return [{\n      step: 2,\n      name: 'compoundName',\n      type: 'text',\n      label: 'Compound Name',\n      validators: [Validators.required],\n      visibility: () => stepperModal.getInsideCompoundPrivilege()\n    }, {\n      step: 2,\n      name: 'locationSuggestions',\n      type: 'checkbox',\n      label: 'Location Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    },\n    // Rent-out specific location fields\n    {\n      step: 2,\n      name: 'detailedAddress',\n      type: 'text',\n      label: 'Detailed Address',\n      validators: [Validators.required],\n      visibility: () => stepperModal.getSellInsideCompoundInputs() || stepperModal.getRentOutInsideCompoundInputs()\n    }, {\n      step: 2,\n      name: 'addressLink',\n      type: 'url',\n      label: 'Address Link',\n      validators: [Validators.pattern(/^https?:\\/\\/.+/)],\n      visibility: () => stepperModal.getSellInsideCompoundInputs() || stepperModal.getRentOutInsideCompoundInputs()\n    }, {\n      step: 2,\n      name: 'cityId',\n      type: 'select',\n      label: 'City',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'areaId',\n      type: 'select',\n      label: 'Area',\n      options: [],\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 2,\n      name: 'subAreaId',\n      type: 'select',\n      label: 'Sub Area',\n      options: [],\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out unit information inputs\n   */\n  createRentOutUnitInformationInputs(stepperModal, includeRooms = true, includeUnitNumber = true) {\n    const baseInputs = [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor Number',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }];\n    // Add unit number for rent-out inside compound (except commercial administrative buildings)\n    if (includeUnitNumber) {\n      baseInputs.push({\n        step: 3,\n        name: 'unitNumber',\n        type: 'text',\n        label: 'Unit Number',\n        validators: [Validators.required],\n        visibility: () => true\n      });\n    }\n    const roomInputs = includeRooms ? [{\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }] : [];\n    const commonInputs = [{\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n    return [...baseInputs, ...roomInputs, ...commonInputs];\n  }\n  /**\n   * Create rent-out penthouse unit information inputs\n   */\n  createRentOutPenthouseUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'terraceArea',\n      type: 'number',\n      label: 'Terrace Area (m²)',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out villa unit information inputs\n   */\n  createRentOutVillaUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'terraceArea',\n      type: 'number',\n      label: 'Terrace Area (m²)',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'gardenArea',\n      type: 'number',\n      label: 'Garden Area (m²)',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out house unit information inputs (for twin houses, town houses, standalone villas)\n   */\n  createRentOutHouseUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'groundArea',\n      type: 'number',\n      label: 'Ground Area (m²)',\n      validators: [Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'bathRooms',\n      type: 'number',\n      label: 'Number of Bathrooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'rooms',\n      type: 'number',\n      label: 'Number of Rooms',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out commercial unit information inputs (for administrative units, medical clinics)\n   */\n  createRentOutCommercialUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor Number',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'furnishingStatus',\n      type: 'select',\n      label: 'Furnishing Status',\n      options: FURNISHING_STATUS_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out pharmacy unit information inputs\n   */\n  createRentOutPharmacyUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor Number',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Fit-Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out commercial store unit information inputs\n   */\n  createRentOutCommercialStoreUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'unitNumber',\n      type: 'text',\n      label: 'Unit Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'floor',\n      type: 'select',\n      label: 'Floor Number',\n      options: FLOOR_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Unit Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Unit View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'fitOutCondition',\n      type: 'select',\n      label: 'Fit-Out Condition',\n      options: FIT_OUT_CONDITION_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity Type',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out commercial administrative building unit information inputs\n   */\n  createRentOutCommercialAdministrativeBuildingUnitInformationInputs(stepperModal) {\n    return [{\n      step: 3,\n      name: 'buildingNumber',\n      type: 'text',\n      label: 'Building Number',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'numberOfFloors',\n      type: 'number',\n      label: 'Number of Floors',\n      validators: [Validators.required, Validators.min(1)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitArea',\n      type: 'number',\n      label: 'Building Area (m²)',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'unitView',\n      type: 'select',\n      label: 'Building View',\n      options: UNIT_VIEW_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'activity',\n      type: 'select',\n      label: 'Activity Type',\n      options: ACTIVITY_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'finishingStatus',\n      type: 'select',\n      label: 'Finishing Status',\n      options: FINISHING_STATUS_TYPES_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'otherAccessories',\n      type: 'multiSelect',\n      label: 'Available Accessories',\n      options: OTHER_ACCESSORIES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 3,\n      name: 'notes',\n      type: 'textarea',\n      label: 'Property Description',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create rent-out financial inputs\n   */\n  createRentOutFinancialInputs(stepperModal) {\n    return [{\n      step: 5,\n      name: 'unitPrice',\n      type: 'number',\n      label: 'Unit Price',\n      validators: [Validators.required, Validators.min(0)],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'rentPriceSuggestions',\n      type: 'checkbox',\n      label: 'Rent Price Suggestions',\n      validators: [],\n      visibility: () => stepperModal.isClient()\n    }, {\n      step: 5,\n      name: 'rentRecurrence',\n      type: 'select',\n      label: 'Rent Recurrence',\n      options: RENT_RECURRENCE_OPTIONS,\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'requiredInsurance',\n      type: 'select',\n      label: 'Required Insurance',\n      options: REQUIRED_INSURANCE_TYPES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 5,\n      name: 'otherExpenses',\n      type: 'multiSelect',\n      label: 'Other Expenses (Tenant Responsibility)',\n      options: OTHER_EXPENSES_OPTIONS,\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  /**\n   * Create media inputs for step 4\n   */\n  createMediaInputs() {\n    return [{\n      step: 4,\n      name: 'mainImage',\n      type: 'file',\n      label: 'Main Image',\n      validators: [Validators.required],\n      visibility: () => true\n    }, {\n      step: 4,\n      name: 'galleryImages',\n      type: 'file',\n      label: 'Gallery Images',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 4,\n      name: 'unitInMasterPlanImage',\n      type: 'file',\n      label: 'Unit in Master Plan Image',\n      validators: [],\n      visibility: () => true\n    }, {\n      step: 4,\n      name: 'video',\n      type: 'file',\n      label: 'Video',\n      validators: [],\n      visibility: () => true\n    }];\n  }\n  // ============================================================================\n  // CONFIGURATION BUILDERS\n  // ============================================================================\n  /**\n   * Generic rent-in configuration builder\n   */\n  createRentInConfig(unitType, stepperModal, options = {}) {\n    const {\n      includeRooms = true,\n      includeDocuments = false\n    } = options;\n    const config = [...this.createRentInLocationInputs(stepperModal), ...this.createRentInUnitInformationInputs(stepperModal, includeRooms)];\n    // Add documents if needed\n    if (includeDocuments) {\n      config.push(...this.createDocumentInputs());\n    }\n    // Add financial inputs\n    config.push(...this.createRentInFinancialInputs(stepperModal));\n    return config;\n  }\n  /**\n   * Generic rent-out configuration builder\n   */\n  createRentOutConfig(unitType, stepperModal, options = {}) {\n    const {\n      includeRooms = true,\n      includeMedia = true,\n      includeUnitNumber = true\n    } = options;\n    const config = [...this.createRentOutLocationInputs(stepperModal), ...this.createRentOutUnitInformationInputs(stepperModal, includeRooms, includeUnitNumber)];\n    // Add media inputs if needed\n    if (includeMedia) {\n      config.push(...this.createMediaInputs());\n    }\n    // Add financial inputs\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\n    return config;\n  }\n  // ============================================================================\n  // SPECIFIC CONFIGURATIONS (Ready for manual modification)\n  // ============================================================================\n  // RENT-IN CONFIGURATIONS\n  createRentInApartmentsConfig(stepperModal) {\n    return this.createRentInConfig('apartments', stepperModal, {\n      includeRooms: true,\n      includeDocuments: false\n    });\n  }\n  createRentInDuplexesConfig(stepperModal) {\n    return this.createRentInConfig('duplexes', stepperModal, {\n      includeRooms: true,\n      includeDocuments: false\n    });\n  }\n  createRentInStudiosConfig(stepperModal) {\n    return this.createRentInConfig('studios', stepperModal, {\n      includeRooms: false,\n      includeDocuments: false\n    });\n  }\n  createRentInVillasConfig(stepperModal) {\n    const config = [...this.createRentInLocationInputs(stepperModal), ...this.createRentInVillasUnitInformationInputs(stepperModal)];\n    // Add financial inputs for step 5\n    config.push(...this.createRentInFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentInPenthousesConfig(stepperModal) {\n    const config = [...this.createRentInLocationInputs(stepperModal), ...this.createRentInPenthousesUnitInformationInputs(stepperModal)];\n    // Add financial inputs for step 5\n    config.push(...this.createRentInFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentInTwinHousesConfig(stepperModal) {\n    const config = [...this.createRentInLocationInputs(stepperModal), ...this.createRentInHouseUnitInformationInputs(stepperModal)];\n    // Add financial inputs for step 5\n    config.push(...this.createRentInFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentInTownHousesConfig(stepperModal) {\n    const config = [...this.createRentInLocationInputs(stepperModal), ...this.createRentInHouseUnitInformationInputs(stepperModal)];\n    // Add financial inputs for step 5\n    config.push(...this.createRentInFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentInStandaloneVillasConfig(stepperModal) {\n    const config = [...this.createRentInLocationInputs(stepperModal), ...this.createRentInHouseUnitInformationInputs(stepperModal)];\n    // Add financial inputs for step 5\n    config.push(...this.createRentInFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentInAdministrativeUnitsConfig(stepperModal) {\n    const config = [...this.createRentInLocationInputs(stepperModal), ...this.createRentInCommercialUnitInformationInputs(stepperModal)];\n    // Add financial inputs for step 5\n    config.push(...this.createRentInFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentInMedicalClinicsConfig(stepperModal) {\n    const config = [...this.createRentInLocationInputs(stepperModal), ...this.createRentInCommercialUnitInformationInputs(stepperModal)];\n    // Add financial inputs for step 5\n    config.push(...this.createRentInFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentInPharmaciesConfig(stepperModal) {\n    const config = [...this.createRentInLocationInputs(stepperModal), ...this.createRentInPharmaciesUnitInformationInputs(stepperModal)];\n    // Add financial inputs for step 5\n    config.push(...this.createRentInFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentInCommercialStoresConfig(stepperModal) {\n    const config = [...this.createRentInLocationInputs(stepperModal), ...this.createRentInCommercialStoresUnitInformationInputs(stepperModal)];\n    // Add financial inputs for step 5\n    config.push(...this.createRentInFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentInCommercialAdministrativeBuildingsConfig(stepperModal) {\n    const config = [...this.createRentInLocationInputs(stepperModal), ...this.createRentInCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal)];\n    // Add financial inputs for step 5\n    config.push(...this.createRentInFinancialInputs(stepperModal));\n    return config;\n  }\n  // RENT-OUT CONFIGURATIONS\n  createRentOutApartmentsConfig(stepperModal) {\n    return this.createRentOutConfig('apartments', stepperModal, {\n      includeRooms: true,\n      includeMedia: true,\n      includeUnitNumber: true // Include unit number for apartments\n    });\n  }\n  createRentOutDuplexesConfig(stepperModal) {\n    return this.createRentOutConfig('duplexes', stepperModal, {\n      includeRooms: true,\n      includeMedia: true,\n      includeUnitNumber: true // Include unit number for duplexes\n    });\n  }\n  createRentOutStudiosConfig(stepperModal) {\n    return this.createRentOutConfig('studios', stepperModal, {\n      includeRooms: false,\n      includeMedia: true,\n      includeUnitNumber: true // Include unit number for studios\n    });\n  }\n  createRentOutVillasConfig(stepperModal) {\n    const config = [...this.createRentOutLocationInputs(stepperModal), ...this.createRentOutVillaUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutPenthousesConfig(stepperModal) {\n    const config = [...this.createRentOutLocationInputs(stepperModal), ...this.createRentOutPenthouseUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutTwinHousesConfig(stepperModal) {\n    const config = [...this.createRentOutLocationInputs(stepperModal), ...this.createRentOutHouseUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutTownHousesConfig(stepperModal) {\n    const config = [...this.createRentOutLocationInputs(stepperModal), ...this.createRentOutHouseUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutStandaloneVillasConfig(stepperModal) {\n    const config = [...this.createRentOutLocationInputs(stepperModal), ...this.createRentOutHouseUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutAdministrativeUnitsConfig(stepperModal) {\n    const config = [...this.createRentOutLocationInputs(stepperModal), ...this.createRentOutCommercialUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutMedicalClinicsConfig(stepperModal) {\n    const config = [...this.createRentOutLocationInputs(stepperModal), ...this.createRentOutCommercialUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutPharmaciesConfig(stepperModal) {\n    const config = [...this.createRentOutLocationInputs(stepperModal), ...this.createRentOutPharmacyUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\n    return config;\n  }\n  createRentOutCommercialStoresConfig(stepperModal) {\n    const config = [...this.createRentOutLocationInputs(stepperModal), ...this.createRentOutCommercialStoreUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\n    return config;\n  }\n  // Commercial configurations (for future use) - exclude unit number\n  createRentOutCommercialAdministrativeBuildingsConfig(stepperModal) {\n    const config = [...this.createRentOutLocationInputs(stepperModal), ...this.createRentOutCommercialAdministrativeBuildingUnitInformationInputs(stepperModal)];\n    // Add media inputs for step 4\n    config.push(...this.createMediaInputs());\n    // Add financial inputs for step 5\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\n    return config;\n  }\n  // ============================================================================\n  // PUBLIC API\n  // ============================================================================\n  /**\n   * Get input configurations for rental cases\n   */\n  getInputConfigs(stepperModal) {\n    return [\n    // RENT-IN CONFIGURATIONS\n    {\n      key: 'rentals_inside_compound_rent_in_apartments',\n      value: this.createRentInApartmentsConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_in_duplexes',\n      value: this.createRentInDuplexesConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_in_studios',\n      value: this.createRentInStudiosConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_in_villas',\n      value: this.createRentInVillasConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_in_penthouses',\n      value: this.createRentInPenthousesConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_in_twin_houses',\n      value: this.createRentInTwinHousesConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_in_town_houses',\n      value: this.createRentInTownHousesConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_in_standalone_villas',\n      value: this.createRentInStandaloneVillasConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_in_administrative_units',\n      value: this.createRentInAdministrativeUnitsConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_in_medical_clinics',\n      value: this.createRentInMedicalClinicsConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_in_pharmacies',\n      value: this.createRentInPharmaciesConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_in_commercial_stores',\n      value: this.createRentInCommercialStoresConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_in_commercial_administrative_buildings',\n      value: this.createRentInCommercialAdministrativeBuildingsConfig(stepperModal)\n    },\n    // RENT-OUT CONFIGURATIONS\n    {\n      key: 'rentals_inside_compound_rent_out_apartments',\n      value: this.createRentOutApartmentsConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_out_duplexes',\n      value: this.createRentOutDuplexesConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_out_studios',\n      value: this.createRentOutStudiosConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_out_villas',\n      value: this.createRentOutVillasConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_out_penthouses',\n      value: this.createRentOutPenthousesConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_out_twin_houses',\n      value: this.createRentOutTwinHousesConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_out_town_houses',\n      value: this.createRentOutTownHousesConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_out_standalone_villas',\n      value: this.createRentOutStandaloneVillasConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_out_administrative_units',\n      value: this.createRentOutAdministrativeUnitsConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_out_medical_clinics',\n      value: this.createRentOutMedicalClinicsConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_out_pharmacies',\n      value: this.createRentOutPharmaciesConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_out_commercial_stores',\n      value: this.createRentOutCommercialStoresConfig(stepperModal)\n    }, {\n      key: 'rentals_inside_compound_rent_out_commercial_administrative_buildings',\n      value: this.createRentOutCommercialAdministrativeBuildingsConfig(stepperModal)\n    }];\n  }\n  /**\n   * Get all available rental configuration keys\n   */\n  getRentalConfigurationKeys() {\n    return [\n    // RENT-IN KEYS\n    'rentals_inside_compound_rent_in_apartments', 'rentals_inside_compound_rent_in_duplexes', 'rentals_inside_compound_rent_in_studios', 'rentals_inside_compound_rent_in_villas', 'rentals_inside_compound_rent_in_penthouses', 'rentals_inside_compound_rent_in_twin_houses', 'rentals_inside_compound_rent_in_town_houses', 'rentals_inside_compound_rent_in_standalone_villas', 'rentals_inside_compound_rent_in_administrative_units', 'rentals_inside_compound_rent_in_medical_clinics', 'rentals_inside_compound_rent_in_pharmacies', 'rentals_inside_compound_rent_in_commercial_stores', 'rentals_inside_compound_rent_in_commercial_administrative_buildings',\n    // RENT-OUT KEYS\n    'rentals_inside_compound_rent_out_apartments', 'rentals_inside_compound_rent_out_duplexes', 'rentals_inside_compound_rent_out_studios', 'rentals_inside_compound_rent_out_villas', 'rentals_inside_compound_rent_out_penthouses', 'rentals_inside_compound_rent_out_twin_houses', 'rentals_inside_compound_rent_out_town_houses', 'rentals_inside_compound_rent_out_standalone_villas', 'rentals_inside_compound_rent_out_administrative_units', 'rentals_inside_compound_rent_out_medical_clinics', 'rentals_inside_compound_rent_out_pharmacies', 'rentals_inside_compound_rent_out_commercial_stores', 'rentals_inside_compound_rent_out_commercial_administrative_buildings'];\n  }\n  /**\n   * Check if a key is rent-in configuration\n   */\n  isRentInConfiguration(key) {\n    return key.includes('_rent_in_');\n  }\n  /**\n   * Check if a key is rent-out configuration\n   */\n  isRentOutConfiguration(key) {\n    return key.includes('_rent_out_');\n  }\n  static ɵfac = /*@__PURE__*/(() => {\n    let ɵRentalConfigService_BaseFactory;\n    return function RentalConfigService_Factory(__ngFactoryType__) {\n      return (ɵRentalConfigService_BaseFactory || (ɵRentalConfigService_BaseFactory = i0.ɵɵgetInheritedFactory(RentalConfigService)))(__ngFactoryType__ || RentalConfigService);\n    };\n  })();\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: RentalConfigService,\n    factory: RentalConfigService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["Validators", "BaseConfigService", "UNIT_VIEW_TYPES_OPTIONS", "FINISHING_STATUS_TYPES_OPTIONS", "OTHER_ACCESSORIES_OPTIONS", "FURNISHING_STATUS_OPTIONS", "RENT_RECURRENCE_OPTIONS", "REQUIRED_INSURANCE_TYPES_OPTIONS", "OTHER_EXPENSES_OPTIONS", "FLOOR_TYPES_OPTIONS", "FIT_OUT_CONDITION_TYPES_OPTIONS", "ACTIVITY_TYPES_OPTIONS", "RentalConfigService", "createRentInLocationInputs", "stepperModal", "step", "name", "type", "label", "validators", "required", "visibility", "getInsideCompoundPrivilege", "isClient", "options", "createRentInUnitInformationInputs", "includeRooms", "baseInputs", "min", "roomInputs", "commonInputs", "createRentInPenthousesUnitInformationInputs", "createRentInVillasUnitInformationInputs", "createRentInHouseUnitInformationInputs", "createRentInCommercialUnitInformationInputs", "createRentInPharmaciesUnitInformationInputs", "createRentInCommercialStoresUnitInformationInputs", "createRentInCommercialAdministrativeBuildingsUnitInformationInputs", "createRentInFinancialInputs", "createRentOutLocationInputs", "getSellInsideCompoundInputs", "getRentOutInsideCompoundInputs", "pattern", "createRentOutUnitInformationInputs", "includeUnitNumber", "push", "createRentOutPenthouseUnitInformationInputs", "createRentOutVillaUnitInformationInputs", "createRentOutHouseUnitInformationInputs", "createRentOutCommercialUnitInformationInputs", "createRentOutPharmacyUnitInformationInputs", "createRentOutCommercialStoreUnitInformationInputs", "createRentOutCommercialAdministrativeBuildingUnitInformationInputs", "createRentOutFinancialInputs", "createMediaInputs", "createRentInConfig", "unitType", "includeDocuments", "config", "createDocumentInputs", "createRentOutConfig", "includeMedia", "createRentInApartmentsConfig", "createRentInDuplexesConfig", "createRentInStudiosConfig", "createRentInVillasConfig", "createRentInPenthousesConfig", "createRentInTwinHousesConfig", "createRentInTownHousesConfig", "createRentInStandaloneVillasConfig", "createRentInAdministrativeUnitsConfig", "createRentInMedicalClinicsConfig", "createRentInPharmaciesConfig", "createRentInCommercialStoresConfig", "createRentInCommercialAdministrativeBuildingsConfig", "createRentOutApartmentsConfig", "createRentOutDuplexesConfig", "createRentOutStudiosConfig", "createRentOutVillasConfig", "createRentOutPenthousesConfig", "createRentOutTwinHousesConfig", "createRentOutTownHousesConfig", "createRentOutStandaloneVillasConfig", "createRentOutAdministrativeUnitsConfig", "createRentOutMedicalClinicsConfig", "createRentOutPharmaciesConfig", "createRentOutCommercialStoresConfig", "createRentOutCommercialAdministrativeBuildingsConfig", "getInputConfigs", "key", "value", "getRentalConfigurationKeys", "isRentInConfiguration", "includes", "isRentOutConfiguration", "__ngFactoryType__", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\shared\\stepper-modal\\services\\inside-compound\\rental-config.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Validators } from '@angular/forms';\r\nimport { BaseConfigService, InputConfig, StepperConfiguration } from '../base-config.service';\r\nimport {\r\n  UNIT_VIEW_TYPES_OPTIONS,\r\n  FINISHING_STATUS_TYPES_OPTIONS,\r\n  OTHER_ACCESSORIES_OPTIONS,\r\n  FURNISHING_STATUS_OPTIONS,\r\n  RENT_RECURRENCE_OPTIONS,\r\n  REQUIRED_INSURANCE_TYPES_OPTIONS,\r\n  OTHER_EXPENSES_OPTIONS,\r\n  FLOOR_TYPES_OPTIONS,\r\n  FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n  ACTIVITY_TYPES_OPTIONS,\r\n} from '../../stepper-modal.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class RentalConfigService extends BaseConfigService {\r\n\r\n  // ============================================================================\r\n  // RENT-IN CONFIGURATIONS (Tenant looking for property)\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Create rental-specific location inputs for rent-in scenarios\r\n   */\r\n  private createRentInLocationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 2,\r\n        name: 'compoundName',\r\n        type: 'text',\r\n        label: 'Preferred Compound Name',\r\n        validators: [Validators.required],\r\n        visibility: () => stepperModal.getInsideCompoundPrivilege(),\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'locationSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Location Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'cityId',\r\n        type: 'select',\r\n        label: 'Preferred City',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'areaId',\r\n        type: 'select',\r\n        label: 'Preferred Area',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'subAreaId',\r\n        type: 'select',\r\n        label: 'Preferred Sub Area',\r\n        options: [],\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-in unit information inputs\r\n   */\r\n  private createRentInUnitInformationInputs(stepperModal: any, includeRooms: boolean = true): InputConfig[] {\r\n    const baseInputs: InputConfig[] = [\r\n      {\r\n        step: 3,\r\n        name: 'favoriteFloor',\r\n        type: 'number',\r\n        label: 'Preferred Floor',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n\r\n    const roomInputs: InputConfig[] = includeRooms ? [\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ] : [];\r\n\r\n    const commonInputs: InputConfig[] = [\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Preferred View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Preferred Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Preferred Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Desired Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Additional Requirements',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n\r\n    return [...baseInputs, ...roomInputs, ...commonInputs];\r\n  }\r\n\r\n  /**\r\n   * Create rent-in penthouses unit information inputs\r\n   */\r\n  private createRentInPenthousesUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Preferred View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Preferred Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Preferred Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Desired Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Additional Requirements',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-in villas unit information inputs\r\n   */\r\n  private createRentInVillasUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Preferred View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Preferred Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Preferred Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Desired Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Additional Requirements',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-in house unit information inputs (for twin houses, town houses, standalone villas)\r\n   */\r\n  private createRentInHouseUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Ground Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Ground Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Preferred View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Preferred Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Preferred Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Desired Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Additional Requirements',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-in commercial unit information inputs (for administrative units, medical clinics)\r\n   */\r\n  private createRentInCommercialUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'favoriteFloor',\r\n        type: 'number',\r\n        label: 'Preferred Floor',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Preferred View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Preferred Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Preferred Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Desired Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Additional Requirements',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-in pharmacies unit information inputs\r\n   */\r\n  private createRentInPharmaciesUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'favoriteFloor',\r\n        type: 'number',\r\n        label: 'Preferred Floor',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Preferred View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Preferred Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Preferred Fit-Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Desired Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Additional Requirements',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-in commercial stores unit information inputs\r\n   */\r\n  private createRentInCommercialStoresUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'favoriteFloor',\r\n        type: 'number',\r\n        label: 'Preferred Floor',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Preferred View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Preferred Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Preferred Fit-Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Preferred Activity Type',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Desired Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Additional Requirements',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-in commercial administrative buildings unit information inputs\r\n   */\r\n  private createRentInCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMin',\r\n        type: 'number',\r\n        label: 'Minimum Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitAreaMax',\r\n        type: 'number',\r\n        label: 'Maximum Unit Area (m²)',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Preferred View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Desired Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Additional Requirements',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-in financial inputs\r\n   */\r\n  private createRentInFinancialInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 5,\r\n        name: 'averageUnitPriceMin',\r\n        type: 'number',\r\n        label: 'Minimum Average Unit Price',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'averageUnitPriceMax',\r\n        type: 'number',\r\n        label: 'Maximum Average Unit Price',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'unitPriceSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Unit Price Suggestions',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'rentRecurrence',\r\n        type: 'select',\r\n        label: 'Rent Recurrence',\r\n        options: RENT_RECURRENCE_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  // ============================================================================\r\n  // RENT-OUT CONFIGURATIONS\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Create rental-specific location inputs for rent-out scenarios\r\n   */\r\n  private createRentOutLocationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 2,\r\n        name: 'compoundName',\r\n        type: 'text',\r\n        label: 'Compound Name',\r\n        validators: [Validators.required],\r\n        visibility: () => stepperModal.getInsideCompoundPrivilege(),\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'locationSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Location Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      // Rent-out specific location fields\r\n      {\r\n        step: 2,\r\n        name: 'detailedAddress',\r\n        type: 'text',\r\n        label: 'Detailed Address',\r\n        validators: [Validators.required],\r\n        visibility: () => stepperModal.getSellInsideCompoundInputs() || stepperModal.getRentOutInsideCompoundInputs(),\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'addressLink',\r\n        type: 'url',\r\n        label: 'Address Link',\r\n        validators: [Validators.pattern(/^https?:\\/\\/.+/)],\r\n        visibility: () => stepperModal.getSellInsideCompoundInputs() || stepperModal.getRentOutInsideCompoundInputs(),\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'cityId',\r\n        type: 'select',\r\n        label: 'City',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'areaId',\r\n        type: 'select',\r\n        label: 'Area',\r\n        options: [],\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 2,\r\n        name: 'subAreaId',\r\n        type: 'select',\r\n        label: 'Sub Area',\r\n        options: [],\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out unit information inputs\r\n   */\r\n  private createRentOutUnitInformationInputs(stepperModal: any, includeRooms: boolean = true, includeUnitNumber: boolean = true): InputConfig[] {\r\n    const baseInputs: InputConfig[] = [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor Number',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n\r\n    // Add unit number for rent-out inside compound (except commercial administrative buildings)\r\n    if (includeUnitNumber) {\r\n      baseInputs.push({\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      });\r\n    }\r\n\r\n    const roomInputs: InputConfig[] = includeRooms ? [\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n    ] : [];\r\n\r\n    const commonInputs: InputConfig[] = [\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n\r\n    return [...baseInputs, ...roomInputs, ...commonInputs];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out penthouse unit information inputs\r\n   */\r\n  private createRentOutPenthouseUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'terraceArea',\r\n        type: 'number',\r\n        label: 'Terrace Area (m²)',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out villa unit information inputs\r\n   */\r\n  private createRentOutVillaUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'terraceArea',\r\n        type: 'number',\r\n        label: 'Terrace Area (m²)',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'gardenArea',\r\n        type: 'number',\r\n        label: 'Garden Area (m²)',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out house unit information inputs (for twin houses, town houses, standalone villas)\r\n   */\r\n  private createRentOutHouseUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'groundArea',\r\n        type: 'number',\r\n        label: 'Ground Area (m²)',\r\n        validators: [Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'bathRooms',\r\n        type: 'number',\r\n        label: 'Number of Bathrooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'rooms',\r\n        type: 'number',\r\n        label: 'Number of Rooms',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out commercial unit information inputs (for administrative units, medical clinics)\r\n   */\r\n  private createRentOutCommercialUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor Number',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'furnishingStatus',\r\n        type: 'select',\r\n        label: 'Furnishing Status',\r\n        options: FURNISHING_STATUS_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out pharmacy unit information inputs\r\n   */\r\n  private createRentOutPharmacyUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor Number',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fit-Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out commercial store unit information inputs\r\n   */\r\n  private createRentOutCommercialStoreUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'unitNumber',\r\n        type: 'text',\r\n        label: 'Unit Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'floor',\r\n        type: 'select',\r\n        label: 'Floor Number',\r\n        options: FLOOR_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Unit Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Unit View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'fitOutCondition',\r\n        type: 'select',\r\n        label: 'Fit-Out Condition',\r\n        options: FIT_OUT_CONDITION_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity Type',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out commercial administrative building unit information inputs\r\n   */\r\n  private createRentOutCommercialAdministrativeBuildingUnitInformationInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 3,\r\n        name: 'buildingNumber',\r\n        type: 'text',\r\n        label: 'Building Number',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'numberOfFloors',\r\n        type: 'number',\r\n        label: 'Number of Floors',\r\n        validators: [Validators.required, Validators.min(1)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitArea',\r\n        type: 'number',\r\n        label: 'Building Area (m²)',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'unitView',\r\n        type: 'select',\r\n        label: 'Building View',\r\n        options: UNIT_VIEW_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'activity',\r\n        type: 'select',\r\n        label: 'Activity Type',\r\n        options: ACTIVITY_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'finishingStatus',\r\n        type: 'select',\r\n        label: 'Finishing Status',\r\n        options: FINISHING_STATUS_TYPES_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'otherAccessories',\r\n        type: 'multiSelect',\r\n        label: 'Available Accessories',\r\n        options: OTHER_ACCESSORIES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 3,\r\n        name: 'notes',\r\n        type: 'textarea',\r\n        label: 'Property Description',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create rent-out financial inputs\r\n   */\r\n  private createRentOutFinancialInputs(stepperModal: any): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 5,\r\n        name: 'unitPrice',\r\n        type: 'number',\r\n        label: 'Unit Price',\r\n        validators: [Validators.required, Validators.min(0)],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'rentPriceSuggestions',\r\n        type: 'checkbox',\r\n        label: 'Rent Price Suggestions',\r\n        validators: [],\r\n        visibility: () => stepperModal.isClient(),\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'rentRecurrence',\r\n        type: 'select',\r\n        label: 'Rent Recurrence',\r\n        options: RENT_RECURRENCE_OPTIONS,\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'requiredInsurance',\r\n        type: 'select',\r\n        label: 'Required Insurance',\r\n        options: REQUIRED_INSURANCE_TYPES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 5,\r\n        name: 'otherExpenses',\r\n        type: 'multiSelect',\r\n        label: 'Other Expenses (Tenant Responsibility)',\r\n        options: OTHER_EXPENSES_OPTIONS,\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Create media inputs for step 4\r\n   */\r\n  private createMediaInputs(): InputConfig[] {\r\n    return [\r\n      {\r\n        step: 4,\r\n        name: 'mainImage',\r\n        type: 'file',\r\n        label: 'Main Image',\r\n        validators: [Validators.required],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 4,\r\n        name: 'galleryImages',\r\n        type: 'file',\r\n        label: 'Gallery Images',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 4,\r\n        name: 'unitInMasterPlanImage',\r\n        type: 'file',\r\n        label: 'Unit in Master Plan Image',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n      {\r\n        step: 4,\r\n        name: 'video',\r\n        type: 'file',\r\n        label: 'Video',\r\n        validators: [],\r\n        visibility: () => true,\r\n      },\r\n    ];\r\n  }\r\n\r\n  // ============================================================================\r\n  // CONFIGURATION BUILDERS\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Generic rent-in configuration builder\r\n   */\r\n  private createRentInConfig(\r\n    unitType: string,\r\n    stepperModal: any,\r\n    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}\r\n  ): InputConfig[] {\r\n    const { includeRooms = true, includeDocuments = false } = options;\r\n\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInLocationInputs(stepperModal),\r\n      ...this.createRentInUnitInformationInputs(stepperModal, includeRooms),\r\n    ];\r\n\r\n    // Add documents if needed\r\n    if (includeDocuments) {\r\n      config.push(...this.createDocumentInputs());\r\n    }\r\n\r\n    // Add financial inputs\r\n    config.push(...this.createRentInFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Generic rent-out configuration builder\r\n   */\r\n  private createRentOutConfig(\r\n    unitType: string,\r\n    stepperModal: any,\r\n    options: { includeRooms?: boolean; includeMedia?: boolean; includeUnitNumber?: boolean } = {}\r\n  ): InputConfig[] {\r\n    const { includeRooms = true, includeMedia = true, includeUnitNumber = true } = options;\r\n\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutLocationInputs(stepperModal),\r\n      ...this.createRentOutUnitInformationInputs(stepperModal, includeRooms, includeUnitNumber),\r\n    ];\r\n\r\n    // Add media inputs if needed\r\n    if (includeMedia) {\r\n      config.push(...this.createMediaInputs());\r\n    }\r\n\r\n    // Add financial inputs\r\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  // ============================================================================\r\n  // SPECIFIC CONFIGURATIONS (Ready for manual modification)\r\n  // ============================================================================\r\n\r\n  // RENT-IN CONFIGURATIONS\r\n  private createRentInApartmentsConfig(stepperModal: any): InputConfig[] {\r\n    return this.createRentInConfig('apartments', stepperModal, {\r\n      includeRooms: true,\r\n      includeDocuments: false\r\n    });\r\n  }\r\n\r\n  private createRentInDuplexesConfig(stepperModal: any): InputConfig[] {\r\n    return this.createRentInConfig('duplexes', stepperModal, {\r\n      includeRooms: true,\r\n      includeDocuments: false\r\n    });\r\n  }\r\n\r\n  private createRentInStudiosConfig(stepperModal: any): InputConfig[] {\r\n    return this.createRentInConfig('studios', stepperModal, {\r\n      includeRooms: false,\r\n      includeDocuments: false\r\n    });\r\n  }\r\n\r\n  private createRentInVillasConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInLocationInputs(stepperModal),\r\n      ...this.createRentInVillasUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentInPenthousesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInLocationInputs(stepperModal),\r\n      ...this.createRentInPenthousesUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentInTwinHousesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInLocationInputs(stepperModal),\r\n      ...this.createRentInHouseUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentInTownHousesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInLocationInputs(stepperModal),\r\n      ...this.createRentInHouseUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentInStandaloneVillasConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInLocationInputs(stepperModal),\r\n      ...this.createRentInHouseUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentInAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInLocationInputs(stepperModal),\r\n      ...this.createRentInCommercialUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentInMedicalClinicsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInLocationInputs(stepperModal),\r\n      ...this.createRentInCommercialUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentInPharmaciesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInLocationInputs(stepperModal),\r\n      ...this.createRentInPharmaciesUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentInCommercialStoresConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInLocationInputs(stepperModal),\r\n      ...this.createRentInCommercialStoresUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentInCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentInLocationInputs(stepperModal),\r\n      ...this.createRentInCommercialAdministrativeBuildingsUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentInFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  // RENT-OUT CONFIGURATIONS\r\n  private createRentOutApartmentsConfig(stepperModal: any): InputConfig[] {\r\n    return this.createRentOutConfig('apartments', stepperModal, {\r\n      includeRooms: true,\r\n      includeMedia: true,\r\n      includeUnitNumber: true // Include unit number for apartments\r\n    });\r\n  }\r\n\r\n  private createRentOutDuplexesConfig(stepperModal: any): InputConfig[] {\r\n    return this.createRentOutConfig('duplexes', stepperModal, {\r\n      includeRooms: true,\r\n      includeMedia: true,\r\n      includeUnitNumber: true // Include unit number for duplexes\r\n    });\r\n  }\r\n\r\n  private createRentOutStudiosConfig(stepperModal: any): InputConfig[] {\r\n    return this.createRentOutConfig('studios', stepperModal, {\r\n      includeRooms: false,\r\n      includeMedia: true,\r\n      includeUnitNumber: true // Include unit number for studios\r\n    });\r\n  }\r\n\r\n  private createRentOutVillasConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutLocationInputs(stepperModal),\r\n      ...this.createRentOutVillaUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutPenthousesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutLocationInputs(stepperModal),\r\n      ...this.createRentOutPenthouseUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutTwinHousesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutLocationInputs(stepperModal),\r\n      ...this.createRentOutHouseUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutTownHousesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutLocationInputs(stepperModal),\r\n      ...this.createRentOutHouseUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutStandaloneVillasConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutLocationInputs(stepperModal),\r\n      ...this.createRentOutHouseUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutLocationInputs(stepperModal),\r\n      ...this.createRentOutCommercialUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutMedicalClinicsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutLocationInputs(stepperModal),\r\n      ...this.createRentOutCommercialUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutPharmaciesConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutLocationInputs(stepperModal),\r\n      ...this.createRentOutPharmacyUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  private createRentOutCommercialStoresConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutLocationInputs(stepperModal),\r\n      ...this.createRentOutCommercialStoreUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  // Commercial configurations (for future use) - exclude unit number\r\n  private createRentOutCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {\r\n    const config: InputConfig[] = [\r\n      ...this.createRentOutLocationInputs(stepperModal),\r\n      ...this.createRentOutCommercialAdministrativeBuildingUnitInformationInputs(stepperModal),\r\n    ];\r\n\r\n    // Add media inputs for step 4\r\n    config.push(...this.createMediaInputs());\r\n\r\n    // Add financial inputs for step 5\r\n    config.push(...this.createRentOutFinancialInputs(stepperModal));\r\n\r\n    return config;\r\n  }\r\n\r\n  // ============================================================================\r\n  // PUBLIC API\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Get input configurations for rental cases\r\n   */\r\n  getInputConfigs(stepperModal: any): StepperConfiguration[] {\r\n    return [\r\n      // RENT-IN CONFIGURATIONS\r\n      {\r\n        key: 'rentals_inside_compound_rent_in_apartments',\r\n        value: this.createRentInApartmentsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_in_duplexes',\r\n        value: this.createRentInDuplexesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_in_studios',\r\n        value: this.createRentInStudiosConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_in_villas',\r\n        value: this.createRentInVillasConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_in_penthouses',\r\n        value: this.createRentInPenthousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_in_twin_houses',\r\n        value: this.createRentInTwinHousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_in_town_houses',\r\n        value: this.createRentInTownHousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_in_standalone_villas',\r\n        value: this.createRentInStandaloneVillasConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_in_administrative_units',\r\n        value: this.createRentInAdministrativeUnitsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_in_medical_clinics',\r\n        value: this.createRentInMedicalClinicsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_in_pharmacies',\r\n        value: this.createRentInPharmaciesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_in_commercial_stores',\r\n        value: this.createRentInCommercialStoresConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_in_commercial_administrative_buildings',\r\n        value: this.createRentInCommercialAdministrativeBuildingsConfig(stepperModal),\r\n      },\r\n\r\n      // RENT-OUT CONFIGURATIONS\r\n      {\r\n        key: 'rentals_inside_compound_rent_out_apartments',\r\n        value: this.createRentOutApartmentsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_out_duplexes',\r\n        value: this.createRentOutDuplexesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_out_studios',\r\n        value: this.createRentOutStudiosConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_out_villas',\r\n        value: this.createRentOutVillasConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_out_penthouses',\r\n        value: this.createRentOutPenthousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_out_twin_houses',\r\n        value: this.createRentOutTwinHousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_out_town_houses',\r\n        value: this.createRentOutTownHousesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_out_standalone_villas',\r\n        value: this.createRentOutStandaloneVillasConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_out_administrative_units',\r\n        value: this.createRentOutAdministrativeUnitsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_out_medical_clinics',\r\n        value: this.createRentOutMedicalClinicsConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_out_pharmacies',\r\n        value: this.createRentOutPharmaciesConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_out_commercial_stores',\r\n        value: this.createRentOutCommercialStoresConfig(stepperModal),\r\n      },\r\n      {\r\n        key: 'rentals_inside_compound_rent_out_commercial_administrative_buildings',\r\n        value: this.createRentOutCommercialAdministrativeBuildingsConfig(stepperModal),\r\n      },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Get all available rental configuration keys\r\n   */\r\n  getRentalConfigurationKeys(): string[] {\r\n    return [\r\n      // RENT-IN KEYS\r\n      'rentals_inside_compound_rent_in_apartments',\r\n      'rentals_inside_compound_rent_in_duplexes',\r\n      'rentals_inside_compound_rent_in_studios',\r\n      'rentals_inside_compound_rent_in_villas',\r\n      'rentals_inside_compound_rent_in_penthouses',\r\n      'rentals_inside_compound_rent_in_twin_houses',\r\n      'rentals_inside_compound_rent_in_town_houses',\r\n      'rentals_inside_compound_rent_in_standalone_villas',\r\n      'rentals_inside_compound_rent_in_administrative_units',\r\n      'rentals_inside_compound_rent_in_medical_clinics',\r\n      'rentals_inside_compound_rent_in_pharmacies',\r\n      'rentals_inside_compound_rent_in_commercial_stores',\r\n      'rentals_inside_compound_rent_in_commercial_administrative_buildings',\r\n\r\n      // RENT-OUT KEYS\r\n      'rentals_inside_compound_rent_out_apartments',\r\n      'rentals_inside_compound_rent_out_duplexes',\r\n      'rentals_inside_compound_rent_out_studios',\r\n      'rentals_inside_compound_rent_out_villas',\r\n      'rentals_inside_compound_rent_out_penthouses',\r\n      'rentals_inside_compound_rent_out_twin_houses',\r\n      'rentals_inside_compound_rent_out_town_houses',\r\n      'rentals_inside_compound_rent_out_standalone_villas',\r\n      'rentals_inside_compound_rent_out_administrative_units',\r\n      'rentals_inside_compound_rent_out_medical_clinics',\r\n      'rentals_inside_compound_rent_out_pharmacies',\r\n      'rentals_inside_compound_rent_out_commercial_stores',\r\n      'rentals_inside_compound_rent_out_commercial_administrative_buildings',\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Check if a key is rent-in configuration\r\n   */\r\n  isRentInConfiguration(key: string): boolean {\r\n    return key.includes('_rent_in_');\r\n  }\r\n\r\n  /**\r\n   * Check if a key is rent-out configuration\r\n   */\r\n  isRentOutConfiguration(key: string): boolean {\r\n    return key.includes('_rent_out_');\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,iBAAiB,QAA2C,wBAAwB;AAC7F,SACEC,uBAAuB,EACvBC,8BAA8B,EAC9BC,yBAAyB,EACzBC,yBAAyB,EACzBC,uBAAuB,EACvBC,gCAAgC,EAChCC,sBAAsB,EACtBC,mBAAmB,EACnBC,+BAA+B,EAC/BC,sBAAsB,QACjB,+BAA+B;;AAKtC,OAAM,MAAOC,mBAAoB,SAAQX,iBAAiB;EAExD;EACA;EACA;EAEA;;;EAGQY,0BAA0BA,CAACC,YAAiB;IAClD,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,yBAAyB;MAChCC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACQ,0BAA0B;KAC1D,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,QAAQ;KACxC,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBM,OAAO,EAAE,EAAE;MACXL,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBM,OAAO,EAAE,EAAE;MACXL,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BM,OAAO,EAAE,EAAE;MACXL,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQI,iCAAiCA,CAACX,YAAiB,EAAEY,YAAA,GAAwB,IAAI;IACvF,MAAMC,UAAU,GAAkB,CAChC;MACEZ,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,MAAMQ,UAAU,GAAkBH,YAAY,GAAG,CAC/C;MACEX,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF,GAAG,EAAE;IAEN,MAAMS,YAAY,GAAkB,CAClC;MACEf,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,6BAA6B;MACpCM,OAAO,EAAEnB,yBAAyB;MAClCc,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,qBAAqB;MAC5BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,yBAAyB;MAChCC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,OAAO,CAAC,GAAGM,UAAU,EAAE,GAAGE,UAAU,EAAE,GAAGC,YAAY,CAAC;EACxD;EAEA;;;EAGQC,2CAA2CA,CAACjB,YAAiB;IACnE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,6BAA6B;MACpCM,OAAO,EAAEnB,yBAAyB;MAClCc,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,qBAAqB;MAC5BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,yBAAyB;MAChCC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQW,uCAAuCA,CAAClB,YAAiB;IAC/D,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,6BAA6B;MACpCM,OAAO,EAAEnB,yBAAyB;MAClCc,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,qBAAqB;MAC5BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,yBAAyB;MAChCC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQY,sCAAsCA,CAACnB,YAAiB;IAC9D,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,0BAA0B;MACjCC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,0BAA0B;MACjCC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,6BAA6B;MACpCM,OAAO,EAAEnB,yBAAyB;MAClCc,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,qBAAqB;MAC5BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,yBAAyB;MAChCC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQa,2CAA2CA,CAACpB,YAAiB;IACnE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,6BAA6B;MACpCM,OAAO,EAAEnB,yBAAyB;MAClCc,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,qBAAqB;MAC5BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,yBAAyB;MAChCC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQc,2CAA2CA,CAACrB,YAAiB;IACnE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,6BAA6B;MACpCM,OAAO,EAAEd,+BAA+B;MACxCS,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,qBAAqB;MAC5BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,yBAAyB;MAChCC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQe,iDAAiDA,CAACtB,YAAiB;IACzE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,6BAA6B;MACpCM,OAAO,EAAEd,+BAA+B;MACxCS,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,yBAAyB;MAChCM,OAAO,EAAEb,sBAAsB;MAC/BQ,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,qBAAqB;MAC5BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,yBAAyB;MAChCC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQgB,kEAAkEA,CAACvB,YAAiB;IAC1F,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,qBAAqB;MAC5BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,yBAAyB;MAChCC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQiB,2BAA2BA,CAACxB,YAAiB;IACnD,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,4BAA4B;MACnCC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBM,OAAO,EAAElB,uBAAuB;MAChCa,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;EACA;EACA;EAEA;;;EAGQkB,2BAA2BA,CAACzB,YAAiB;IACnD,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,eAAe;MACtBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACQ,0BAA0B;KAC1D,EACD;MACEP,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,QAAQ;KACxC;IACD;IACA;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAAC0B,2BAA2B,EAAE,IAAI1B,YAAY,CAAC2B,8BAA8B;KAC5G,EACD;MACE1B,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,cAAc;MACrBC,UAAU,EAAE,CAACnB,UAAU,CAAC0C,OAAO,CAAC,gBAAgB,CAAC,CAAC;MAClDrB,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAAC0B,2BAA2B,EAAE,IAAI1B,YAAY,CAAC2B,8BAA8B;KAC5G,EACD;MACE1B,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,EAAE;MACXL,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,MAAM;MACbM,OAAO,EAAE,EAAE;MACXL,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBM,OAAO,EAAE,EAAE;MACXL,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQsB,kCAAkCA,CAAC7B,YAAiB,EAAEY,YAAA,GAAwB,IAAI,EAAEkB,iBAAA,GAA6B,IAAI;IAC3H,MAAMjB,UAAU,GAAkB,CAChC;MACEZ,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,cAAc;MACrBM,OAAO,EAAEf,mBAAmB;MAC5BU,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED;IACA,IAAIuB,iBAAiB,EAAE;MACrBjB,UAAU,CAACkB,IAAI,CAAC;QACd9B,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,aAAa;QACpBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;QACjCC,UAAU,EAAEA,CAAA,KAAM;OACnB,CAAC;IACJ;IAEA,MAAMQ,UAAU,GAAkBH,YAAY,GAAG,CAC/C;MACEX,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF,GAAG,EAAE;IAEN,MAAMS,YAAY,GAAkB,CAClC;MACEf,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BM,OAAO,EAAEnB,yBAAyB;MAClCc,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;IAED,OAAO,CAAC,GAAGM,UAAU,EAAE,GAAGE,UAAU,EAAE,GAAGC,YAAY,CAAC;EACxD;EAEA;;;EAGQgB,2CAA2CA,CAAChC,YAAiB;IACnE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BC,UAAU,EAAE,CAACnB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BM,OAAO,EAAEnB,yBAAyB;MAClCc,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQ0B,uCAAuCA,CAACjC,YAAiB;IAC/D,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BC,UAAU,EAAE,CAACnB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACnB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BM,OAAO,EAAEnB,yBAAyB;MAClCc,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQ2B,uCAAuCA,CAAClC,YAAiB;IAC/D,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACnB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/BP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,qBAAqB;MAC5BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BM,OAAO,EAAEnB,yBAAyB;MAClCc,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQ4B,4CAA4CA,CAACnC,YAAiB;IACpE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,cAAc;MACrBM,OAAO,EAAEf,mBAAmB;MAC5BU,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BM,OAAO,EAAEnB,yBAAyB;MAClCc,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQ6B,0CAA0CA,CAACpC,YAAiB;IAClE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,cAAc;MACrBM,OAAO,EAAEf,mBAAmB;MAC5BU,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BM,OAAO,EAAEd,+BAA+B;MACxCS,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQ8B,iDAAiDA,CAACrC,YAAiB;IACzE,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,aAAa;MACpBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,cAAc;MACrBM,OAAO,EAAEf,mBAAmB;MAC5BU,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,mBAAmB;MAC1BM,OAAO,EAAEd,+BAA+B;MACxCS,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,eAAe;MACtBM,OAAO,EAAEb,sBAAsB;MAC/BQ,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQ+B,kEAAkEA,CAACtC,YAAiB;IAC1F,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,eAAe;MACtBM,OAAO,EAAEtB,uBAAuB;MAChCiB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,eAAe;MACtBM,OAAO,EAAEb,sBAAsB;MAC/BQ,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,kBAAkB;MACzBM,OAAO,EAAErB,8BAA8B;MACvCgB,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,uBAAuB;MAC9BM,OAAO,EAAEpB,yBAAyB;MAClCe,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQgC,4BAA4BA,CAACvC,YAAiB;IACpD,OAAO,CACL;MACEC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,YAAY;MACnBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,CAAC;MACpDP,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,wBAAwB;MAC/BC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAMP,YAAY,CAACS,QAAQ;KACxC,EACD;MACER,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,iBAAiB;MACxBM,OAAO,EAAElB,uBAAuB;MAChCa,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,mBAAmB;MACzBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,oBAAoB;MAC3BM,OAAO,EAAEjB,gCAAgC;MACzCY,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,wCAAwC;MAC/CM,OAAO,EAAEhB,sBAAsB;MAC/BW,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;;;EAGQiC,iBAAiBA,CAAA;IACvB,OAAO,CACL;MACEvC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,YAAY;MACnBC,UAAU,EAAE,CAACnB,UAAU,CAACoB,QAAQ,CAAC;MACjCC,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,gBAAgB;MACvBC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,uBAAuB;MAC7BC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,2BAA2B;MAClCC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,EACD;MACEN,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,EAAE;MACdE,UAAU,EAAEA,CAAA,KAAM;KACnB,CACF;EACH;EAEA;EACA;EACA;EAEA;;;EAGQkC,kBAAkBA,CACxBC,QAAgB,EAChB1C,YAAiB,EACjBU,OAAA,GAAkE,EAAE;IAEpE,MAAM;MAAEE,YAAY,GAAG,IAAI;MAAE+B,gBAAgB,GAAG;IAAK,CAAE,GAAGjC,OAAO;IAEjE,MAAMkC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7C,0BAA0B,CAACC,YAAY,CAAC,EAChD,GAAG,IAAI,CAACW,iCAAiC,CAACX,YAAY,EAAEY,YAAY,CAAC,CACtE;IAED;IACA,IAAI+B,gBAAgB,EAAE;MACpBC,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACc,oBAAoB,EAAE,CAAC;IAC7C;IAEA;IACAD,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACP,2BAA2B,CAACxB,YAAY,CAAC,CAAC;IAE9D,OAAO4C,MAAM;EACf;EAEA;;;EAGQE,mBAAmBA,CACzBJ,QAAgB,EAChB1C,YAAiB,EACjBU,OAAA,GAA2F,EAAE;IAE7F,MAAM;MAAEE,YAAY,GAAG,IAAI;MAAEmC,YAAY,GAAG,IAAI;MAAEjB,iBAAiB,GAAG;IAAI,CAAE,GAAGpB,OAAO;IAEtF,MAAMkC,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACnB,2BAA2B,CAACzB,YAAY,CAAC,EACjD,GAAG,IAAI,CAAC6B,kCAAkC,CAAC7B,YAAY,EAAEY,YAAY,EAAEkB,iBAAiB,CAAC,CAC1F;IAED;IACA,IAAIiB,YAAY,EAAE;MAChBH,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACS,iBAAiB,EAAE,CAAC;IAC1C;IAEA;IACAI,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACQ,4BAA4B,CAACvC,YAAY,CAAC,CAAC;IAE/D,OAAO4C,MAAM;EACf;EAEA;EACA;EACA;EAEA;EACQI,4BAA4BA,CAAChD,YAAiB;IACpD,OAAO,IAAI,CAACyC,kBAAkB,CAAC,YAAY,EAAEzC,YAAY,EAAE;MACzDY,YAAY,EAAE,IAAI;MAClB+B,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEQM,0BAA0BA,CAACjD,YAAiB;IAClD,OAAO,IAAI,CAACyC,kBAAkB,CAAC,UAAU,EAAEzC,YAAY,EAAE;MACvDY,YAAY,EAAE,IAAI;MAClB+B,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEQO,yBAAyBA,CAAClD,YAAiB;IACjD,OAAO,IAAI,CAACyC,kBAAkB,CAAC,SAAS,EAAEzC,YAAY,EAAE;MACtDY,YAAY,EAAE,KAAK;MACnB+B,gBAAgB,EAAE;KACnB,CAAC;EACJ;EAEQQ,wBAAwBA,CAACnD,YAAiB;IAChD,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7C,0BAA0B,CAACC,YAAY,CAAC,EAChD,GAAG,IAAI,CAACkB,uCAAuC,CAAClB,YAAY,CAAC,CAC9D;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACP,2BAA2B,CAACxB,YAAY,CAAC,CAAC;IAE9D,OAAO4C,MAAM;EACf;EAEQQ,4BAA4BA,CAACpD,YAAiB;IACpD,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7C,0BAA0B,CAACC,YAAY,CAAC,EAChD,GAAG,IAAI,CAACiB,2CAA2C,CAACjB,YAAY,CAAC,CAClE;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACP,2BAA2B,CAACxB,YAAY,CAAC,CAAC;IAE9D,OAAO4C,MAAM;EACf;EAEQS,4BAA4BA,CAACrD,YAAiB;IACpD,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7C,0BAA0B,CAACC,YAAY,CAAC,EAChD,GAAG,IAAI,CAACmB,sCAAsC,CAACnB,YAAY,CAAC,CAC7D;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACP,2BAA2B,CAACxB,YAAY,CAAC,CAAC;IAE9D,OAAO4C,MAAM;EACf;EAEQU,4BAA4BA,CAACtD,YAAiB;IACpD,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7C,0BAA0B,CAACC,YAAY,CAAC,EAChD,GAAG,IAAI,CAACmB,sCAAsC,CAACnB,YAAY,CAAC,CAC7D;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACP,2BAA2B,CAACxB,YAAY,CAAC,CAAC;IAE9D,OAAO4C,MAAM;EACf;EAEQW,kCAAkCA,CAACvD,YAAiB;IAC1D,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7C,0BAA0B,CAACC,YAAY,CAAC,EAChD,GAAG,IAAI,CAACmB,sCAAsC,CAACnB,YAAY,CAAC,CAC7D;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACP,2BAA2B,CAACxB,YAAY,CAAC,CAAC;IAE9D,OAAO4C,MAAM;EACf;EAEQY,qCAAqCA,CAACxD,YAAiB;IAC7D,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7C,0BAA0B,CAACC,YAAY,CAAC,EAChD,GAAG,IAAI,CAACoB,2CAA2C,CAACpB,YAAY,CAAC,CAClE;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACP,2BAA2B,CAACxB,YAAY,CAAC,CAAC;IAE9D,OAAO4C,MAAM;EACf;EAEQa,gCAAgCA,CAACzD,YAAiB;IACxD,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7C,0BAA0B,CAACC,YAAY,CAAC,EAChD,GAAG,IAAI,CAACoB,2CAA2C,CAACpB,YAAY,CAAC,CAClE;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACP,2BAA2B,CAACxB,YAAY,CAAC,CAAC;IAE9D,OAAO4C,MAAM;EACf;EAEQc,4BAA4BA,CAAC1D,YAAiB;IACpD,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7C,0BAA0B,CAACC,YAAY,CAAC,EAChD,GAAG,IAAI,CAACqB,2CAA2C,CAACrB,YAAY,CAAC,CAClE;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACP,2BAA2B,CAACxB,YAAY,CAAC,CAAC;IAE9D,OAAO4C,MAAM;EACf;EAEQe,kCAAkCA,CAAC3D,YAAiB;IAC1D,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7C,0BAA0B,CAACC,YAAY,CAAC,EAChD,GAAG,IAAI,CAACsB,iDAAiD,CAACtB,YAAY,CAAC,CACxE;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACP,2BAA2B,CAACxB,YAAY,CAAC,CAAC;IAE9D,OAAO4C,MAAM;EACf;EAEQgB,mDAAmDA,CAAC5D,YAAiB;IAC3E,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAAC7C,0BAA0B,CAACC,YAAY,CAAC,EAChD,GAAG,IAAI,CAACuB,kEAAkE,CAACvB,YAAY,CAAC,CACzF;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACP,2BAA2B,CAACxB,YAAY,CAAC,CAAC;IAE9D,OAAO4C,MAAM;EACf;EAEA;EACQiB,6BAA6BA,CAAC7D,YAAiB;IACrD,OAAO,IAAI,CAAC8C,mBAAmB,CAAC,YAAY,EAAE9C,YAAY,EAAE;MAC1DY,YAAY,EAAE,IAAI;MAClBmC,YAAY,EAAE,IAAI;MAClBjB,iBAAiB,EAAE,IAAI,CAAC;KACzB,CAAC;EACJ;EAEQgC,2BAA2BA,CAAC9D,YAAiB;IACnD,OAAO,IAAI,CAAC8C,mBAAmB,CAAC,UAAU,EAAE9C,YAAY,EAAE;MACxDY,YAAY,EAAE,IAAI;MAClBmC,YAAY,EAAE,IAAI;MAClBjB,iBAAiB,EAAE,IAAI,CAAC;KACzB,CAAC;EACJ;EAEQiC,0BAA0BA,CAAC/D,YAAiB;IAClD,OAAO,IAAI,CAAC8C,mBAAmB,CAAC,SAAS,EAAE9C,YAAY,EAAE;MACvDY,YAAY,EAAE,KAAK;MACnBmC,YAAY,EAAE,IAAI;MAClBjB,iBAAiB,EAAE,IAAI,CAAC;KACzB,CAAC;EACJ;EAEQkC,yBAAyBA,CAAChE,YAAiB;IACjD,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACnB,2BAA2B,CAACzB,YAAY,CAAC,EACjD,GAAG,IAAI,CAACiC,uCAAuC,CAACjC,YAAY,CAAC,CAC9D;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACS,iBAAiB,EAAE,CAAC;IAExC;IACAI,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACQ,4BAA4B,CAACvC,YAAY,CAAC,CAAC;IAE/D,OAAO4C,MAAM;EACf;EAEQqB,6BAA6BA,CAACjE,YAAiB;IACrD,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACnB,2BAA2B,CAACzB,YAAY,CAAC,EACjD,GAAG,IAAI,CAACgC,2CAA2C,CAAChC,YAAY,CAAC,CAClE;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACS,iBAAiB,EAAE,CAAC;IAExC;IACAI,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACQ,4BAA4B,CAACvC,YAAY,CAAC,CAAC;IAE/D,OAAO4C,MAAM;EACf;EAEQsB,6BAA6BA,CAAClE,YAAiB;IACrD,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACnB,2BAA2B,CAACzB,YAAY,CAAC,EACjD,GAAG,IAAI,CAACkC,uCAAuC,CAAClC,YAAY,CAAC,CAC9D;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACS,iBAAiB,EAAE,CAAC;IAExC;IACAI,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACQ,4BAA4B,CAACvC,YAAY,CAAC,CAAC;IAE/D,OAAO4C,MAAM;EACf;EAEQuB,6BAA6BA,CAACnE,YAAiB;IACrD,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACnB,2BAA2B,CAACzB,YAAY,CAAC,EACjD,GAAG,IAAI,CAACkC,uCAAuC,CAAClC,YAAY,CAAC,CAC9D;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACS,iBAAiB,EAAE,CAAC;IAExC;IACAI,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACQ,4BAA4B,CAACvC,YAAY,CAAC,CAAC;IAE/D,OAAO4C,MAAM;EACf;EAEQwB,mCAAmCA,CAACpE,YAAiB;IAC3D,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACnB,2BAA2B,CAACzB,YAAY,CAAC,EACjD,GAAG,IAAI,CAACkC,uCAAuC,CAAClC,YAAY,CAAC,CAC9D;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACS,iBAAiB,EAAE,CAAC;IAExC;IACAI,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACQ,4BAA4B,CAACvC,YAAY,CAAC,CAAC;IAE/D,OAAO4C,MAAM;EACf;EAEQyB,sCAAsCA,CAACrE,YAAiB;IAC9D,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACnB,2BAA2B,CAACzB,YAAY,CAAC,EACjD,GAAG,IAAI,CAACmC,4CAA4C,CAACnC,YAAY,CAAC,CACnE;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACS,iBAAiB,EAAE,CAAC;IAExC;IACAI,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACQ,4BAA4B,CAACvC,YAAY,CAAC,CAAC;IAE/D,OAAO4C,MAAM;EACf;EAEQ0B,iCAAiCA,CAACtE,YAAiB;IACzD,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACnB,2BAA2B,CAACzB,YAAY,CAAC,EACjD,GAAG,IAAI,CAACmC,4CAA4C,CAACnC,YAAY,CAAC,CACnE;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACS,iBAAiB,EAAE,CAAC;IAExC;IACAI,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACQ,4BAA4B,CAACvC,YAAY,CAAC,CAAC;IAE/D,OAAO4C,MAAM;EACf;EAEQ2B,6BAA6BA,CAACvE,YAAiB;IACrD,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACnB,2BAA2B,CAACzB,YAAY,CAAC,EACjD,GAAG,IAAI,CAACoC,0CAA0C,CAACpC,YAAY,CAAC,CACjE;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACS,iBAAiB,EAAE,CAAC;IAExC;IACAI,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACQ,4BAA4B,CAACvC,YAAY,CAAC,CAAC;IAE/D,OAAO4C,MAAM;EACf;EAEQ4B,mCAAmCA,CAACxE,YAAiB;IAC3D,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACnB,2BAA2B,CAACzB,YAAY,CAAC,EACjD,GAAG,IAAI,CAACqC,iDAAiD,CAACrC,YAAY,CAAC,CACxE;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACS,iBAAiB,EAAE,CAAC;IAExC;IACAI,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACQ,4BAA4B,CAACvC,YAAY,CAAC,CAAC;IAE/D,OAAO4C,MAAM;EACf;EAEA;EACQ6B,oDAAoDA,CAACzE,YAAiB;IAC5E,MAAM4C,MAAM,GAAkB,CAC5B,GAAG,IAAI,CAACnB,2BAA2B,CAACzB,YAAY,CAAC,EACjD,GAAG,IAAI,CAACsC,kEAAkE,CAACtC,YAAY,CAAC,CACzF;IAED;IACA4C,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACS,iBAAiB,EAAE,CAAC;IAExC;IACAI,MAAM,CAACb,IAAI,CAAC,GAAG,IAAI,CAACQ,4BAA4B,CAACvC,YAAY,CAAC,CAAC;IAE/D,OAAO4C,MAAM;EACf;EAEA;EACA;EACA;EAEA;;;EAGA8B,eAAeA,CAAC1E,YAAiB;IAC/B,OAAO;IACL;IACA;MACE2E,GAAG,EAAE,4CAA4C;MACjDC,KAAK,EAAE,IAAI,CAAC5B,4BAA4B,CAAChD,YAAY;KACtD,EACD;MACE2E,GAAG,EAAE,0CAA0C;MAC/CC,KAAK,EAAE,IAAI,CAAC3B,0BAA0B,CAACjD,YAAY;KACpD,EACD;MACE2E,GAAG,EAAE,yCAAyC;MAC9CC,KAAK,EAAE,IAAI,CAAC1B,yBAAyB,CAAClD,YAAY;KACnD,EACD;MACE2E,GAAG,EAAE,wCAAwC;MAC7CC,KAAK,EAAE,IAAI,CAACzB,wBAAwB,CAACnD,YAAY;KAClD,EACD;MACE2E,GAAG,EAAE,4CAA4C;MACjDC,KAAK,EAAE,IAAI,CAACxB,4BAA4B,CAACpD,YAAY;KACtD,EACD;MACE2E,GAAG,EAAE,6CAA6C;MAClDC,KAAK,EAAE,IAAI,CAACvB,4BAA4B,CAACrD,YAAY;KACtD,EACD;MACE2E,GAAG,EAAE,6CAA6C;MAClDC,KAAK,EAAE,IAAI,CAACtB,4BAA4B,CAACtD,YAAY;KACtD,EACD;MACE2E,GAAG,EAAE,mDAAmD;MACxDC,KAAK,EAAE,IAAI,CAACrB,kCAAkC,CAACvD,YAAY;KAC5D,EACD;MACE2E,GAAG,EAAE,sDAAsD;MAC3DC,KAAK,EAAE,IAAI,CAACpB,qCAAqC,CAACxD,YAAY;KAC/D,EACD;MACE2E,GAAG,EAAE,iDAAiD;MACtDC,KAAK,EAAE,IAAI,CAACnB,gCAAgC,CAACzD,YAAY;KAC1D,EACD;MACE2E,GAAG,EAAE,4CAA4C;MACjDC,KAAK,EAAE,IAAI,CAAClB,4BAA4B,CAAC1D,YAAY;KACtD,EACD;MACE2E,GAAG,EAAE,mDAAmD;MACxDC,KAAK,EAAE,IAAI,CAACjB,kCAAkC,CAAC3D,YAAY;KAC5D,EACD;MACE2E,GAAG,EAAE,qEAAqE;MAC1EC,KAAK,EAAE,IAAI,CAAChB,mDAAmD,CAAC5D,YAAY;KAC7E;IAED;IACA;MACE2E,GAAG,EAAE,6CAA6C;MAClDC,KAAK,EAAE,IAAI,CAACf,6BAA6B,CAAC7D,YAAY;KACvD,EACD;MACE2E,GAAG,EAAE,2CAA2C;MAChDC,KAAK,EAAE,IAAI,CAACd,2BAA2B,CAAC9D,YAAY;KACrD,EACD;MACE2E,GAAG,EAAE,0CAA0C;MAC/CC,KAAK,EAAE,IAAI,CAACb,0BAA0B,CAAC/D,YAAY;KACpD,EACD;MACE2E,GAAG,EAAE,yCAAyC;MAC9CC,KAAK,EAAE,IAAI,CAACZ,yBAAyB,CAAChE,YAAY;KACnD,EACD;MACE2E,GAAG,EAAE,6CAA6C;MAClDC,KAAK,EAAE,IAAI,CAACX,6BAA6B,CAACjE,YAAY;KACvD,EACD;MACE2E,GAAG,EAAE,8CAA8C;MACnDC,KAAK,EAAE,IAAI,CAACV,6BAA6B,CAAClE,YAAY;KACvD,EACD;MACE2E,GAAG,EAAE,8CAA8C;MACnDC,KAAK,EAAE,IAAI,CAACT,6BAA6B,CAACnE,YAAY;KACvD,EACD;MACE2E,GAAG,EAAE,oDAAoD;MACzDC,KAAK,EAAE,IAAI,CAACR,mCAAmC,CAACpE,YAAY;KAC7D,EACD;MACE2E,GAAG,EAAE,uDAAuD;MAC5DC,KAAK,EAAE,IAAI,CAACP,sCAAsC,CAACrE,YAAY;KAChE,EACD;MACE2E,GAAG,EAAE,kDAAkD;MACvDC,KAAK,EAAE,IAAI,CAACN,iCAAiC,CAACtE,YAAY;KAC3D,EACD;MACE2E,GAAG,EAAE,6CAA6C;MAClDC,KAAK,EAAE,IAAI,CAACL,6BAA6B,CAACvE,YAAY;KACvD,EACD;MACE2E,GAAG,EAAE,oDAAoD;MACzDC,KAAK,EAAE,IAAI,CAACJ,mCAAmC,CAACxE,YAAY;KAC7D,EACD;MACE2E,GAAG,EAAE,sEAAsE;MAC3EC,KAAK,EAAE,IAAI,CAACH,oDAAoD,CAACzE,YAAY;KAC9E,CACF;EACH;EAEA;;;EAGA6E,0BAA0BA,CAAA;IACxB,OAAO;IACL;IACA,4CAA4C,EAC5C,0CAA0C,EAC1C,yCAAyC,EACzC,wCAAwC,EACxC,4CAA4C,EAC5C,6CAA6C,EAC7C,6CAA6C,EAC7C,mDAAmD,EACnD,sDAAsD,EACtD,iDAAiD,EACjD,4CAA4C,EAC5C,mDAAmD,EACnD,qEAAqE;IAErE;IACA,6CAA6C,EAC7C,2CAA2C,EAC3C,0CAA0C,EAC1C,yCAAyC,EACzC,6CAA6C,EAC7C,8CAA8C,EAC9C,8CAA8C,EAC9C,oDAAoD,EACpD,uDAAuD,EACvD,kDAAkD,EAClD,6CAA6C,EAC7C,oDAAoD,EACpD,sEAAsE,CACvE;EACH;EAEA;;;EAGAC,qBAAqBA,CAACH,GAAW;IAC/B,OAAOA,GAAG,CAACI,QAAQ,CAAC,WAAW,CAAC;EAClC;EAEA;;;EAGAC,sBAAsBA,CAACL,GAAW;IAChC,OAAOA,GAAG,CAACI,QAAQ,CAAC,YAAY,CAAC;EACnC;;;;+GAvsEWjF,mBAAmB,IAAAmF,iBAAA,IAAnBnF,mBAAmB;IAAA;EAAA;;WAAnBA,mBAAmB;IAAAoF,OAAA,EAAnBpF,mBAAmB,CAAAqF,IAAA;IAAAC,UAAA,EAFlB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}