{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/authentication.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = () => [\"/developer/dashboards\"];\nfunction DeveloperRegistrationStepperComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function DeveloperRegistrationStepperComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStep());\n    });\n    i0.ɵɵtext(1, \" Back to previous step \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_12_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"fullName\"), \" \");\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_12_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"email_phone\"), \" \");\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_12_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_12_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sending...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_12_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Send Verification Code\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"h3\", 14);\n    i0.ɵɵtext(2, \"Enter Your Basic Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 15)(4, \"label\", 16);\n    i0.ɵɵelement(5, \"i\", 17);\n    i0.ɵɵtext(6, \" Company Name \");\n    i0.ɵɵelement(7, \"span\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 19);\n    i0.ɵɵlistener(\"blur\", function DeveloperRegistrationStepperComponent_div_12_Template_input_blur_8_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"fullName\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, DeveloperRegistrationStepperComponent_div_12_div_9_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 15)(11, \"label\", 21);\n    i0.ɵɵelement(12, \"i\", 22);\n    i0.ɵɵtext(13, \" Company email or phone number \");\n    i0.ɵɵelement(14, \"span\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 23);\n    i0.ɵɵlistener(\"blur\", function DeveloperRegistrationStepperComponent_div_12_Template_input_blur_15_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"email_phone\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, DeveloperRegistrationStepperComponent_div_12_div_16_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function DeveloperRegistrationStepperComponent_div_12_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleNextStepAndSendCode());\n    });\n    i0.ɵɵtemplate(18, DeveloperRegistrationStepperComponent_div_12_span_18_Template, 1, 0, \"span\", 25)(19, DeveloperRegistrationStepperComponent_div_12_span_19_Template, 2, 0, \"span\", 26)(20, DeveloperRegistrationStepperComponent_div_12_span_20_Template, 2, 0, \"span\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 27);\n    i0.ɵɵtext(22, \" Need help? \");\n    i0.ɵɵelementStart(23, \"span\", 28);\n    i0.ɵɵtext(24, \"Contact us\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"fullName\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"fullName\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"email_phone\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"email_phone\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"loading\", ctx_r1.isLoadingSendOtp);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isStep1Valid() || ctx_r1.isLoadingSendOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingSendOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingSendOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoadingSendOtp);\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_13_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"input\", 39);\n    i0.ɵɵlistener(\"input\", function DeveloperRegistrationStepperComponent_div_13_div_5_Template_input_input_1_listener($event) {\n      const i_r6 = i0.ɵɵrestoreView(_r5).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.autoFocusNext($event, i_r6));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", i_r6);\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_13_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1, \" Resend in \");\n    i0.ɵɵelementStart(2, \"span\", 41);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" 0:\", ctx_r1.countdown < 10 ? \"0\" + ctx_r1.countdown : ctx_r1.countdown, \" \");\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_13_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function DeveloperRegistrationStepperComponent_div_13_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onResendCode());\n    });\n    i0.ɵɵtext(1, \" Resend Code \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_13_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.otpErrorMessage, \" \");\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_13_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_13_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Verifying...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_13_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Verification Code - Next\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"h3\", 14);\n    i0.ɵɵtext(2, \"Enter Verification Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31)(4, \"div\", 32);\n    i0.ɵɵtemplate(5, DeveloperRegistrationStepperComponent_div_13_div_5_Template, 2, 1, \"div\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 34);\n    i0.ɵɵtemplate(7, DeveloperRegistrationStepperComponent_div_13_span_7_Template, 4, 1, \"span\", 35)(8, DeveloperRegistrationStepperComponent_div_13_button_8_Template, 2, 0, \"button\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, DeveloperRegistrationStepperComponent_div_13_div_9_Template, 2, 1, \"div\", 37);\n    i0.ɵɵelementStart(10, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function DeveloperRegistrationStepperComponent_div_13_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.checkOTP());\n    });\n    i0.ɵɵtemplate(11, DeveloperRegistrationStepperComponent_div_13_span_11_Template, 1, 0, \"span\", 25)(12, DeveloperRegistrationStepperComponent_div_13_span_12_Template, 2, 0, \"span\", 26)(13, DeveloperRegistrationStepperComponent_div_13_span_13_Template, 2, 0, \"span\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 27);\n    i0.ɵɵtext(15, \" Need help? \");\n    i0.ɵɵelementStart(16, \"span\", 28);\n    i0.ɵɵtext(17, \"Contact us\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.verificationCodeControls);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showResendButton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showResendButton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.otpErrorMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"loading\", ctx_r1.isLoadingCheckOtp);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isStep2Valid() || ctx_r1.isLoadingCheckOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingCheckOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingCheckOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoadingCheckOtp);\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_14_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"image\"), \" \");\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_14_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"commercialRegistryImage\"), \" \");\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_14_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileCount(\"taxCardImage\"), \" \");\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"h3\", 14);\n    i0.ɵɵtext(2, \"Please Upload Required Documents\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 44)(4, \"p\", 45);\n    i0.ɵɵtext(5, \" You can upload the required documents now or skip and add them later when you first use the required services \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 46)(7, \"div\", 47)(8, \"label\", 48)(9, \"div\", 49);\n    i0.ɵɵelement(10, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 51);\n    i0.ɵɵtext(12, \" Company logo image for account \");\n    i0.ɵɵtemplate(13, DeveloperRegistrationStepperComponent_div_14_span_13_Template, 2, 1, \"span\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 53);\n    i0.ɵɵtext(15, \"PNG, JPG\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"input\", 54);\n    i0.ɵɵlistener(\"change\", function DeveloperRegistrationStepperComponent_div_14_Template_input_change_16_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"image\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 47)(18, \"label\", 55)(19, \"div\", 49);\n    i0.ɵɵelement(20, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 51);\n    i0.ɵɵtext(22, \" Commercial register photo \");\n    i0.ɵɵtemplate(23, DeveloperRegistrationStepperComponent_div_14_span_23_Template, 2, 1, \"span\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 53);\n    i0.ɵɵtext(25, \"PNG, JPG, PDF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"input\", 56);\n    i0.ɵɵlistener(\"change\", function DeveloperRegistrationStepperComponent_div_14_Template_input_change_26_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"commercialRegistryImage\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 47)(28, \"label\", 57)(29, \"div\", 49);\n    i0.ɵɵelement(30, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 51);\n    i0.ɵɵtext(32, \" Tax card image \");\n    i0.ɵɵtemplate(33, DeveloperRegistrationStepperComponent_div_14_span_33_Template, 2, 1, \"span\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 53);\n    i0.ɵɵtext(35, \"PNG, JPG, PDF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"input\", 58);\n    i0.ɵɵlistener(\"change\", function DeveloperRegistrationStepperComponent_div_14_Template_input_change_36_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileChange($event, \"taxCardImage\"));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(37, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function DeveloperRegistrationStepperComponent_div_14_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(38, \" Upload Documents \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function DeveloperRegistrationStepperComponent_div_14_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtext(40, \" Skip and return later \");\n    i0.ɵɵelement(41, \"i\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 27);\n    i0.ɵɵtext(43, \" Need help? \");\n    i0.ɵɵelementStart(44, \"span\", 28);\n    i0.ɵɵtext(45, \"Contact us\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"image\") > 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"commercialRegistryImage\") > 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileCount(\"taxCardImage\") > 0);\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_15_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"phone\"), \" \");\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_15_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"email\"), \" \");\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_15_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"password\"), \" \");\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_15_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"password_confirmation\"), \" \");\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_15_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFormError(), \" \");\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_15_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"agreeTerms\"), \" \");\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_15_span_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_15_span_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Creating Account...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_15_span_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Create Account\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"h3\", 14);\n    i0.ɵɵtext(2, \"Enter Your Account Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 15)(4, \"label\", 63);\n    i0.ɵɵelement(5, \"i\", 22);\n    i0.ɵɵtext(6, \" Phone \");\n    i0.ɵɵelement(7, \"span\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 64);\n    i0.ɵɵtemplate(9, DeveloperRegistrationStepperComponent_div_15_div_9_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 15)(11, \"label\", 65);\n    i0.ɵɵelement(12, \"i\", 66);\n    i0.ɵɵtext(13, \" Email \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 67);\n    i0.ɵɵtemplate(15, DeveloperRegistrationStepperComponent_div_15_div_15_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 15)(17, \"label\", 68);\n    i0.ɵɵelement(18, \"i\", 69);\n    i0.ɵɵtext(19, \" Password \");\n    i0.ɵɵelement(20, \"span\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 70);\n    i0.ɵɵelement(22, \"input\", 71);\n    i0.ɵɵelementStart(23, \"i\", 72);\n    i0.ɵɵlistener(\"click\", function DeveloperRegistrationStepperComponent_div_15_Template_i_click_23_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.togglePasswordVisibility());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, DeveloperRegistrationStepperComponent_div_15_div_24_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 15)(26, \"label\", 73);\n    i0.ɵɵelement(27, \"i\", 69);\n    i0.ɵɵtext(28, \" Confirm Password \");\n    i0.ɵɵelement(29, \"span\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 70);\n    i0.ɵɵelement(31, \"input\", 74);\n    i0.ɵɵelementStart(32, \"i\", 72);\n    i0.ɵɵlistener(\"click\", function DeveloperRegistrationStepperComponent_div_15_Template_i_click_32_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleConfirmPasswordVisibility());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(33, DeveloperRegistrationStepperComponent_div_15_div_33_Template, 2, 1, \"div\", 20)(34, DeveloperRegistrationStepperComponent_div_15_div_34_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 15)(36, \"div\", 75);\n    i0.ɵɵelement(37, \"input\", 76);\n    i0.ɵɵelementStart(38, \"label\", 77);\n    i0.ɵɵtext(39, \" I agree to the Terms and Conditions \");\n    i0.ɵɵelement(40, \"span\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(41, DeveloperRegistrationStepperComponent_div_15_div_41_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function DeveloperRegistrationStepperComponent_div_15_Template_button_click_42_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.createAccount());\n    });\n    i0.ɵɵtemplate(43, DeveloperRegistrationStepperComponent_div_15_span_43_Template, 1, 0, \"span\", 25)(44, DeveloperRegistrationStepperComponent_div_15_span_44_Template, 2, 0, \"span\", 26)(45, DeveloperRegistrationStepperComponent_div_15_span_45_Template, 2, 0, \"span\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 27);\n    i0.ɵɵtext(47, \" Need help? \");\n    i0.ɵɵelementStart(48, \"span\", 28);\n    i0.ɵɵtext(49, \"Contact us\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"phone\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"phone\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"email\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"email\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"password\"));\n    i0.ɵɵproperty(\"type\", ctx_r1.showPassword ? \"text\" : \"password\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.showPassword ? \"ki-outline ki-eye\" : \"ki-outline ki-eye-slash\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"password\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"password_confirmation\") || ctx_r1.getFormError());\n    i0.ɵɵproperty(\"type\", ctx_r1.showConfirmPassword ? \"text\" : \"password\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.showConfirmPassword ? \"ki-outline ki-eye\" : \"ki-outline ki-eye-slash\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"password_confirmation\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFormError());\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"agreeTerms\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"agreeTerms\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"loading\", ctx_r1.isLoadingCreateAccount);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isStep4Valid() || ctx_r1.isLoadingCreateAccount);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingCreateAccount);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingCreateAccount);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoadingCreateAccount);\n  }\n}\nfunction DeveloperRegistrationStepperComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79)(2, \"div\", 80);\n    i0.ɵɵelement(3, \"i\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 82);\n    i0.ɵɵtext(5, \"Registration Successful\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 83);\n    i0.ɵɵtext(7, \" Your account has been successfully created. You can now enjoy the various and amazing services provided by Easy Deal through the website or dashboard. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 84);\n    i0.ɵɵelement(9, \"img\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 86);\n    i0.ɵɵtext(11, \" Go to Website \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 87)(13, \"span\", 88);\n    i0.ɵɵtext(14, \"Learn all about your account and how to get started\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nexport class DeveloperRegistrationStepperComponent {\n  fb;\n  authenticationService;\n  cd;\n  onBack = new EventEmitter();\n  onComplete = new EventEmitter();\n  registrationForm;\n  currentStep = 1;\n  totalSteps = 5;\n  isLoading = false;\n  isLoadingSendOtp = false;\n  isLoadingCheckOtp = false;\n  isLoadingCreateAccount = false;\n  otpErrorMessage = '';\n  uploadedFiles = {};\n  verificationDigits = ['', '', '', '', ''];\n  countdown = 30;\n  showResendButton = false;\n  showPassword = false;\n  showConfirmPassword = false;\n  // Validators\n  static noNumbers = Validators.pattern(/^[^0-9]*$/);\n  static emailOrPhonePattern = Validators.pattern(/^([^\\s@]+@[^\\s@]+\\.[^\\s@]+|01[0125]\\d{8}|05\\d{8}|\\+201[0125]\\d{8}|\\+9665\\d{8})$/);\n  static isEmail(value) {\n    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value);\n  }\n  constructor(fb, authenticationService, cd) {\n    this.fb = fb;\n    this.authenticationService = authenticationService;\n    this.cd = cd;\n    this.registrationForm = this.createForm();\n  }\n  ngOnInit() {}\n  createForm() {\n    return this.fb.group({\n      // Step 1: Company Name, Email/Phone\n      fullName: ['', [Validators.required, Validators.minLength(2), DeveloperRegistrationStepperComponent.noNumbers]],\n      email_phone: ['', [Validators.required, DeveloperRegistrationStepperComponent.emailOrPhonePattern]],\n      // Step 2: Verification Code\n      verificationCode: this.fb.array(Array(5).fill('').map(() => this.fb.control('', [Validators.required, Validators.pattern('[0-9]')]))),\n      // Step 4: Phone, Email, Password, Terms\n      phone: ['', [Validators.required, DeveloperRegistrationStepperComponent.emailOrPhonePattern]],\n      email: ['', [Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8), Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/)]],\n      password_confirmation: ['', [Validators.required]],\n      agreeTerms: [false, [Validators.requiredTrue]]\n    });\n  }\n  // Helper methods for validation\n  isFieldInvalid(fieldName) {\n    const field = this.registrationForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  markFieldAsTouched(fieldName) {\n    this.registrationForm.get(fieldName)?.markAsTouched();\n  }\n  getFieldError(fieldName) {\n    const field = this.registrationForm.get(fieldName);\n    if (!field?.errors) return '';\n    const errors = field.errors;\n    if (errors['required']) return 'This field is required';\n    if (errors['pattern'] && fieldName === 'fullName') return 'Company name cannot contain numbers';\n    if (errors['pattern'] && fieldName === 'email_phone') return 'Enter valid email or phone number';\n    if (errors['pattern'] && fieldName === 'phone') return 'Enter valid phone number';\n    if (errors['pattern'] && fieldName === 'password') return 'Need uppercase, lowercase & number';\n    if (errors['email']) return 'Enter valid email';\n    if (errors['minlength']) return `Min ${errors['minlength'].requiredLength} chars`;\n    return 'Invalid input';\n  }\n  getFormError() {\n    const password = this.registrationForm.get('password')?.value;\n    const confirmPassword = this.registrationForm.get('password_confirmation')?.value;\n    if (password && confirmPassword && password !== confirmPassword) {\n      return 'Passwords do not match';\n    }\n    return '';\n  }\n  // Check if step is valid\n  isStep1Valid() {\n    const fullName = this.registrationForm.get('fullName');\n    const emailPhone = this.registrationForm.get('email_phone');\n    return !!(fullName?.valid && emailPhone?.valid);\n  }\n  isStep2Valid() {\n    const verificationCode = this.registrationForm.get('verificationCode');\n    return verificationCode.valid;\n  }\n  isStep4Valid() {\n    const phone = this.registrationForm.get('phone');\n    const email = this.registrationForm.get('email');\n    const password = this.registrationForm.get('password');\n    const passwordConfirmation = this.registrationForm.get('password_confirmation');\n    const agreeTerms = this.registrationForm.get('agreeTerms');\n    // Check if passwords match\n    const passwordsMatch = password?.value === passwordConfirmation?.value;\n    return !!(phone?.valid && email?.valid && password?.valid && passwordConfirmation?.valid && agreeTerms?.valid && passwordsMatch);\n  }\n  nextStep() {\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    } else {\n      this.onBack.emit();\n    }\n  }\n  get verificationCodeControls() {\n    return this.registrationForm.get('verificationCode').controls;\n  }\n  onDigitInput(index) {\n    const code = this.verificationDigits.join('');\n    this.registrationForm.patchValue({\n      verificationCode: code\n    });\n  }\n  handleNextStepAndSendCode() {\n    this.sendVerificationCode(true);\n  }\n  sendVerificationCode(moveToNextStep = false) {\n    this.isLoadingSendOtp = true;\n    const input = this.registrationForm.get('email_phone')?.value?.trim();\n    let params = {};\n    if (DeveloperRegistrationStepperComponent.isEmail(input)) {\n      params.email = input;\n    } else {\n      params.phone = input;\n    }\n    console.log(params);\n    this.authenticationService.sendOtp(params).subscribe(response => {\n      console.log('OTP sent:', response);\n      this.isLoadingSendOtp = false;\n      this.startCountdown();\n      // Only move to next step if requested\n      if (moveToNextStep) {\n        this.nextStep();\n      }\n      this.cd.markForCheck();\n    }, error => {\n      console.error('Failed to send OTP:', error);\n      this.isLoadingSendOtp = false;\n      this.cd.markForCheck();\n    });\n  }\n  checkOTP() {\n    this.isLoadingCheckOtp = true;\n    const input = this.registrationForm.get('email_phone')?.value?.trim();\n    const codeArray = this.registrationForm.get('verificationCode')?.value;\n    const otp = codeArray.join('');\n    let params = {};\n    if (DeveloperRegistrationStepperComponent.isEmail(input)) {\n      params.email = input;\n    } else {\n      params.phone = input;\n    }\n    params.otp = otp;\n    console.log('Checking OTP with params:', params);\n    this.authenticationService.checkOtp(params).subscribe({\n      next: response => {\n        console.log('OTP checked successfully:', response);\n        this.isLoadingCheckOtp = false;\n        this.otpErrorMessage = '';\n        this.cd.markForCheck();\n        this.nextStep();\n      },\n      error: error => {\n        console.error('Failed to check OTP:', error);\n        this.isLoadingCheckOtp = false;\n        this.otpErrorMessage = error?.error?.message || 'Invalid verification code';\n        this.clearOtpInputs();\n        this.cd.markForCheck();\n      }\n    });\n  }\n  clearOtpInputs() {\n    this.verificationDigits = ['', '', '', '', ''];\n    const verificationCodeArray = this.registrationForm.get('verificationCode');\n    verificationCodeArray.controls.forEach(control => control.setValue(''));\n  }\n  createAccount() {\n    this.isLoadingCreateAccount = true;\n    const emailValue = this.registrationForm.get('email')?.value?.trim();\n    let params = this.registrationForm.value;\n    params.role = 'developer';\n    // Handle optional email field - only include if it has a value\n    if (emailValue && emailValue.length > 0) {\n      params.email = emailValue;\n    } else {\n      // Remove email from params if it's empty\n      delete params.email;\n    }\n    const formData = new FormData();\n    // Append all form fields\n    for (const key in params) {\n      if (params.hasOwnProperty(key)) {\n        formData.append(key, params[key]);\n      }\n    }\n    // Append uploaded files (if any)\n    for (const fileType in this.uploadedFiles) {\n      const files = this.uploadedFiles[fileType];\n      if (files?.length) {\n        formData.append(fileType, files[0]);\n      }\n    }\n    this.authenticationService.register(formData).subscribe({\n      next: response => {\n        this.isLoadingCreateAccount = false;\n        let user = response.data;\n        localStorage.setItem('authToken', user.authToken);\n        this.authenticationService.setCurrentUser(response.data);\n        this.cd.markForCheck();\n        this.nextStep();\n      },\n      error: error => {\n        this.isLoadingCreateAccount = false;\n        let errorMessage = 'Failed to create account. Please try again.';\n        if (error?.error?.message) {\n          errorMessage = error.error.message;\n        } else if (error?.message) {\n          errorMessage = error.message;\n        }\n        Swal.fire(errorMessage);\n        this.cd.markForCheck();\n      }\n    });\n  }\n  startCountdown() {\n    this.showResendButton = false;\n    this.countdown = 30;\n    const intervalId = setInterval(() => {\n      this.countdown--;\n      if (this.countdown === 0) {\n        clearInterval(intervalId);\n        this.showResendButton = true;\n      }\n      this.cd.markForCheck();\n    }, 1000);\n  }\n  autoFocusNext(event, index) {\n    const input = event.target;\n    if (input.value && index < 5) {\n      const nextInput = input.parentElement?.nextElementSibling?.querySelector('input');\n      nextInput?.focus();\n    }\n  }\n  onResendCode() {\n    this.sendVerificationCode(false);\n  }\n  // Toggle password visibility\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  toggleConfirmPasswordVisibility() {\n    this.showConfirmPassword = !this.showConfirmPassword;\n  }\n  // File management\n  onFileChange(event, fileType) {\n    const files = event.target.files;\n    if (!files?.length) return;\n    this.uploadedFiles[fileType] = [...(this.uploadedFiles[fileType] || []), ...Array.from(files)];\n    console.log(`Uploaded ${files.length} file(s) for ${fileType}`);\n  }\n  getFileCount(fileType) {\n    return this.uploadedFiles[fileType]?.length || 0;\n  }\n  static ɵfac = function DeveloperRegistrationStepperComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DeveloperRegistrationStepperComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthenticationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DeveloperRegistrationStepperComponent,\n    selectors: [[\"app-developer-registration-stepper\"]],\n    outputs: {\n      onBack: \"onBack\",\n      onComplete: \"onComplete\"\n    },\n    decls: 17,\n    vars: 11,\n    consts: [[1, \"client-registration-stepper\"], [1, \"stepper-header\"], [1, \"stepper-title\"], [1, \"stepper-progress\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"d-flex\"], [1, \"progress-text\"], [\"type\", \"button\", \"class\", \"back-to-previous\", 3, \"click\", 4, \"ngIf\"], [1, \"stepper-form\", 3, \"formGroup\"], [\"class\", \"step-content\", 4, \"ngIf\"], [\"class\", \"step-content success-step\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"back-to-previous\", 3, \"click\"], [1, \"step-content\"], [1, \"step-title\"], [1, \"form-group\"], [\"for\", \"fullName\", 1, \"form-label\"], [1, \"ki-outline\", \"ki-office-bag\"], [1, \"required\"], [\"type\", \"text\", \"id\", \"fullName\", \"formControlName\", \"fullName\", \"placeholder\", \"Real Estate Development Company\", \"pattern\", \"[^0-9]*\", \"title\", \"Company name cannot contain numbers\", \"required\", \"\", 1, \"form-control\", 3, \"blur\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"form-label\"], [1, \"ki-outline\", \"ki-phone\"], [\"type\", \"text\", \"formControlName\", \"email_phone\", \"placeholder\", \"<EMAIL> or 01xxxxxxxxx\", \"title\", \"Enter a valid email address or phone number\", \"autocomplete\", \"email tel\", \"required\", \"\", 1, \"form-control\", 3, \"blur\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-verification\", 3, \"click\", \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"help-text\"], [1, \"contact-link\"], [1, \"invalid-feedback\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"verification-code-section\"], [\"formArrayName\", \"verificationCode\", 1, \"verification-inputs\"], [\"class\", \"code-input\", 4, \"ngFor\", \"ngForOf\"], [1, \"countdown-section\"], [\"class\", \"countdown-text\", 4, \"ngIf\"], [\"class\", \"btn btn-link\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"alert alert-danger mt-3\", \"role\", \"alert\", 4, \"ngIf\"], [1, \"code-input\"], [\"type\", \"text\", \"maxlength\", \"1\", 1, \"verification-input\", 3, \"input\", \"formControlName\"], [1, \"countdown-text\"], [1, \"countdown-timer\"], [1, \"btn\", \"btn-link\", 3, \"click\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"mt-3\"], [1, \"documents-section\"], [1, \"documents-description\"], [1, \"upload-card-container\"], [1, \"card\", \"mb-3\", \"cursor-pointer\"], [\"for\", \"image\", 1, \"card-body\", \"text-center\", \"py-2\"], [1, \"upload-icon\", \"cursor-pointer\"], [1, \"fas\", \"fa-arrow-up\"], [1, \"upload-text\", \"cursor-pointer\"], [\"class\", \"badge bg-success ms-2\", 4, \"ngIf\"], [1, \"upload-subtitle\"], [\"type\", \"file\", \"id\", \"image\", \"accept\", \".png,.jpg,.jpeg\", 1, \"d-none\", 3, \"change\"], [\"for\", \"commercialRegistryImage\", 1, \"card-body\", \"text-center\", \"py-2\"], [\"type\", \"file\", \"id\", \"commercialRegistryImage\", \"accept\", \".png,.jpg,.jpeg,.pdf\", \"multiple\", \"\", 1, \"d-none\", 3, \"change\"], [\"for\", \"taxCardImage\", 1, \"card-body\", \"text-center\", \"py-2\"], [\"type\", \"file\", \"id\", \"taxCardImage\", \"accept\", \".png,.jpg,.jpeg,.pdf\", \"multiple\", \"\", 1, \"d-none\", 3, \"change\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-verification\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-link\", \"skip-button\", 3, \"click\"], [1, \"ki-outline\", \"ki-arrow-right\"], [1, \"badge\", \"bg-success\", \"ms-2\"], [\"for\", \"phone\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phone\", \"formControlName\", \"phone\", \"placeholder\", \"01xxxxxxxxx\", \"required\", \"\", \"autocomplete\", \"tel\", 1, \"form-control\"], [\"for\", \"email\", 1, \"form-label\"], [1, \"ki-outline\", \"ki-user\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>..\", \"autocomplete\", \"email\", 1, \"form-control\"], [\"for\", \"password\", 1, \"form-label\"], [1, \"ki-outline\", \"ki-lock\"], [1, \"password-input-container\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"********\", \"minlength\", \"8\", \"pattern\", \"^(?=.*[a-z])(?=.*[A-Z])(?=.*\\\\d).{8,}$\", \"title\", \"Password must be at least 8 characters with uppercase, lowercase and number\", \"required\", \"\", \"autocomplete\", \"new-password\", 1, \"form-control\", 3, \"type\"], [1, \"password-toggle-icon\", 3, \"click\"], [\"for\", \"confirmPassword\", 1, \"form-label\"], [\"id\", \"confirmPassword\", \"formControlName\", \"password_confirmation\", \"placeholder\", \"********\", \"required\", \"\", \"autocomplete\", \"new-password\", 1, \"form-control\", 3, \"type\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"agreeTerms\", \"formControlName\", \"agreeTerms\", 1, \"form-check-input\"], [\"for\", \"agreeTerms\", 1, \"form-check-label\"], [1, \"step-content\", \"success-step\"], [1, \"success-content\"], [1, \"success-icon\"], [1, \"ki-outline\", \"ki-check-circle\"], [1, \"success-title\"], [1, \"success-message\"], [1, \"success-illustration\"], [\"src\", \"assets/media/login/successfully.png\", \"alt\", \"Success\", 1, \"success-image\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-success-action\", 3, \"routerLink\"], [1, \"additional-info\"], [1, \"info-link\"]],\n    template: function DeveloperRegistrationStepperComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n        i0.ɵɵtext(3, \"Developer Registration\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4);\n        i0.ɵɵelement(6, \"div\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 6)(8, \"span\", 7);\n        i0.ɵɵtext(9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(10, DeveloperRegistrationStepperComponent_button_10_Template, 2, 0, \"button\", 8);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(11, \"form\", 9);\n        i0.ɵɵtemplate(12, DeveloperRegistrationStepperComponent_div_12_Template, 25, 12, \"div\", 10)(13, DeveloperRegistrationStepperComponent_div_13_Template, 18, 10, \"div\", 10)(14, DeveloperRegistrationStepperComponent_div_14_Template, 46, 3, \"div\", 10)(15, DeveloperRegistrationStepperComponent_div_15_Template, 50, 28, \"div\", 10)(16, DeveloperRegistrationStepperComponent_div_16_Template, 15, 2, \"div\", 11);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵstyleProp(\"width\", ctx.currentStep / ctx.totalSteps * 100, \"%\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate2(\"Step \", ctx.currentStep, \" of \", ctx.totalSteps, \"\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 0 && ctx.currentStep < 5);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"formGroup\", ctx.registrationForm);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 5);\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MinLengthValidator, i1.MaxLengthValidator, i1.PatternValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormArrayName, i4.RouterLink],\n    styles: [\".required[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  font-weight: bold;\\n  margin-left: 2px;\\n}\\n\\n.client-registration-stepper[_ngcontent-%COMP%] {\\n  width: 100%;\\n  direction: ltr;\\n  text-align: left;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-title[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  margin-bottom: 15px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 6px;\\n  background-color: #e9ecef;\\n  border-radius: 3px;\\n  overflow: hidden;\\n  margin-bottom: 10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #28a745 0%, #20c997 100%);\\n  border-radius: 3px;\\n  transition: width 0.3s ease;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 5px;\\n  margin-top: 5px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  margin: 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .back-to-previous[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #007bff;\\n  font-size: 0.85rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  padding: 3px 0;\\n  text-decoration: none;\\n  transition: color 0.3s ease;\\n  white-space: nowrap;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .back-to-previous[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n  text-decoration: underline;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .back-to-previous[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  min-height: 50px;\\n  margin-bottom: 5px;\\n  text-align: center;\\n  padding: 2px 5px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.success-step[_ngcontent-%COMP%] {\\n  min-height: 50px;\\n  padding: 5px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n  color: #232176;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n  text-align: center;\\n  padding-bottom: 3px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n  margin: 0 -10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n  padding: 0 10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin-bottom: 6px;\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 0.9rem;\\n  text-align: left;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 1rem;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  transition: all 0.3s ease;\\n  background-color: #fff;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .invalid-feedback[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #dc3545;\\n  font-size: 12px;\\n  margin-top: 5px;\\n  font-weight: 500;\\n  text-align: left;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in;\\n}\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-5px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .password-input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .password-input-container[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  padding-right: 45px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .password-input-container[_ngcontent-%COMP%]   .password-toggle-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  cursor: pointer;\\n  color: #6c757d;\\n  font-size: 16px;\\n  z-index: 10;\\n  transition: color 0.2s ease;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .password-input-container[_ngcontent-%COMP%]   .password-toggle-icon[_ngcontent-%COMP%]:hover {\\n  color: #495057;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%] {\\n  margin: 15px 0;\\n  text-align: center;\\n  padding: 0 5px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 10px;\\n  margin-bottom: 12px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  text-align: center;\\n  font-size: 1.5rem;\\n  font-weight: bold;\\n  border: 2px solid #ddd;\\n  border-radius: 8px;\\n  background-color: #fff;\\n  color: #333;\\n  transition: all 0.3s ease;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%]::-webkit-outer-spin-button, .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%]::-webkit-inner-spin-button {\\n  -webkit-appearance: none;\\n  margin: 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[type=number][_ngcontent-%COMP%] {\\n  -moz-appearance: textfield;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .countdown-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 12px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .countdown-section[_ngcontent-%COMP%]   .countdown-text[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 13px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .countdown-section[_ngcontent-%COMP%]   .countdown-text[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-weight: bold;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .documents-description[_ngcontent-%COMP%] {\\n  color: #34A853;\\n  font-size: 13px;\\n  line-height: 1.4;\\n  margin-bottom: 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%] {\\n  margin-bottom: 6px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  border: 2px dashed #e9ecef;\\n  border-radius: 6px;\\n  background: #f8f9fa;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover {\\n  border-color: #4c63d2;\\n  background: #f0f4ff;\\n  box-shadow: 0 2px 8px rgba(76, 99, 210, 0.15);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover:hover {\\n  transform: translateY(-1px);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #4c63d2;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .upload-text[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #333;\\n  font-size: 12px;\\n  font-weight: 600;\\n  margin-bottom: 3px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .upload-text[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  padding: 2px 6px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .upload-card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .upload-subtitle[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 10px;\\n  font-weight: 400;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .document-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 8px 12px;\\n  margin-bottom: 8px;\\n  border: 1px solid #e9ecef;\\n  border-radius: 6px;\\n  background: #f8f9fa;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .document-item[_ngcontent-%COMP%]   .document-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .document-item[_ngcontent-%COMP%]   .document-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-size: 1rem;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .document-item[_ngcontent-%COMP%]   .document-header[_ngcontent-%COMP%]   .document-title[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 500;\\n  font-size: 13px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .document-item[_ngcontent-%COMP%]   .document-info[_ngcontent-%COMP%]   .document-subtitle[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 11px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .skip-button[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  background: none;\\n  color: #007bff;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin: 12px auto 0 auto;\\n  text-decoration: none;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  gap: 10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .skip-button[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n  text-decoration: none;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .skip-button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  box-shadow: none;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .documents-section[_ngcontent-%COMP%]   .skip-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin-left: 4px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .progress-bar-container[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .progress-bar-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 5px;\\n  height: 8px;\\n  background: #e9ecef;\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .progress-bar-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #28a745;\\n  border-radius: 2px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  margin-top: 12px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-top: 3px;\\n  width: 18px;\\n  height: 18px;\\n  cursor: pointer;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  color: #333;\\n  cursor: pointer;\\n  line-height: 1.4;\\n  text-align: left;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .terms-link[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .terms-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 30px;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);\\n  border-radius: 12px;\\n  border: 1px solid #d4edda;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #28a745;\\n  margin-bottom: 15px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #155724;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  margin-bottom: 10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #155724;\\n  font-size: 0.95rem;\\n  line-height: 1.5;\\n  margin: 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%] {\\n  margin-top: 25px;\\n  padding: 20px;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  border: 1px solid #e9ecef;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin-bottom: 15px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  margin-bottom: 10px;\\n  font-size: 0.9rem;\\n  color: #555;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 1rem;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 15px;\\n  margin-top: 30px;\\n  flex-direction: row;\\n  padding-top: 20px;\\n  border-top: 1px solid #e9ecef;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  padding: 12px 24px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  min-width: 120px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover:hover {\\n  transform: translateY(-1px);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  color: white;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover:hover {\\n  transform: translateY(-1px);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%]:hover:not(:disabled):hover {\\n  transform: translateY(-1px);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 10px 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%] {\\n  max-width: 320px;\\n  margin: 0 auto;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: #28a745;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-title[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin-bottom: 8px;\\n  direction: ltr;\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.8rem;\\n  line-height: 1.3;\\n  margin-bottom: 12px;\\n  direction: ltr;\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-illustration[_ngcontent-%COMP%] {\\n  margin: 10px 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-illustration[_ngcontent-%COMP%]   .success-image[_ngcontent-%COMP%] {\\n  max-width: 180px;\\n  width: 100%;\\n  height: auto;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .btn-success-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4c63d2 0%, #3b4db8 100%);\\n  border: none;\\n  color: white;\\n  padding: 10px 25px;\\n  font-size: 13px;\\n  font-weight: 600;\\n  border-radius: 25px;\\n  margin-bottom: 10px;\\n  transition: all 0.3s ease;\\n  direction: ltr;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .btn-success-action[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(76, 99, 210, 0.3);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .additional-info[_ngcontent-%COMP%]   .info-link[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 12px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  text-decoration: none;\\n  direction: ltr;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .additional-info[_ngcontent-%COMP%]   .info-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  padding: 12px 15px;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n  border: 1px solid transparent;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .alert.alert-danger[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  border-color: #f5c6cb;\\n  color: #721c24;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%] {\\n  color: #28a745 !important;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  width: 100%;\\n  padding: 8px 12px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  border-radius: 20px;\\n  margin: 8px auto 6px auto;\\n  background: linear-gradient(135deg, #4c63d2 0%, #3b4db8 100%);\\n  color: white;\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  box-shadow: 0 4px 12px rgba(76, 99, 210, 0.3);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%]:hover:not(:disabled):hover {\\n  transform: translateY(-1px);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 8px;\\n  color: #6c757d;\\n  font-size: 12px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  cursor: pointer;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-title[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n    min-height: auto;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 15px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%] {\\n    gap: 10px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n    font-size: 20px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .work-scope-section[_ngcontent-%COMP%]   .work-scope-options[_ngcontent-%COMP%] {\\n    gap: 15px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .work-scope-section[_ngcontent-%COMP%]   .work-scope-options[_ngcontent-%COMP%]   .scope-option[_ngcontent-%COMP%]   .scope-label[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    min-width: auto;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-title[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: 10px 12px;\\n    font-size: 13px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%] {\\n    gap: 8px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n    font-size: 18px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵlistener", "DeveloperRegistrationStepperComponent_button_10_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "previousStep", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "getFieldError", "ɵɵelement", "DeveloperRegistrationStepperComponent_div_12_Template_input_blur_8_listener", "_r3", "mark<PERSON>ieldAsTouched", "ɵɵtemplate", "DeveloperRegistrationStepperComponent_div_12_div_9_Template", "DeveloperRegistrationStepperComponent_div_12_Template_input_blur_15_listener", "DeveloperRegistrationStepperComponent_div_12_div_16_Template", "DeveloperRegistrationStepperComponent_div_12_Template_button_click_17_listener", "handleNextStepAndSendCode", "DeveloperRegistrationStepperComponent_div_12_span_18_Template", "DeveloperRegistrationStepperComponent_div_12_span_19_Template", "DeveloperRegistrationStepperComponent_div_12_span_20_Template", "ɵɵclassProp", "isFieldInvalid", "ɵɵproperty", "isLoadingSendOtp", "isStep1Valid", "DeveloperRegistrationStepperComponent_div_13_div_5_Template_input_input_1_listener", "$event", "i_r6", "_r5", "index", "autoFocusNext", "countdown", "DeveloperRegistrationStepperComponent_div_13_button_8_Template_button_click_0_listener", "_r7", "onResendCode", "otpErrorMessage", "DeveloperRegistrationStepperComponent_div_13_div_5_Template", "DeveloperRegistrationStepperComponent_div_13_span_7_Template", "DeveloperRegistrationStepperComponent_div_13_button_8_Template", "DeveloperRegistrationStepperComponent_div_13_div_9_Template", "DeveloperRegistrationStepperComponent_div_13_Template_button_click_10_listener", "_r4", "checkOTP", "DeveloperRegistrationStepperComponent_div_13_span_11_Template", "DeveloperRegistrationStepperComponent_div_13_span_12_Template", "DeveloperRegistrationStepperComponent_div_13_span_13_Template", "verificationCodeControls", "showResendButton", "isLoadingCheckOtp", "isStep2Valid", "getFileCount", "DeveloperRegistrationStepperComponent_div_14_span_13_Template", "DeveloperRegistrationStepperComponent_div_14_Template_input_change_16_listener", "_r8", "onFileChange", "DeveloperRegistrationStepperComponent_div_14_span_23_Template", "DeveloperRegistrationStepperComponent_div_14_Template_input_change_26_listener", "DeveloperRegistrationStepperComponent_div_14_span_33_Template", "DeveloperRegistrationStepperComponent_div_14_Template_input_change_36_listener", "DeveloperRegistrationStepperComponent_div_14_Template_button_click_37_listener", "nextStep", "DeveloperRegistrationStepperComponent_div_14_Template_button_click_39_listener", "getFormError", "DeveloperRegistrationStepperComponent_div_15_div_9_Template", "DeveloperRegistrationStepperComponent_div_15_div_15_Template", "DeveloperRegistrationStepperComponent_div_15_Template_i_click_23_listener", "_r9", "togglePasswordVisibility", "DeveloperRegistrationStepperComponent_div_15_div_24_Template", "DeveloperRegistrationStepperComponent_div_15_Template_i_click_32_listener", "toggleConfirmPasswordVisibility", "DeveloperRegistrationStepperComponent_div_15_div_33_Template", "DeveloperRegistrationStepperComponent_div_15_div_34_Template", "DeveloperRegistrationStepperComponent_div_15_div_41_Template", "DeveloperRegistrationStepperComponent_div_15_Template_button_click_42_listener", "createAccount", "DeveloperRegistrationStepperComponent_div_15_span_43_Template", "DeveloperRegistrationStepperComponent_div_15_span_44_Template", "DeveloperRegistrationStepperComponent_div_15_span_45_Template", "showPassword", "ɵɵclassMap", "showConfirmPassword", "isLoadingCreateAccount", "isStep4Valid", "ɵɵpureFunction0", "_c0", "DeveloperRegistrationStepperComponent", "fb", "authenticationService", "cd", "onBack", "onComplete", "registrationForm", "currentStep", "totalSteps", "isLoading", "uploadedFiles", "verificationDigits", "noNumbers", "pattern", "emailOrPhonePattern", "isEmail", "value", "test", "constructor", "createForm", "ngOnInit", "group", "fullName", "required", "<PERSON><PERSON><PERSON><PERSON>", "email_phone", "verificationCode", "array", "Array", "fill", "map", "control", "phone", "email", "password", "password_confirmation", "agreeTerms", "requiredTrue", "fieldName", "field", "get", "invalid", "dirty", "touched", "<PERSON><PERSON><PERSON><PERSON>ched", "errors", "<PERSON><PERSON><PERSON><PERSON>", "confirmPassword", "emailPhone", "valid", "passwordConfirmation", "passwordsMatch", "emit", "controls", "onDigitInput", "code", "join", "patchValue", "sendVerificationCode", "moveToNextStep", "input", "trim", "params", "console", "log", "sendOtp", "subscribe", "response", "startCountdown", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "codeArray", "otp", "checkOtp", "next", "message", "clearOtpInputs", "verificationCodeArray", "for<PERSON>ach", "setValue", "emailValue", "role", "length", "formData", "FormData", "key", "hasOwnProperty", "append", "fileType", "files", "register", "user", "data", "localStorage", "setItem", "authToken", "setCurrentUser", "errorMessage", "fire", "intervalId", "setInterval", "clearInterval", "event", "target", "nextInput", "parentElement", "nextElement<PERSON><PERSON>ling", "querySelector", "focus", "from", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthenticationService", "ChangeDetectorRef", "selectors", "outputs", "decls", "vars", "consts", "template", "DeveloperRegistrationStepperComponent_Template", "rf", "ctx", "DeveloperRegistrationStepperComponent_button_10_Template", "DeveloperRegistrationStepperComponent_div_12_Template", "DeveloperRegistrationStepperComponent_div_13_Template", "DeveloperRegistrationStepperComponent_div_14_Template", "DeveloperRegistrationStepperComponent_div_15_Template", "DeveloperRegistrationStepperComponent_div_16_Template", "ɵɵstyleProp", "ɵɵtextInterpolate2"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\authentication\\components\\developer-registration-stepper\\developer-registration-stepper.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\authentication\\components\\developer-registration-stepper\\developer-registration-stepper.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Output,\r\n  EventEmitter,\r\n  ChangeDetectorRef, OnInit,\r\n} from '@angular/core';\r\nimport { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { DeveloperRegistrationData } from '../../models';\r\nimport { AuthenticationService } from '../../services/authentication.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-developer-registration-stepper',\r\n  templateUrl: './developer-registration-stepper.component.html',\r\n  styleUrls: ['./developer-registration-stepper.component.scss'],\r\n})\r\nexport class DeveloperRegistrationStepperComponent implements OnInit {\r\n  @Output() onBack = new EventEmitter<void>();\r\n  @Output() onComplete = new EventEmitter<DeveloperRegistrationData>();\r\n\r\n  registrationForm: FormGroup;\r\n  currentStep = 1;\r\n  totalSteps = 5;\r\n  isLoading = false;\r\n  isLoadingSendOtp: boolean = false;\r\n  isLoadingCheckOtp: boolean = false;\r\n  isLoadingCreateAccount: boolean = false;\r\n  otpErrorMessage: string = '';\r\n  uploadedFiles: { [key: string]: File[] } = {};\r\n  verificationDigits: string[] = ['', '', '', '', ''];\r\n  countdown: number = 30;\r\n  showResendButton: boolean = false;\r\n  showPassword: boolean = false;\r\n  showConfirmPassword: boolean = false;\r\n\r\n  // Validators\r\n  static noNumbers = Validators.pattern(/^[^0-9]*$/);\r\n  static emailOrPhonePattern = Validators.pattern(/^([^\\s@]+@[^\\s@]+\\.[^\\s@]+|01[0125]\\d{8}|05\\d{8}|\\+201[0125]\\d{8}|\\+9665\\d{8})$/);\r\n\r\n  private static isEmail(value: string): boolean {\r\n    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value);\r\n  }\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authenticationService: AuthenticationService,\r\n    private cd: ChangeDetectorRef\r\n  ) {\r\n    this.registrationForm = this.createForm();\r\n  }\r\n\r\n  ngOnInit(): void {}\r\n\r\n  private createForm(): FormGroup {\r\n    return this.fb.group({\r\n      // Step 1: Company Name, Email/Phone\r\n      fullName: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.minLength(2),\r\n          DeveloperRegistrationStepperComponent.noNumbers,\r\n        ],\r\n      ],\r\n      email_phone: [\r\n        '',\r\n        [Validators.required, DeveloperRegistrationStepperComponent.emailOrPhonePattern],\r\n      ],\r\n\r\n      // Step 2: Verification Code\r\n      verificationCode: this.fb.array(\r\n        Array(5)\r\n          .fill('')\r\n          .map(() =>\r\n            this.fb.control('', [\r\n              Validators.required,\r\n              Validators.pattern('[0-9]'),\r\n            ])\r\n          )\r\n      ),\r\n\r\n      // Step 4: Phone, Email, Password, Terms\r\n      phone: ['', [Validators.required, DeveloperRegistrationStepperComponent.emailOrPhonePattern]],\r\n      email: ['', [Validators.email]],\r\n      password: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.minLength(8),\r\n          Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/),\r\n        ],\r\n      ],\r\n      password_confirmation: ['', [Validators.required]],\r\n      agreeTerms: [false, [Validators.requiredTrue]],\r\n    });\r\n  }\r\n\r\n  // Helper methods for validation\r\n  isFieldInvalid(fieldName: string): boolean {\r\n    const field = this.registrationForm.get(fieldName);\r\n    return !!(field && field.invalid && (field.dirty || field.touched));\r\n  }\r\n\r\n  markFieldAsTouched(fieldName: string): void {\r\n    this.registrationForm.get(fieldName)?.markAsTouched();\r\n  }\r\n\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.registrationForm.get(fieldName);\r\n    if (!field?.errors) return '';\r\n\r\n    const errors = field.errors;\r\n    if (errors['required']) return 'This field is required';\r\n    if (errors['pattern'] && fieldName === 'fullName') return 'Company name cannot contain numbers';\r\n    if (errors['pattern'] && fieldName === 'email_phone') return 'Enter valid email or phone number';\r\n    if (errors['pattern'] && fieldName === 'phone') return 'Enter valid phone number';\r\n    if (errors['pattern'] && fieldName === 'password') return 'Need uppercase, lowercase & number';\r\n    if (errors['email']) return 'Enter valid email';\r\n    if (errors['minlength']) return `Min ${errors['minlength'].requiredLength} chars`;\r\n\r\n    return 'Invalid input';\r\n  }\r\n\r\n  getFormError(): string {\r\n    const password = this.registrationForm.get('password')?.value;\r\n    const confirmPassword = this.registrationForm.get(\r\n      'password_confirmation'\r\n    )?.value;\r\n\r\n    if (password && confirmPassword && password !== confirmPassword) {\r\n      return 'Passwords do not match';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  // Check if step is valid\r\n  isStep1Valid(): boolean {\r\n    const fullName = this.registrationForm.get('fullName');\r\n    const emailPhone = this.registrationForm.get('email_phone');\r\n\r\n    return !!(fullName?.valid && emailPhone?.valid);\r\n  }\r\n\r\n  isStep2Valid(): boolean {\r\n    const verificationCode = this.registrationForm.get(\r\n      'verificationCode'\r\n    ) as FormArray;\r\n    return verificationCode.valid;\r\n  }\r\n\r\n  isStep4Valid(): boolean {\r\n    const phone = this.registrationForm.get('phone');\r\n    const email = this.registrationForm.get('email');\r\n    const password = this.registrationForm.get('password');\r\n    const passwordConfirmation = this.registrationForm.get(\r\n      'password_confirmation'\r\n    );\r\n    const agreeTerms = this.registrationForm.get('agreeTerms');\r\n\r\n    // Check if passwords match\r\n    const passwordsMatch = password?.value === passwordConfirmation?.value;\r\n\r\n    return !!(\r\n      phone?.valid &&\r\n      email?.valid &&\r\n      password?.valid &&\r\n      passwordConfirmation?.valid &&\r\n      agreeTerms?.valid &&\r\n      passwordsMatch\r\n    );\r\n  }\r\n\r\n  nextStep(): void {\r\n    if (this.currentStep < this.totalSteps) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  previousStep(): void {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    } else {\r\n      this.onBack.emit();\r\n    }\r\n  }\r\n\r\n  get verificationCodeControls() {\r\n    return (this.registrationForm.get('verificationCode') as FormArray)\r\n      .controls;\r\n  }\r\n\r\n  onDigitInput(index: number): void {\r\n    const code = this.verificationDigits.join('');\r\n    this.registrationForm.patchValue({ verificationCode: code });\r\n  }\r\n\r\n  handleNextStepAndSendCode(): void {\r\n    this.sendVerificationCode(true);\r\n  }\r\n\r\n  sendVerificationCode(moveToNextStep: boolean = false) {\r\n    this.isLoadingSendOtp = true;\r\n    const input = this.registrationForm.get('email_phone')?.value?.trim();\r\n\r\n    let params: { email?: string; phone?: string } = {};\r\n    if (DeveloperRegistrationStepperComponent.isEmail(input)) {\r\n      params.email = input;\r\n    } else {\r\n      params.phone = input;\r\n    }\r\n\r\n    console.log(params);\r\n    this.authenticationService.sendOtp(params).subscribe(\r\n      (response: any) => {\r\n        console.log('OTP sent:', response);\r\n        this.isLoadingSendOtp = false;\r\n        this.startCountdown();\r\n\r\n        // Only move to next step if requested\r\n        if (moveToNextStep) {\r\n          this.nextStep();\r\n        }\r\n\r\n        this.cd.markForCheck();\r\n      },\r\n      (error: any) => {\r\n        console.error('Failed to send OTP:', error);\r\n        this.isLoadingSendOtp = false;\r\n        this.cd.markForCheck();\r\n      }\r\n    );\r\n  }\r\n\r\n  checkOTP() {\r\n    this.isLoadingCheckOtp = true;\r\n    const input = this.registrationForm.get('email_phone')?.value?.trim();\r\n    const codeArray = this.registrationForm.get('verificationCode')?.value;\r\n    const otp = codeArray.join('');\r\n\r\n    let params: { email?: string; phone?: string; otp?: number } = {};\r\n    if (DeveloperRegistrationStepperComponent.isEmail(input)) {\r\n      params.email = input;\r\n    } else {\r\n      params.phone = input;\r\n    }\r\n    params.otp = otp;\r\n\r\n    console.log('Checking OTP with params:', params);\r\n\r\n    this.authenticationService.checkOtp(params).subscribe({\r\n      next: (response: any) => {\r\n        console.log('OTP checked successfully:', response);\r\n        this.isLoadingCheckOtp = false;\r\n        this.otpErrorMessage = '';\r\n        this.cd.markForCheck();\r\n        this.nextStep();\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Failed to check OTP:', error);\r\n        this.isLoadingCheckOtp = false;\r\n        this.otpErrorMessage = error?.error?.message || 'Invalid verification code';\r\n         this.clearOtpInputs();\r\n        this.cd.markForCheck();\r\n      },\r\n    });\r\n  }\r\n\r\n  private clearOtpInputs() {\r\n    this.verificationDigits = ['', '', '', '', ''];\r\n    const verificationCodeArray = this.registrationForm.get(\r\n      'verificationCode'\r\n    ) as FormArray;\r\n    verificationCodeArray.controls.forEach((control) => control.setValue(''));\r\n  }\r\n\r\n  createAccount(): void {\r\n    this.isLoadingCreateAccount = true;\r\n    const emailValue = this.registrationForm.get('email')?.value?.trim();\r\n\r\n    let params = this.registrationForm.value;\r\n    params.role = 'developer';\r\n\r\n    // Handle optional email field - only include if it has a value\r\n    if (emailValue && emailValue.length > 0) {\r\n      params.email = emailValue;\r\n    } else {\r\n      // Remove email from params if it's empty\r\n      delete params.email;\r\n    }\r\n\r\n    const formData = new FormData();\r\n\r\n    // Append all form fields\r\n    for (const key in params) {\r\n      if (params.hasOwnProperty(key)) {\r\n        formData.append(key, params[key]);\r\n      }\r\n    }\r\n\r\n    // Append uploaded files (if any)\r\n    for (const fileType in this.uploadedFiles) {\r\n      const files = this.uploadedFiles[fileType];\r\n      if (files?.length) {\r\n         formData.append(fileType, files[0]);\r\n      }\r\n    }\r\n\r\n    this.authenticationService.register(formData).subscribe({\r\n      next: (response: any) => {\r\n        this.isLoadingCreateAccount = false;\r\n        let user = response.data;\r\n        localStorage.setItem('authToken', user.authToken);\r\n        this.authenticationService.setCurrentUser(response.data);\r\n        this.cd.markForCheck();\r\n        this.nextStep();\r\n      },\r\n      error: (error: any) => {\r\n        this.isLoadingCreateAccount = false;\r\n\r\n         let errorMessage = 'Failed to create account. Please try again.';\r\n        if (error?.error?.message) {\r\n          errorMessage = error.error.message;\r\n        } else if (error?.message) {\r\n          errorMessage = error.message;\r\n        }\r\n\r\n        Swal.fire(errorMessage);\r\n        this.cd.markForCheck();\r\n      },\r\n    });\r\n  }\r\n\r\n  startCountdown() {\r\n    this.showResendButton = false;\r\n    this.countdown = 30;\r\n\r\n    const intervalId = setInterval(() => {\r\n      this.countdown--;\r\n      if (this.countdown === 0) {\r\n        clearInterval(intervalId);\r\n        this.showResendButton = true;\r\n      }\r\n      this.cd.markForCheck();\r\n    }, 1000);\r\n  }\r\n\r\n  autoFocusNext(event: any, index: number): void {\r\n    const input = event.target;\r\n    if (input.value && index < 5) {\r\n      const nextInput =\r\n        input.parentElement?.nextElementSibling?.querySelector('input');\r\n      nextInput?.focus();\r\n    }\r\n  }\r\n\r\n  onResendCode() {\r\n    this.sendVerificationCode(false);\r\n  }\r\n\r\n  // Toggle password visibility\r\n  togglePasswordVisibility() {\r\n    this.showPassword = !this.showPassword;\r\n  }\r\n\r\n  toggleConfirmPasswordVisibility() {\r\n    this.showConfirmPassword = !this.showConfirmPassword;\r\n  }\r\n\r\n  // File management\r\n  onFileChange(event: Event, fileType: string): void {\r\n    const files = (event.target as HTMLInputElement).files;\r\n    if (!files?.length) return;\r\n\r\n    this.uploadedFiles[fileType] = [\r\n      ...(this.uploadedFiles[fileType] || []),\r\n      ...Array.from(files),\r\n    ];\r\n    console.log(`Uploaded ${files.length} file(s) for ${fileType}`);\r\n  }\r\n\r\n  getFileCount(fileType: string): number {\r\n    return this.uploadedFiles[fileType]?.length || 0;\r\n  }\r\n}\r\n", "<div class=\"client-registration-stepper\">\r\n  <!-- Stepper Header -->\r\n  <div class=\"stepper-header\">\r\n    <h2 class=\"stepper-title\">Developer Registration</h2>\r\n    <div class=\"stepper-progress\">\r\n      <div class=\"progress-bar\">\r\n        <div class=\"progress-fill\" [style.width.%]=\"(currentStep / totalSteps) * 100\"></div>\r\n      </div>\r\n      <div class=\"d-flex\">\r\n        <span class=\"progress-text\">Step {{ currentStep }} of {{ totalSteps }}</span>\r\n        <button *ngIf=\"currentStep > 0 && currentStep < 5\" type=\"button\" class=\"back-to-previous\"\r\n          (click)=\"previousStep()\">\r\n          Back to previous step\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Stepper Content -->\r\n  <form [formGroup]=\"registrationForm\" class=\"stepper-form\">\r\n    <!-- Step 1: Basic Information -->\r\n    <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n      <h3 class=\"step-title\">Enter Your Basic Information</h3>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"fullName\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-office-bag\"></i>\r\n          Company Name <span class=\"required\"></span>\r\n        </label>\r\n        <input type=\"text\" id=\"fullName\" formControlName=\"fullName\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('fullName')\" placeholder=\"Real Estate Development Company\"\r\n          pattern=\"[^0-9]*\" title=\"Company name cannot contain numbers\" (blur)=\"markFieldAsTouched('fullName')\"\r\n          required />\r\n        <div *ngIf=\"isFieldInvalid('fullName')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"fullName\") }}\r\n        </div>\r\n      </div>\r\n\r\n\r\n\r\n      <!-- Email or Phone -->\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">\r\n          <i class=\"ki-outline ki-phone\"></i>\r\n          Company email or phone number <span class=\"required\"></span>\r\n        </label>\r\n        <input type=\"text\" formControlName=\"email_phone\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('email_phone')\" placeholder=\"<EMAIL> or 01xxxxxxxxx\"\r\n          title=\"Enter a valid email address or phone number\" autocomplete=\"email tel\"\r\n          (blur)=\"markFieldAsTouched('email_phone')\" required />\r\n        <div *ngIf=\"isFieldInvalid('email_phone')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"email_phone\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Send Verification Button -->\r\n      <button type=\"button\" class=\"btn btn-primary btn-verification\" [class.loading]=\"isLoadingSendOtp\"\r\n        [disabled]=\"!isStep1Valid() || isLoadingSendOtp\" (click)=\"handleNextStepAndSendCode()\">\r\n        <span *ngIf=\"isLoadingSendOtp\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n        <span *ngIf=\"isLoadingSendOtp\">Sending...</span>\r\n        <span *ngIf=\"!isLoadingSendOtp\">Send Verification Code</span>\r\n      </button>\r\n\r\n      <!-- Help Text -->\r\n      <div class=\"help-text\">\r\n        Need help? <span class=\"contact-link\">Contact us</span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 2: Verification Code -->\r\n    <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n      <h3 class=\"step-title\">Enter Verification Code</h3>\r\n\r\n      <!-- Verification Code Input -->\r\n      <div class=\"verification-code-section\">\r\n        <div formArrayName=\"verificationCode\" class=\"verification-inputs\">\r\n          <div class=\"code-input\" *ngFor=\"let ctrl of verificationCodeControls; let i = index\">\r\n            <input type=\"text\" maxlength=\"1\" class=\"verification-input\" [formControlName]=\"i\"\r\n              (input)=\"autoFocusNext($event, i)\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Countdown Timer -->\r\n      <div class=\"countdown-section\">\r\n        <span class=\"countdown-text\" *ngIf=\"!showResendButton\">\r\n          Resend in\r\n          <span class=\"countdown-timer\">\r\n            0:{{ countdown < 10 ? \"0\" + countdown : countdown }} </span>\r\n          </span>\r\n\r\n          <button *ngIf=\"showResendButton\" class=\"btn btn-link\" (click)=\"onResendCode()\">\r\n            Resend Code\r\n          </button>\r\n      </div>\r\n\r\n      <!-- OTP Error Message -->\r\n      <div *ngIf=\"otpErrorMessage\" class=\"alert alert-danger mt-3\" role=\"alert\">\r\n        {{ otpErrorMessage }}\r\n      </div>\r\n\r\n      <!-- Next Button -->\r\n      <button type=\"button\" class=\"btn btn-primary btn-verification\" [class.loading]=\"isLoadingCheckOtp\"\r\n        [disabled]=\"!isStep2Valid() || isLoadingCheckOtp\" (click)=\"checkOTP()\">\r\n        <span *ngIf=\"isLoadingCheckOtp\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n        <span *ngIf=\"isLoadingCheckOtp\">Verifying...</span>\r\n        <span *ngIf=\"!isLoadingCheckOtp\">Verification Code - Next</span>\r\n      </button>\r\n\r\n      <!-- Help Text -->\r\n      <div class=\"help-text\">\r\n        Need help? <span class=\"contact-link\">Contact us</span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 3: Documents Upload -->\r\n    <div *ngIf=\"currentStep === 3\" class=\"step-content\">\r\n      <h3 class=\"step-title\">Please Upload Required Documents</h3>\r\n\r\n      <div class=\"documents-section\">\r\n        <p class=\"documents-description\">\r\n          You can upload the required documents now or skip and add them later\r\n          when you first use the required services\r\n        </p>\r\n\r\n        <!-- Document Upload Cards -->\r\n        <div class=\"upload-card-container\">\r\n          <!-- Company Registration Certificate -->\r\n          <div class=\"card mb-3 cursor-pointer\">\r\n            <label for=\"image\" class=\"card-body text-center py-2\">\r\n              <div class=\"upload-icon cursor-pointer\">\r\n                <i class=\"fas fa-arrow-up\"></i>\r\n              </div>\r\n              <span class=\"upload-text cursor-pointer\">\r\n                Company logo image for account\r\n                <span *ngIf=\"getFileCount('image') > 0\" class=\"badge bg-success ms-2\">\r\n                  {{ getFileCount(\"image\") }}\r\n                </span>\r\n              </span>\r\n              <div class=\"upload-subtitle\">PNG, JPG</div>\r\n              <input type=\"file\" id=\"image\" class=\"d-none\" (change)=\"onFileChange($event, 'image')\"\r\n                accept=\".png,.jpg,.jpeg\" />\r\n            </label>\r\n          </div>\r\n\r\n          <!-- Commercial Registration -->\r\n          <div class=\"card mb-3 cursor-pointer\">\r\n            <label for=\"commercialRegistryImage\" class=\"card-body text-center py-2\">\r\n              <div class=\"upload-icon cursor-pointer\">\r\n                <i class=\"fas fa-arrow-up\"></i>\r\n              </div>\r\n              <span class=\"upload-text cursor-pointer\">\r\n                Commercial register photo\r\n                <span *ngIf=\"getFileCount('commercialRegistryImage') > 0\" class=\"badge bg-success ms-2\">\r\n                  {{ getFileCount(\"commercialRegistryImage\") }}\r\n                </span>\r\n              </span>\r\n              <div class=\"upload-subtitle\">PNG, JPG, PDF</div>\r\n              <input type=\"file\" id=\"commercialRegistryImage\" class=\"d-none\"\r\n                (change)=\"onFileChange($event, 'commercialRegistryImage')\" accept=\".png,.jpg,.jpeg,.pdf\" multiple />\r\n            </label>\r\n          </div>\r\n\r\n          <!-- Tax Card -->\r\n          <div class=\"card mb-3 cursor-pointer\">\r\n            <label for=\"taxCardImage\" class=\"card-body text-center py-2\">\r\n              <div class=\"upload-icon cursor-pointer\">\r\n                <i class=\"fas fa-arrow-up\"></i>\r\n              </div>\r\n              <span class=\"upload-text cursor-pointer\">\r\n                Tax card image\r\n                <span *ngIf=\"getFileCount('taxCardImage') > 0\" class=\"badge bg-success ms-2\">\r\n                  {{ getFileCount(\"taxCardImage\") }}\r\n                </span>\r\n              </span>\r\n              <div class=\"upload-subtitle\">PNG, JPG, PDF</div>\r\n              <input type=\"file\" id=\"taxCardImage\" class=\"d-none\" (change)=\"onFileChange($event, 'taxCardImage')\"\r\n                accept=\".png,.jpg,.jpeg,.pdf\" multiple />\r\n            </label>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Upload Documents Button -->\r\n        <button type=\"button\" class=\"btn btn-primary btn-verification\" (click)=\"nextStep()\">\r\n          Upload Documents\r\n        </button>\r\n\r\n        <!-- Skip Button -->\r\n        <button type=\"button\" class=\"btn btn-link skip-button\" (click)=\"nextStep()\">\r\n          Skip and return later\r\n          <i class=\"ki-outline ki-arrow-right\"></i>\r\n        </button>\r\n\r\n        <!-- Help Text -->\r\n        <div class=\"help-text\">\r\n          Need help? <span class=\"contact-link\">Contact us</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 4: Choose Your Areas -->\r\n    <!-- <div *ngIf=\"currentStep === 4\" class=\"step-content\">\r\n      <h3 class=\"step-title\">{{ getStepTitle() }}</h3>\r\n\r\n      <div class=\"areas-section\">\r\n        <p class=\"areas-description\">\r\n          You can now choose your work areas and specialization to reach clients or properties you want to advertise for\r\n        </p>\r\n\r\n        <!-- Work Scope Selection -->\r\n    <!-- <div class=\"work-scope-section\">\r\n          <h4 class=\"work-scope-title\">Your work scope</h4>\r\n          <div class=\"work-scope-options\">\r\n            <div class=\"scope-option\">\r\n              <input type=\"radio\" id=\"outside-compound\" name=\"workScope\" value=\"outside\" class=\"scope-radio\">\r\n              <label for=\"outside-compound\" class=\"scope-label\">Outside Compound</label>\r\n            </div>\r\n            <div class=\"scope-option\">\r\n              <input type=\"radio\" id=\"inside-compound\" name=\"workScope\" value=\"inside\" class=\"scope-radio\">\r\n              <label for=\"inside-compound\" class=\"scope-label\">Inside Compound</label>\r\n            </div>\r\n            <div class=\"scope-option\">\r\n              <input type=\"radio\" id=\"both-scope\" name=\"workScope\" value=\"both\" class=\"scope-radio\">\r\n              <label for=\"both-scope\" class=\"scope-label\">Both</label>\r\n            </div>\r\n          </div>\r\n        </div> -->\r\n\r\n    <!-- Area Selection Items -->\r\n    <!-- <div class=\"area-item\">\r\n          <div class=\"area-header\">\r\n            <span class=\"area-title\">Enter your work scope</span>\r\n          </div>\r\n          <div class=\"area-dropdown\">\r\n            <select class=\"form-control\">\r\n              <option value=\"\" disabled selected>Choose main governorates/areas</option>\r\n              <option value=\"cairo\">Cairo</option>\r\n              <option value=\"giza\">Giza</option>\r\n              <option value=\"alexandria\">Alexandria</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"area-item\">\r\n          <div class=\"area-header\">\r\n            <span class=\"area-title\">Enter sub-areas </span>\r\n          </div>\r\n          <div class=\"area-dropdown\">\r\n            <select class=\"form-control\">\r\n              <option value=\"\" disabled selected>Choose specific sub-areas </option>\r\n              <option value=\"maadi\">Maadi</option>\r\n              <option value=\"zamalek\">Zamalek</option>\r\n              <option value=\"heliopolis\">Heliopolis</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"area-item\">\r\n          <div class=\"area-header\">\r\n            <span class=\"area-title\">Enter specializations you develop</span>\r\n          </div>\r\n          <div class=\"area-dropdown\">\r\n            <select class=\"form-control\">\r\n              <option value=\"\" disabled selected>Choose specialization - types of properties you develop</option>\r\n              <option value=\"residential\">Residential</option>\r\n              <option value=\"commercial\">Commercial</option>\r\n              <option value=\"administrative\">Administrative</option>\r\n            </select>\r\n          </div>\r\n        </div> -->\r\n\r\n    <!-- Complete Registration Button -->\r\n    <!-- <button type=\"button\" class=\"btn btn-primary btn-verification\" (click)=\"nextStep()\">\r\n          Complete Registration\r\n        </button> -->\r\n\r\n    <!-- Skip Button -->\r\n    <!-- <button type=\"button\" class=\"btn btn-link skip-button\" (click)=\"nextStep()\">\r\n          Skip and return later\r\n          <i class=\"ki-outline ki-arrow-right\"></i>\r\n        </button> -->\r\n\r\n    <!-- Help Text -->\r\n    <!-- <div class=\"help-text\">\r\n          Need help? <span class=\"contact-link\">Contact us</span>\r\n        </div>\r\n      </div>\r\n    </div> -->\r\n\r\n    <!-- Step 4: Phone, Email and Password -->\r\n    <div *ngIf=\"currentStep === 4\" class=\"step-content\">\r\n      <h3 class=\"step-title\">Enter Your Account Details</h3>\r\n\r\n      <!-- Phone -->\r\n      <div class=\"form-group\">\r\n        <label for=\"phone\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-phone\"></i>\r\n          Phone <span class=\"required\"></span>\r\n        </label>\r\n        <input type=\"tel\" id=\"phone\" formControlName=\"phone\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('phone')\" placeholder=\"01xxxxxxxxx\" required autocomplete=\"tel\" />\r\n        <div *ngIf=\"isFieldInvalid('phone')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"phone\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Email -->\r\n      <div class=\"form-group\">\r\n        <label for=\"email\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-user\"></i>\r\n          Email\r\n        </label>\r\n        <input type=\"email\" id=\"email\" formControlName=\"email\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('email')\" placeholder=\"<EMAIL>..\" autocomplete=\"email\" />\r\n        <div *ngIf=\"isFieldInvalid('email')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"email\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Password -->\r\n      <div class=\"form-group\">\r\n        <label for=\"password\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-lock\"></i>\r\n          Password <span class=\"required\"></span>\r\n        </label>\r\n        <div class=\"password-input-container\">\r\n          <input [type]=\"showPassword ? 'text' : 'password'\" id=\"password\" formControlName=\"password\"\r\n            class=\"form-control\" [class.is-invalid]=\"isFieldInvalid('password')\" placeholder=\"********\" minlength=\"8\"\r\n            pattern=\"^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,}$\"\r\n            title=\"Password must be at least 8 characters with uppercase, lowercase and number\" required\r\n            autocomplete=\"new-password\" />\r\n          <i [class]=\"showPassword ? 'ki-outline ki-eye' : 'ki-outline ki-eye-slash'\" class=\"password-toggle-icon\"\r\n            (click)=\"togglePasswordVisibility()\"></i>\r\n        </div>\r\n        <div *ngIf=\"isFieldInvalid('password')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"password\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!--Confirm Password -->\r\n      <div class=\"form-group\">\r\n        <label for=\"confirmPassword\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-lock\"></i>\r\n          Confirm Password <span class=\"required\"></span>\r\n        </label>\r\n        <div class=\"password-input-container\">\r\n          <input [type]=\"showConfirmPassword ? 'text' : 'password'\" id=\"confirmPassword\"\r\n            formControlName=\"password_confirmation\" class=\"form-control\" [class.is-invalid]=\"\r\n              isFieldInvalid('password_confirmation') || getFormError()\r\n            \" placeholder=\"********\" required autocomplete=\"new-password\" />\r\n          <i [class]=\"showConfirmPassword ? 'ki-outline ki-eye' : 'ki-outline ki-eye-slash'\"\r\n            class=\"password-toggle-icon\" (click)=\"toggleConfirmPasswordVisibility()\"></i>\r\n        </div>\r\n        <div *ngIf=\"isFieldInvalid('password_confirmation')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"password_confirmation\") }}\r\n        </div>\r\n        <div *ngIf=\"getFormError()\" class=\"invalid-feedback\">\r\n          {{ getFormError() }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Terms Agreement -->\r\n      <div class=\"form-group\">\r\n        <div class=\"form-check\">\r\n          <input type=\"checkbox\" id=\"agreeTerms\" formControlName=\"agreeTerms\" class=\"form-check-input\"\r\n            [class.is-invalid]=\"isFieldInvalid('agreeTerms')\" />\r\n          <label for=\"agreeTerms\" class=\"form-check-label\">\r\n            I agree to the Terms and Conditions <span class=\"required\"></span>\r\n          </label>\r\n        </div>\r\n        <div *ngIf=\"isFieldInvalid('agreeTerms')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"agreeTerms\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Create Account Button -->\r\n      <button type=\"button\" class=\"btn btn-primary btn-verification\" [class.loading]=\"isLoadingCreateAccount\"\r\n        [disabled]=\"!isStep4Valid() || isLoadingCreateAccount\" (click)=\"createAccount()\">\r\n        <span *ngIf=\"isLoadingCreateAccount\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n        <span *ngIf=\"isLoadingCreateAccount\">Creating Account...</span>\r\n        <span *ngIf=\"!isLoadingCreateAccount\">Create Account</span>\r\n      </button>\r\n\r\n      <!-- Help Text -->\r\n      <div class=\"help-text\">\r\n        Need help? <span class=\"contact-link\">Contact us</span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 5: Success Page (Previously Step 6) -->\r\n    <div *ngIf=\"currentStep === 5\" class=\"step-content success-step\">\r\n      <div class=\"success-content\">\r\n        <div class=\"success-icon\">\r\n          <i class=\"ki-outline ki-check-circle\"></i>\r\n        </div>\r\n\r\n        <h3 class=\"success-title\">Registration Successful</h3>\r\n\r\n        <p class=\"success-message\">\r\n          Your account has been successfully created. You can now enjoy the\r\n          various and amazing services provided by Easy Deal through the website\r\n          or dashboard.\r\n        </p>\r\n\r\n        <div class=\"success-illustration\">\r\n          <!-- <img src=\"/assets/media/login/successfully.png\" alt=\"Success\" class=\"success-image\" /> -->\r\n          <!-- <img src=\"~src/assets/media/login/successfully.png\" alt=\"Success\" class=\"success-image\" /> -->\r\n          <img src=\"assets/media/login/successfully.png\" alt=\"Success\" class=\"success-image\" />\r\n        </div>\r\n\r\n        <button type=\"button\" class=\"btn btn-primary btn-success-action\" [routerLink]=\"['/developer/dashboards']\">\r\n          Go to Website\r\n        </button>\r\n\r\n        <div class=\"additional-info\">\r\n          <span class=\"info-link\">Learn all about your account and how to get started</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </form>\r\n</div>"], "mappings": "AAAA,SAGEA,YAAY,QAEP,eAAe;AACtB,SAA4CC,UAAU,QAAQ,gBAAgB;AAG9E,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;ICCtBC,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAAE,UAAA,mBAAAC,iFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IACxBT,EAAA,CAAAU,MAAA,8BACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IAoBTX,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,kBACF;;;;;IAeAd,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,qBACF;;;;;IAMAd,EAAA,CAAAe,SAAA,eAAkG;;;;;IAClGf,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAChDX,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAU,MAAA,6BAAsB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IAtC/DX,EADF,CAAAC,cAAA,cAAoD,aAC3B;IAAAD,EAAA,CAAAU,MAAA,mCAA4B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAGtDX,EADF,CAAAC,cAAA,cAAwB,gBACmB;IACvCD,EAAA,CAAAe,SAAA,YAAwC;IACxCf,EAAA,CAAAU,MAAA,qBAAa;IAAAV,EAAA,CAAAe,SAAA,eAA8B;IAC7Cf,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAC,cAAA,gBAGa;IADmDD,EAAA,CAAAE,UAAA,kBAAAc,4EAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAQF,MAAA,CAAAY,kBAAA,CAAmB,UAAU,CAAC;IAAA,EAAC;IAFvGlB,EAAA,CAAAW,YAAA,EAGa;IACbX,EAAA,CAAAmB,UAAA,IAAAC,2DAAA,kBAAiE;IAGnEpB,EAAA,CAAAW,YAAA,EAAM;IAMJX,EADF,CAAAC,cAAA,eAAwB,iBACI;IACxBD,EAAA,CAAAe,SAAA,aAAmC;IACnCf,EAAA,CAAAU,MAAA,uCAA8B;IAAAV,EAAA,CAAAe,SAAA,gBAA8B;IAC9Df,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAC,cAAA,iBAGwD;IAAtDD,EAAA,CAAAE,UAAA,kBAAAmB,6EAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAQF,MAAA,CAAAY,kBAAA,CAAmB,aAAa,CAAC;IAAA,EAAC;IAH5ClB,EAAA,CAAAW,YAAA,EAGwD;IACxDX,EAAA,CAAAmB,UAAA,KAAAG,4DAAA,kBAAoE;IAGtEtB,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,kBACyF;IAAtCD,EAAA,CAAAE,UAAA,mBAAAqB,+EAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkB,yBAAA,EAA2B;IAAA,EAAC;IAGtFxB,EAFA,CAAAmB,UAAA,KAAAM,6DAAA,mBAA2F,KAAAC,6DAAA,mBAC5D,KAAAC,6DAAA,mBACC;IAClC3B,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,MAAA,oBAAW;IAAAV,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAEpDV,EAFoD,CAAAW,YAAA,EAAO,EACnD,EACF;;;;IArCAX,EAAA,CAAAY,SAAA,GAA+C;IAA/CZ,EAAA,CAAA4B,WAAA,eAAAtB,MAAA,CAAAuB,cAAA,aAA+C;IAG3C7B,EAAA,CAAAY,SAAA,EAAgC;IAAhCZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAuB,cAAA,aAAgC;IAcpC7B,EAAA,CAAAY,SAAA,GAAkD;IAAlDZ,EAAA,CAAA4B,WAAA,eAAAtB,MAAA,CAAAuB,cAAA,gBAAkD;IAG9C7B,EAAA,CAAAY,SAAA,EAAmC;IAAnCZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAuB,cAAA,gBAAmC;IAMoB7B,EAAA,CAAAY,SAAA,EAAkC;IAAlCZ,EAAA,CAAA4B,WAAA,YAAAtB,MAAA,CAAAyB,gBAAA,CAAkC;IAC/F/B,EAAA,CAAA8B,UAAA,cAAAxB,MAAA,CAAA0B,YAAA,MAAA1B,MAAA,CAAAyB,gBAAA,CAAgD;IACzC/B,EAAA,CAAAY,SAAA,EAAsB;IAAtBZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAyB,gBAAA,CAAsB;IACtB/B,EAAA,CAAAY,SAAA,EAAsB;IAAtBZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAyB,gBAAA,CAAsB;IACtB/B,EAAA,CAAAY,SAAA,EAAuB;IAAvBZ,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAAyB,gBAAA,CAAuB;;;;;;IAiB1B/B,EADF,CAAAC,cAAA,cAAqF,gBAE5C;IAArCD,EAAA,CAAAE,UAAA,mBAAA+B,mFAAAC,MAAA;MAAA,MAAAC,IAAA,GAAAnC,EAAA,CAAAI,aAAA,CAAAgC,GAAA,EAAAC,KAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgC,aAAA,CAAAJ,MAAA,EAAAC,IAAA,CAAwB;IAAA,EAAC;IACtCnC,EAFE,CAAAW,YAAA,EACuC,EACnC;;;;IAFwDX,EAAA,CAAAY,SAAA,EAAqB;IAArBZ,EAAA,CAAA8B,UAAA,oBAAAK,IAAA,CAAqB;;;;;IAQrFnC,EAAA,CAAAC,cAAA,eAAuD;IACrDD,EAAA,CAAAU,MAAA,kBACA;IAAAV,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAU,MAAA,GAAqD;IACvDV,EADuD,CAAAW,YAAA,EAAO,EACvD;;;;IADLX,EAAA,CAAAY,SAAA,GAAqD;IAArDZ,EAAA,CAAAa,kBAAA,QAAAP,MAAA,CAAAiC,SAAA,cAAAjC,MAAA,CAAAiC,SAAA,GAAAjC,MAAA,CAAAiC,SAAA,MAAqD;;;;;;IAGvDvC,EAAA,CAAAC,cAAA,iBAA+E;IAAzBD,EAAA,CAAAE,UAAA,mBAAAsC,uFAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoC,YAAA,EAAc;IAAA,EAAC;IAC5E1C,EAAA,CAAAU,MAAA,oBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IAIbX,EAAA,CAAAC,cAAA,cAA0E;IACxED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAqC,eAAA,MACF;;;;;IAKE3C,EAAA,CAAAe,SAAA,eAAmG;;;;;IACnGf,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAU,MAAA,mBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IACnDX,EAAA,CAAAC,cAAA,WAAiC;IAAAD,EAAA,CAAAU,MAAA,+BAAwB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IAnClEX,EADF,CAAAC,cAAA,cAAoD,aAC3B;IAAAD,EAAA,CAAAU,MAAA,8BAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAIjDX,EADF,CAAAC,cAAA,cAAuC,cAC6B;IAChED,EAAA,CAAAmB,UAAA,IAAAyB,2DAAA,kBAAqF;IAKzF5C,EADE,CAAAW,YAAA,EAAM,EACF;IAGNX,EAAA,CAAAC,cAAA,cAA+B;IAO3BD,EANF,CAAAmB,UAAA,IAAA0B,4DAAA,mBAAuD,IAAAC,8DAAA,qBAM0B;IAGnF9C,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAmB,UAAA,IAAA4B,2DAAA,kBAA0E;IAK1E/C,EAAA,CAAAC,cAAA,kBACyE;IAArBD,EAAA,CAAAE,UAAA,mBAAA8C,+EAAA;MAAAhD,EAAA,CAAAI,aAAA,CAAA6C,GAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,QAAA,EAAU;IAAA,EAAC;IAGtElD,EAFA,CAAAmB,UAAA,KAAAgC,6DAAA,mBAA4F,KAAAC,6DAAA,mBAC5D,KAAAC,6DAAA,mBACC;IACnCrD,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,MAAA,oBAAW;IAAAV,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAEpDV,EAFoD,CAAAW,YAAA,EAAO,EACnD,EACF;;;;IArCyCX,EAAA,CAAAY,SAAA,GAA6B;IAA7BZ,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAAgD,wBAAA,CAA6B;IAS1CtD,EAAA,CAAAY,SAAA,GAAuB;IAAvBZ,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAAiD,gBAAA,CAAuB;IAM1CvD,EAAA,CAAAY,SAAA,EAAsB;IAAtBZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAiD,gBAAA,CAAsB;IAM7BvD,EAAA,CAAAY,SAAA,EAAqB;IAArBZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAqC,eAAA,CAAqB;IAKoC3C,EAAA,CAAAY,SAAA,EAAmC;IAAnCZ,EAAA,CAAA4B,WAAA,YAAAtB,MAAA,CAAAkD,iBAAA,CAAmC;IAChGxD,EAAA,CAAA8B,UAAA,cAAAxB,MAAA,CAAAmD,YAAA,MAAAnD,MAAA,CAAAkD,iBAAA,CAAiD;IAC1CxD,EAAA,CAAAY,SAAA,EAAuB;IAAvBZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAkD,iBAAA,CAAuB;IACvBxD,EAAA,CAAAY,SAAA,EAAuB;IAAvBZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAkD,iBAAA,CAAuB;IACvBxD,EAAA,CAAAY,SAAA,EAAwB;IAAxBZ,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAAkD,iBAAA,CAAwB;;;;;IA6BvBxD,EAAA,CAAAC,cAAA,eAAsE;IACpED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAoD,YAAA,eACF;;;;;IAgBA1D,EAAA,CAAAC,cAAA,eAAwF;IACtFD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAoD,YAAA,iCACF;;;;;IAgBA1D,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAoD,YAAA,sBACF;;;;;;IAxDV1D,EADF,CAAAC,cAAA,cAAoD,aAC3B;IAAAD,EAAA,CAAAU,MAAA,uCAAgC;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAG1DX,EADF,CAAAC,cAAA,cAA+B,YACI;IAC/BD,EAAA,CAAAU,MAAA,sHAEF;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAOEX,EAJN,CAAAC,cAAA,cAAmC,cAEK,gBACkB,cACZ;IACtCD,EAAA,CAAAe,SAAA,aAA+B;IACjCf,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,gBAAyC;IACvCD,EAAA,CAAAU,MAAA,wCACA;IAAAV,EAAA,CAAAmB,UAAA,KAAAwC,6DAAA,mBAAsE;IAGxE3D,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAU,MAAA,gBAAQ;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC3CX,EAAA,CAAAC,cAAA,iBAC6B;IADgBD,EAAA,CAAAE,UAAA,oBAAA0D,+EAAA1B,MAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAAyD,GAAA;MAAA,MAAAvD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAwD,YAAA,CAAA5B,MAAA,EAAqB,OAAO,CAAC;IAAA,EAAC;IAGzFlC,EAHI,CAAAW,YAAA,EAC6B,EACvB,EACJ;IAKFX,EAFJ,CAAAC,cAAA,eAAsC,iBACoC,eAC9B;IACtCD,EAAA,CAAAe,SAAA,aAA+B;IACjCf,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,gBAAyC;IACvCD,EAAA,CAAAU,MAAA,mCACA;IAAAV,EAAA,CAAAmB,UAAA,KAAA4C,6DAAA,mBAAwF;IAG1F/D,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAU,MAAA,qBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAChDX,EAAA,CAAAC,cAAA,iBACsG;IAApGD,EAAA,CAAAE,UAAA,oBAAA8D,+EAAA9B,MAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAAyD,GAAA;MAAA,MAAAvD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAwD,YAAA,CAAA5B,MAAA,EAAqB,yBAAyB,CAAC;IAAA,EAAC;IAEhElC,EAHI,CAAAW,YAAA,EACsG,EAChG,EACJ;IAKFX,EAFJ,CAAAC,cAAA,eAAsC,iBACyB,eACnB;IACtCD,EAAA,CAAAe,SAAA,aAA+B;IACjCf,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,gBAAyC;IACvCD,EAAA,CAAAU,MAAA,wBACA;IAAAV,EAAA,CAAAmB,UAAA,KAAA8C,6DAAA,mBAA6E;IAG/EjE,EAAA,CAAAW,YAAA,EAAO;IACPX,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAU,MAAA,qBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAChDX,EAAA,CAAAC,cAAA,iBAC2C;IADSD,EAAA,CAAAE,UAAA,oBAAAgE,+EAAAhC,MAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAAyD,GAAA;MAAA,MAAAvD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAwD,YAAA,CAAA5B,MAAA,EAAqB,cAAc,CAAC;IAAA,EAAC;IAIzGlC,EAJM,CAAAW,YAAA,EAC2C,EACrC,EACJ,EACF;IAGNX,EAAA,CAAAC,cAAA,kBAAoF;IAArBD,EAAA,CAAAE,UAAA,mBAAAiE,+EAAA;MAAAnE,EAAA,CAAAI,aAAA,CAAAyD,GAAA;MAAA,MAAAvD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8D,QAAA,EAAU;IAAA,EAAC;IACjFpE,EAAA,CAAAU,MAAA,0BACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,kBAA4E;IAArBD,EAAA,CAAAE,UAAA,mBAAAmE,+EAAA;MAAArE,EAAA,CAAAI,aAAA,CAAAyD,GAAA;MAAA,MAAAvD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8D,QAAA,EAAU;IAAA,EAAC;IACzEpE,EAAA,CAAAU,MAAA,+BACA;IAAAV,EAAA,CAAAe,SAAA,aAAyC;IAC3Cf,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,MAAA,oBAAW;IAAAV,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAGtDV,EAHsD,CAAAW,YAAA,EAAO,EACnD,EACF,EACF;;;;IA/DaX,EAAA,CAAAY,SAAA,IAA+B;IAA/BZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAoD,YAAA,cAA+B;IAkB/B1D,EAAA,CAAAY,SAAA,IAAiD;IAAjDZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAoD,YAAA,gCAAiD;IAkBjD1D,EAAA,CAAAY,SAAA,IAAsC;IAAtCZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAoD,YAAA,qBAAsC;;;;;IAkIrD1D,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,eACF;;;;;IAWAd,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,eACF;;;;;IAkBAd,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,kBACF;;;;;IAiBAd,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,+BACF;;;;;IACAd,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAgE,YAAA,QACF;;;;;IAYAtE,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,oBACF;;;;;IAMAd,EAAA,CAAAe,SAAA,eAAwG;;;;;IACxGf,EAAA,CAAAC,cAAA,WAAqC;IAAAD,EAAA,CAAAU,MAAA,0BAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAC/DX,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IAzF7DX,EADF,CAAAC,cAAA,cAAoD,aAC3B;IAAAD,EAAA,CAAAU,MAAA,iCAA0B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAIpDX,EADF,CAAAC,cAAA,cAAwB,gBACgB;IACpCD,EAAA,CAAAe,SAAA,YAAmC;IACnCf,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAe,SAAA,eAA8B;IACtCf,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAe,SAAA,gBACuG;IACvGf,EAAA,CAAAmB,UAAA,IAAAoD,2DAAA,kBAA8D;IAGhEvE,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,eAAwB,iBACgB;IACpCD,EAAA,CAAAe,SAAA,aAAkC;IAClCf,EAAA,CAAAU,MAAA,eACF;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAe,SAAA,iBACqG;IACrGf,EAAA,CAAAmB,UAAA,KAAAqD,4DAAA,kBAA8D;IAGhExE,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,eAAwB,iBACmB;IACvCD,EAAA,CAAAe,SAAA,aAAkC;IAClCf,EAAA,CAAAU,MAAA,kBAAS;IAAAV,EAAA,CAAAe,SAAA,gBAA8B;IACzCf,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAC,cAAA,eAAsC;IACpCD,EAAA,CAAAe,SAAA,iBAIgC;IAChCf,EAAA,CAAAC,cAAA,aACuC;IAArCD,EAAA,CAAAE,UAAA,mBAAAuE,0EAAA;MAAAzE,EAAA,CAAAI,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqE,wBAAA,EAA0B;IAAA,EAAC;IACxC3E,EADyC,CAAAW,YAAA,EAAI,EACvC;IACNX,EAAA,CAAAmB,UAAA,KAAAyD,4DAAA,kBAAiE;IAGnE5E,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,eAAwB,iBAC0B;IAC9CD,EAAA,CAAAe,SAAA,aAAkC;IAClCf,EAAA,CAAAU,MAAA,0BAAiB;IAAAV,EAAA,CAAAe,SAAA,gBAA8B;IACjDf,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAC,cAAA,eAAsC;IACpCD,EAAA,CAAAe,SAAA,iBAGkE;IAClEf,EAAA,CAAAC,cAAA,aAC2E;IAA5CD,EAAA,CAAAE,UAAA,mBAAA2E,0EAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwE,+BAAA,EAAiC;IAAA,EAAC;IAC5E9E,EAD6E,CAAAW,YAAA,EAAI,EAC3E;IAINX,EAHA,CAAAmB,UAAA,KAAA4D,4DAAA,kBAA8E,KAAAC,4DAAA,kBAGzB;IAGvDhF,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,eAAwB,eACE;IACtBD,EAAA,CAAAe,SAAA,iBACsD;IACtDf,EAAA,CAAAC,cAAA,iBAAiD;IAC/CD,EAAA,CAAAU,MAAA,6CAAoC;IAAAV,EAAA,CAAAe,SAAA,gBAA8B;IAEtEf,EADE,CAAAW,YAAA,EAAQ,EACJ;IACNX,EAAA,CAAAmB,UAAA,KAAA8D,4DAAA,kBAAmE;IAGrEjF,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,kBACmF;IAA1BD,EAAA,CAAAE,UAAA,mBAAAgF,+EAAA;MAAAlF,EAAA,CAAAI,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6E,aAAA,EAAe;IAAA,EAAC;IAGhFnF,EAFA,CAAAmB,UAAA,KAAAiE,6DAAA,mBAAiG,KAAAC,6DAAA,mBAC5D,KAAAC,6DAAA,mBACC;IACxCtF,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,MAAA,oBAAW;IAAAV,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAEpDV,EAFoD,CAAAW,YAAA,EAAO,EACnD,EACF;;;;IAvFAX,EAAA,CAAAY,SAAA,GAA4C;IAA5CZ,EAAA,CAAA4B,WAAA,eAAAtB,MAAA,CAAAuB,cAAA,UAA4C;IACxC7B,EAAA,CAAAY,SAAA,EAA6B;IAA7BZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAuB,cAAA,UAA6B;IAYjC7B,EAAA,CAAAY,SAAA,GAA4C;IAA5CZ,EAAA,CAAA4B,WAAA,eAAAtB,MAAA,CAAAuB,cAAA,UAA4C;IACxC7B,EAAA,CAAAY,SAAA,EAA6B;IAA7BZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAuB,cAAA,UAA6B;IAaV7B,EAAA,CAAAY,SAAA,GAA+C;IAA/CZ,EAAA,CAAA4B,WAAA,eAAAtB,MAAA,CAAAuB,cAAA,aAA+C;IAD/D7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAiF,YAAA,uBAA2C;IAK/CvF,EAAA,CAAAY,SAAA,EAAwE;IAAxEZ,EAAA,CAAAwF,UAAA,CAAAlF,MAAA,CAAAiF,YAAA,mDAAwE;IAGvEvF,EAAA,CAAAY,SAAA,EAAgC;IAAhCZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAuB,cAAA,aAAgC;IAa2B7B,EAAA,CAAAY,SAAA,GAE5D;IAF4DZ,EAAA,CAAA4B,WAAA,eAAAtB,MAAA,CAAAuB,cAAA,6BAAAvB,MAAA,CAAAgE,YAAA,GAE5D;IAHItE,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAmF,mBAAA,uBAAkD;IAItDzF,EAAA,CAAAY,SAAA,EAA+E;IAA/EZ,EAAA,CAAAwF,UAAA,CAAAlF,MAAA,CAAAmF,mBAAA,mDAA+E;IAG9EzF,EAAA,CAAAY,SAAA,EAA6C;IAA7CZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAuB,cAAA,0BAA6C;IAG7C7B,EAAA,CAAAY,SAAA,EAAoB;IAApBZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAgE,YAAA,GAAoB;IAStBtE,EAAA,CAAAY,SAAA,GAAiD;IAAjDZ,EAAA,CAAA4B,WAAA,eAAAtB,MAAA,CAAAuB,cAAA,eAAiD;IAK/C7B,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAuB,cAAA,eAAkC;IAMqB7B,EAAA,CAAAY,SAAA,EAAwC;IAAxCZ,EAAA,CAAA4B,WAAA,YAAAtB,MAAA,CAAAoF,sBAAA,CAAwC;IACrG1F,EAAA,CAAA8B,UAAA,cAAAxB,MAAA,CAAAqF,YAAA,MAAArF,MAAA,CAAAoF,sBAAA,CAAsD;IAC/C1F,EAAA,CAAAY,SAAA,EAA4B;IAA5BZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAoF,sBAAA,CAA4B;IAC5B1F,EAAA,CAAAY,SAAA,EAA4B;IAA5BZ,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAoF,sBAAA,CAA4B;IAC5B1F,EAAA,CAAAY,SAAA,EAA6B;IAA7BZ,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAAoF,sBAAA,CAA6B;;;;;IAYpC1F,EAFJ,CAAAC,cAAA,cAAiE,cAClC,cACD;IACxBD,EAAA,CAAAe,SAAA,YAA0C;IAC5Cf,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAU,MAAA,8BAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAEtDX,EAAA,CAAAC,cAAA,YAA2B;IACzBD,EAAA,CAAAU,MAAA,+JAGF;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAEJX,EAAA,CAAAC,cAAA,cAAkC;IAGhCD,EAAA,CAAAe,SAAA,cAAqF;IACvFf,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,kBAA0G;IACxGD,EAAA,CAAAU,MAAA,uBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAGPX,EADF,CAAAC,cAAA,eAA6B,gBACH;IAAAD,EAAA,CAAAU,MAAA,2DAAmD;IAGjFV,EAHiF,CAAAW,YAAA,EAAO,EAC9E,EACF,EACF;;;IAR+DX,EAAA,CAAAY,SAAA,IAAwC;IAAxCZ,EAAA,CAAA8B,UAAA,eAAA9B,EAAA,CAAA4F,eAAA,IAAAC,GAAA,EAAwC;;;AD1YjH,OAAM,MAAOC,qCAAqC;EA4BtCC,EAAA;EACAC,qBAAA;EACAC,EAAA;EA7BAC,MAAM,GAAG,IAAIrG,YAAY,EAAQ;EACjCsG,UAAU,GAAG,IAAItG,YAAY,EAA6B;EAEpEuG,gBAAgB;EAChBC,WAAW,GAAG,CAAC;EACfC,UAAU,GAAG,CAAC;EACdC,SAAS,GAAG,KAAK;EACjBxE,gBAAgB,GAAY,KAAK;EACjCyB,iBAAiB,GAAY,KAAK;EAClCkC,sBAAsB,GAAY,KAAK;EACvC/C,eAAe,GAAW,EAAE;EAC5B6D,aAAa,GAA8B,EAAE;EAC7CC,kBAAkB,GAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACnDlE,SAAS,GAAW,EAAE;EACtBgB,gBAAgB,GAAY,KAAK;EACjCgC,YAAY,GAAY,KAAK;EAC7BE,mBAAmB,GAAY,KAAK;EAEpC;EACA,OAAOiB,SAAS,GAAG5G,UAAU,CAAC6G,OAAO,CAAC,WAAW,CAAC;EAClD,OAAOC,mBAAmB,GAAG9G,UAAU,CAAC6G,OAAO,CAAC,iFAAiF,CAAC;EAE1H,OAAOE,OAAOA,CAACC,KAAa;IAClC,OAAO,4BAA4B,CAACC,IAAI,CAACD,KAAK,CAAC;EACjD;EAEAE,YACUjB,EAAe,EACfC,qBAA4C,EAC5CC,EAAqB;IAFrB,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,EAAE,GAAFA,EAAE;IAEV,IAAI,CAACG,gBAAgB,GAAG,IAAI,CAACa,UAAU,EAAE;EAC3C;EAEAC,QAAQA,CAAA,GAAU;EAEVD,UAAUA,CAAA;IAChB,OAAO,IAAI,CAAClB,EAAE,CAACoB,KAAK,CAAC;MACnB;MACAC,QAAQ,EAAE,CACR,EAAE,EACF,CACEtH,UAAU,CAACuH,QAAQ,EACnBvH,UAAU,CAACwH,SAAS,CAAC,CAAC,CAAC,EACvBxB,qCAAqC,CAACY,SAAS,CAChD,CACF;MACDa,WAAW,EAAE,CACX,EAAE,EACF,CAACzH,UAAU,CAACuH,QAAQ,EAAEvB,qCAAqC,CAACc,mBAAmB,CAAC,CACjF;MAED;MACAY,gBAAgB,EAAE,IAAI,CAACzB,EAAE,CAAC0B,KAAK,CAC7BC,KAAK,CAAC,CAAC,CAAC,CACLC,IAAI,CAAC,EAAE,CAAC,CACRC,GAAG,CAAC,MACH,IAAI,CAAC7B,EAAE,CAAC8B,OAAO,CAAC,EAAE,EAAE,CAClB/H,UAAU,CAACuH,QAAQ,EACnBvH,UAAU,CAAC6G,OAAO,CAAC,OAAO,CAAC,CAC5B,CAAC,CACH,CACJ;MAED;MACAmB,KAAK,EAAE,CAAC,EAAE,EAAE,CAAChI,UAAU,CAACuH,QAAQ,EAAEvB,qCAAqC,CAACc,mBAAmB,CAAC,CAAC;MAC7FmB,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjI,UAAU,CAACiI,KAAK,CAAC,CAAC;MAC/BC,QAAQ,EAAE,CACR,EAAE,EACF,CACElI,UAAU,CAACuH,QAAQ,EACnBvH,UAAU,CAACwH,SAAS,CAAC,CAAC,CAAC,EACvBxH,UAAU,CAAC6G,OAAO,CAAC,iCAAiC,CAAC,CACtD,CACF;MACDsB,qBAAqB,EAAE,CAAC,EAAE,EAAE,CAACnI,UAAU,CAACuH,QAAQ,CAAC,CAAC;MAClDa,UAAU,EAAE,CAAC,KAAK,EAAE,CAACpI,UAAU,CAACqI,YAAY,CAAC;KAC9C,CAAC;EACJ;EAEA;EACAtG,cAAcA,CAACuG,SAAiB;IAC9B,MAAMC,KAAK,GAAG,IAAI,CAACjC,gBAAgB,CAACkC,GAAG,CAACF,SAAS,CAAC;IAClD,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAACE,OAAO,KAAKF,KAAK,CAACG,KAAK,IAAIH,KAAK,CAACI,OAAO,CAAC,CAAC;EACrE;EAEAvH,kBAAkBA,CAACkH,SAAiB;IAClC,IAAI,CAAChC,gBAAgB,CAACkC,GAAG,CAACF,SAAS,CAAC,EAAEM,aAAa,EAAE;EACvD;EAEA5H,aAAaA,CAACsH,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAACjC,gBAAgB,CAACkC,GAAG,CAACF,SAAS,CAAC;IAClD,IAAI,CAACC,KAAK,EAAEM,MAAM,EAAE,OAAO,EAAE;IAE7B,MAAMA,MAAM,GAAGN,KAAK,CAACM,MAAM;IAC3B,IAAIA,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,wBAAwB;IACvD,IAAIA,MAAM,CAAC,SAAS,CAAC,IAAIP,SAAS,KAAK,UAAU,EAAE,OAAO,qCAAqC;IAC/F,IAAIO,MAAM,CAAC,SAAS,CAAC,IAAIP,SAAS,KAAK,aAAa,EAAE,OAAO,mCAAmC;IAChG,IAAIO,MAAM,CAAC,SAAS,CAAC,IAAIP,SAAS,KAAK,OAAO,EAAE,OAAO,0BAA0B;IACjF,IAAIO,MAAM,CAAC,SAAS,CAAC,IAAIP,SAAS,KAAK,UAAU,EAAE,OAAO,oCAAoC;IAC9F,IAAIO,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IAC/C,IAAIA,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,OAAOA,MAAM,CAAC,WAAW,CAAC,CAACC,cAAc,QAAQ;IAEjF,OAAO,eAAe;EACxB;EAEAtE,YAAYA,CAAA;IACV,MAAM0D,QAAQ,GAAG,IAAI,CAAC5B,gBAAgB,CAACkC,GAAG,CAAC,UAAU,CAAC,EAAExB,KAAK;IAC7D,MAAM+B,eAAe,GAAG,IAAI,CAACzC,gBAAgB,CAACkC,GAAG,CAC/C,uBAAuB,CACxB,EAAExB,KAAK;IAER,IAAIkB,QAAQ,IAAIa,eAAe,IAAIb,QAAQ,KAAKa,eAAe,EAAE;MAC/D,OAAO,wBAAwB;IACjC;IACA,OAAO,EAAE;EACX;EAEA;EACA7G,YAAYA,CAAA;IACV,MAAMoF,QAAQ,GAAG,IAAI,CAAChB,gBAAgB,CAACkC,GAAG,CAAC,UAAU,CAAC;IACtD,MAAMQ,UAAU,GAAG,IAAI,CAAC1C,gBAAgB,CAACkC,GAAG,CAAC,aAAa,CAAC;IAE3D,OAAO,CAAC,EAAElB,QAAQ,EAAE2B,KAAK,IAAID,UAAU,EAAEC,KAAK,CAAC;EACjD;EAEAtF,YAAYA,CAAA;IACV,MAAM+D,gBAAgB,GAAG,IAAI,CAACpB,gBAAgB,CAACkC,GAAG,CAChD,kBAAkB,CACN;IACd,OAAOd,gBAAgB,CAACuB,KAAK;EAC/B;EAEApD,YAAYA,CAAA;IACV,MAAMmC,KAAK,GAAG,IAAI,CAAC1B,gBAAgB,CAACkC,GAAG,CAAC,OAAO,CAAC;IAChD,MAAMP,KAAK,GAAG,IAAI,CAAC3B,gBAAgB,CAACkC,GAAG,CAAC,OAAO,CAAC;IAChD,MAAMN,QAAQ,GAAG,IAAI,CAAC5B,gBAAgB,CAACkC,GAAG,CAAC,UAAU,CAAC;IACtD,MAAMU,oBAAoB,GAAG,IAAI,CAAC5C,gBAAgB,CAACkC,GAAG,CACpD,uBAAuB,CACxB;IACD,MAAMJ,UAAU,GAAG,IAAI,CAAC9B,gBAAgB,CAACkC,GAAG,CAAC,YAAY,CAAC;IAE1D;IACA,MAAMW,cAAc,GAAGjB,QAAQ,EAAElB,KAAK,KAAKkC,oBAAoB,EAAElC,KAAK;IAEtE,OAAO,CAAC,EACNgB,KAAK,EAAEiB,KAAK,IACZhB,KAAK,EAAEgB,KAAK,IACZf,QAAQ,EAAEe,KAAK,IACfC,oBAAoB,EAAED,KAAK,IAC3Bb,UAAU,EAAEa,KAAK,IACjBE,cAAc,CACf;EACH;EAEA7E,QAAQA,CAAA;IACN,IAAI,IAAI,CAACiC,WAAW,GAAG,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACD,WAAW,EAAE;IACpB;EACF;EAEA5F,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC4F,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB,CAAC,MAAM;MACL,IAAI,CAACH,MAAM,CAACgD,IAAI,EAAE;IACpB;EACF;EAEA,IAAI5F,wBAAwBA,CAAA;IAC1B,OAAQ,IAAI,CAAC8C,gBAAgB,CAACkC,GAAG,CAAC,kBAAkB,CAAe,CAChEa,QAAQ;EACb;EAEAC,YAAYA,CAAC/G,KAAa;IACxB,MAAMgH,IAAI,GAAG,IAAI,CAAC5C,kBAAkB,CAAC6C,IAAI,CAAC,EAAE,CAAC;IAC7C,IAAI,CAAClD,gBAAgB,CAACmD,UAAU,CAAC;MAAE/B,gBAAgB,EAAE6B;IAAI,CAAE,CAAC;EAC9D;EAEA7H,yBAAyBA,CAAA;IACvB,IAAI,CAACgI,oBAAoB,CAAC,IAAI,CAAC;EACjC;EAEAA,oBAAoBA,CAACC,cAAA,GAA0B,KAAK;IAClD,IAAI,CAAC1H,gBAAgB,GAAG,IAAI;IAC5B,MAAM2H,KAAK,GAAG,IAAI,CAACtD,gBAAgB,CAACkC,GAAG,CAAC,aAAa,CAAC,EAAExB,KAAK,EAAE6C,IAAI,EAAE;IAErE,IAAIC,MAAM,GAAuC,EAAE;IACnD,IAAI9D,qCAAqC,CAACe,OAAO,CAAC6C,KAAK,CAAC,EAAE;MACxDE,MAAM,CAAC7B,KAAK,GAAG2B,KAAK;IACtB,CAAC,MAAM;MACLE,MAAM,CAAC9B,KAAK,GAAG4B,KAAK;IACtB;IAEAG,OAAO,CAACC,GAAG,CAACF,MAAM,CAAC;IACnB,IAAI,CAAC5D,qBAAqB,CAAC+D,OAAO,CAACH,MAAM,CAAC,CAACI,SAAS,CACjDC,QAAa,IAAI;MAChBJ,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEG,QAAQ,CAAC;MAClC,IAAI,CAAClI,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACmI,cAAc,EAAE;MAErB;MACA,IAAIT,cAAc,EAAE;QAClB,IAAI,CAACrF,QAAQ,EAAE;MACjB;MAEA,IAAI,CAAC6B,EAAE,CAACkE,YAAY,EAAE;IACxB,CAAC,EACAC,KAAU,IAAI;MACbP,OAAO,CAACO,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAI,CAACrI,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACkE,EAAE,CAACkE,YAAY,EAAE;IACxB,CAAC,CACF;EACH;EAEAjH,QAAQA,CAAA;IACN,IAAI,CAACM,iBAAiB,GAAG,IAAI;IAC7B,MAAMkG,KAAK,GAAG,IAAI,CAACtD,gBAAgB,CAACkC,GAAG,CAAC,aAAa,CAAC,EAAExB,KAAK,EAAE6C,IAAI,EAAE;IACrE,MAAMU,SAAS,GAAG,IAAI,CAACjE,gBAAgB,CAACkC,GAAG,CAAC,kBAAkB,CAAC,EAAExB,KAAK;IACtE,MAAMwD,GAAG,GAAGD,SAAS,CAACf,IAAI,CAAC,EAAE,CAAC;IAE9B,IAAIM,MAAM,GAAqD,EAAE;IACjE,IAAI9D,qCAAqC,CAACe,OAAO,CAAC6C,KAAK,CAAC,EAAE;MACxDE,MAAM,CAAC7B,KAAK,GAAG2B,KAAK;IACtB,CAAC,MAAM;MACLE,MAAM,CAAC9B,KAAK,GAAG4B,KAAK;IACtB;IACAE,MAAM,CAACU,GAAG,GAAGA,GAAG;IAEhBT,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEF,MAAM,CAAC;IAEhD,IAAI,CAAC5D,qBAAqB,CAACuE,QAAQ,CAACX,MAAM,CAAC,CAACI,SAAS,CAAC;MACpDQ,IAAI,EAAGP,QAAa,IAAI;QACtBJ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEG,QAAQ,CAAC;QAClD,IAAI,CAACzG,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACb,eAAe,GAAG,EAAE;QACzB,IAAI,CAACsD,EAAE,CAACkE,YAAY,EAAE;QACtB,IAAI,CAAC/F,QAAQ,EAAE;MACjB,CAAC;MACDgG,KAAK,EAAGA,KAAU,IAAI;QACpBP,OAAO,CAACO,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAAC5G,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACb,eAAe,GAAGyH,KAAK,EAAEA,KAAK,EAAEK,OAAO,IAAI,2BAA2B;QAC1E,IAAI,CAACC,cAAc,EAAE;QACtB,IAAI,CAACzE,EAAE,CAACkE,YAAY,EAAE;MACxB;KACD,CAAC;EACJ;EAEQO,cAAcA,CAAA;IACpB,IAAI,CAACjE,kBAAkB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC9C,MAAMkE,qBAAqB,GAAG,IAAI,CAACvE,gBAAgB,CAACkC,GAAG,CACrD,kBAAkB,CACN;IACdqC,qBAAqB,CAACxB,QAAQ,CAACyB,OAAO,CAAE/C,OAAO,IAAKA,OAAO,CAACgD,QAAQ,CAAC,EAAE,CAAC,CAAC;EAC3E;EAEA1F,aAAaA,CAAA;IACX,IAAI,CAACO,sBAAsB,GAAG,IAAI;IAClC,MAAMoF,UAAU,GAAG,IAAI,CAAC1E,gBAAgB,CAACkC,GAAG,CAAC,OAAO,CAAC,EAAExB,KAAK,EAAE6C,IAAI,EAAE;IAEpE,IAAIC,MAAM,GAAG,IAAI,CAACxD,gBAAgB,CAACU,KAAK;IACxC8C,MAAM,CAACmB,IAAI,GAAG,WAAW;IAEzB;IACA,IAAID,UAAU,IAAIA,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;MACvCpB,MAAM,CAAC7B,KAAK,GAAG+C,UAAU;IAC3B,CAAC,MAAM;MACL;MACA,OAAOlB,MAAM,CAAC7B,KAAK;IACrB;IAEA,MAAMkD,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAE/B;IACA,KAAK,MAAMC,GAAG,IAAIvB,MAAM,EAAE;MACxB,IAAIA,MAAM,CAACwB,cAAc,CAACD,GAAG,CAAC,EAAE;QAC9BF,QAAQ,CAACI,MAAM,CAACF,GAAG,EAAEvB,MAAM,CAACuB,GAAG,CAAC,CAAC;MACnC;IACF;IAEA;IACA,KAAK,MAAMG,QAAQ,IAAI,IAAI,CAAC9E,aAAa,EAAE;MACzC,MAAM+E,KAAK,GAAG,IAAI,CAAC/E,aAAa,CAAC8E,QAAQ,CAAC;MAC1C,IAAIC,KAAK,EAAEP,MAAM,EAAE;QAChBC,QAAQ,CAACI,MAAM,CAACC,QAAQ,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;MACtC;IACF;IAEA,IAAI,CAACvF,qBAAqB,CAACwF,QAAQ,CAACP,QAAQ,CAAC,CAACjB,SAAS,CAAC;MACtDQ,IAAI,EAAGP,QAAa,IAAI;QACtB,IAAI,CAACvE,sBAAsB,GAAG,KAAK;QACnC,IAAI+F,IAAI,GAAGxB,QAAQ,CAACyB,IAAI;QACxBC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEH,IAAI,CAACI,SAAS,CAAC;QACjD,IAAI,CAAC7F,qBAAqB,CAAC8F,cAAc,CAAC7B,QAAQ,CAACyB,IAAI,CAAC;QACxD,IAAI,CAACzF,EAAE,CAACkE,YAAY,EAAE;QACtB,IAAI,CAAC/F,QAAQ,EAAE;MACjB,CAAC;MACDgG,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAAC1E,sBAAsB,GAAG,KAAK;QAElC,IAAIqG,YAAY,GAAG,6CAA6C;QACjE,IAAI3B,KAAK,EAAEA,KAAK,EAAEK,OAAO,EAAE;UACzBsB,YAAY,GAAG3B,KAAK,CAACA,KAAK,CAACK,OAAO;QACpC,CAAC,MAAM,IAAIL,KAAK,EAAEK,OAAO,EAAE;UACzBsB,YAAY,GAAG3B,KAAK,CAACK,OAAO;QAC9B;QAEA1K,IAAI,CAACiM,IAAI,CAACD,YAAY,CAAC;QACvB,IAAI,CAAC9F,EAAE,CAACkE,YAAY,EAAE;MACxB;KACD,CAAC;EACJ;EAEAD,cAAcA,CAAA;IACZ,IAAI,CAAC3G,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAChB,SAAS,GAAG,EAAE;IAEnB,MAAM0J,UAAU,GAAGC,WAAW,CAAC,MAAK;MAClC,IAAI,CAAC3J,SAAS,EAAE;MAChB,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;QACxB4J,aAAa,CAACF,UAAU,CAAC;QACzB,IAAI,CAAC1I,gBAAgB,GAAG,IAAI;MAC9B;MACA,IAAI,CAAC0C,EAAE,CAACkE,YAAY,EAAE;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA7H,aAAaA,CAAC8J,KAAU,EAAE/J,KAAa;IACrC,MAAMqH,KAAK,GAAG0C,KAAK,CAACC,MAAM;IAC1B,IAAI3C,KAAK,CAAC5C,KAAK,IAAIzE,KAAK,GAAG,CAAC,EAAE;MAC5B,MAAMiK,SAAS,GACb5C,KAAK,CAAC6C,aAAa,EAAEC,kBAAkB,EAAEC,aAAa,CAAC,OAAO,CAAC;MACjEH,SAAS,EAAEI,KAAK,EAAE;IACpB;EACF;EAEAhK,YAAYA,CAAA;IACV,IAAI,CAAC8G,oBAAoB,CAAC,KAAK,CAAC;EAClC;EAEA;EACA7E,wBAAwBA,CAAA;IACtB,IAAI,CAACY,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAT,+BAA+BA,CAAA;IAC7B,IAAI,CAACW,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEA;EACA3B,YAAYA,CAACsI,KAAY,EAAEd,QAAgB;IACzC,MAAMC,KAAK,GAAIa,KAAK,CAACC,MAA2B,CAACd,KAAK;IACtD,IAAI,CAACA,KAAK,EAAEP,MAAM,EAAE;IAEpB,IAAI,CAACxE,aAAa,CAAC8E,QAAQ,CAAC,GAAG,CAC7B,IAAI,IAAI,CAAC9E,aAAa,CAAC8E,QAAQ,CAAC,IAAI,EAAE,CAAC,EACvC,GAAG5D,KAAK,CAACiF,IAAI,CAACpB,KAAK,CAAC,CACrB;IACD1B,OAAO,CAACC,GAAG,CAAC,YAAYyB,KAAK,CAACP,MAAM,gBAAgBM,QAAQ,EAAE,CAAC;EACjE;EAEA5H,YAAYA,CAAC4H,QAAgB;IAC3B,OAAO,IAAI,CAAC9E,aAAa,CAAC8E,QAAQ,CAAC,EAAEN,MAAM,IAAI,CAAC;EAClD;;qCA9WWlF,qCAAqC,EAAA9F,EAAA,CAAA4M,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9M,EAAA,CAAA4M,iBAAA,CAAAG,EAAA,CAAAC,qBAAA,GAAAhN,EAAA,CAAA4M,iBAAA,CAAA5M,EAAA,CAAAiN,iBAAA;EAAA;;UAArCnH,qCAAqC;IAAAoH,SAAA;IAAAC,OAAA;MAAAjH,MAAA;MAAAC,UAAA;IAAA;IAAAiH,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCb9CzN,EAHJ,CAAAC,cAAA,aAAyC,aAEX,YACA;QAAAD,EAAA,CAAAU,MAAA,6BAAsB;QAAAV,EAAA,CAAAW,YAAA,EAAK;QAEnDX,EADF,CAAAC,cAAA,aAA8B,aACF;QACxBD,EAAA,CAAAe,SAAA,aAAoF;QACtFf,EAAA,CAAAW,YAAA,EAAM;QAEJX,EADF,CAAAC,cAAA,aAAoB,cACU;QAAAD,EAAA,CAAAU,MAAA,GAA0C;QAAAV,EAAA,CAAAW,YAAA,EAAO;QAC7EX,EAAA,CAAAmB,UAAA,KAAAwM,wDAAA,oBAC2B;QAKjC3N,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;QAGNX,EAAA,CAAAC,cAAA,eAA0D;QAmXxDD,EAjXA,CAAAmB,UAAA,KAAAyM,qDAAA,oBAAoD,KAAAC,qDAAA,oBAiDA,KAAAC,qDAAA,mBA8CA,KAAAC,qDAAA,oBA8KA,KAAAC,qDAAA,mBAoGa;QA8BrEhO,EADE,CAAAW,YAAA,EAAO,EACH;;;QA9Z6BX,EAAA,CAAAY,SAAA,GAAkD;QAAlDZ,EAAA,CAAAiO,WAAA,UAAAP,GAAA,CAAArH,WAAA,GAAAqH,GAAA,CAAApH,UAAA,YAAkD;QAGjDtG,EAAA,CAAAY,SAAA,GAA0C;QAA1CZ,EAAA,CAAAkO,kBAAA,UAAAR,GAAA,CAAArH,WAAA,UAAAqH,GAAA,CAAApH,UAAA,KAA0C;QAC7DtG,EAAA,CAAAY,SAAA,EAAwC;QAAxCZ,EAAA,CAAA8B,UAAA,SAAA4L,GAAA,CAAArH,WAAA,QAAAqH,GAAA,CAAArH,WAAA,KAAwC;QASjDrG,EAAA,CAAAY,SAAA,EAA8B;QAA9BZ,EAAA,CAAA8B,UAAA,cAAA4L,GAAA,CAAAtH,gBAAA,CAA8B;QAE5BpG,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAA8B,UAAA,SAAA4L,GAAA,CAAArH,WAAA,OAAuB;QAiDvBrG,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAA8B,UAAA,SAAA4L,GAAA,CAAArH,WAAA,OAAuB;QA8CvBrG,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAA8B,UAAA,SAAA4L,GAAA,CAAArH,WAAA,OAAuB;QA8KvBrG,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAA8B,UAAA,SAAA4L,GAAA,CAAArH,WAAA,OAAuB;QAoGvBrG,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAA8B,UAAA,SAAA4L,GAAA,CAAArH,WAAA,OAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}