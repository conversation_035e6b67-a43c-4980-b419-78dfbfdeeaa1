{"ast": null, "code": "import { AbstractCrudService } from '../../shared/services/abstract-crud.service';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PropertyService extends AbstractCrudService {\n  http;\n  apiUrl = `${environment.apiUrl}/unit`;\n  constructor(http) {\n    super(http);\n    this.http = http;\n  }\n  getUnitTypes() {\n    return this.http.get(`${environment.apiUrl}/unit/unit-types`, {});\n  }\n  getCities(cityId, limit = 100, offset = 0, sort = 'asc', sortBy = 'id') {\n    const params = {\n      limit,\n      offset,\n      sort,\n      sortBy\n    };\n    if (cityId) {\n      params['filters[cityId]'] = cityId;\n    }\n    return this.http.get(`${environment.apiUrl}/location/city`, {\n      params\n    });\n  }\n  getAreas(cityId, limit = 100, offset = 0, sort = 'asc', sortBy = 'id') {\n    const params = {\n      limit,\n      offset,\n      sort,\n      sortBy\n    };\n    if (cityId) {\n      params['cityId'] = cityId;\n    }\n    return this.http.get(`${environment.apiUrl}/location/area`, {\n      params\n    });\n  }\n  getSubAreas(areaId, limit = 100, offset = 0, sort = 'asc', sortBy = 'id') {\n    const params = {\n      limit,\n      offset,\n      sort,\n      sortBy\n    };\n    if (areaId) {\n      params['areaId'] = areaId;\n    }\n    return this.http.get(`${environment.apiUrl}/location/sub-area`, {\n      params\n    });\n  }\n  createProperty(formData) {\n    return this.http.post(`${this.apiUrl}/create-unit`, formData);\n  }\n  static ɵfac = function PropertyService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PropertyService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PropertyService,\n    factory: PropertyService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["AbstractCrudService", "environment", "PropertyService", "http", "apiUrl", "constructor", "getUnitTypes", "get", "getCities", "cityId", "limit", "offset", "sort", "sortBy", "params", "<PERSON><PERSON><PERSON><PERSON>", "getSubAreas", "areaId", "createProperty", "formData", "post", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\services\\property.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { AbstractCrudService } from '../../shared/services/abstract-crud.service';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class PropertyService extends AbstractCrudService {\r\n  apiUrl = `${environment.apiUrl}/unit`;\r\n\r\n  constructor(protected override http: HttpClient) {\r\n    super(http);\r\n  }\r\n\r\n  getUnitTypes(): Observable<any> {\r\n    return this.http.get(`${environment.apiUrl}/unit/unit-types`, {});\r\n  }\r\n\r\n  getCities(\r\n    cityId?: number,\r\n    limit: number = 100,\r\n    offset: number = 0,\r\n    sort: string = 'asc',\r\n    sortBy: string = 'id'\r\n  ): Observable<any> {\r\n    const params: any = {\r\n      limit,\r\n      offset,\r\n      sort,\r\n      sortBy,\r\n    };\r\n\r\n    if (cityId) {\r\n      params['filters[cityId]'] = cityId;\r\n    }\r\n\r\n    return this.http.get(`${environment.apiUrl}/location/city`, { params });\r\n  }\r\n\r\n  getAreas(\r\n    cityId?: number,\r\n    limit: number = 100,\r\n    offset: number = 0,\r\n    sort: string = 'asc',\r\n    sortBy: string = 'id'\r\n  ): Observable<any> {\r\n    const params: any = {\r\n      limit,\r\n      offset,\r\n      sort,\r\n      sortBy,\r\n    };\r\n\r\n    if (cityId) {\r\n      params['cityId'] = cityId;\r\n    }\r\n\r\n    return this.http.get(`${environment.apiUrl}/location/area`, { params });\r\n  }\r\n\r\n  getSubAreas(\r\n    areaId?: number,\r\n    limit: number = 100,\r\n    offset: number = 0,\r\n    sort: string = 'asc',\r\n    sortBy: string = 'id'\r\n  ): Observable<any> {\r\n    const params: any = {\r\n      limit,\r\n      offset,\r\n      sort,\r\n      sortBy,\r\n    };\r\n\r\n    if (areaId) {\r\n      params['areaId'] = areaId;\r\n    }\r\n\r\n    return this.http.get(`${environment.apiUrl}/location/sub-area`, { params });\r\n  }\r\n\r\n  createProperty(formData: FormData): Observable<any> {\r\n    return this.http.post(`${this.apiUrl}/create-unit`, formData);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,mBAAmB,QAAQ,6CAA6C;AAGjF,SAASC,WAAW,QAAQ,8BAA8B;;;AAK1D,OAAM,MAAOC,eAAgB,SAAQF,mBAAmB;EAGvBG,IAAA;EAF/BC,MAAM,GAAG,GAAGH,WAAW,CAACG,MAAM,OAAO;EAErCC,YAA+BF,IAAgB;IAC7C,KAAK,CAACA,IAAI,CAAC;IADkB,KAAAA,IAAI,GAAJA,IAAI;EAEnC;EAEAG,YAAYA,CAAA;IACV,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAC,GAAGN,WAAW,CAACG,MAAM,kBAAkB,EAAE,EAAE,CAAC;EACnE;EAEAI,SAASA,CACPC,MAAe,EACfC,KAAA,GAAgB,GAAG,EACnBC,MAAA,GAAiB,CAAC,EAClBC,IAAA,GAAe,KAAK,EACpBC,MAAA,GAAiB,IAAI;IAErB,MAAMC,MAAM,GAAQ;MAClBJ,KAAK;MACLC,MAAM;MACNC,IAAI;MACJC;KACD;IAED,IAAIJ,MAAM,EAAE;MACVK,MAAM,CAAC,iBAAiB,CAAC,GAAGL,MAAM;IACpC;IAEA,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAC,GAAGN,WAAW,CAACG,MAAM,gBAAgB,EAAE;MAAEU;IAAM,CAAE,CAAC;EACzE;EAEAC,QAAQA,CACNN,MAAe,EACfC,KAAA,GAAgB,GAAG,EACnBC,MAAA,GAAiB,CAAC,EAClBC,IAAA,GAAe,KAAK,EACpBC,MAAA,GAAiB,IAAI;IAErB,MAAMC,MAAM,GAAQ;MAClBJ,KAAK;MACLC,MAAM;MACNC,IAAI;MACJC;KACD;IAED,IAAIJ,MAAM,EAAE;MACVK,MAAM,CAAC,QAAQ,CAAC,GAAGL,MAAM;IAC3B;IAEA,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAC,GAAGN,WAAW,CAACG,MAAM,gBAAgB,EAAE;MAAEU;IAAM,CAAE,CAAC;EACzE;EAEAE,WAAWA,CACTC,MAAe,EACfP,KAAA,GAAgB,GAAG,EACnBC,MAAA,GAAiB,CAAC,EAClBC,IAAA,GAAe,KAAK,EACpBC,MAAA,GAAiB,IAAI;IAErB,MAAMC,MAAM,GAAQ;MAClBJ,KAAK;MACLC,MAAM;MACNC,IAAI;MACJC;KACD;IAED,IAAII,MAAM,EAAE;MACVH,MAAM,CAAC,QAAQ,CAAC,GAAGG,MAAM;IAC3B;IAEA,OAAO,IAAI,CAACd,IAAI,CAACI,GAAG,CAAC,GAAGN,WAAW,CAACG,MAAM,oBAAoB,EAAE;MAAEU;IAAM,CAAE,CAAC;EAC7E;EAEAI,cAAcA,CAACC,QAAkB;IAC/B,OAAO,IAAI,CAAChB,IAAI,CAACiB,IAAI,CAAC,GAAG,IAAI,CAAChB,MAAM,cAAc,EAAEe,QAAQ,CAAC;EAC/D;;qCA5EWjB,eAAe,EAAAmB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;;WAAftB,eAAe;IAAAuB,OAAA,EAAfvB,eAAe,CAAAwB,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}