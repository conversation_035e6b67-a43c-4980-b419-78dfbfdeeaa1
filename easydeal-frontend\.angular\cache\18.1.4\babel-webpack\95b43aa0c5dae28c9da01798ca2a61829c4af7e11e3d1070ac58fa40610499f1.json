{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/authentication.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = () => [\"/requests\"];\nfunction ClientRegistrationStepperComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ClientRegistrationStepperComponent_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStep());\n    });\n    i0.ɵɵtext(1, \" Back to previous step \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientRegistrationStepperComponent_div_12_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \" Please select your gender \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientRegistrationStepperComponent_div_12_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"fullName\"), \" \");\n  }\n}\nfunction ClientRegistrationStepperComponent_div_12_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"email_phone\"), \" \");\n  }\n}\nfunction ClientRegistrationStepperComponent_div_12_span_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 36);\n  }\n}\nfunction ClientRegistrationStepperComponent_div_12_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sending...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientRegistrationStepperComponent_div_12_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Send Verification Code\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientRegistrationStepperComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"h3\", 14);\n    i0.ɵɵtext(2, \"Choose Gender \");\n    i0.ɵɵelement(3, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 16)(5, \"div\", 17)(6, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ClientRegistrationStepperComponent_div_12_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectGender(\"female\"));\n    });\n    i0.ɵɵelement(7, \"i\", 19);\n    i0.ɵɵtext(8, \" Female \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ClientRegistrationStepperComponent_div_12_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectGender(\"male\"));\n    });\n    i0.ɵɵelement(10, \"i\", 21);\n    i0.ɵɵtext(11, \" Male \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, ClientRegistrationStepperComponent_div_12_div_12_Template, 2, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 23)(14, \"label\", 24);\n    i0.ɵɵelement(15, \"i\", 25);\n    i0.ɵɵtext(16, \" Full Name \");\n    i0.ɵɵelement(17, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 26);\n    i0.ɵɵlistener(\"blur\", function ClientRegistrationStepperComponent_div_12_Template_input_blur_18_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"fullName\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ClientRegistrationStepperComponent_div_12_div_19_Template, 2, 1, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 23)(21, \"label\", 27);\n    i0.ɵɵelement(22, \"i\", 28);\n    i0.ɵɵtext(23, \" Enter your email or phone number \");\n    i0.ɵɵelement(24, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 29);\n    i0.ɵɵlistener(\"blur\", function ClientRegistrationStepperComponent_div_12_Template_input_blur_25_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.markFieldAsTouched(\"email_phone\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, ClientRegistrationStepperComponent_div_12_div_26_Template, 2, 1, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function ClientRegistrationStepperComponent_div_12_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleNextStepAndSendCode());\n    });\n    i0.ɵɵtemplate(28, ClientRegistrationStepperComponent_div_12_span_28_Template, 1, 0, \"span\", 31)(29, ClientRegistrationStepperComponent_div_12_span_29_Template, 2, 0, \"span\", 32)(30, ClientRegistrationStepperComponent_div_12_span_30_Template, 2, 0, \"span\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 33);\n    i0.ɵɵtext(32, \" Need help? \");\n    i0.ɵɵelementStart(33, \"span\", 34);\n    i0.ɵɵtext(34, \"Contact us\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"selected\", ctx_r1.selectedGender === \"female\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"selected\", ctx_r1.selectedGender === \"male\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"gender\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"fullName\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"fullName\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"email_phone\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"email_phone\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"loading\", ctx_r1.isLoadingSendOtp);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isStep1Valid() || ctx_r1.isLoadingSendOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingSendOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingSendOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoadingSendOtp);\n  }\n}\nfunction ClientRegistrationStepperComponent_div_13_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"input\", 45);\n    i0.ɵɵlistener(\"input\", function ClientRegistrationStepperComponent_div_13_div_5_Template_input_input_1_listener($event) {\n      const i_r6 = i0.ɵɵrestoreView(_r5).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.autoFocusNext($event, i_r6));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", i_r6);\n  }\n}\nfunction ClientRegistrationStepperComponent_div_13_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46);\n    i0.ɵɵtext(1, \" Resend in \");\n    i0.ɵɵelementStart(2, \"span\", 47);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" 0:\", ctx_r1.countdown < 10 ? \"0\" + ctx_r1.countdown : ctx_r1.countdown, \" \");\n  }\n}\nfunction ClientRegistrationStepperComponent_div_13_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ClientRegistrationStepperComponent_div_13_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onResendCode());\n    });\n    i0.ɵɵtext(1, \" Resend Code \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientRegistrationStepperComponent_div_13_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.otpErrorMessage, \" \");\n  }\n}\nfunction ClientRegistrationStepperComponent_div_13_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 36);\n  }\n}\nfunction ClientRegistrationStepperComponent_div_13_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Verifying...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientRegistrationStepperComponent_div_13_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Verification Code - Next\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientRegistrationStepperComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"h3\", 14);\n    i0.ɵɵtext(2, \"Enter Verification Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37)(4, \"div\", 38);\n    i0.ɵɵtemplate(5, ClientRegistrationStepperComponent_div_13_div_5_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 40);\n    i0.ɵɵtemplate(7, ClientRegistrationStepperComponent_div_13_span_7_Template, 4, 1, \"span\", 41)(8, ClientRegistrationStepperComponent_div_13_button_8_Template, 2, 0, \"button\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, ClientRegistrationStepperComponent_div_13_div_9_Template, 2, 1, \"div\", 43);\n    i0.ɵɵelementStart(10, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function ClientRegistrationStepperComponent_div_13_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.checkOTP());\n    });\n    i0.ɵɵtemplate(11, ClientRegistrationStepperComponent_div_13_span_11_Template, 1, 0, \"span\", 31)(12, ClientRegistrationStepperComponent_div_13_span_12_Template, 2, 0, \"span\", 32)(13, ClientRegistrationStepperComponent_div_13_span_13_Template, 2, 0, \"span\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 33);\n    i0.ɵɵtext(15, \" Need help? \");\n    i0.ɵɵelementStart(16, \"span\", 34);\n    i0.ɵɵtext(17, \"Contact us\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.verificationCodeControls);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showResendButton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showResendButton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.otpErrorMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"loading\", ctx_r1.isLoadingCheckOtp);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isStep2Valid() || ctx_r1.isLoadingCheckOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingCheckOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingCheckOtp);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoadingCheckOtp);\n  }\n}\nfunction ClientRegistrationStepperComponent_div_14_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"email\"), \" \");\n  }\n}\nfunction ClientRegistrationStepperComponent_div_14_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"phone\"), \" \");\n  }\n}\nfunction ClientRegistrationStepperComponent_div_14_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"password\"), \" \");\n  }\n}\nfunction ClientRegistrationStepperComponent_div_14_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"password_confirmation\"), \" \");\n  }\n}\nfunction ClientRegistrationStepperComponent_div_14_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFormError(), \" \");\n  }\n}\nfunction ClientRegistrationStepperComponent_div_14_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"agreeTerms\"), \" \");\n  }\n}\nfunction ClientRegistrationStepperComponent_div_14_span_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 36);\n  }\n}\nfunction ClientRegistrationStepperComponent_div_14_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Creating Account...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientRegistrationStepperComponent_div_14_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Create Account\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientRegistrationStepperComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"h3\", 14);\n    i0.ɵɵtext(2, \"Email, Phone and Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23)(4, \"label\", 50);\n    i0.ɵɵelement(5, \"i\", 25);\n    i0.ɵɵtext(6, \" Email \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 51);\n    i0.ɵɵtemplate(8, ClientRegistrationStepperComponent_div_14_div_8_Template, 2, 1, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 23)(10, \"label\", 52);\n    i0.ɵɵelement(11, \"i\", 28);\n    i0.ɵɵtext(12, \" Phone \");\n    i0.ɵɵelement(13, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 53);\n    i0.ɵɵtemplate(15, ClientRegistrationStepperComponent_div_14_div_15_Template, 2, 1, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 23)(17, \"label\", 54);\n    i0.ɵɵelement(18, \"i\", 55);\n    i0.ɵɵtext(19, \" Password \");\n    i0.ɵɵelement(20, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"input\", 56);\n    i0.ɵɵtemplate(22, ClientRegistrationStepperComponent_div_14_div_22_Template, 2, 1, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 23)(24, \"label\", 57);\n    i0.ɵɵelement(25, \"i\", 55);\n    i0.ɵɵtext(26, \" Confirm Password \");\n    i0.ɵɵelement(27, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"input\", 58);\n    i0.ɵɵtemplate(29, ClientRegistrationStepperComponent_div_14_div_29_Template, 2, 1, \"div\", 22)(30, ClientRegistrationStepperComponent_div_14_div_30_Template, 2, 1, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 23)(32, \"div\", 59);\n    i0.ɵɵelement(33, \"input\", 60);\n    i0.ɵɵelementStart(34, \"label\", 61);\n    i0.ɵɵtext(35, \" I agree to the Terms and Conditions \");\n    i0.ɵɵelement(36, \"span\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(37, ClientRegistrationStepperComponent_div_14_div_37_Template, 2, 1, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function ClientRegistrationStepperComponent_div_14_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.createAccount());\n    });\n    i0.ɵɵtemplate(39, ClientRegistrationStepperComponent_div_14_span_39_Template, 1, 0, \"span\", 31)(40, ClientRegistrationStepperComponent_div_14_span_40_Template, 2, 0, \"span\", 32)(41, ClientRegistrationStepperComponent_div_14_span_41_Template, 2, 0, \"span\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"email\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"email\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"phone\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"phone\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"password\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"password\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"password_confirmation\") || ctx_r1.getFormError());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"password_confirmation\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFormError());\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r1.isFieldInvalid(\"agreeTerms\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isFieldInvalid(\"agreeTerms\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"loading\", ctx_r1.isLoadingCreateAccount);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isStep3Valid() || ctx_r1.isLoadingCreateAccount);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingCreateAccount);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingCreateAccount);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoadingCreateAccount);\n  }\n}\nfunction ClientRegistrationStepperComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63)(2, \"div\", 64);\n    i0.ɵɵelement(3, \"i\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 66);\n    i0.ɵɵtext(5, \"registration Success\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 67);\n    i0.ɵɵtext(7, \" Your account has been successfully created. You can now enjoy the various and amazing services provided by Easy deal through the website or dashboard. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 68);\n    i0.ɵɵelement(9, \"img\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 70);\n    i0.ɵɵtext(11, \" Go to website \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 71)(13, \"span\", 72);\n    i0.ɵɵtext(14, \"Learn all about your account and how to get started\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nexport class ClientRegistrationStepperComponent {\n  fb;\n  authenticationService;\n  cd;\n  onBack = new EventEmitter();\n  onComplete = new EventEmitter();\n  registrationForm;\n  currentStep = 1;\n  totalSteps = 4;\n  selectedGender = '';\n  verificationDigits = ['', '', '', '', ''];\n  countdown = 25;\n  showResendButton = false;\n  isLoadingSendOtp = false;\n  isLoadingCheckOtp = false;\n  isLoadingCreateAccount = false;\n  otpErrorMessage = '';\n  // Validators\n  static noNumbers = Validators.pattern(/^[^0-9]*$/);\n  static emailOrPhonePattern = Validators.pattern(/^([^\\s@]+@[^\\s@]+\\.[^\\s@]+|01[0125]\\d{8}|05\\d{8}|\\+201[0125]\\d{8}|\\+9665\\d{8})$/);\n  static isEmail(value) {\n    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value);\n  }\n  constructor(fb, authenticationService, cd) {\n    this.fb = fb;\n    this.authenticationService = authenticationService;\n    this.cd = cd;\n    this.registrationForm = this.createForm();\n  }\n  ngOnInit() {}\n  createForm() {\n    return this.fb.group({\n      // Step 1: Gender, Full Name, Email/Phone\n      gender: ['', [Validators.required]],\n      fullName: ['', [Validators.required, Validators.minLength(2), ClientRegistrationStepperComponent.noNumbers]],\n      email_phone: ['', [Validators.required, ClientRegistrationStepperComponent.emailOrPhonePattern]],\n      // Step 2: Verification Code\n      verificationCode: this.fb.array(Array(5).fill('').map(() => this.fb.control('', [Validators.required, Validators.pattern('[0-9]')]))),\n      // Step 3: Email, Phone, Password, Terms\n      email: ['', [Validators.email]],\n      phone: ['', [Validators.required, Validators.pattern(/^(01[0125]\\d{8}|05\\d{8}|\\+201[0125]\\d{8}|\\+9665\\d{8})$/)]],\n      password: ['', [Validators.required, Validators.minLength(8), Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/)]],\n      password_confirmation: ['', [Validators.required]],\n      agreeTerms: [false, [Validators.requiredTrue]]\n    });\n  }\n  get verificationCodeControls() {\n    return this.registrationForm.get('verificationCode').controls;\n  }\n  // Helper methods for validation\n  isFieldInvalid(fieldName) {\n    const field = this.registrationForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  markFieldAsTouched(fieldName) {\n    this.registrationForm.get(fieldName)?.markAsTouched();\n  }\n  getFieldError(fieldName) {\n    const field = this.registrationForm.get(fieldName);\n    if (!field?.errors) return '';\n    const errors = field.errors;\n    if (errors['required']) return 'This field is required';\n    if (errors['pattern'] && fieldName === 'fullName') return 'Name cannot contain numbers';\n    if (errors['pattern'] && fieldName === 'email_phone') return 'Enter valid email or phone number';\n    if (errors['pattern'] && fieldName === 'phone') return 'Enter valid phone number';\n    if (errors['pattern'] && fieldName === 'password') return 'Need uppercase, lowercase & number';\n    if (errors['email']) return 'Enter valid email';\n    if (errors['minlength']) return `Min ${errors['minlength'].requiredLength} chars`;\n    return 'Invalid input';\n  }\n  getFormError() {\n    const password = this.registrationForm.get('password')?.value;\n    const confirmPassword = this.registrationForm.get('password_confirmation')?.value;\n    if (password && confirmPassword && password !== confirmPassword) {\n      return 'Passwords do not match';\n    }\n    return '';\n  }\n  // Check if step is valid\n  isStep1Valid() {\n    const gender = this.registrationForm.get('gender');\n    const fullName = this.registrationForm.get('fullName');\n    const emailPhone = this.registrationForm.get('email_phone');\n    return !!(gender?.valid && fullName?.valid && emailPhone?.valid && this.selectedGender);\n  }\n  isStep2Valid() {\n    const verificationCode = this.registrationForm.get('verificationCode');\n    return verificationCode.valid;\n  }\n  isStep3Valid() {\n    const email = this.registrationForm.get('email');\n    const phone = this.registrationForm.get('phone');\n    const password = this.registrationForm.get('password');\n    const passwordConfirmation = this.registrationForm.get('password_confirmation');\n    const agreeTerms = this.registrationForm.get('agreeTerms');\n    // Check if passwords match\n    const passwordsMatch = password?.value === passwordConfirmation?.value;\n    return !!(email?.valid && phone?.valid && password?.valid && passwordConfirmation?.valid && agreeTerms?.valid && passwordsMatch);\n  }\n  nextStep() {\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    } else {\n      this.onBack.emit();\n    }\n  }\n  selectGender(gender) {\n    this.selectedGender = gender;\n    this.registrationForm.patchValue({\n      gender\n    });\n    this.registrationForm.get('gender')?.markAsTouched();\n  }\n  onDigitInput(index) {\n    const code = this.verificationDigits.join('');\n    this.registrationForm.patchValue({\n      verificationCode: code\n    });\n  }\n  handleNextStepAndSendCode() {\n    this.sendVerificationCode(true); // true = move to next step\n  }\n  sendVerificationCode(moveToNextStep = false) {\n    this.isLoadingSendOtp = true;\n    const input = this.registrationForm.get('email_phone')?.value?.trim();\n    let params = {};\n    if (ClientRegistrationStepperComponent.isEmail(input)) {\n      params.email = input;\n    } else {\n      params.phone = input;\n    }\n    console.log(params);\n    this.authenticationService.sendOtp(params).subscribe(response => {\n      console.log('OTP sent:', response);\n      this.isLoadingSendOtp = false;\n      this.startCountdown();\n      // Only move to next step if requested\n      if (moveToNextStep) {\n        this.nextStep();\n      }\n      this.cd.markForCheck();\n    }, error => {\n      console.error('Failed to send OTP:', error);\n      this.isLoadingSendOtp = false;\n      this.cd.markForCheck();\n    });\n  }\n  checkOTP() {\n    this.isLoadingCheckOtp = true;\n    const input = this.registrationForm.get('email_phone')?.value?.trim();\n    const codeArray = this.registrationForm.get('verificationCode')?.value;\n    const otp = codeArray.join('');\n    let params = {};\n    if (ClientRegistrationStepperComponent.isEmail(input)) {\n      params.email = input;\n    } else {\n      params.phone = input;\n    }\n    params.otp = otp;\n    console.log('Checking OTP with params:', params);\n    this.authenticationService.checkOtp(params).subscribe({\n      next: response => {\n        console.log('OTP checked successfully:', response);\n        this.isLoadingCheckOtp = false;\n        this.otpErrorMessage = '';\n        this.cd.markForCheck();\n        this.nextStep();\n      },\n      error: error => {\n        console.error('Failed to check OTP:', error);\n        this.isLoadingCheckOtp = false;\n        this.otpErrorMessage = error?.error?.message;\n        // Clear the OTP inputs\n        this.clearOtpInputs();\n        this.cd.markForCheck();\n      }\n    });\n  }\n  clearOtpInputs() {\n    this.verificationDigits = ['', '', '', '', ''];\n    const verificationCodeArray = this.registrationForm.get('verificationCode');\n    verificationCodeArray.controls.forEach(control => control.setValue(''));\n  }\n  createAccount() {\n    this.isLoadingCreateAccount = true;\n    const input = this.registrationForm.get('email_phone')?.value?.trim();\n    const emailValue = this.registrationForm.get('email')?.value?.trim();\n    let params = {\n      ...this.registrationForm.value\n    };\n    // Handle email_phone field\n    if (ClientRegistrationStepperComponent.isEmail(input)) {\n      params.email = input;\n    } else {\n      params.phone = input;\n    }\n    // Handle optional email field - only include if it has a value\n    if (emailValue && emailValue.length > 0) {\n      params.email = emailValue;\n    } else {\n      // Remove email from params if it's empty\n      delete params.email;\n    }\n    params.role = 'client';\n    console.log('Final payload to be sent:', params);\n    this.authenticationService.register(params).subscribe({\n      next: response => {\n        console.log('registered successfully', response);\n        this.isLoadingCreateAccount = false;\n        let user = response.data;\n        localStorage.setItem('authToken', user.authToken);\n        this.authenticationService.setCurrentUser(response.data);\n        this.cd.markForCheck();\n        this.nextStep();\n      },\n      error: error => {\n        console.error('Failed to register:', error);\n        this.isLoadingCreateAccount = false;\n        // Better error handling\n        let errorMessage = 'Failed to create account. Please try again.';\n        if (error?.error?.message) {\n          errorMessage = error.error.message;\n        } else if (error?.message) {\n          errorMessage = error.message;\n        }\n        Swal.fire(errorMessage);\n        this.cd.markForCheck();\n      }\n    });\n  }\n  autoFocusNext(event, index) {\n    const input = event.target;\n    if (input.value && index < 5) {\n      const nextInput = input.parentElement?.nextElementSibling?.querySelector('input');\n      nextInput?.focus();\n    }\n  }\n  startCountdown() {\n    this.showResendButton = false;\n    this.countdown = 25;\n    const intervalId = setInterval(() => {\n      this.countdown--;\n      if (this.countdown === 0) {\n        clearInterval(intervalId);\n        this.showResendButton = true;\n      }\n      this.cd.markForCheck();\n    }, 1000);\n  }\n  onResendCode() {\n    this.sendVerificationCode(false); // false = don't move to next step\n  }\n  static ɵfac = function ClientRegistrationStepperComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ClientRegistrationStepperComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthenticationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClientRegistrationStepperComponent,\n    selectors: [[\"app-client-registration-stepper\"]],\n    outputs: {\n      onBack: \"onBack\",\n      onComplete: \"onComplete\"\n    },\n    decls: 16,\n    vars: 10,\n    consts: [[1, \"client-registration-stepper\"], [1, \"stepper-header\"], [1, \"stepper-title\"], [1, \"stepper-progress\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"d-flex\"], [1, \"progress-text\"], [\"type\", \"button\", \"class\", \"back-to-previous\", 3, \"click\", 4, \"ngIf\"], [1, \"stepper-form\", 3, \"formGroup\"], [\"class\", \"step-content\", 4, \"ngIf\"], [\"class\", \"step-content success-step\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"back-to-previous\", 3, \"click\"], [1, \"step-content\"], [1, \"step-title\"], [1, \"required\"], [1, \"gender-selection\"], [1, \"gender-buttons\"], [\"type\", \"button\", 1, \"gender-btn\", \"female\", 3, \"click\"], [1, \"ki-outline\", \"ki-lovely\"], [\"type\", \"button\", 1, \"gender-btn\", 3, \"click\"], [1, \"ki-outline\", \"ki-profile-user\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"form-group\"], [\"for\", \"fullName\", 1, \"form-label\"], [1, \"ki-outline\", \"ki-user\"], [\"type\", \"text\", \"id\", \"fullName\", \"formControlName\", \"fullName\", \"placeholder\", \"Name\", \"pattern\", \"[^0-9]*\", \"title\", \"Name cannot contain numbers\", \"required\", \"\", 1, \"form-control\", 3, \"blur\"], [1, \"form-label\"], [1, \"ki-outline\", \"ki-phone\"], [\"type\", \"text\", \"formControlName\", \"email_phone\", \"placeholder\", \"<EMAIL> or 01xxxxxxxxx\", \"title\", \"Enter a valid email address or phone number\", \"autocomplete\", \"email tel\", \"required\", \"\", 1, \"form-control\", 3, \"blur\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-verification\", 3, \"click\", \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"help-text\"], [1, \"contact-link\"], [1, \"invalid-feedback\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"verification-code-section\"], [\"formArrayName\", \"verificationCode\", 1, \"verification-inputs\"], [\"class\", \"code-input\", 4, \"ngFor\", \"ngForOf\"], [1, \"countdown-section\"], [\"class\", \"countdown-text\", 4, \"ngIf\"], [\"class\", \"btn btn-link\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"alert alert-danger mt-3\", \"role\", \"alert\", 4, \"ngIf\"], [1, \"code-input\"], [\"type\", \"text\", \"maxlength\", \"1\", 1, \"verification-input\", 3, \"input\", \"formControlName\"], [1, \"countdown-text\"], [1, \"countdown-timer\"], [1, \"btn\", \"btn-link\", 3, \"click\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"mt-3\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>..\", \"autocomplete\", \"email\", 1, \"form-control\"], [\"for\", \"phone\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phone\", \"formControlName\", \"phone\", \"placeholder\", \"01xxxxxxxxx\", \"required\", \"\", \"autocomplete\", \"tel\", 1, \"form-control\"], [\"for\", \"password\", 1, \"form-label\"], [1, \"ki-outline\", \"ki-lock\"], [\"type\", \"password\", \"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"********\", \"minlength\", \"8\", \"pattern\", \"^(?=.*[a-z])(?=.*[A-Z])(?=.*\\\\d).{8,}$\", \"title\", \"Password must be at least 8 characters with uppercase, lowercase and number\", \"required\", \"\", \"autocomplete\", \"new-password\", 1, \"form-control\"], [\"for\", \"confirmPassword\", 1, \"form-label\"], [\"type\", \"password\", \"id\", \"confirmPassword\", \"formControlName\", \"password_confirmation\", \"placeholder\", \"********\", \"required\", \"\", \"autocomplete\", \"new-password\", 1, \"form-control\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"agreeTerms\", \"formControlName\", \"agreeTerms\", 1, \"form-check-input\"], [\"for\", \"agreeTerms\", 1, \"form-check-label\"], [1, \"step-content\", \"success-step\"], [1, \"success-content\"], [1, \"success-icon\"], [1, \"ki-outline\", \"ki-check-circle\"], [1, \"success-title\"], [1, \"success-message\"], [1, \"success-illustration\"], [\"src\", \"assets/media/login/successfully.png\", \"alt\", \"Success\", 1, \"success-image\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-success-action\", 3, \"routerLink\"], [1, \"additional-info\"], [1, \"info-link\"]],\n    template: function ClientRegistrationStepperComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n        i0.ɵɵtext(3, \"Register as a Client\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4);\n        i0.ɵɵelement(6, \"div\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 6)(8, \"span\", 7);\n        i0.ɵɵtext(9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(10, ClientRegistrationStepperComponent_button_10_Template, 2, 0, \"button\", 8);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(11, \"form\", 9);\n        i0.ɵɵtemplate(12, ClientRegistrationStepperComponent_div_12_Template, 35, 17, \"div\", 10)(13, ClientRegistrationStepperComponent_div_13_Template, 18, 10, \"div\", 10)(14, ClientRegistrationStepperComponent_div_14_Template, 42, 22, \"div\", 10)(15, ClientRegistrationStepperComponent_div_15_Template, 15, 2, \"div\", 11);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵstyleProp(\"width\", ctx.currentStep / ctx.totalSteps * 100, \"%\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate2(\"Step \", ctx.currentStep, \" of \", ctx.totalSteps, \"\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 0 && ctx.currentStep < 4);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"formGroup\", ctx.registrationForm);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MinLengthValidator, i1.MaxLengthValidator, i1.PatternValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormArrayName, i4.RouterLink],\n    styles: [\".required[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  font-weight: bold;\\n  margin-left: 2px;\\n}\\n\\n.client-registration-stepper[_ngcontent-%COMP%] {\\n  width: 100%;\\n  direction: ltr;\\n  text-align: left;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-title[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  margin-bottom: 15px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 6px;\\n  background-color: #e9ecef;\\n  border-radius: 3px;\\n  overflow: hidden;\\n  margin-bottom: 10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #28a745 0%, #20c997 100%);\\n  border-radius: 3px;\\n  transition: width 0.3s ease;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 5px;\\n  margin-top: 5px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  margin: 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .back-to-previous[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #007bff;\\n  font-size: 0.85rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  padding: 3px 0;\\n  text-decoration: none;\\n  transition: color 0.3s ease;\\n  white-space: nowrap;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .back-to-previous[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n  text-decoration: underline;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .back-to-previous[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  min-height: 50px;\\n  margin-bottom: 5px;\\n  text-align: center;\\n  padding: 2px 5px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content.success-step[_ngcontent-%COMP%] {\\n  min-height: 50px;\\n  padding: 5px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n  color: #232176;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n  text-align: center;\\n  padding-bottom: 3px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n  margin: 0 -10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n  padding: 0 10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin-bottom: 6px;\\n  font-weight: 500;\\n  color: #333;\\n  font-size: 0.9rem;\\n  text-align: left;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 1rem;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  transition: all 0.3s ease;\\n  background-color: #fff;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .invalid-feedback[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #dc3545;\\n  font-size: 12px;\\n  margin-top: 5px;\\n  font-weight: 500;\\n  text-align: left;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in;\\n}\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-5px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-help-text[_ngcontent-%COMP%] {\\n  margin-top: 3px;\\n  text-align: left;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-help-text[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 11px;\\n  line-height: 1.3;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .gender-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .gender-selection[_ngcontent-%COMP%]   .gender-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  justify-content: center;\\n  margin-bottom: 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .gender-selection[_ngcontent-%COMP%]   .gender-buttons[_ngcontent-%COMP%]   .gender-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 6px 12px;\\n  border: 2px solid #e9ecef;\\n  border-radius: 25px;\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  font-weight: 500;\\n  font-size: 13px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  min-width: 100px;\\n  justify-content: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .gender-selection[_ngcontent-%COMP%]   .gender-buttons[_ngcontent-%COMP%]   .gender-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .gender-selection[_ngcontent-%COMP%]   .gender-buttons[_ngcontent-%COMP%]   .gender-btn[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff;\\n  background: #e7f3ff;\\n  color: #007bff;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .gender-selection[_ngcontent-%COMP%]   .gender-buttons[_ngcontent-%COMP%]   .gender-btn.selected[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  background: #007bff;\\n  color: white;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .gender-selection[_ngcontent-%COMP%]   .gender-buttons[_ngcontent-%COMP%]   .gender-btn.female.selected[_ngcontent-%COMP%] {\\n  border-color: #e91e63;\\n  background: #e91e63;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .gender-selection[_ngcontent-%COMP%]   .gender-buttons[_ngcontent-%COMP%]   .gender-btn.female[_ngcontent-%COMP%]:hover {\\n  border-color: #e91e63;\\n  background: #fce4ec;\\n  color: #e91e63;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%] {\\n  margin: 15px 0;\\n  text-align: center;\\n  padding: 0 5px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 10px;\\n  margin-bottom: 12px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  text-align: center;\\n  font-size: 1.5rem;\\n  font-weight: bold;\\n  border: 2px solid #ddd;\\n  border-radius: 8px;\\n  background-color: #fff;\\n  color: #333;\\n  transition: all 0.3s ease;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%]::-webkit-outer-spin-button, .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%]::-webkit-inner-spin-button {\\n  -webkit-appearance: none;\\n  margin: 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[type=number][_ngcontent-%COMP%] {\\n  -moz-appearance: textfield;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .countdown-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 12px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .countdown-section[_ngcontent-%COMP%]   .countdown-text[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 13px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .countdown-section[_ngcontent-%COMP%]   .countdown-text[_ngcontent-%COMP%]   .countdown-timer[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-weight: bold;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .progress-bar-container[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .progress-bar-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 5px;\\n  height: 8px;\\n  background: #e9ecef;\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .progress-bar-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: #28a745;\\n  border-radius: 2px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .progress-bar-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill.step-1[_ngcontent-%COMP%] {\\n  background: #28a745;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .progress-bar-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill.step-2[_ngcontent-%COMP%] {\\n  background: #28a745;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .progress-bar-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill.step-3[_ngcontent-%COMP%] {\\n  background: #28a745;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .progress-bar-container[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill.step-4[_ngcontent-%COMP%] {\\n  background: #28a745;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  margin-top: 12px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-top: 3px;\\n  width: 18px;\\n  height: 18px;\\n  cursor: pointer;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  color: #333;\\n  cursor: pointer;\\n  line-height: 1.4;\\n  text-align: left;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .terms-link[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .terms-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 30px;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);\\n  border-radius: 12px;\\n  border: 1px solid #d4edda;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #28a745;\\n  margin-bottom: 15px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #155724;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  margin-bottom: 10px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #155724;\\n  font-size: 0.95rem;\\n  line-height: 1.5;\\n  margin: 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%] {\\n  margin-top: 25px;\\n  padding: 20px;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  border: 1px solid #e9ecef;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin-bottom: 15px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  margin-bottom: 10px;\\n  font-size: 0.9rem;\\n  color: #555;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 1rem;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 15px;\\n  margin-top: 30px;\\n  flex-direction: row;\\n  padding-top: 20px;\\n  border-top: 1px solid #e9ecef;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 120px;\\n  justify-content: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n  transform: translateY(-1px);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  color: white;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 10px 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%] {\\n  max-width: 320px;\\n  margin: 0 auto;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: #28a745;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-title[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin-bottom: 8px;\\n  direction: ltr;\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.8rem;\\n  line-height: 1.3;\\n  margin-bottom: 12px;\\n  direction: ltr;\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-illustration[_ngcontent-%COMP%] {\\n  margin: 10px 0;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .success-illustration[_ngcontent-%COMP%]   .success-image[_ngcontent-%COMP%] {\\n  max-width: 180px;\\n  width: 100%;\\n  height: auto;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .btn-success-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4c63d2 0%, #3b4db8 100%);\\n  border: none;\\n  color: white;\\n  padding: 10px 25px;\\n  font-size: 13px;\\n  font-weight: 600;\\n  border-radius: 25px;\\n  margin: 10px auto;\\n  transition: all 0.3s ease;\\n  direction: ltr;\\n  display: block;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .btn-success-action[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(76, 99, 210, 0.3);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .additional-info[_ngcontent-%COMP%]   .info-link[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 12px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  text-decoration: none;\\n  direction: ltr;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .success-step[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   .additional-info[_ngcontent-%COMP%]   .info-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  padding: 12px 15px;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n  border: 1px solid transparent;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .alert.alert-danger[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  border-color: #f5c6cb;\\n  color: #721c24;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%] {\\n  color: #28a745 !important;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 8px 12px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  border-radius: 20px;\\n  margin: 8px auto 6px auto;\\n  background: linear-gradient(135deg, #4c63d2 0%, #3b4db8 100%);\\n  border: none;\\n  color: white;\\n  transition: all 0.3s ease;\\n  display: block;\\n  text-align: center;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(76, 99, 210, 0.3);\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n  background: #6c757d !important;\\n  box-shadow: none !important;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .btn-verification[_ngcontent-%COMP%]:disabled:hover {\\n  transform: none !important;\\n  box-shadow: none !important;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 8px;\\n  color: #6c757d;\\n  font-size: 12px;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  cursor: pointer;\\n  text-decoration: none;\\n}\\n.client-registration-stepper[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]   .contact-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-title[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n    min-height: auto;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 15px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .gender-selection[_ngcontent-%COMP%]   .gender-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .gender-selection[_ngcontent-%COMP%]   .gender-buttons[_ngcontent-%COMP%]   .gender-btn[_ngcontent-%COMP%] {\\n    min-width: auto;\\n    padding: 12px 20px;\\n    font-size: 14px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%] {\\n    gap: 10px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n    font-size: 20px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .welcome-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .terms-content[_ngcontent-%COMP%]   .client-benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .stepper-navigation[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    min-width: auto;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-title[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-header[_ngcontent-%COMP%]   .stepper-progress[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: 10px 12px;\\n    font-size: 13px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .gender-selection[_ngcontent-%COMP%]   .gender-buttons[_ngcontent-%COMP%]   .gender-btn[_ngcontent-%COMP%] {\\n    padding: 10px 15px;\\n    font-size: 13px;\\n    min-width: auto;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .gender-selection[_ngcontent-%COMP%]   .gender-buttons[_ngcontent-%COMP%]   .gender-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%] {\\n    gap: 8px;\\n  }\\n  .client-registration-stepper[_ngcontent-%COMP%]   .stepper-form[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .verification-code-section[_ngcontent-%COMP%]   .verification-inputs[_ngcontent-%COMP%]   .code-input[_ngcontent-%COMP%]   .verification-input[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n    font-size: 18px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵlistener", "ClientRegistrationStepperComponent_button_10_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "previousStep", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "getFieldError", "ɵɵelement", "ClientRegistrationStepperComponent_div_12_Template_button_click_6_listener", "_r3", "selectGender", "ClientRegistrationStepperComponent_div_12_Template_button_click_9_listener", "ɵɵtemplate", "ClientRegistrationStepperComponent_div_12_div_12_Template", "ClientRegistrationStepperComponent_div_12_Template_input_blur_18_listener", "mark<PERSON>ieldAsTouched", "ClientRegistrationStepperComponent_div_12_div_19_Template", "ClientRegistrationStepperComponent_div_12_Template_input_blur_25_listener", "ClientRegistrationStepperComponent_div_12_div_26_Template", "ClientRegistrationStepperComponent_div_12_Template_button_click_27_listener", "handleNextStepAndSendCode", "ClientRegistrationStepperComponent_div_12_span_28_Template", "ClientRegistrationStepperComponent_div_12_span_29_Template", "ClientRegistrationStepperComponent_div_12_span_30_Template", "ɵɵclassProp", "selected<PERSON><PERSON>", "ɵɵproperty", "isFieldInvalid", "isLoadingSendOtp", "isStep1Valid", "ClientRegistrationStepperComponent_div_13_div_5_Template_input_input_1_listener", "$event", "i_r6", "_r5", "index", "autoFocusNext", "countdown", "ClientRegistrationStepperComponent_div_13_button_8_Template_button_click_0_listener", "_r7", "onResendCode", "otpErrorMessage", "ClientRegistrationStepperComponent_div_13_div_5_Template", "ClientRegistrationStepperComponent_div_13_span_7_Template", "ClientRegistrationStepperComponent_div_13_button_8_Template", "ClientRegistrationStepperComponent_div_13_div_9_Template", "ClientRegistrationStepperComponent_div_13_Template_button_click_10_listener", "_r4", "checkOTP", "ClientRegistrationStepperComponent_div_13_span_11_Template", "ClientRegistrationStepperComponent_div_13_span_12_Template", "ClientRegistrationStepperComponent_div_13_span_13_Template", "verificationCodeControls", "showResendButton", "isLoadingCheckOtp", "isStep2Valid", "getFormError", "ClientRegistrationStepperComponent_div_14_div_8_Template", "ClientRegistrationStepperComponent_div_14_div_15_Template", "ClientRegistrationStepperComponent_div_14_div_22_Template", "ClientRegistrationStepperComponent_div_14_div_29_Template", "ClientRegistrationStepperComponent_div_14_div_30_Template", "ClientRegistrationStepperComponent_div_14_div_37_Template", "ClientRegistrationStepperComponent_div_14_Template_button_click_38_listener", "_r8", "createAccount", "ClientRegistrationStepperComponent_div_14_span_39_Template", "ClientRegistrationStepperComponent_div_14_span_40_Template", "ClientRegistrationStepperComponent_div_14_span_41_Template", "isLoadingCreateAccount", "isStep3Valid", "ɵɵpureFunction0", "_c0", "ClientRegistrationStepperComponent", "fb", "authenticationService", "cd", "onBack", "onComplete", "registrationForm", "currentStep", "totalSteps", "verificationDigits", "noNumbers", "pattern", "emailOrPhonePattern", "isEmail", "value", "test", "constructor", "createForm", "ngOnInit", "group", "gender", "required", "fullName", "<PERSON><PERSON><PERSON><PERSON>", "email_phone", "verificationCode", "array", "Array", "fill", "map", "control", "email", "phone", "password", "password_confirmation", "agreeTerms", "requiredTrue", "get", "controls", "fieldName", "field", "invalid", "dirty", "touched", "<PERSON><PERSON><PERSON><PERSON>ched", "errors", "<PERSON><PERSON><PERSON><PERSON>", "confirmPassword", "emailPhone", "valid", "passwordConfirmation", "passwordsMatch", "nextStep", "emit", "patchValue", "onDigitInput", "code", "join", "sendVerificationCode", "moveToNextStep", "input", "trim", "params", "console", "log", "sendOtp", "subscribe", "response", "startCountdown", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "codeArray", "otp", "checkOtp", "next", "message", "clearOtpInputs", "verificationCodeArray", "for<PERSON>ach", "setValue", "emailValue", "length", "role", "register", "user", "data", "localStorage", "setItem", "authToken", "setCurrentUser", "errorMessage", "fire", "event", "target", "nextInput", "parentElement", "nextElement<PERSON><PERSON>ling", "querySelector", "focus", "intervalId", "setInterval", "clearInterval", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthenticationService", "ChangeDetectorRef", "selectors", "outputs", "decls", "vars", "consts", "template", "ClientRegistrationStepperComponent_Template", "rf", "ctx", "ClientRegistrationStepperComponent_button_10_Template", "ClientRegistrationStepperComponent_div_12_Template", "ClientRegistrationStepperComponent_div_13_Template", "ClientRegistrationStepperComponent_div_14_Template", "ClientRegistrationStepperComponent_div_15_Template", "ɵɵstyleProp", "ɵɵtextInterpolate2"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\authentication\\components\\client-registration-stepper\\client-registration-stepper.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\authentication\\components\\client-registration-stepper\\client-registration-stepper.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Output,\r\n  EventEmitter,\r\n  OnInit,\r\n  ChangeDetectorRef,\r\n} from '@angular/core';\r\nimport {\r\n  <PERSON><PERSON><PERSON>y,\r\n  FormBuilder,\r\n  FormGroup,\r\n  Validators,\r\n} from '@angular/forms';\r\nimport { ClientRegistrationData } from '../../models';\r\nimport { AuthenticationService } from '../../services/authentication.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-client-registration-stepper',\r\n  templateUrl: './client-registration-stepper.component.html',\r\n  styleUrls: ['./client-registration-stepper.component.scss'],\r\n})\r\nexport class ClientRegistrationStepperComponent implements OnInit {\r\n  @Output() onBack = new EventEmitter<void>();\r\n  @Output() onComplete = new EventEmitter<ClientRegistrationData>();\r\n\r\n  registrationForm: FormGroup;\r\n  currentStep = 1;\r\n  totalSteps = 4;\r\n  selectedGender = '';\r\n  verificationDigits: string[] = ['', '', '', '', ''];\r\n  countdown: number = 25;\r\n  showResendButton: boolean = false;\r\n  isLoadingSendOtp: boolean = false;\r\n  isLoadingCheckOtp: boolean = false;\r\n  isLoadingCreateAccount: boolean = false;\r\n  otpErrorMessage: string = '';\r\n\r\n  // Validators\r\n  static noNumbers = Validators.pattern(/^[^0-9]*$/);\r\n  static emailOrPhonePattern = Validators.pattern(/^([^\\s@]+@[^\\s@]+\\.[^\\s@]+|01[0125]\\d{8}|05\\d{8}|\\+201[0125]\\d{8}|\\+9665\\d{8})$/);\r\n\r\n  private static isEmail(value: string): boolean {\r\n    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value);\r\n  }\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authenticationService: AuthenticationService,\r\n    private cd: ChangeDetectorRef\r\n  ) {\r\n    this.registrationForm = this.createForm();\r\n  }\r\n\r\n  ngOnInit(): void {}\r\n\r\n  private createForm(): FormGroup {\r\n    return this.fb.group({\r\n      // Step 1: Gender, Full Name, Email/Phone\r\n      gender: ['', [Validators.required]],\r\n      fullName: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.minLength(2),\r\n          ClientRegistrationStepperComponent.noNumbers,\r\n        ],\r\n      ],\r\n      email_phone: [\r\n        '',\r\n        [Validators.required, ClientRegistrationStepperComponent.emailOrPhonePattern],\r\n      ],\r\n\r\n      // Step 2: Verification Code\r\n      verificationCode: this.fb.array(\r\n        Array(5)\r\n          .fill('')\r\n          .map(() =>\r\n            this.fb.control('', [\r\n              Validators.required,\r\n              Validators.pattern('[0-9]'),\r\n            ])\r\n          )\r\n      ),\r\n\r\n      // Step 3: Email, Phone, Password, Terms\r\n      email: ['', [Validators.email]],\r\n      phone: ['', [Validators.required, Validators.pattern(/^(01[0125]\\d{8}|05\\d{8}|\\+201[0125]\\d{8}|\\+9665\\d{8})$/)]],\r\n      password: [\r\n        '',\r\n        [\r\n          Validators.required,\r\n          Validators.minLength(8),\r\n          Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/),\r\n        ],\r\n      ],\r\n      password_confirmation: ['', [Validators.required]],\r\n      agreeTerms: [false, [Validators.requiredTrue]],\r\n    });\r\n  }\r\n\r\n  get verificationCodeControls() {\r\n    return (this.registrationForm.get('verificationCode') as FormArray)\r\n      .controls;\r\n  }\r\n\r\n  // Helper methods for validation\r\n  isFieldInvalid(fieldName: string): boolean {\r\n    const field = this.registrationForm.get(fieldName);\r\n    return !!(field && field.invalid && (field.dirty || field.touched));\r\n  }\r\n\r\n  markFieldAsTouched(fieldName: string): void {\r\n    this.registrationForm.get(fieldName)?.markAsTouched();\r\n  }\r\n\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.registrationForm.get(fieldName);\r\n    if (!field?.errors) return '';\r\n\r\n    const errors = field.errors;\r\n    if (errors['required']) return 'This field is required';\r\n    if (errors['pattern'] && fieldName === 'fullName') return 'Name cannot contain numbers';\r\n    if (errors['pattern'] && fieldName === 'email_phone') return 'Enter valid email or phone number';\r\n    if (errors['pattern'] && fieldName === 'phone') return 'Enter valid phone number';\r\n    if (errors['pattern'] && fieldName === 'password') return 'Need uppercase, lowercase & number';\r\n    if (errors['email']) return 'Enter valid email';\r\n    if (errors['minlength']) return `Min ${errors['minlength'].requiredLength} chars`;\r\n\r\n    return 'Invalid input';\r\n  }\r\n\r\n  getFormError(): string {\r\n    const password = this.registrationForm.get('password')?.value;\r\n    const confirmPassword = this.registrationForm.get(\r\n      'password_confirmation'\r\n    )?.value;\r\n\r\n    if (password && confirmPassword && password !== confirmPassword) {\r\n      return 'Passwords do not match';\r\n    }\r\n    return '';\r\n  }\r\n\r\n  // Check if step is valid\r\n  isStep1Valid(): boolean {\r\n    const gender = this.registrationForm.get('gender');\r\n    const fullName = this.registrationForm.get('fullName');\r\n    const emailPhone = this.registrationForm.get('email_phone');\r\n\r\n    return !!(\r\n      gender?.valid &&\r\n      fullName?.valid &&\r\n      emailPhone?.valid &&\r\n      this.selectedGender\r\n    );\r\n  }\r\n\r\n  isStep2Valid(): boolean {\r\n    const verificationCode = this.registrationForm.get(\r\n      'verificationCode'\r\n    ) as FormArray;\r\n    return verificationCode.valid;\r\n  }\r\n\r\n  isStep3Valid(): boolean {\r\n    const email = this.registrationForm.get('email');\r\n    const phone = this.registrationForm.get('phone');\r\n    const password = this.registrationForm.get('password');\r\n    const passwordConfirmation = this.registrationForm.get(\r\n      'password_confirmation'\r\n    );\r\n    const agreeTerms = this.registrationForm.get('agreeTerms');\r\n\r\n    // Check if passwords match\r\n    const passwordsMatch = password?.value === passwordConfirmation?.value;\r\n\r\n    return !!(\r\n      email?.valid &&\r\n      phone?.valid &&\r\n      password?.valid &&\r\n      passwordConfirmation?.valid &&\r\n      agreeTerms?.valid &&\r\n      passwordsMatch\r\n    );\r\n  }\r\n\r\n  nextStep(): void {\r\n    if (this.currentStep < this.totalSteps) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n\r\n  previousStep(): void {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    } else {\r\n      this.onBack.emit();\r\n    }\r\n  }\r\n\r\n  selectGender(gender: string): void {\r\n    this.selectedGender = gender;\r\n    this.registrationForm.patchValue({ gender });\r\n    this.registrationForm.get('gender')?.markAsTouched();\r\n  }\r\n\r\n  onDigitInput(index: number): void {\r\n    const code = this.verificationDigits.join('');\r\n    this.registrationForm.patchValue({ verificationCode: code });\r\n  }\r\n\r\n  handleNextStepAndSendCode(): void {\r\n    this.sendVerificationCode(true); // true = move to next step\r\n  }\r\n\r\n  sendVerificationCode(moveToNextStep: boolean = false) {\r\n    this.isLoadingSendOtp = true;\r\n    const input = this.registrationForm.get('email_phone')?.value?.trim();\r\n\r\n    let params: { email?: string; phone?: string } = {};\r\n    if (ClientRegistrationStepperComponent.isEmail(input)) {\r\n      params.email = input;\r\n    } else {\r\n      params.phone = input;\r\n    }\r\n\r\n    console.log(params);\r\n    this.authenticationService.sendOtp(params).subscribe(\r\n      (response: any) => {\r\n        console.log('OTP sent:', response);\r\n        this.isLoadingSendOtp = false;\r\n        this.startCountdown();\r\n\r\n        // Only move to next step if requested\r\n        if (moveToNextStep) {\r\n          this.nextStep();\r\n        }\r\n\r\n        this.cd.markForCheck();\r\n      },\r\n      (error: any) => {\r\n        console.error('Failed to send OTP:', error);\r\n        this.isLoadingSendOtp = false;\r\n        this.cd.markForCheck();\r\n      }\r\n    );\r\n  }\r\n\r\n  checkOTP() {\r\n    this.isLoadingCheckOtp = true;\r\n    const input = this.registrationForm.get('email_phone')?.value?.trim();\r\n    const codeArray = this.registrationForm.get('verificationCode')?.value;\r\n    const otp = codeArray.join('');\r\n\r\n    let params: { email?: string; phone?: string; otp?: number } = {};\r\n    if (ClientRegistrationStepperComponent.isEmail(input)) {\r\n      params.email = input;\r\n    } else {\r\n      params.phone = input;\r\n    }\r\n\r\n    params.otp = otp;\r\n    console.log('Checking OTP with params:', params);\r\n\r\n    this.authenticationService.checkOtp(params).subscribe({\r\n      next: (response: any) => {\r\n        console.log('OTP checked successfully:', response);\r\n        this.isLoadingCheckOtp = false;\r\n        this.otpErrorMessage = '';\r\n        this.cd.markForCheck();\r\n        this.nextStep();\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Failed to check OTP:', error);\r\n        this.isLoadingCheckOtp = false;\r\n        this.otpErrorMessage = error?.error?.message;\r\n        // Clear the OTP inputs\r\n        this.clearOtpInputs();\r\n\r\n        this.cd.markForCheck();\r\n      },\r\n    });\r\n  }\r\n\r\n  private clearOtpInputs() {\r\n    this.verificationDigits = ['', '', '', '', ''];\r\n    const verificationCodeArray = this.registrationForm.get(\r\n      'verificationCode'\r\n    ) as FormArray;\r\n    verificationCodeArray.controls.forEach((control) => control.setValue(''));\r\n  }\r\n\r\n  createAccount(): void {\r\n    this.isLoadingCreateAccount = true;\r\n\r\n    const input = this.registrationForm.get('email_phone')?.value?.trim();\r\n    const emailValue = this.registrationForm.get('email')?.value?.trim();\r\n\r\n    let params: any = { ...this.registrationForm.value };\r\n\r\n    // Handle email_phone field\r\n    if (ClientRegistrationStepperComponent.isEmail(input)) {\r\n      params.email = input;\r\n    } else {\r\n      params.phone = input;\r\n    }\r\n\r\n    // Handle optional email field - only include if it has a value\r\n    if (emailValue && emailValue.length > 0) {\r\n      params.email = emailValue;\r\n    } else {\r\n      // Remove email from params if it's empty\r\n      delete params.email;\r\n    }\r\n\r\n    params.role = 'client';\r\n\r\n    console.log('Final payload to be sent:', params);\r\n\r\n    this.authenticationService.register(params).subscribe({\r\n      next: (response: any) => {\r\n        console.log('registered successfully', response);\r\n        this.isLoadingCreateAccount = false;\r\n        let user = response.data;\r\n        localStorage.setItem('authToken', user.authToken);\r\n        this.authenticationService.setCurrentUser(response.data);\r\n\r\n        this.cd.markForCheck();\r\n        this.nextStep();\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Failed to register:', error);\r\n        this.isLoadingCreateAccount = false;\r\n\r\n        // Better error handling\r\n        let errorMessage = 'Failed to create account. Please try again.';\r\n        if (error?.error?.message) {\r\n          errorMessage = error.error.message;\r\n        } else if (error?.message) {\r\n          errorMessage = error.message;\r\n        }\r\n\r\n        Swal.fire(errorMessage);\r\n        this.cd.markForCheck();\r\n      },\r\n    });\r\n  }\r\n\r\n  autoFocusNext(event: any, index: number): void {\r\n    const input = event.target;\r\n    if (input.value && index < 5) {\r\n      const nextInput =\r\n        input.parentElement?.nextElementSibling?.querySelector('input');\r\n      nextInput?.focus();\r\n    }\r\n  }\r\n\r\n  startCountdown() {\r\n    this.showResendButton = false;\r\n    this.countdown = 25;\r\n\r\n    const intervalId = setInterval(() => {\r\n      this.countdown--;\r\n      if (this.countdown === 0) {\r\n        clearInterval(intervalId);\r\n        this.showResendButton = true;\r\n      }\r\n      this.cd.markForCheck();\r\n    }, 1000);\r\n  }\r\n  onResendCode() {\r\n    this.sendVerificationCode(false); // false = don't move to next step\r\n  }\r\n}\r\n", "<div class=\"client-registration-stepper\">\r\n  <!-- Stepper Header -->\r\n  <div class=\"stepper-header\">\r\n    <h2 class=\"stepper-title\">Register as a Client</h2>\r\n    <div class=\"stepper-progress\">\r\n      <div class=\"progress-bar\">\r\n        <div class=\"progress-fill\" [style.width.%]=\"(currentStep / totalSteps) * 100\"></div>\r\n      </div>\r\n      <div class=\"d-flex\">\r\n        <span class=\"progress-text\">Step {{ currentStep }} of {{ totalSteps }}</span>\r\n        <button *ngIf=\"currentStep > 0 && currentStep < 4\" type=\"button\" class=\"back-to-previous\"\r\n          (click)=\"previousStep()\">\r\n          Back to previous step\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Stepper Content -->\r\n  <form [formGroup]=\"registrationForm\" class=\"stepper-form\">\r\n    <!-- Step 1: Gender Selection, Full Name, Email/Phone -->\r\n    <div *ngIf=\"currentStep === 1\" class=\"step-content\">\r\n      <h3 class=\"step-title\">Choose Gender <span class=\"required\"></span></h3>\r\n\r\n      <!-- Gender Selection -->\r\n      <div class=\"gender-selection\">\r\n        <div class=\"gender-buttons\">\r\n          <button type=\"button\" class=\"gender-btn female\" [class.selected]=\"selectedGender === 'female'\"\r\n            (click)=\"selectGender('female')\">\r\n            <i class=\"ki-outline ki-lovely\"></i>\r\n            Female\r\n          </button>\r\n          <button type=\"button\" class=\"gender-btn\" [class.selected]=\"selectedGender === 'male'\"\r\n            (click)=\"selectGender('male')\">\r\n            <i class=\"ki-outline ki-profile-user\"></i>\r\n            Male\r\n          </button>\r\n        </div>\r\n        <div *ngIf=\"isFieldInvalid('gender')\" class=\"invalid-feedback\">\r\n          Please select your gender\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Full Name -->\r\n      <div class=\"form-group\">\r\n        <label for=\"fullName\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-user\"></i>\r\n          Full Name <span class=\"required\"></span>\r\n        </label>\r\n        <input type=\"text\" id=\"fullName\" formControlName=\"fullName\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('fullName')\" placeholder=\"Name\" pattern=\"[^0-9]*\"\r\n          title=\"Name cannot contain numbers\" (blur)=\"markFieldAsTouched('fullName')\" required />\r\n        <div *ngIf=\"isFieldInvalid('fullName')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"fullName\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Email or Phone -->\r\n      <div class=\"form-group\">\r\n        <label class=\"form-label\">\r\n          <i class=\"ki-outline ki-phone\"></i>\r\n          Enter your email or phone number <span class=\"required\"></span>\r\n        </label>\r\n        <input type=\"text\" formControlName=\"email_phone\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('email_phone')\" placeholder=\"<EMAIL> or 01xxxxxxxxx\"\r\n          title=\"Enter a valid email address or phone number\" autocomplete=\"email tel\"\r\n          (blur)=\"markFieldAsTouched('email_phone')\" required />\r\n        <div *ngIf=\"isFieldInvalid('email_phone')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"email_phone\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Send Verification Button -->\r\n      <button type=\"button\" class=\"btn btn-primary btn-verification\" [class.loading]=\"isLoadingSendOtp\"\r\n        [disabled]=\"!isStep1Valid() || isLoadingSendOtp\" (click)=\"handleNextStepAndSendCode()\">\r\n        <span *ngIf=\"isLoadingSendOtp\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n        <span *ngIf=\"isLoadingSendOtp\">Sending...</span>\r\n        <span *ngIf=\"!isLoadingSendOtp\">Send Verification Code</span>\r\n      </button>\r\n\r\n      <!-- Help Text -->\r\n      <div class=\"help-text\">\r\n        Need help? <span class=\"contact-link\">Contact us</span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 2: Verification Code -->\r\n    <div *ngIf=\"currentStep === 2\" class=\"step-content\">\r\n      <h3 class=\"step-title\">Enter Verification Code</h3>\r\n\r\n      <div class=\"verification-code-section\">\r\n        <div formArrayName=\"verificationCode\" class=\"verification-inputs\">\r\n          <div class=\"code-input\" *ngFor=\"let ctrl of verificationCodeControls; let i = index\">\r\n            <input type=\"text\" maxlength=\"1\" class=\"verification-input\" [formControlName]=\"i\"\r\n              (input)=\"autoFocusNext($event, i)\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"countdown-section\">\r\n        <span class=\"countdown-text\" *ngIf=\"!showResendButton\">\r\n          Resend in\r\n          <span class=\"countdown-timer\">\r\n            0:{{ countdown < 10 ? \"0\" + countdown : countdown }} </span>\r\n          </span>\r\n\r\n          <button *ngIf=\"showResendButton\" class=\"btn btn-link\" (click)=\"onResendCode()\">\r\n            Resend Code\r\n          </button>\r\n      </div>\r\n\r\n      <!-- OTP Error Message -->\r\n      <div *ngIf=\"otpErrorMessage\" class=\"alert alert-danger mt-3\" role=\"alert\">\r\n        {{ otpErrorMessage }}\r\n      </div>\r\n\r\n      <button type=\"button\" class=\"btn btn-primary btn-verification\" [class.loading]=\"isLoadingCheckOtp\"\r\n        [disabled]=\"!isStep2Valid() || isLoadingCheckOtp\" (click)=\"checkOTP()\">\r\n        <span *ngIf=\"isLoadingCheckOtp\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n        <span *ngIf=\"isLoadingCheckOtp\">Verifying...</span>\r\n        <span *ngIf=\"!isLoadingCheckOtp\">Verification Code - Next</span>\r\n      </button>\r\n\r\n      <div class=\"help-text\">\r\n        Need help? <span class=\"contact-link\">Contact us</span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Step 3: Username and Password -->\r\n    <div *ngIf=\"currentStep === 3\" class=\"step-content\">\r\n      <h3 class=\"step-title\">Email, Phone and Password</h3>\r\n\r\n      <!-- Email -->\r\n      <div class=\"form-group\">\r\n        <label for=\"email\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-user\"></i>\r\n          Email\r\n        </label>\r\n        <input type=\"email\" id=\"email\" formControlName=\"email\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('email')\" placeholder=\"<EMAIL>..\" autocomplete=\"email\" />\r\n        <div *ngIf=\"isFieldInvalid('email')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"email\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Phone -->\r\n      <div class=\"form-group\">\r\n        <label for=\"phone\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-phone\"></i>\r\n          Phone <span class=\"required\"></span>\r\n        </label>\r\n        <input type=\"tel\" id=\"phone\" formControlName=\"phone\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('phone')\" placeholder=\"01xxxxxxxxx\" required autocomplete=\"tel\" />\r\n        <div *ngIf=\"isFieldInvalid('phone')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"phone\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Password -->\r\n      <div class=\"form-group\">\r\n        <label for=\"password\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-lock\"></i>\r\n          Password <span class=\"required\"></span>\r\n        </label>\r\n        <input type=\"password\" id=\"password\" formControlName=\"password\" class=\"form-control\"\r\n          [class.is-invalid]=\"isFieldInvalid('password')\" placeholder=\"********\" minlength=\"8\"\r\n          pattern=\"^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,}$\"\r\n          title=\"Password must be at least 8 characters with uppercase, lowercase and number\" required\r\n          autocomplete=\"new-password\" />\r\n        <div *ngIf=\"isFieldInvalid('password')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"password\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!--Confirm Password -->\r\n      <div class=\"form-group\">\r\n        <label for=\"confirmPassword\" class=\"form-label\">\r\n          <i class=\"ki-outline ki-lock\"></i>\r\n          Confirm Password <span class=\"required\"></span>\r\n        </label>\r\n        <input type=\"password\" id=\"confirmPassword\" formControlName=\"password_confirmation\" class=\"form-control\"\r\n          [class.is-invalid]=\"\r\n            isFieldInvalid('password_confirmation') || getFormError()\r\n          \" placeholder=\"********\" required autocomplete=\"new-password\" />\r\n        <div *ngIf=\"isFieldInvalid('password_confirmation')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"password_confirmation\") }}\r\n        </div>\r\n        <div *ngIf=\"getFormError()\" class=\"invalid-feedback\">\r\n          {{ getFormError() }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Terms Agreement -->\r\n      <div class=\"form-group\">\r\n        <div class=\"form-check\">\r\n          <input type=\"checkbox\" id=\"agreeTerms\" formControlName=\"agreeTerms\" class=\"form-check-input\"\r\n            [class.is-invalid]=\"isFieldInvalid('agreeTerms')\" />\r\n          <label for=\"agreeTerms\" class=\"form-check-label\">\r\n            I agree to the Terms and Conditions <span class=\"required\"></span>\r\n          </label>\r\n        </div>\r\n        <div *ngIf=\"isFieldInvalid('agreeTerms')\" class=\"invalid-feedback\">\r\n          {{ getFieldError(\"agreeTerms\") }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Create Account Button -->\r\n      <button type=\"button\" class=\"btn btn-primary btn-verification\" [class.loading]=\"isLoadingCreateAccount\"\r\n        [disabled]=\"!isStep3Valid() || isLoadingCreateAccount\" (click)=\"createAccount()\">\r\n        <span *ngIf=\"isLoadingCreateAccount\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\r\n        <span *ngIf=\"isLoadingCreateAccount\">Creating Account...</span>\r\n        <span *ngIf=\"!isLoadingCreateAccount\">Create Account</span>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Step 4: Success Page -->\r\n    <div *ngIf=\"currentStep === 4\" class=\"step-content success-step\">\r\n      <div class=\"success-content\">\r\n        <div class=\"success-icon\">\r\n          <i class=\"ki-outline ki-check-circle\"></i>\r\n        </div>\r\n\r\n        <h3 class=\"success-title\">registration Success</h3>\r\n\r\n        <p class=\"success-message\">\r\n          Your account has been successfully created. You can now enjoy the\r\n          various and amazing services provided by Easy deal through the website\r\n          or dashboard.\r\n        </p>\r\n\r\n        <div class=\"success-illustration\">\r\n          <!-- Success illustration will go here -->\r\n          <!-- <img src=\"/assets/media/login/successfully.png\" alt=\"Success\" class=\"success-image\" /> -->\r\n          <!-- <img src=\"~src/assets/media/login/successfully.png\" alt=\"Success\" class=\"success-image\" /> -->\r\n          <img src=\"assets/media/login/successfully.png\" alt=\"Success\" class=\"success-image\" />\r\n        </div>\r\n\r\n        <button type=\"button\" class=\"btn btn-primary btn-success-action\" [routerLink]=\"['/requests']\">\r\n          Go to website\r\n        </button>\r\n\r\n        <div class=\"additional-info\">\r\n          <span class=\"info-link\">Learn all about your account and how to get started</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </form>\r\n</div>"], "mappings": "AAAA,SAGEA,YAAY,QAGP,eAAe;AACtB,SAIEC,UAAU,QACL,gBAAgB;AAGvB,OAAOC,IAAI,MAAM,aAAa;;;;;;;;;;ICLtBC,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAAE,UAAA,mBAAAC,8EAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IACxBT,EAAA,CAAAU,MAAA,8BACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IAyBTX,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAU,MAAA,kCACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;IAYNX,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,kBACF;;;;;IAaAd,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,qBACF;;;;;IAMAd,EAAA,CAAAe,SAAA,eAAkG;;;;;IAClGf,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAU,MAAA,iBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAChDX,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAU,MAAA,6BAAsB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IAvD/DX,EADF,CAAAC,cAAA,cAAoD,aAC3B;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAe,SAAA,eAA8B;IAAAf,EAAA,CAAAW,YAAA,EAAK;IAKpEX,EAFJ,CAAAC,cAAA,cAA8B,cACA,iBAES;IAAjCD,EAAA,CAAAE,UAAA,mBAAAc,2EAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,YAAA,CAAa,QAAQ,CAAC;IAAA,EAAC;IAChClB,EAAA,CAAAe,SAAA,YAAoC;IACpCf,EAAA,CAAAU,MAAA,eACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,iBACiC;IAA/BD,EAAA,CAAAE,UAAA,mBAAAiB,2EAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,YAAA,CAAa,MAAM,CAAC;IAAA,EAAC;IAC9BlB,EAAA,CAAAe,SAAA,aAA0C;IAC1Cf,EAAA,CAAAU,MAAA,cACF;IACFV,EADE,CAAAW,YAAA,EAAS,EACL;IACNX,EAAA,CAAAoB,UAAA,KAAAC,yDAAA,kBAA+D;IAGjErB,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,eAAwB,iBACmB;IACvCD,EAAA,CAAAe,SAAA,aAAkC;IAClCf,EAAA,CAAAU,MAAA,mBAAU;IAAAV,EAAA,CAAAe,SAAA,gBAA8B;IAC1Cf,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAC,cAAA,iBAEyF;IAAnDD,EAAA,CAAAE,UAAA,kBAAAoB,0EAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAQF,MAAA,CAAAiB,kBAAA,CAAmB,UAAU,CAAC;IAAA,EAAC;IAF7EvB,EAAA,CAAAW,YAAA,EAEyF;IACzFX,EAAA,CAAAoB,UAAA,KAAAI,yDAAA,kBAAiE;IAGnExB,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,eAAwB,iBACI;IACxBD,EAAA,CAAAe,SAAA,aAAmC;IACnCf,EAAA,CAAAU,MAAA,0CAAiC;IAAAV,EAAA,CAAAe,SAAA,gBAA8B;IACjEf,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAC,cAAA,iBAGwD;IAAtDD,EAAA,CAAAE,UAAA,kBAAAuB,0EAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAQF,MAAA,CAAAiB,kBAAA,CAAmB,aAAa,CAAC;IAAA,EAAC;IAH5CvB,EAAA,CAAAW,YAAA,EAGwD;IACxDX,EAAA,CAAAoB,UAAA,KAAAM,yDAAA,kBAAoE;IAGtE1B,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,kBACyF;IAAtCD,EAAA,CAAAE,UAAA,mBAAAyB,4EAAA;MAAA3B,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,yBAAA,EAA2B;IAAA,EAAC;IAGtF5B,EAFA,CAAAoB,UAAA,KAAAS,0DAAA,mBAA2F,KAAAC,0DAAA,mBAC5D,KAAAC,0DAAA,mBACC;IAClC/B,EAAA,CAAAW,YAAA,EAAS;IAGTX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,MAAA,oBAAW;IAAAV,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAEpDV,EAFoD,CAAAW,YAAA,EAAO,EACnD,EACF;;;;IAzDgDX,EAAA,CAAAY,SAAA,GAA8C;IAA9CZ,EAAA,CAAAgC,WAAA,aAAA1B,MAAA,CAAA2B,cAAA,cAA8C;IAKrDjC,EAAA,CAAAY,SAAA,GAA4C;IAA5CZ,EAAA,CAAAgC,WAAA,aAAA1B,MAAA,CAAA2B,cAAA,YAA4C;IAMjFjC,EAAA,CAAAY,SAAA,GAA8B;IAA9BZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA6B,cAAA,WAA8B;IAYlCnC,EAAA,CAAAY,SAAA,GAA+C;IAA/CZ,EAAA,CAAAgC,WAAA,eAAA1B,MAAA,CAAA6B,cAAA,aAA+C;IAE3CnC,EAAA,CAAAY,SAAA,EAAgC;IAAhCZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA6B,cAAA,aAAgC;IAYpCnC,EAAA,CAAAY,SAAA,GAAkD;IAAlDZ,EAAA,CAAAgC,WAAA,eAAA1B,MAAA,CAAA6B,cAAA,gBAAkD;IAG9CnC,EAAA,CAAAY,SAAA,EAAmC;IAAnCZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA6B,cAAA,gBAAmC;IAMoBnC,EAAA,CAAAY,SAAA,EAAkC;IAAlCZ,EAAA,CAAAgC,WAAA,YAAA1B,MAAA,CAAA8B,gBAAA,CAAkC;IAC/FpC,EAAA,CAAAkC,UAAA,cAAA5B,MAAA,CAAA+B,YAAA,MAAA/B,MAAA,CAAA8B,gBAAA,CAAgD;IACzCpC,EAAA,CAAAY,SAAA,EAAsB;IAAtBZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA8B,gBAAA,CAAsB;IACtBpC,EAAA,CAAAY,SAAA,EAAsB;IAAtBZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA8B,gBAAA,CAAsB;IACtBpC,EAAA,CAAAY,SAAA,EAAuB;IAAvBZ,EAAA,CAAAkC,UAAA,UAAA5B,MAAA,CAAA8B,gBAAA,CAAuB;;;;;;IAgB1BpC,EADF,CAAAC,cAAA,cAAqF,gBAE5C;IAArCD,EAAA,CAAAE,UAAA,mBAAAoC,gFAAAC,MAAA;MAAA,MAAAC,IAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAAqC,GAAA,EAAAC,KAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqC,aAAA,CAAAJ,MAAA,EAAAC,IAAA,CAAwB;IAAA,EAAC;IACtCxC,EAFE,CAAAW,YAAA,EACuC,EACnC;;;;IAFwDX,EAAA,CAAAY,SAAA,EAAqB;IAArBZ,EAAA,CAAAkC,UAAA,oBAAAM,IAAA,CAAqB;;;;;IAOrFxC,EAAA,CAAAC,cAAA,eAAuD;IACrDD,EAAA,CAAAU,MAAA,kBACA;IAAAV,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAU,MAAA,GAAqD;IACvDV,EADuD,CAAAW,YAAA,EAAO,EACvD;;;;IADLX,EAAA,CAAAY,SAAA,GAAqD;IAArDZ,EAAA,CAAAa,kBAAA,QAAAP,MAAA,CAAAsC,SAAA,cAAAtC,MAAA,CAAAsC,SAAA,GAAAtC,MAAA,CAAAsC,SAAA,MAAqD;;;;;;IAGvD5C,EAAA,CAAAC,cAAA,iBAA+E;IAAzBD,EAAA,CAAAE,UAAA,mBAAA2C,oFAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAA0C,GAAA;MAAA,MAAAxC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyC,YAAA,EAAc;IAAA,EAAC;IAC5E/C,EAAA,CAAAU,MAAA,oBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;IAIbX,EAAA,CAAAC,cAAA,cAA0E;IACxED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAA0C,eAAA,MACF;;;;;IAIEhD,EAAA,CAAAe,SAAA,eAAmG;;;;;IACnGf,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAU,MAAA,mBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IACnDX,EAAA,CAAAC,cAAA,WAAiC;IAAAD,EAAA,CAAAU,MAAA,+BAAwB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IAhClEX,EADF,CAAAC,cAAA,cAAoD,aAC3B;IAAAD,EAAA,CAAAU,MAAA,8BAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAGjDX,EADF,CAAAC,cAAA,cAAuC,cAC6B;IAChED,EAAA,CAAAoB,UAAA,IAAA6B,wDAAA,kBAAqF;IAKzFjD,EADE,CAAAW,YAAA,EAAM,EACF;IAENX,EAAA,CAAAC,cAAA,cAA+B;IAO3BD,EANF,CAAAoB,UAAA,IAAA8B,yDAAA,mBAAuD,IAAAC,2DAAA,qBAM0B;IAGnFnD,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAoB,UAAA,IAAAgC,wDAAA,kBAA0E;IAI1EpD,EAAA,CAAAC,cAAA,kBACyE;IAArBD,EAAA,CAAAE,UAAA,mBAAAmD,4EAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiD,QAAA,EAAU;IAAA,EAAC;IAGtEvD,EAFA,CAAAoB,UAAA,KAAAoC,0DAAA,mBAA4F,KAAAC,0DAAA,mBAC5D,KAAAC,0DAAA,mBACC;IACnC1D,EAAA,CAAAW,YAAA,EAAS;IAETX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,MAAA,oBAAW;IAAAV,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAEpDV,EAFoD,CAAAW,YAAA,EAAO,EACnD,EACF;;;;IAlCyCX,EAAA,CAAAY,SAAA,GAA6B;IAA7BZ,EAAA,CAAAkC,UAAA,YAAA5B,MAAA,CAAAqD,wBAAA,CAA6B;IAQ1C3D,EAAA,CAAAY,SAAA,GAAuB;IAAvBZ,EAAA,CAAAkC,UAAA,UAAA5B,MAAA,CAAAsD,gBAAA,CAAuB;IAM1C5D,EAAA,CAAAY,SAAA,EAAsB;IAAtBZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAsD,gBAAA,CAAsB;IAM7B5D,EAAA,CAAAY,SAAA,EAAqB;IAArBZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA0C,eAAA,CAAqB;IAIoChD,EAAA,CAAAY,SAAA,EAAmC;IAAnCZ,EAAA,CAAAgC,WAAA,YAAA1B,MAAA,CAAAuD,iBAAA,CAAmC;IAChG7D,EAAA,CAAAkC,UAAA,cAAA5B,MAAA,CAAAwD,YAAA,MAAAxD,MAAA,CAAAuD,iBAAA,CAAiD;IAC1C7D,EAAA,CAAAY,SAAA,EAAuB;IAAvBZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAuD,iBAAA,CAAuB;IACvB7D,EAAA,CAAAY,SAAA,EAAuB;IAAvBZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAuD,iBAAA,CAAuB;IACvB7D,EAAA,CAAAY,SAAA,EAAwB;IAAxBZ,EAAA,CAAAkC,UAAA,UAAA5B,MAAA,CAAAuD,iBAAA,CAAwB;;;;;IAoB/B7D,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,eACF;;;;;IAWAd,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,eACF;;;;;IAcAd,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,kBACF;;;;;IAaAd,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,+BACF;;;;;IACAd,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAyD,YAAA,QACF;;;;;IAYA/D,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IADJX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,oBACF;;;;;IAMAd,EAAA,CAAAe,SAAA,eAAwG;;;;;IACxGf,EAAA,CAAAC,cAAA,WAAqC;IAAAD,EAAA,CAAAU,MAAA,0BAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAC/DX,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IAjF7DX,EADF,CAAAC,cAAA,cAAoD,aAC3B;IAAAD,EAAA,CAAAU,MAAA,gCAAyB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAInDX,EADF,CAAAC,cAAA,cAAwB,gBACgB;IACpCD,EAAA,CAAAe,SAAA,YAAkC;IAClCf,EAAA,CAAAU,MAAA,cACF;IAAAV,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAe,SAAA,gBACqG;IACrGf,EAAA,CAAAoB,UAAA,IAAA4C,wDAAA,kBAA8D;IAGhEhE,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,cAAwB,iBACgB;IACpCD,EAAA,CAAAe,SAAA,aAAmC;IACnCf,EAAA,CAAAU,MAAA,eAAM;IAAAV,EAAA,CAAAe,SAAA,gBAA8B;IACtCf,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAe,SAAA,iBACuG;IACvGf,EAAA,CAAAoB,UAAA,KAAA6C,yDAAA,kBAA8D;IAGhEjE,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,eAAwB,iBACmB;IACvCD,EAAA,CAAAe,SAAA,aAAkC;IAClCf,EAAA,CAAAU,MAAA,kBAAS;IAAAV,EAAA,CAAAe,SAAA,gBAA8B;IACzCf,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAe,SAAA,iBAIgC;IAChCf,EAAA,CAAAoB,UAAA,KAAA8C,yDAAA,kBAAiE;IAGnElE,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,eAAwB,iBAC0B;IAC9CD,EAAA,CAAAe,SAAA,aAAkC;IAClCf,EAAA,CAAAU,MAAA,0BAAiB;IAAAV,EAAA,CAAAe,SAAA,gBAA8B;IACjDf,EAAA,CAAAW,YAAA,EAAQ;IACRX,EAAA,CAAAe,SAAA,iBAGkE;IAIlEf,EAHA,CAAAoB,UAAA,KAAA+C,yDAAA,kBAA8E,KAAAC,yDAAA,kBAGzB;IAGvDpE,EAAA,CAAAW,YAAA,EAAM;IAIJX,EADF,CAAAC,cAAA,eAAwB,eACE;IACtBD,EAAA,CAAAe,SAAA,iBACsD;IACtDf,EAAA,CAAAC,cAAA,iBAAiD;IAC/CD,EAAA,CAAAU,MAAA,6CAAoC;IAAAV,EAAA,CAAAe,SAAA,gBAA8B;IAEtEf,EADE,CAAAW,YAAA,EAAQ,EACJ;IACNX,EAAA,CAAAoB,UAAA,KAAAiD,yDAAA,kBAAmE;IAGrErE,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,kBACmF;IAA1BD,EAAA,CAAAE,UAAA,mBAAAoE,4EAAA;MAAAtE,EAAA,CAAAI,aAAA,CAAAmE,GAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkE,aAAA,EAAe;IAAA,EAAC;IAGhFxE,EAFA,CAAAoB,UAAA,KAAAqD,0DAAA,mBAAiG,KAAAC,0DAAA,mBAC5D,KAAAC,0DAAA,mBACC;IAE1C3E,EADE,CAAAW,YAAA,EAAS,EACL;;;;IA1EAX,EAAA,CAAAY,SAAA,GAA4C;IAA5CZ,EAAA,CAAAgC,WAAA,eAAA1B,MAAA,CAAA6B,cAAA,UAA4C;IACxCnC,EAAA,CAAAY,SAAA,EAA6B;IAA7BZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA6B,cAAA,UAA6B;IAYjCnC,EAAA,CAAAY,SAAA,GAA4C;IAA5CZ,EAAA,CAAAgC,WAAA,eAAA1B,MAAA,CAAA6B,cAAA,UAA4C;IACxCnC,EAAA,CAAAY,SAAA,EAA6B;IAA7BZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA6B,cAAA,UAA6B;IAYjCnC,EAAA,CAAAY,SAAA,GAA+C;IAA/CZ,EAAA,CAAAgC,WAAA,eAAA1B,MAAA,CAAA6B,cAAA,aAA+C;IAI3CnC,EAAA,CAAAY,SAAA,EAAgC;IAAhCZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA6B,cAAA,aAAgC;IAYpCnC,EAAA,CAAAY,SAAA,GAEC;IAFDZ,EAAA,CAAAgC,WAAA,eAAA1B,MAAA,CAAA6B,cAAA,6BAAA7B,MAAA,CAAAyD,YAAA,GAEC;IACG/D,EAAA,CAAAY,SAAA,EAA6C;IAA7CZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA6B,cAAA,0BAA6C;IAG7CnC,EAAA,CAAAY,SAAA,EAAoB;IAApBZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAyD,YAAA,GAAoB;IAStB/D,EAAA,CAAAY,SAAA,GAAiD;IAAjDZ,EAAA,CAAAgC,WAAA,eAAA1B,MAAA,CAAA6B,cAAA,eAAiD;IAK/CnC,EAAA,CAAAY,SAAA,GAAkC;IAAlCZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA6B,cAAA,eAAkC;IAMqBnC,EAAA,CAAAY,SAAA,EAAwC;IAAxCZ,EAAA,CAAAgC,WAAA,YAAA1B,MAAA,CAAAsE,sBAAA,CAAwC;IACrG5E,EAAA,CAAAkC,UAAA,cAAA5B,MAAA,CAAAuE,YAAA,MAAAvE,MAAA,CAAAsE,sBAAA,CAAsD;IAC/C5E,EAAA,CAAAY,SAAA,EAA4B;IAA5BZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAsE,sBAAA,CAA4B;IAC5B5E,EAAA,CAAAY,SAAA,EAA4B;IAA5BZ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAsE,sBAAA,CAA4B;IAC5B5E,EAAA,CAAAY,SAAA,EAA6B;IAA7BZ,EAAA,CAAAkC,UAAA,UAAA5B,MAAA,CAAAsE,sBAAA,CAA6B;;;;;IAOpC5E,EAFJ,CAAAC,cAAA,cAAiE,cAClC,cACD;IACxBD,EAAA,CAAAe,SAAA,YAA0C;IAC5Cf,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAU,MAAA,2BAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAEnDX,EAAA,CAAAC,cAAA,YAA2B;IACzBD,EAAA,CAAAU,MAAA,+JAGF;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAEJX,EAAA,CAAAC,cAAA,cAAkC;IAIhCD,EAAA,CAAAe,SAAA,cAAqF;IACvFf,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,kBAA8F;IAC5FD,EAAA,CAAAU,MAAA,uBACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAGPX,EADF,CAAAC,cAAA,eAA6B,gBACH;IAAAD,EAAA,CAAAU,MAAA,2DAAmD;IAGjFV,EAHiF,CAAAW,YAAA,EAAO,EAC9E,EACF,EACF;;;IAR+DX,EAAA,CAAAY,SAAA,IAA4B;IAA5BZ,EAAA,CAAAkC,UAAA,eAAAlC,EAAA,CAAA8E,eAAA,IAAAC,GAAA,EAA4B;;;ADvNrG,OAAM,MAAOC,kCAAkC;EAyBnCC,EAAA;EACAC,qBAAA;EACAC,EAAA;EA1BAC,MAAM,GAAG,IAAIvF,YAAY,EAAQ;EACjCwF,UAAU,GAAG,IAAIxF,YAAY,EAA0B;EAEjEyF,gBAAgB;EAChBC,WAAW,GAAG,CAAC;EACfC,UAAU,GAAG,CAAC;EACdvD,cAAc,GAAG,EAAE;EACnBwD,kBAAkB,GAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACnD7C,SAAS,GAAW,EAAE;EACtBgB,gBAAgB,GAAY,KAAK;EACjCxB,gBAAgB,GAAY,KAAK;EACjCyB,iBAAiB,GAAY,KAAK;EAClCe,sBAAsB,GAAY,KAAK;EACvC5B,eAAe,GAAW,EAAE;EAE5B;EACA,OAAO0C,SAAS,GAAG5F,UAAU,CAAC6F,OAAO,CAAC,WAAW,CAAC;EAClD,OAAOC,mBAAmB,GAAG9F,UAAU,CAAC6F,OAAO,CAAC,iFAAiF,CAAC;EAE1H,OAAOE,OAAOA,CAACC,KAAa;IAClC,OAAO,4BAA4B,CAACC,IAAI,CAACD,KAAK,CAAC;EACjD;EAEAE,YACUf,EAAe,EACfC,qBAA4C,EAC5CC,EAAqB;IAFrB,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,EAAE,GAAFA,EAAE;IAEV,IAAI,CAACG,gBAAgB,GAAG,IAAI,CAACW,UAAU,EAAE;EAC3C;EAEAC,QAAQA,CAAA,GAAU;EAEVD,UAAUA,CAAA;IAChB,OAAO,IAAI,CAAChB,EAAE,CAACkB,KAAK,CAAC;MACnB;MACAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAACtG,UAAU,CAACuG,QAAQ,CAAC,CAAC;MACnCC,QAAQ,EAAE,CACR,EAAE,EACF,CACExG,UAAU,CAACuG,QAAQ,EACnBvG,UAAU,CAACyG,SAAS,CAAC,CAAC,CAAC,EACvBvB,kCAAkC,CAACU,SAAS,CAC7C,CACF;MACDc,WAAW,EAAE,CACX,EAAE,EACF,CAAC1G,UAAU,CAACuG,QAAQ,EAAErB,kCAAkC,CAACY,mBAAmB,CAAC,CAC9E;MAED;MACAa,gBAAgB,EAAE,IAAI,CAACxB,EAAE,CAACyB,KAAK,CAC7BC,KAAK,CAAC,CAAC,CAAC,CACLC,IAAI,CAAC,EAAE,CAAC,CACRC,GAAG,CAAC,MACH,IAAI,CAAC5B,EAAE,CAAC6B,OAAO,CAAC,EAAE,EAAE,CAClBhH,UAAU,CAACuG,QAAQ,EACnBvG,UAAU,CAAC6F,OAAO,CAAC,OAAO,CAAC,CAC5B,CAAC,CACH,CACJ;MAED;MACAoB,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjH,UAAU,CAACiH,KAAK,CAAC,CAAC;MAC/BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAClH,UAAU,CAACuG,QAAQ,EAAEvG,UAAU,CAAC6F,OAAO,CAAC,wDAAwD,CAAC,CAAC,CAAC;MAChHsB,QAAQ,EAAE,CACR,EAAE,EACF,CACEnH,UAAU,CAACuG,QAAQ,EACnBvG,UAAU,CAACyG,SAAS,CAAC,CAAC,CAAC,EACvBzG,UAAU,CAAC6F,OAAO,CAAC,iCAAiC,CAAC,CACtD,CACF;MACDuB,qBAAqB,EAAE,CAAC,EAAE,EAAE,CAACpH,UAAU,CAACuG,QAAQ,CAAC,CAAC;MAClDc,UAAU,EAAE,CAAC,KAAK,EAAE,CAACrH,UAAU,CAACsH,YAAY,CAAC;KAC9C,CAAC;EACJ;EAEA,IAAIzD,wBAAwBA,CAAA;IAC1B,OAAQ,IAAI,CAAC2B,gBAAgB,CAAC+B,GAAG,CAAC,kBAAkB,CAAe,CAChEC,QAAQ;EACb;EAEA;EACAnF,cAAcA,CAACoF,SAAiB;IAC9B,MAAMC,KAAK,GAAG,IAAI,CAAClC,gBAAgB,CAAC+B,GAAG,CAACE,SAAS,CAAC;IAClD,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAKD,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACG,OAAO,CAAC,CAAC;EACrE;EAEApG,kBAAkBA,CAACgG,SAAiB;IAClC,IAAI,CAACjC,gBAAgB,CAAC+B,GAAG,CAACE,SAAS,CAAC,EAAEK,aAAa,EAAE;EACvD;EAEA9G,aAAaA,CAACyG,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAAClC,gBAAgB,CAAC+B,GAAG,CAACE,SAAS,CAAC;IAClD,IAAI,CAACC,KAAK,EAAEK,MAAM,EAAE,OAAO,EAAE;IAE7B,MAAMA,MAAM,GAAGL,KAAK,CAACK,MAAM;IAC3B,IAAIA,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,wBAAwB;IACvD,IAAIA,MAAM,CAAC,SAAS,CAAC,IAAIN,SAAS,KAAK,UAAU,EAAE,OAAO,6BAA6B;IACvF,IAAIM,MAAM,CAAC,SAAS,CAAC,IAAIN,SAAS,KAAK,aAAa,EAAE,OAAO,mCAAmC;IAChG,IAAIM,MAAM,CAAC,SAAS,CAAC,IAAIN,SAAS,KAAK,OAAO,EAAE,OAAO,0BAA0B;IACjF,IAAIM,MAAM,CAAC,SAAS,CAAC,IAAIN,SAAS,KAAK,UAAU,EAAE,OAAO,oCAAoC;IAC9F,IAAIM,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,mBAAmB;IAC/C,IAAIA,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,OAAOA,MAAM,CAAC,WAAW,CAAC,CAACC,cAAc,QAAQ;IAEjF,OAAO,eAAe;EACxB;EAEA/D,YAAYA,CAAA;IACV,MAAMkD,QAAQ,GAAG,IAAI,CAAC3B,gBAAgB,CAAC+B,GAAG,CAAC,UAAU,CAAC,EAAEvB,KAAK;IAC7D,MAAMiC,eAAe,GAAG,IAAI,CAACzC,gBAAgB,CAAC+B,GAAG,CAC/C,uBAAuB,CACxB,EAAEvB,KAAK;IAER,IAAImB,QAAQ,IAAIc,eAAe,IAAId,QAAQ,KAAKc,eAAe,EAAE;MAC/D,OAAO,wBAAwB;IACjC;IACA,OAAO,EAAE;EACX;EAEA;EACA1F,YAAYA,CAAA;IACV,MAAM+D,MAAM,GAAG,IAAI,CAACd,gBAAgB,CAAC+B,GAAG,CAAC,QAAQ,CAAC;IAClD,MAAMf,QAAQ,GAAG,IAAI,CAAChB,gBAAgB,CAAC+B,GAAG,CAAC,UAAU,CAAC;IACtD,MAAMW,UAAU,GAAG,IAAI,CAAC1C,gBAAgB,CAAC+B,GAAG,CAAC,aAAa,CAAC;IAE3D,OAAO,CAAC,EACNjB,MAAM,EAAE6B,KAAK,IACb3B,QAAQ,EAAE2B,KAAK,IACfD,UAAU,EAAEC,KAAK,IACjB,IAAI,CAAChG,cAAc,CACpB;EACH;EAEA6B,YAAYA,CAAA;IACV,MAAM2C,gBAAgB,GAAG,IAAI,CAACnB,gBAAgB,CAAC+B,GAAG,CAChD,kBAAkB,CACN;IACd,OAAOZ,gBAAgB,CAACwB,KAAK;EAC/B;EAEApD,YAAYA,CAAA;IACV,MAAMkC,KAAK,GAAG,IAAI,CAACzB,gBAAgB,CAAC+B,GAAG,CAAC,OAAO,CAAC;IAChD,MAAML,KAAK,GAAG,IAAI,CAAC1B,gBAAgB,CAAC+B,GAAG,CAAC,OAAO,CAAC;IAChD,MAAMJ,QAAQ,GAAG,IAAI,CAAC3B,gBAAgB,CAAC+B,GAAG,CAAC,UAAU,CAAC;IACtD,MAAMa,oBAAoB,GAAG,IAAI,CAAC5C,gBAAgB,CAAC+B,GAAG,CACpD,uBAAuB,CACxB;IACD,MAAMF,UAAU,GAAG,IAAI,CAAC7B,gBAAgB,CAAC+B,GAAG,CAAC,YAAY,CAAC;IAE1D;IACA,MAAMc,cAAc,GAAGlB,QAAQ,EAAEnB,KAAK,KAAKoC,oBAAoB,EAAEpC,KAAK;IAEtE,OAAO,CAAC,EACNiB,KAAK,EAAEkB,KAAK,IACZjB,KAAK,EAAEiB,KAAK,IACZhB,QAAQ,EAAEgB,KAAK,IACfC,oBAAoB,EAAED,KAAK,IAC3Bd,UAAU,EAAEc,KAAK,IACjBE,cAAc,CACf;EACH;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC7C,WAAW,GAAG,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACD,WAAW,EAAE;IACpB;EACF;EAEA9E,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC8E,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB,CAAC,MAAM;MACL,IAAI,CAACH,MAAM,CAACiD,IAAI,EAAE;IACpB;EACF;EAEAnH,YAAYA,CAACkF,MAAc;IACzB,IAAI,CAACnE,cAAc,GAAGmE,MAAM;IAC5B,IAAI,CAACd,gBAAgB,CAACgD,UAAU,CAAC;MAAElC;IAAM,CAAE,CAAC;IAC5C,IAAI,CAACd,gBAAgB,CAAC+B,GAAG,CAAC,QAAQ,CAAC,EAAEO,aAAa,EAAE;EACtD;EAEAW,YAAYA,CAAC7F,KAAa;IACxB,MAAM8F,IAAI,GAAG,IAAI,CAAC/C,kBAAkB,CAACgD,IAAI,CAAC,EAAE,CAAC;IAC7C,IAAI,CAACnD,gBAAgB,CAACgD,UAAU,CAAC;MAAE7B,gBAAgB,EAAE+B;IAAI,CAAE,CAAC;EAC9D;EAEA5G,yBAAyBA,CAAA;IACvB,IAAI,CAAC8G,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;EACnC;EAEAA,oBAAoBA,CAACC,cAAA,GAA0B,KAAK;IAClD,IAAI,CAACvG,gBAAgB,GAAG,IAAI;IAC5B,MAAMwG,KAAK,GAAG,IAAI,CAACtD,gBAAgB,CAAC+B,GAAG,CAAC,aAAa,CAAC,EAAEvB,KAAK,EAAE+C,IAAI,EAAE;IAErE,IAAIC,MAAM,GAAuC,EAAE;IACnD,IAAI9D,kCAAkC,CAACa,OAAO,CAAC+C,KAAK,CAAC,EAAE;MACrDE,MAAM,CAAC/B,KAAK,GAAG6B,KAAK;IACtB,CAAC,MAAM;MACLE,MAAM,CAAC9B,KAAK,GAAG4B,KAAK;IACtB;IAEAG,OAAO,CAACC,GAAG,CAACF,MAAM,CAAC;IACnB,IAAI,CAAC5D,qBAAqB,CAAC+D,OAAO,CAACH,MAAM,CAAC,CAACI,SAAS,CACjDC,QAAa,IAAI;MAChBJ,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEG,QAAQ,CAAC;MAClC,IAAI,CAAC/G,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACgH,cAAc,EAAE;MAErB;MACA,IAAIT,cAAc,EAAE;QAClB,IAAI,CAACP,QAAQ,EAAE;MACjB;MAEA,IAAI,CAACjD,EAAE,CAACkE,YAAY,EAAE;IACxB,CAAC,EACAC,KAAU,IAAI;MACbP,OAAO,CAACO,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAI,CAAClH,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAAC+C,EAAE,CAACkE,YAAY,EAAE;IACxB,CAAC,CACF;EACH;EAEA9F,QAAQA,CAAA;IACN,IAAI,CAACM,iBAAiB,GAAG,IAAI;IAC7B,MAAM+E,KAAK,GAAG,IAAI,CAACtD,gBAAgB,CAAC+B,GAAG,CAAC,aAAa,CAAC,EAAEvB,KAAK,EAAE+C,IAAI,EAAE;IACrE,MAAMU,SAAS,GAAG,IAAI,CAACjE,gBAAgB,CAAC+B,GAAG,CAAC,kBAAkB,CAAC,EAAEvB,KAAK;IACtE,MAAM0D,GAAG,GAAGD,SAAS,CAACd,IAAI,CAAC,EAAE,CAAC;IAE9B,IAAIK,MAAM,GAAqD,EAAE;IACjE,IAAI9D,kCAAkC,CAACa,OAAO,CAAC+C,KAAK,CAAC,EAAE;MACrDE,MAAM,CAAC/B,KAAK,GAAG6B,KAAK;IACtB,CAAC,MAAM;MACLE,MAAM,CAAC9B,KAAK,GAAG4B,KAAK;IACtB;IAEAE,MAAM,CAACU,GAAG,GAAGA,GAAG;IAChBT,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEF,MAAM,CAAC;IAEhD,IAAI,CAAC5D,qBAAqB,CAACuE,QAAQ,CAACX,MAAM,CAAC,CAACI,SAAS,CAAC;MACpDQ,IAAI,EAAGP,QAAa,IAAI;QACtBJ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEG,QAAQ,CAAC;QAClD,IAAI,CAACtF,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACb,eAAe,GAAG,EAAE;QACzB,IAAI,CAACmC,EAAE,CAACkE,YAAY,EAAE;QACtB,IAAI,CAACjB,QAAQ,EAAE;MACjB,CAAC;MACDkB,KAAK,EAAGA,KAAU,IAAI;QACpBP,OAAO,CAACO,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACzF,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACb,eAAe,GAAGsG,KAAK,EAAEA,KAAK,EAAEK,OAAO;QAC5C;QACA,IAAI,CAACC,cAAc,EAAE;QAErB,IAAI,CAACzE,EAAE,CAACkE,YAAY,EAAE;MACxB;KACD,CAAC;EACJ;EAEQO,cAAcA,CAAA;IACpB,IAAI,CAACnE,kBAAkB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC9C,MAAMoE,qBAAqB,GAAG,IAAI,CAACvE,gBAAgB,CAAC+B,GAAG,CACrD,kBAAkB,CACN;IACdwC,qBAAqB,CAACvC,QAAQ,CAACwC,OAAO,CAAEhD,OAAO,IAAKA,OAAO,CAACiD,QAAQ,CAAC,EAAE,CAAC,CAAC;EAC3E;EAEAvF,aAAaA,CAAA;IACX,IAAI,CAACI,sBAAsB,GAAG,IAAI;IAElC,MAAMgE,KAAK,GAAG,IAAI,CAACtD,gBAAgB,CAAC+B,GAAG,CAAC,aAAa,CAAC,EAAEvB,KAAK,EAAE+C,IAAI,EAAE;IACrE,MAAMmB,UAAU,GAAG,IAAI,CAAC1E,gBAAgB,CAAC+B,GAAG,CAAC,OAAO,CAAC,EAAEvB,KAAK,EAAE+C,IAAI,EAAE;IAEpE,IAAIC,MAAM,GAAQ;MAAE,GAAG,IAAI,CAACxD,gBAAgB,CAACQ;IAAK,CAAE;IAEpD;IACA,IAAId,kCAAkC,CAACa,OAAO,CAAC+C,KAAK,CAAC,EAAE;MACrDE,MAAM,CAAC/B,KAAK,GAAG6B,KAAK;IACtB,CAAC,MAAM;MACLE,MAAM,CAAC9B,KAAK,GAAG4B,KAAK;IACtB;IAEA;IACA,IAAIoB,UAAU,IAAIA,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;MACvCnB,MAAM,CAAC/B,KAAK,GAAGiD,UAAU;IAC3B,CAAC,MAAM;MACL;MACA,OAAOlB,MAAM,CAAC/B,KAAK;IACrB;IAEA+B,MAAM,CAACoB,IAAI,GAAG,QAAQ;IAEtBnB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEF,MAAM,CAAC;IAEhD,IAAI,CAAC5D,qBAAqB,CAACiF,QAAQ,CAACrB,MAAM,CAAC,CAACI,SAAS,CAAC;MACpDQ,IAAI,EAAGP,QAAa,IAAI;QACtBJ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEG,QAAQ,CAAC;QAChD,IAAI,CAACvE,sBAAsB,GAAG,KAAK;QACnC,IAAIwF,IAAI,GAAGjB,QAAQ,CAACkB,IAAI;QACxBC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEH,IAAI,CAACI,SAAS,CAAC;QACjD,IAAI,CAACtF,qBAAqB,CAACuF,cAAc,CAACtB,QAAQ,CAACkB,IAAI,CAAC;QAExD,IAAI,CAAClF,EAAE,CAACkE,YAAY,EAAE;QACtB,IAAI,CAACjB,QAAQ,EAAE;MACjB,CAAC;MACDkB,KAAK,EAAGA,KAAU,IAAI;QACpBP,OAAO,CAACO,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAAC1E,sBAAsB,GAAG,KAAK;QAEnC;QACA,IAAI8F,YAAY,GAAG,6CAA6C;QAChE,IAAIpB,KAAK,EAAEA,KAAK,EAAEK,OAAO,EAAE;UACzBe,YAAY,GAAGpB,KAAK,CAACA,KAAK,CAACK,OAAO;QACpC,CAAC,MAAM,IAAIL,KAAK,EAAEK,OAAO,EAAE;UACzBe,YAAY,GAAGpB,KAAK,CAACK,OAAO;QAC9B;QAEA5J,IAAI,CAAC4K,IAAI,CAACD,YAAY,CAAC;QACvB,IAAI,CAACvF,EAAE,CAACkE,YAAY,EAAE;MACxB;KACD,CAAC;EACJ;EAEA1G,aAAaA,CAACiI,KAAU,EAAElI,KAAa;IACrC,MAAMkG,KAAK,GAAGgC,KAAK,CAACC,MAAM;IAC1B,IAAIjC,KAAK,CAAC9C,KAAK,IAAIpD,KAAK,GAAG,CAAC,EAAE;MAC5B,MAAMoI,SAAS,GACblC,KAAK,CAACmC,aAAa,EAAEC,kBAAkB,EAAEC,aAAa,CAAC,OAAO,CAAC;MACjEH,SAAS,EAAEI,KAAK,EAAE;IACpB;EACF;EAEA9B,cAAcA,CAAA;IACZ,IAAI,CAACxF,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAChB,SAAS,GAAG,EAAE;IAEnB,MAAMuI,UAAU,GAAGC,WAAW,CAAC,MAAK;MAClC,IAAI,CAACxI,SAAS,EAAE;MAChB,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;QACxByI,aAAa,CAACF,UAAU,CAAC;QACzB,IAAI,CAACvH,gBAAgB,GAAG,IAAI;MAC9B;MACA,IAAI,CAACuB,EAAE,CAACkE,YAAY,EAAE;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;EACAtG,YAAYA,CAAA;IACV,IAAI,CAAC2F,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;EACpC;;qCA/VW1D,kCAAkC,EAAAhF,EAAA,CAAAsL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxL,EAAA,CAAAsL,iBAAA,CAAAG,EAAA,CAAAC,qBAAA,GAAA1L,EAAA,CAAAsL,iBAAA,CAAAtL,EAAA,CAAA2L,iBAAA;EAAA;;UAAlC3G,kCAAkC;IAAA4G,SAAA;IAAAC,OAAA;MAAAzG,MAAA;MAAAC,UAAA;IAAA;IAAAyG,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCnB3CnM,EAHJ,CAAAC,cAAA,aAAyC,aAEX,YACA;QAAAD,EAAA,CAAAU,MAAA,2BAAoB;QAAAV,EAAA,CAAAW,YAAA,EAAK;QAEjDX,EADF,CAAAC,cAAA,aAA8B,aACF;QACxBD,EAAA,CAAAe,SAAA,aAAoF;QACtFf,EAAA,CAAAW,YAAA,EAAM;QAEJX,EADF,CAAAC,cAAA,aAAoB,cACU;QAAAD,EAAA,CAAAU,MAAA,GAA0C;QAAAV,EAAA,CAAAW,YAAA,EAAO;QAC7EX,EAAA,CAAAoB,UAAA,KAAAiL,qDAAA,oBAC2B;QAKjCrM,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;QAGNX,EAAA,CAAAC,cAAA,eAA0D;QAqMxDD,EAnMA,CAAAoB,UAAA,KAAAkL,kDAAA,oBAAoD,KAAAC,kDAAA,oBAkEA,KAAAC,kDAAA,oBA0CA,KAAAC,kDAAA,mBAuFa;QA+BrEzM,EADE,CAAAW,YAAA,EAAO,EACH;;;QAjP6BX,EAAA,CAAAY,SAAA,GAAkD;QAAlDZ,EAAA,CAAA0M,WAAA,UAAAN,GAAA,CAAA7G,WAAA,GAAA6G,GAAA,CAAA5G,UAAA,YAAkD;QAGjDxF,EAAA,CAAAY,SAAA,GAA0C;QAA1CZ,EAAA,CAAA2M,kBAAA,UAAAP,GAAA,CAAA7G,WAAA,UAAA6G,GAAA,CAAA5G,UAAA,KAA0C;QAC7DxF,EAAA,CAAAY,SAAA,EAAwC;QAAxCZ,EAAA,CAAAkC,UAAA,SAAAkK,GAAA,CAAA7G,WAAA,QAAA6G,GAAA,CAAA7G,WAAA,KAAwC;QASjDvF,EAAA,CAAAY,SAAA,EAA8B;QAA9BZ,EAAA,CAAAkC,UAAA,cAAAkK,GAAA,CAAA9G,gBAAA,CAA8B;QAE5BtF,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAAkC,UAAA,SAAAkK,GAAA,CAAA7G,WAAA,OAAuB;QAkEvBvF,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAAkC,UAAA,SAAAkK,GAAA,CAAA7G,WAAA,OAAuB;QA0CvBvF,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAAkC,UAAA,SAAAkK,GAAA,CAAA7G,WAAA,OAAuB;QAuFvBvF,EAAA,CAAAY,SAAA,EAAuB;QAAvBZ,EAAA,CAAAkC,UAAA,SAAAkK,GAAA,CAAA7G,WAAA,OAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}