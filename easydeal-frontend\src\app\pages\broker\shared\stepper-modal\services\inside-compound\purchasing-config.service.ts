import { Injectable } from '@angular/core';
import { Validators } from '@angular/forms';
import { BaseConfigService, InputConfig, StepperConfiguration } from '../base-config.service';
import {
  PAYMENT_METHOD_OPTIONS,
} from '../../stepper-modal.constants';

@Injectable({
  providedIn: 'root'
})
export class PurchasingConfigService extends BaseConfigService {

  /**
   * Create enhanced financial inputs with payment methods for purchasing
   */
  private createPurchasingFinancialInputs(stepperModal: any): InputConfig[] {
    return this.createFinancialInputs(stepperModal);
  }

  /**
   * Configuration builder for primary inside compound purchasing units
   */
  private createPrimaryInsideCompoundPurchasingConfig(
    unitType: string,
    stepperModal: any,
    options: { includeRooms?: boolean; includeDocuments?: boolean } = {}
  ): InputConfig[] {
    const { includeRooms = true, includeDocuments = false } = options;

    const config: InputConfig[] = [
      ...this.createLocationInputs(stepperModal),
      ...this.createUnitInformationInputs(stepperModal, includeRooms),
    ];

    // Add documents if needed
    if (includeDocuments) {
      config.push(...this.createDocumentInputs());
    }

    // Add enhanced financial inputs with payment methods
    config.push(...this.createPurchasingFinancialInputs(stepperModal));

    return config;
  }

  /**
   * Configuration builder for primary inside compound purchasing penthouses
   */
  private createPrimaryInsideCompoundPurchasingPenthousesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createPenthouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing villas
   */
  private createPrimaryInsideCompoundPurchasingVillasConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createVillaUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing twin houses
   */
  private createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createHouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing town houses
   */
  private createPrimaryInsideCompoundPurchasingTownHousesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createHouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing standalone villas
   */
  private createPrimaryInsideCompoundPurchasingStandaloneVillasConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createHouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing administrative units
   */
  private createPrimaryInsideCompoundPurchasingAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialUnitInformationInputs(stepperModal, true), // Include fitout condition
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing medical clinics
   */
  private createPrimaryInsideCompoundPurchasingMedicalClinicsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialUnitInformationInputs(stepperModal, false), // No fitout condition
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing pharmacies
   */
  private createPrimaryInsideCompoundPurchasingPharmaciesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialUnitInformationInputs(stepperModal, false), // No fitout condition
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing shops
   */
  private createPrimaryInsideCompoundPurchasingShopsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createShopUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  /**
   * Configuration builder for primary inside compound purchasing commercial administrative buildings
   */
  private createPrimaryInsideCompoundPurchasingCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialAdminBuildingUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  // RESALE INSIDE COMPOUND PURCHASING CONFIGURATIONS
  private createResaleInsideCompoundPurchasingApartmentsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createUnitInformationInputs(stepperModal, true), // Include rooms
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingDuplexesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createUnitInformationInputs(stepperModal, true), // Include rooms
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingStudiosConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createUnitInformationInputs(stepperModal, false), // No rooms for studios
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingPenthousesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createPenthouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingVillasConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createVillaUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingTwinHousesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createHouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingTownHousesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createHouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingStandaloneVillasConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createHouseUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingAdministrativeUnitsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialUnitInformationInputs(stepperModal, true), // Include fitout condition
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingMedicalClinicsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialUnitInformationInputs(stepperModal, false), // No fitout condition
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingPharmaciesConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialUnitInformationInputs(stepperModal, false), // No fitout condition
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingShopsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createShopUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  private createResaleInsideCompoundPurchasingCommercialAdministrativeBuildingsConfig(stepperModal: any): InputConfig[] {
    return [
      ...this.createLocationInputs(stepperModal),
      ...this.createCommercialAdminBuildingUnitInformationInputs(stepperModal),
      ...this.createPurchasingFinancialInputs(stepperModal),
    ];
  }

  getInputConfigs(stepperModal: any): StepperConfiguration[] {
    return [
      // PRIMARY INSIDE COMPOUND PURCHASING
      {
        key: 'primary_inside_compound_purchasing_apartments',
        value: this.createPrimaryInsideCompoundPurchasingConfig('apartments', stepperModal, {
          includeRooms: true,
          includeDocuments: false
        }),
      },
      {
        key: 'primary_inside_compound_purchasing_duplexes',
        value: this.createPrimaryInsideCompoundPurchasingConfig('duplexes', stepperModal, {
          includeRooms: true,
          includeDocuments: false
        }),
      },
      {
        key: 'primary_inside_compound_purchasing_studios',
        value: this.createPrimaryInsideCompoundPurchasingConfig('studios', stepperModal, {
          includeRooms: false,
          includeDocuments: false
        }),
      },
      {
        key: 'primary_inside_compound_purchasing_penthouses',
        value: this.createPrimaryInsideCompoundPurchasingPenthousesConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_villas',
        value: this.createPrimaryInsideCompoundPurchasingVillasConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_twin_houses',
        value: this.createPrimaryInsideCompoundPurchasingTwinHousesConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_town_houses',
        value: this.createPrimaryInsideCompoundPurchasingTownHousesConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_standalone_villas',
        value: this.createPrimaryInsideCompoundPurchasingStandaloneVillasConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_administrative_units',
        value: this.createPrimaryInsideCompoundPurchasingAdministrativeUnitsConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_medical_clinics',
        value: this.createPrimaryInsideCompoundPurchasingMedicalClinicsConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_pharmacies',
        value: this.createPrimaryInsideCompoundPurchasingPharmaciesConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_shops',
        value: this.createPrimaryInsideCompoundPurchasingShopsConfig(stepperModal),
      },
      {
        key: 'primary_inside_compound_purchasing_commercial_administrative_buildings',
        value: this.createPrimaryInsideCompoundPurchasingCommercialAdministrativeBuildingsConfig(stepperModal),
      },

      // RESALE INSIDE COMPOUND PURCHASING
      {
        key: 'resale_inside_compound_purchasing_apartments',
        value: this.createResaleInsideCompoundPurchasingApartmentsConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_duplexes',
        value: this.createResaleInsideCompoundPurchasingDuplexesConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_studios',
        value: this.createResaleInsideCompoundPurchasingStudiosConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_penthouses',
        value: this.createResaleInsideCompoundPurchasingPenthousesConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_villas',
        value: this.createResaleInsideCompoundPurchasingVillasConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_twin_houses',
        value: this.createResaleInsideCompoundPurchasingTwinHousesConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_town_houses',
        value: this.createResaleInsideCompoundPurchasingTownHousesConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_standalone_villas',
        value: this.createResaleInsideCompoundPurchasingStandaloneVillasConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_administrative_units',
        value: this.createResaleInsideCompoundPurchasingAdministrativeUnitsConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_medical_clinics',
        value: this.createResaleInsideCompoundPurchasingMedicalClinicsConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_pharmacies',
        value: this.createResaleInsideCompoundPurchasingPharmaciesConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_shops',
        value: this.createResaleInsideCompoundPurchasingShopsConfig(stepperModal),
      },
      {
        key: 'resale_inside_compound_purchasing_commercial_administrative_buildings',
        value: this.createResaleInsideCompoundPurchasingCommercialAdministrativeBuildingsConfig(stepperModal),
      },
    ];
  }

  getConfigurationKeys(): string[] {
    return [
      // PRIMARY INSIDE COMPOUND PURCHASING
      'primary_inside_compound_purchasing_apartments',
      'primary_inside_compound_purchasing_duplexes',
      'primary_inside_compound_purchasing_studios',
      'primary_inside_compound_purchasing_penthouses',
      'primary_inside_compound_purchasing_villas',
      'primary_inside_compound_purchasing_twin_houses',
      'primary_inside_compound_purchasing_town_houses',
      'primary_inside_compound_purchasing_standalone_villas',
      'primary_inside_compound_purchasing_administrative_units',
      'primary_inside_compound_purchasing_medical_clinics',
      'primary_inside_compound_purchasing_pharmacies',
      'primary_inside_compound_purchasing_shops',
      'primary_inside_compound_purchasing_commercial_administrative_buildings',
      // RESALE INSIDE COMPOUND PURCHASING
      'resale_inside_compound_purchasing_apartments',
      'resale_inside_compound_purchasing_duplexes',
      'resale_inside_compound_purchasing_studios',
      'resale_inside_compound_purchasing_penthouses',
      'resale_inside_compound_purchasing_villas',
      'resale_inside_compound_purchasing_twin_houses',
      'resale_inside_compound_purchasing_town_houses',
      'resale_inside_compound_purchasing_standalone_villas',
      'resale_inside_compound_purchasing_administrative_units',
      'resale_inside_compound_purchasing_medical_clinics',
      'resale_inside_compound_purchasing_pharmacies',
      'resale_inside_compound_purchasing_shops',
      'resale_inside_compound_purchasing_commercial_administrative_buildings',
    ];
  }
}
