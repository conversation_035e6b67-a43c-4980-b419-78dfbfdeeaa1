{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nlet HomeComponent = class HomeComponent {\n  authService;\n  currentUser = null;\n  isLoggedIn = false;\n  constructor(authService) {\n    this.authService = authService;\n  }\n  ngOnInit() {\n    this.checkUserSession();\n  }\n  checkUserSession() {\n    // Check if user is logged in by checking localStorage\n    const authToken = localStorage.getItem('authToken');\n    const currentUser = localStorage.getItem('currentUser');\n    if (authToken && currentUser) {\n      try {\n        this.currentUser = JSON.parse(currentUser);\n        this.isLoggedIn = true;\n      } catch (error) {\n        // If parsing fails, user is not logged in\n        this.isLoggedIn = false;\n        this.currentUser = null;\n      }\n    } else {\n      this.isLoggedIn = false;\n      this.currentUser = null;\n    }\n  }\n  getUserDisplayName() {\n    if (this.currentUser) {\n      return this.currentUser.name || this.currentUser.firstName || this.currentUser.username || 'User';\n    }\n    return 'Guest';\n  }\n  getUserProfileImage() {\n    if (this.currentUser && this.currentUser.profileImage) {\n      return this.currentUser.profileImage;\n    }\n    // Return default avatar if no profile image\n    return 'assets/media/avatars/blank.png';\n  }\n};\nHomeComponent = __decorate([Component({\n  selector: 'app-home',\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})], HomeComponent);\nexport { HomeComponent };", "map": {"version": 3, "names": ["Component", "HomeComponent", "authService", "currentUser", "isLoggedIn", "constructor", "ngOnInit", "checkUserSession", "authToken", "localStorage", "getItem", "JSON", "parse", "error", "getUserDisplayName", "name", "firstName", "username", "getUserProfileImage", "profileImage", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AuthenticationService } from '../../authentication/services/authentication.service';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrls: ['./home.component.scss']\r\n})\r\nexport class HomeComponent implements OnInit {\r\n  currentUser: any = null;\r\n  isLoggedIn: boolean = false;\r\n\r\n  constructor(private authService: AuthenticationService) { }\r\n\r\n  ngOnInit(): void {\r\n    this.checkUserSession();\r\n  }\r\n\r\n  checkUserSession(): void {\r\n    // Check if user is logged in by checking localStorage\r\n    const authToken = localStorage.getItem('authToken');\r\n    const currentUser = localStorage.getItem('currentUser');\r\n\r\n    if (authToken && currentUser) {\r\n      try {\r\n        this.currentUser = JSON.parse(currentUser);\r\n        this.isLoggedIn = true;\r\n      } catch (error) {\r\n        // If parsing fails, user is not logged in\r\n        this.isLoggedIn = false;\r\n        this.currentUser = null;\r\n      }\r\n    } else {\r\n      this.isLoggedIn = false;\r\n      this.currentUser = null;\r\n    }\r\n  }\r\n\r\n  getUserDisplayName(): string {\r\n    if (this.currentUser) {\r\n      return this.currentUser.name || this.currentUser.firstName || this.currentUser.username || 'User';\r\n    }\r\n    return 'Guest';\r\n  }\r\n\r\n  getUserProfileImage(): string {\r\n    if (this.currentUser && this.currentUser.profileImage) {\r\n      return this.currentUser.profileImage;\r\n    }\r\n    // Return default avatar if no profile image\r\n    return 'assets/media/avatars/blank.png';\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AAQ1C,IAAMC,aAAa,GAAnB,MAAMA,aAAa;EAIJC,WAAA;EAHpBC,WAAW,GAAQ,IAAI;EACvBC,UAAU,GAAY,KAAK;EAE3BC,YAAoBH,WAAkC;IAAlC,KAAAA,WAAW,GAAXA,WAAW;EAA2B;EAE1DI,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd;IACA,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMP,WAAW,GAAGM,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAEvD,IAAIF,SAAS,IAAIL,WAAW,EAAE;MAC5B,IAAI;QACF,IAAI,CAACA,WAAW,GAAGQ,IAAI,CAACC,KAAK,CAACT,WAAW,CAAC;QAC1C,IAAI,CAACC,UAAU,GAAG,IAAI;MACxB,CAAC,CAAC,OAAOS,KAAK,EAAE;QACd;QACA,IAAI,CAACT,UAAU,GAAG,KAAK;QACvB,IAAI,CAACD,WAAW,GAAG,IAAI;MACzB;IACF,CAAC,MAAM;MACL,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAACD,WAAW,GAAG,IAAI;IACzB;EACF;EAEAW,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACX,WAAW,EAAE;MACpB,OAAO,IAAI,CAACA,WAAW,CAACY,IAAI,IAAI,IAAI,CAACZ,WAAW,CAACa,SAAS,IAAI,IAAI,CAACb,WAAW,CAACc,QAAQ,IAAI,MAAM;IACnG;IACA,OAAO,OAAO;EAChB;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACf,WAAW,IAAI,IAAI,CAACA,WAAW,CAACgB,YAAY,EAAE;MACrD,OAAO,IAAI,CAAChB,WAAW,CAACgB,YAAY;IACtC;IACA;IACA,OAAO,gCAAgC;EACzC;CAED;AA7CYlB,aAAa,GAAAmB,UAAA,EALzBpB,SAAS,CAAC;EACTqB,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,uBAAuB;EACpCC,SAAS,EAAE,CAAC,uBAAuB;CACpC,CAAC,C,EACWtB,aAAa,CA6CzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}