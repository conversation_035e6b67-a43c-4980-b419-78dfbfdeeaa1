import { ChangeDetectorRef, Component, EventEmitter, Output, OnInit } from '@angular/core';
import { PropertyService } from 'src/app/pages/broker/services/property.service';

@Component({
  selector: 'app-model-unit-filter',
  templateUrl: './model-unit-filter.component.html',
  styleUrl: './model-unit-filter.component.scss'
})
export class ModelUnitFilterComponent implements OnInit {

  unitTypes: { key: string; value: string }[] = [];
  areas: any[] = [];

  @Output() filtersApplied = new EventEmitter<any>();

  filter = {
    finishingType: '',
    status: '',
    unitType:'',
    unitArea:'',
    area:'',
    view:'',
    price:'',
  };

  constructor( private propertyService: PropertyService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.loadUnitTypes();
    this.loadAreas();
  }

  finishingTypes: { key: string; value: string }[] = [
    { key: 'On Brick', value: 'on_brick' },
    { key: 'Semi Finished', value: 'semi_finished' },
    { key: 'Company Finished', value: 'company_finished' },
    { key: 'Super Lux', value: 'super_lux' },
    { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
  ];

 status: { key: string; value: string }[] = [
    { key: 'NEW', value: 'new' },
    { key: 'AVAILABLE', value: 'available' },
    { key: 'RESERVED', value: 'reserved' },
    { key: 'SOLD', value: 'sold' },
  ];

  views: { key: string; value: string }[] = [
    { key: 'WATER VIEW', value: 'water_view' },
    { key: 'GARDENS AND LANDSCAPE', value: 'gardens_and_landscape' },
    { key: 'STREET', value: 'street' },
    { key: 'ENTERTAINMENT AREA', value: 'entertainment_area' },
    { key: 'GARDEN', value: 'garden' },
    { key: 'MAIN STREET', value: 'main_street' },
    { key: 'SQUARE', value: 'square' },
    { key: 'SIDE STREET', value: 'side_street' },
    { key: 'REAR VIEW', value: 'rear_view' },
  ];

  // toggleDropdown() {
  //   this.isOpen = !this.isOpen;
  // }

  apply() {
    // this.isOpen = false;
    this.filtersApplied.emit(this.filter);
  }

  loadUnitTypes(): void {
    this.propertyService.getUnitTypes().subscribe({
      next: (response) => {
        this.unitTypes = Object.entries(response.data).map(([key, value]) => ({
          key,
          value: value as string,
        }));
        console.log('Raw API Response:', this.unitTypes);
      },
      error: (err) => {
        console.error('Error loading unitTypes:', err);
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  // loadCities(): void {
  //   this.propertyService.getCities().subscribe({
  //     next: (response) => {
  //       if (response && response.data) {
  //         this.cities = response.data;
  //       } else {
  //         console.warn('No cities data in response');
  //         this.cities = [];
  //       }
  //     },
  //     error: (err) => {
  //       console.error('Error loading cities:', err);
  //     },
  //     complete: () => {
  //       this.cdr.detectChanges();
  //     },
  //   });
  // }

  loadAreas(cityId?: number): void {
    this.propertyService.getAreas(cityId).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.areas = response.data;
        } else {
          console.warn('No areas data in response');
          this.areas = [];
        }
      },
      error: (err) => {
        console.error('Error loading areas:', err);
        this.areas = [];
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

}
