{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard, NoAuthGuard } from './modules/auth/services/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\n// import { ProfileComponent } from './pages/shared/profile/profile.component';\nexport const routes = [\n// {\n//   path: 'auth',\n//   loadChildren: () =>\n//     import('./modules/auth/auth.module').then((m) => m.AuthModule),\n// },\n{\n  path: 'authentication',\n  canActivate: [NoAuthGuard],\n  loadChildren: () => import('./pages/authentication/authentication.module').then(m => m.AuthenticationModule)\n}, {\n  path: 'error',\n  loadChildren: () => import('./modules/errors/errors.module').then(m => m.ErrorsModule)\n}, {\n  path: '',\n  canActivate: [AuthGuard],\n  loadChildren: () => import('./_metronic/layout/layout.module').then(m => m.LayoutModule)\n}, {\n  path: '**',\n  redirectTo: 'error/404'\n}\n// { path: 'testt', component: ProfileComponent },\n];\nexport class AppRoutingModule {\n  static ɵfac = function AppRoutingModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AppRoutingModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppRoutingModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forRoot(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "NoAuth<PERSON><PERSON>", "routes", "path", "canActivate", "loadChildren", "then", "m", "AuthenticationModule", "ErrorsModule", "LayoutModule", "redirectTo", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\nimport { AuthGuard, NoAuthGuard } from './modules/auth/services/auth.guard';\r\nimport { HomeComponent } from './pages/home/<USER>';\r\n// import { ProfileComponent } from './pages/shared/profile/profile.component';\r\n\r\nexport const routes: Routes = [\r\n  // {\r\n  //   path: 'auth',\r\n  //   loadChildren: () =>\r\n  //     import('./modules/auth/auth.module').then((m) => m.AuthModule),\r\n  // },\r\n  {\r\n    path: 'authentication',\r\n    canActivate: [NoAuthGuard],\r\n    loadChildren: () =>\r\n      import('./pages/authentication/authentication.module').then(\r\n        (m) => m.AuthenticationModule\r\n      ),\r\n  },\r\n\r\n  {\r\n    path: 'error',\r\n    loadChildren: () =>\r\n      import('./modules/errors/errors.module').then((m) => m.ErrorsModule),\r\n  },\r\n  {\r\n    path: '',\r\n    canActivate: [AuthGuard],\r\n    loadChildren: () =>\r\n      import('./_metronic/layout/layout.module').then((m) => m.LayoutModule),\r\n  },\r\n\r\n  { path: '**', redirectTo: 'error/404' },\r\n  // { path: 'testt', component: ProfileComponent },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class AppRoutingModule {}\r\n"], "mappings": "AACA,SAAiBA,YAAY,QAAQ,iBAAiB;AACtD,SAASC,SAAS,EAAEC,WAAW,QAAQ,oCAAoC;;;AAE3E;AAEA,OAAO,MAAMC,MAAM,GAAW;AAC5B;AACA;AACA;AACA;AACA;AACA;EACEC,IAAI,EAAE,gBAAgB;EACtBC,WAAW,EAAE,CAACH,WAAW,CAAC;EAC1BI,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CACxDC,CAAC,IAAKA,CAAC,CAACC,oBAAoB;CAElC,EAED;EACEL,IAAI,EAAE,OAAO;EACbE,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACE,YAAY;CACtE,EACD;EACEN,IAAI,EAAE,EAAE;EACRC,WAAW,EAAE,CAACJ,SAAS,CAAC;EACxBK,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACG,YAAY;CACxE,EAED;EAAEP,IAAI,EAAE,IAAI;EAAEQ,UAAU,EAAE;AAAW;AACrC;AAAA,CACD;AAMD,OAAM,MAAOC,gBAAgB;;qCAAhBA,gBAAgB;EAAA;;UAAhBA;EAAgB;;cAHjBb,YAAY,CAACc,OAAO,CAACX,MAAM,CAAC,EAC5BH,YAAY;EAAA;;;2EAEXa,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAhB,YAAA;IAAAiB,OAAA,GAFjBjB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}