:host {
  height: 100%;
  margin: 0;

  .flex-root {
    height: 100%;
  }
}

.page-loaded {
  app-layout {
    opacity: 1;
    transition: opacity 1s ease-in-out;
  }
}

// Home page specific styles
.home-page-layout {
  .app-wrapper {
    margin-top: 0 !important;
  }

  .app-main {
    margin-left: 0 !important;
    width: 100% !important;
  }

  .app-content {
    padding: 0 !important;
  }

  // Remove container styling for home page
  #kt_app_content_container {
    padding: 0 !important;
    margin: 0 !important;
  }
}
