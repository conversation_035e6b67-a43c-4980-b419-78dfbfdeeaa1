{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { BehaviorSubject, forkJoin, of } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport Swal from 'sweetalert2';\nimport { STEPPER_CONFIG, SPECIALIZATION_SCOPE_OPTIONS, TYPE_OPTIONS } from './stepper-modal.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/createrequest.service\";\nimport * as i3 from \"../../services/property.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"./stepper-input-config.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../broker-title/broker-title.component\";\nconst _c0 = a0 => ({\n  \"is-invalid\": a0\n});\nconst _c1 = () => [\"cityId\", \"areaId\", \"subAreaId\", \"compoundName\", \"detailedAddress\", \"addressLink\", \"projectManagement\", \"projectConstructor\", \"locationSuggestions\"];\nconst _c2 = () => ({\n  \"max-height\": \"200px\",\n  \"overflow-y\": \"auto\"\n});\nconst _c3 = () => ({});\nconst _c4 = () => [];\nfunction StepperModalComponent_div_17_div_11_div_4_p_11_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \";\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_17_div_11_div_4_p_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 28);\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, StepperModalComponent_div_17_div_11_div_4_p_11_span_3_Template, 2, 0, \"span\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r1 = ctx.$implicit;\n    const last_r2 = ctx.last;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", message_r1, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !last_r2);\n  }\n}\nfunction StepperModalComponent_div_17_div_11_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37);\n    i0.ɵɵelement(2, \"i\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"div\", 40)(5, \"strong\", 41);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 42);\n    i0.ɵɵtext(9, \"Required\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 43);\n    i0.ɵɵtemplate(11, StepperModalComponent_div_17_div_11_div_4_p_11_Template, 4, 2, \"p\", 44);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const error_r3 = ctx.$implicit;\n    i0.ɵɵclassProp(\"border-start\", true)(\"border-danger\", true)(\"border-4\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 8, error_r3.field));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", error_r3.messages);\n  }\n}\nfunction StepperModalComponent_div_17_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"div\", 33)(3, \"div\", 34);\n    i0.ɵɵtemplate(4, StepperModalComponent_div_17_div_11_div_4_Template, 12, 10, \"div\", 35);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const stepError_r4 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", stepError_r4.errors);\n  }\n}\nfunction StepperModalComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\", 24)(3, \"div\", 25);\n    i0.ɵɵelement(4, \"i\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h5\", 27);\n    i0.ɵɵtext(7, \"Validation Errors Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 28);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"div\", 29);\n    i0.ɵɵtemplate(11, StepperModalComponent_div_17_div_11_Template, 5, 1, \"div\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"Please correct the following \", ctx_r4.validationErrors.length, \" issue(s) before proceeding\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.validationErrors);\n  }\n}\nfunction StepperModalComponent_div_19_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 58);\n    i0.ɵɵlistener(\"click\", function StepperModalComponent_div_19_li_10_Template_a_click_1_listener() {\n      let tmp_5_0;\n      const scope_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      ctx_r4.select(ctx_r4.getCurrentForm(), \"specializationScope\", scope_r7.value);\n      return i0.ɵɵresetView((tmp_5_0 = ctx_r4.getCurrentForm().get(\"specializationScope\")) == null ? null : tmp_5_0.markAsTouched());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const scope_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(scope_r7.key);\n  }\n}\nfunction StepperModalComponent_div_19_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" Specialization Scope is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_19_li_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 58);\n    i0.ɵɵlistener(\"click\", function StepperModalComponent_div_19_li_21_Template_a_click_1_listener() {\n      let tmp_5_0;\n      const type_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      ctx_r4.select(ctx_r4.getCurrentForm(), \"type\", type_r9.value);\n      return i0.ɵɵresetView((tmp_5_0 = ctx_r4.getCurrentForm().get(\"type\")) == null ? null : tmp_5_0.markAsTouched());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const type_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(type_r9.key);\n  }\n}\nfunction StepperModalComponent_div_19_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_19_li_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 58);\n    i0.ɵɵlistener(\"click\", function StepperModalComponent_div_19_li_32_Template_a_click_1_listener() {\n      let tmp_5_0;\n      const unitType_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      ctx_r4.select(ctx_r4.getCurrentForm(), \"unitType\", unitType_r11.value);\n      return i0.ɵɵresetView((tmp_5_0 = ctx_r4.getCurrentForm().get(\"unitType\")) == null ? null : tmp_5_0.markAsTouched());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const unitType_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(unitType_r11.key);\n  }\n}\nfunction StepperModalComponent_div_19_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" Unit Type is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 46)(2, \"label\", 47);\n    i0.ɵɵtext(3, \"Specialization Scope\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 48)(5, \"button\", 49)(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"ul\", 51);\n    i0.ɵɵtemplate(10, StepperModalComponent_div_19_li_10_Template, 3, 1, \"li\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, StepperModalComponent_div_19_div_11_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 46)(13, \"label\", 47);\n    i0.ɵɵtext(14, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 48)(16, \"button\", 54)(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"ul\", 55);\n    i0.ɵɵtemplate(21, StepperModalComponent_div_19_li_21_Template, 3, 1, \"li\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, StepperModalComponent_div_19_div_22_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 46)(24, \"label\", 47);\n    i0.ɵɵtext(25, \"Unit Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 48)(27, \"button\", 56)(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"ul\", 57);\n    i0.ɵɵtemplate(32, StepperModalComponent_div_19_li_32_Template, 3, 1, \"li\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(33, StepperModalComponent_div_19_div_33_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_9_0;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r4.getText(ctx_r4.specializationScopeOptions, (tmp_1_0 = ctx_r4.getCurrentForm().get(\"specializationScope\")) == null ? null : tmp_1_0.value) || \"Select Specialization Scope\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.specializationScopeOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"specializationScope\")) == null ? null : tmp_3_0.touched) && ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"specializationScope\")) == null ? null : tmp_3_0.invalid));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r4.getText(ctx_r4.getFilteredTypeOptions(), (tmp_4_0 = ctx_r4.getCurrentForm().get(\"type\")) == null ? null : tmp_4_0.value) || \"Select Type\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getFilteredTypeOptions());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r4.getCurrentForm().get(\"type\")) == null ? null : tmp_6_0.touched) && ((tmp_6_0 = ctx_r4.getCurrentForm().get(\"type\")) == null ? null : tmp_6_0.invalid));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r4.getText(ctx_r4.getFilteredUnitTypeOptions(), (tmp_7_0 = ctx_r4.getCurrentForm().get(\"unitType\")) == null ? null : tmp_7_0.value) || \"Select Unit Type\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getFilteredUnitTypeOptions());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r4.getCurrentForm().get(\"unitType\")) == null ? null : tmp_9_0.touched) && ((tmp_9_0 = ctx_r4.getCurrentForm().get(\"unitType\")) == null ? null : tmp_9_0.invalid));\n  }\n}\nfunction StepperModalComponent_div_20_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 69);\n    i0.ɵɵlistener(\"click\", function StepperModalComponent_div_20_li_10_Template_a_click_1_listener() {\n      const city_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onSelectChange(\"cityId\", city_r13.value, city_r13.key));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const city_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(city_r13.key);\n  }\n}\nfunction StepperModalComponent_div_20_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" City is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_20_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵelement(1, \"i\", 71);\n    i0.ɵɵtext(2, \" Loading cities... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_20_li_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 69);\n    i0.ɵɵlistener(\"click\", function StepperModalComponent_div_20_li_23_Template_a_click_1_listener() {\n      const area_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onSelectChange(\"areaId\", area_r15.value, area_r15.key));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const area_r15 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(area_r15.key);\n  }\n}\nfunction StepperModalComponent_div_20_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" Area is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_20_li_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 69);\n    i0.ɵɵlistener(\"click\", function StepperModalComponent_div_20_li_35_Template_a_click_1_listener() {\n      const subArea_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onSelectChange(\"subAreaId\", subArea_r17.value, subArea_r17.key));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const subArea_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(subArea_r17.key);\n  }\n}\nfunction StepperModalComponent_div_20_div_37_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵelement(1, \"i\", 82);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Location Suggestions selection is required to proceed\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StepperModalComponent_div_20_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 72);\n    i0.ɵɵelement(2, \"input\", 73);\n    i0.ɵɵelementStart(3, \"label\", 74)(4, \"div\", 75);\n    i0.ɵɵelement(5, \"i\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 77)(7, \"span\", 78);\n    i0.ɵɵtext(8, \"Location Suggestions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"small\", 79);\n    i0.ɵɵtext(10, \" Get personalized location recommendations based on your preferences \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(11, StepperModalComponent_div_20_div_37_div_11_Template, 4, 0, \"div\", 80);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-error\", ((tmp_2_0 = ctx_r4.getCurrentForm().get(\"locationSuggestions\")) == null ? null : tmp_2_0.touched) && ((tmp_2_0 = ctx_r4.getCurrentForm().get(\"locationSuggestions\")) == null ? null : tmp_2_0.invalid));\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-invalid\", ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"locationSuggestions\")) == null ? null : tmp_3_0.touched) && ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"locationSuggestions\")) == null ? null : tmp_3_0.invalid) ? \"true\" : \"false\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r4.getCurrentForm().get(\"locationSuggestions\")) == null ? null : tmp_4_0.touched) && ((tmp_4_0 = ctx_r4.getCurrentForm().get(\"locationSuggestions\")) == null ? null : tmp_4_0.invalid));\n  }\n}\nfunction StepperModalComponent_div_20_div_38_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelement(1, \"i\", 82);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Location Suggestions selection is required to proceed\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StepperModalComponent_div_20_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 72);\n    i0.ɵɵelement(2, \"input\", 83);\n    i0.ɵɵelementStart(3, \"label\", 84)(4, \"div\", 75);\n    i0.ɵɵelement(5, \"i\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 77)(7, \"span\", 78);\n    i0.ɵɵtext(8, \"Location Suggestions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"small\", 79);\n    i0.ɵɵtext(10, \" Get personalized location recommendations based on your preferences \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(11, StepperModalComponent_div_20_div_38_div_11_Template, 4, 0, \"div\", 85);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-error\", ((tmp_2_0 = ctx_r4.getCurrentForm().get(\"locationSuggestions\")) == null ? null : tmp_2_0.touched) && ((tmp_2_0 = ctx_r4.getCurrentForm().get(\"locationSuggestions\")) == null ? null : tmp_2_0.invalid));\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-invalid\", ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"locationSuggestions\")) == null ? null : tmp_3_0.touched) && ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"locationSuggestions\")) == null ? null : tmp_3_0.invalid) ? \"true\" : \"false\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r4.getCurrentForm().get(\"locationSuggestions\")) == null ? null : tmp_4_0.touched) && ((tmp_4_0 = ctx_r4.getCurrentForm().get(\"locationSuggestions\")) == null ? null : tmp_4_0.invalid));\n  }\n}\nfunction StepperModalComponent_div_20_div_39_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" Compound Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_20_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"label\", 47);\n    i0.ɵɵtext(2, \"Compound Name \");\n    i0.ɵɵelement(3, \"span\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 88);\n    i0.ɵɵtemplate(5, StepperModalComponent_div_20_div_39_div_5_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_2_0 = ctx_r4.getCurrentForm().get(\"compoundName\")) == null ? null : tmp_2_0.touched) && ((tmp_2_0 = ctx_r4.getCurrentForm().get(\"compoundName\")) == null ? null : tmp_2_0.invalid)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"compoundName\")) == null ? null : tmp_3_0.touched) && ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"compoundName\")) == null ? null : tmp_3_0.invalid));\n  }\n}\nfunction StepperModalComponent_div_20_div_40_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" Detailed Address is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_20_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"label\", 47);\n    i0.ɵɵtext(2, \"Detailed Address \");\n    i0.ɵɵelement(3, \"span\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 89);\n    i0.ɵɵtemplate(5, StepperModalComponent_div_20_div_40_div_5_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_2_0 = ctx_r4.getCurrentForm().get(\"detailedAddress\")) == null ? null : tmp_2_0.touched) && ((tmp_2_0 = ctx_r4.getCurrentForm().get(\"detailedAddress\")) == null ? null : tmp_2_0.invalid)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"detailedAddress\")) == null ? null : tmp_3_0.touched) && ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"detailedAddress\")) == null ? null : tmp_3_0.invalid));\n  }\n}\nfunction StepperModalComponent_div_20_div_41_div_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Please enter a valid URL. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction StepperModalComponent_div_20_div_41_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, StepperModalComponent_div_20_div_41_div_4_ng_container_1_Template, 2, 0, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r4.getCurrentForm().get(\"addressLink\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"pattern\"]);\n  }\n}\nfunction StepperModalComponent_div_20_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"label\", 47);\n    i0.ɵɵtext(2, \"Detailed Address Link\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 90);\n    i0.ɵɵtemplate(4, StepperModalComponent_div_20_div_41_div_4_Template, 2, 1, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_2_0 = ctx_r4.getCurrentForm().get(\"addressLink\")) == null ? null : tmp_2_0.touched) && ((tmp_2_0 = ctx_r4.getCurrentForm().get(\"addressLink\")) == null ? null : tmp_2_0.invalid)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"addressLink\")) == null ? null : tmp_3_0.touched) && ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"addressLink\")) == null ? null : tmp_3_0.invalid));\n  }\n}\nfunction StepperModalComponent_div_20_div_42_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" Project Management is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_20_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"label\", 47);\n    i0.ɵɵtext(2, \"Project Management\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 91);\n    i0.ɵɵtemplate(4, StepperModalComponent_div_20_div_42_div_4_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_2_0 = ctx_r4.getCurrentForm().get(\"projectManagement\")) == null ? null : tmp_2_0.touched) && ((tmp_2_0 = ctx_r4.getCurrentForm().get(\"projectManagement\")) == null ? null : tmp_2_0.invalid)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"projectManagement\")) == null ? null : tmp_3_0.touched) && ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"projectManagement\")) == null ? null : tmp_3_0.invalid));\n  }\n}\nfunction StepperModalComponent_div_20_div_43_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" Project Constructor is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_20_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"label\", 47);\n    i0.ɵɵtext(2, \"Project Constructor\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 92);\n    i0.ɵɵtemplate(4, StepperModalComponent_div_20_div_43_div_4_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_2_0 = ctx_r4.getCurrentForm().get(\"projectConstructor\")) == null ? null : tmp_2_0.touched) && ((tmp_2_0 = ctx_r4.getCurrentForm().get(\"projectConstructor\")) == null ? null : tmp_2_0.invalid)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"projectConstructor\")) == null ? null : tmp_3_0.touched) && ((tmp_3_0 = ctx_r4.getCurrentForm().get(\"projectConstructor\")) == null ? null : tmp_3_0.invalid));\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 87);\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_div_6_small_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 79);\n    i0.ɵɵtext(1, \" Get personalized location recommendations based on your preferences \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_div_6_small_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 79);\n    i0.ɵɵtext(1, \" Get personalized location recommendations for your purchase requirements \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_div_6_small_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 79);\n    i0.ɵɵtext(1, \" Receive budget optimization suggestions from our experts \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_div_6_small_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 79);\n    i0.ɵɵtext(1, \" Get market-based pricing recommendations for your property \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_div_6_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelement(1, \"i\", 82);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const input_r18 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"id\", input_r18.name + \"_help\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", input_r18.label, \" is required to proceed\");\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵelement(1, \"input\", 103);\n    i0.ɵɵelementStart(2, \"label\", 104)(3, \"div\", 75);\n    i0.ɵɵelement(4, \"i\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 77)(6, \"span\", 78);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, StepperModalComponent_div_20_div_44_ng_container_1_div_6_small_8_Template, 2, 0, \"small\", 105)(9, StepperModalComponent_div_20_div_44_ng_container_1_div_6_small_9_Template, 2, 0, \"small\", 105)(10, StepperModalComponent_div_20_div_44_ng_container_1_div_6_small_10_Template, 2, 0, \"small\", 105)(11, StepperModalComponent_div_20_div_44_ng_container_1_div_6_small_11_Template, 2, 0, \"small\", 105);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, StepperModalComponent_div_20_div_44_ng_container_1_div_6_div_12_Template, 4, 2, \"div\", 106);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    let tmp_9_0;\n    let tmp_16_0;\n    const input_r18 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"has-error\", ((tmp_5_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_5_0.touched) && ((tmp_5_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_5_0.invalid));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", input_r18.name)(\"id\", input_r18.name);\n    i0.ɵɵattribute(\"aria-describedby\", input_r18.name + \"_help\")(\"aria-invalid\", ((tmp_9_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_9_0.touched) && ((tmp_9_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_9_0.invalid) ? \"true\" : \"false\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", input_r18.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(input_r18.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", input_r18.name === \"locationSuggestions\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", input_r18.name === \"locationSuggestion\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", input_r18.name === \"budgetSuggestions\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", input_r18.name === \"rentPriceSuggestions\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_16_0.touched) && ((tmp_16_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_16_0.invalid));\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_input_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 108);\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    const input_r18 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formControlName\", input_r18.name)(\"placeholder\", \"Enter \" + input_r18.label)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_7_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_7_0.touched) && ((tmp_7_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_7_0.invalid)));\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_input_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 109);\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    const input_r18 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formControlName\", input_r18.name)(\"placeholder\", \"Enter \" + input_r18.label)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_7_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_7_0.touched) && ((tmp_7_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_7_0.invalid)));\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_input_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 110);\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    const input_r18 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formControlName\", input_r18.name)(\"placeholder\", \"Enter \" + input_r18.label)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_7_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_7_0.touched) && ((tmp_7_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_7_0.invalid)));\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_div_10_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 69);\n    i0.ɵɵlistener(\"click\", function StepperModalComponent_div_20_div_44_ng_container_1_div_10_li_6_Template_a_click_1_listener() {\n      const option_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const input_r18 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.select(ctx_r4.getCurrentForm(), input_r18.name, option_r20.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r20 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r20.key);\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"button\", 111)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ul\", 112);\n    i0.ɵɵtemplate(6, StepperModalComponent_div_20_div_44_ng_container_1_div_10_li_6_Template, 3, 1, \"li\", 52);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_6_0;\n    const input_r18 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", input_r18.name + \"Dropdown\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getText(input_r18.options, (tmp_6_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_6_0.value) || \"Select \" + input_r18.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", input_r18.options && input_r18.options.length > 10 ? i0.ɵɵpureFunction0(5, _c2) : i0.ɵɵpureFunction0(6, _c3));\n    i0.ɵɵattribute(\"aria-labelledby\", input_r18.name + \"Dropdown\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", input_r18.options);\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_textarea_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"textarea\", 113);\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    const input_r18 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formControlName\", input_r18.name)(\"placeholder\", \"Enter \" + input_r18.label)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_7_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_7_0.touched) && ((tmp_7_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_7_0.invalid)));\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_div_12_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const input_r18 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", input_r18.label, \" is required. \");\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_div_12_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const input_r18 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Please enter a valid \", input_r18.label, \". \");\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_div_12_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_6_0;\n    const input_r18 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", input_r18.label, \" must be greater than or equal to \", (tmp_6_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_6_0.errors == null ? null : tmp_6_0.errors[\"min\"] == null ? null : tmp_6_0.errors[\"min\"].min, \". \");\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, StepperModalComponent_div_20_div_44_ng_container_1_div_12_ng_container_1_Template, 2, 1, \"ng-container\", 15)(2, StepperModalComponent_div_20_div_44_ng_container_1_div_12_ng_container_2_Template, 2, 1, \"ng-container\", 15)(3, StepperModalComponent_div_20_div_44_ng_container_1_div_12_ng_container_3_Template, 2, 2, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    const input_r18 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_5_0.errors == null ? null : tmp_5_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_6_0.errors == null ? null : tmp_6_0.errors[\"pattern\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_7_0.errors == null ? null : tmp_7_0.errors[\"min\"]);\n  }\n}\nfunction StepperModalComponent_div_20_div_44_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"label\", 94)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, StepperModalComponent_div_20_div_44_ng_container_1_span_4_Template, 1, 0, \"span\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerStart(5, 96);\n    i0.ɵɵtemplate(6, StepperModalComponent_div_20_div_44_ng_container_1_div_6_Template, 13, 13, \"div\", 97)(7, StepperModalComponent_div_20_div_44_ng_container_1_input_7_Template, 1, 5, \"input\", 98)(8, StepperModalComponent_div_20_div_44_ng_container_1_input_8_Template, 1, 5, \"input\", 99)(9, StepperModalComponent_div_20_div_44_ng_container_1_input_9_Template, 1, 5, \"input\", 100)(10, StepperModalComponent_div_20_div_44_ng_container_1_div_10_Template, 7, 7, \"div\", 101)(11, StepperModalComponent_div_20_div_44_ng_container_1_textarea_11_Template, 1, 5, \"textarea\", 102);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(12, StepperModalComponent_div_20_div_44_ng_container_1_div_12_Template, 4, 3, \"div\", 53);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_13_0;\n    const input_r18 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(input_r18.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isInputRequired(input_r18));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", input_r18.type);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"checkbox\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"text\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"number\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"url\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"select\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"textarea\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_13_0.touched) && ((tmp_13_0 = ctx_r4.getCurrentForm().get(input_r18.name)) == null ? null : tmp_13_0.invalid));\n  }\n}\nfunction StepperModalComponent_div_20_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵtemplate(1, StepperModalComponent_div_20_div_44_ng_container_1_Template, 13, 10, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const input_r18 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpureFunction0(1, _c1).includes(input_r18.name));\n  }\n}\nfunction StepperModalComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 46)(2, \"label\", 47);\n    i0.ɵɵtext(3, \"City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 48)(5, \"button\", 60)(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"ul\", 61);\n    i0.ɵɵtemplate(10, StepperModalComponent_div_20_li_10_Template, 3, 1, \"li\", 52);\n    i0.ɵɵpipe(11, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, StepperModalComponent_div_20_div_12_Template, 2, 0, \"div\", 53)(13, StepperModalComponent_div_20_div_13_Template, 3, 0, \"div\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 46)(15, \"label\", 47);\n    i0.ɵɵtext(16, \"Area\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 48)(18, \"button\", 63)(19, \"span\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"ul\", 64);\n    i0.ɵɵtemplate(23, StepperModalComponent_div_20_li_23_Template, 3, 1, \"li\", 52);\n    i0.ɵɵpipe(24, \"async\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(25, StepperModalComponent_div_20_div_25_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 46)(27, \"label\", 47);\n    i0.ɵɵtext(28, \"Sub Area\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 48)(30, \"button\", 65)(31, \"span\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(33, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"ul\", 66);\n    i0.ɵɵtemplate(35, StepperModalComponent_div_20_li_35_Template, 3, 1, \"li\", 52);\n    i0.ɵɵpipe(36, \"async\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(37, StepperModalComponent_div_20_div_37_Template, 12, 4, \"div\", 67)(38, StepperModalComponent_div_20_div_38_Template, 12, 4, \"div\", 67)(39, StepperModalComponent_div_20_div_39_Template, 6, 4, \"div\", 67)(40, StepperModalComponent_div_20_div_40_Template, 6, 4, \"div\", 67)(41, StepperModalComponent_div_20_div_41_Template, 5, 4, \"div\", 67)(42, StepperModalComponent_div_20_div_42_Template, 5, 4, \"div\", 67)(43, StepperModalComponent_div_20_div_43_Template, 5, 4, \"div\", 67)(44, StepperModalComponent_div_20_div_44_Template, 2, 2, \"div\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_9_0;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.isLoadingCities);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedCityName || \"Select City\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(11, 21, ctx_r4.cities$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r4.getCurrentForm().get(\"cityId\")) == null ? null : tmp_4_0.touched) && ((tmp_4_0 = ctx_r4.getCurrentForm().get(\"cityId\")) == null ? null : tmp_4_0.invalid));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isLoadingCities);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", !ctx_r4.selectedCityId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedAreaName || \"Select Area\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(24, 23, ctx_r4.areas$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r4.getCurrentForm().get(\"areaId\")) == null ? null : tmp_9_0.touched) && ((tmp_9_0 = ctx_r4.getCurrentForm().get(\"areaId\")) == null ? null : tmp_9_0.invalid));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", !ctx_r4.selectedAreaId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedSubAreaName || \"Select Sub Area\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(36, 25, ctx_r4.subAreas$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getInsideCompoundPrivilege() && ctx_r4.isClient());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.getInsideCompoundPrivilege() && ctx_r4.isClient() && ctx_r4.getCurrentForm().get(\"locationSuggestions\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getInsideCompoundPrivilege());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getSellInsideCompoundInputs() || ctx_r4.getRentOutInsideCompoundInputs() || ctx_r4.getRentOutsideCompoundInputs() || ctx_r4.getSellOutsideCompoundInputs());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getSellInsideCompoundInputs() || ctx_r4.getRentOutInsideCompoundInputs() || ctx_r4.getRentOutsideCompoundInputs() || ctx_r4.getSellOutsideCompoundInputs());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getSellInsideCompoundInputs());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getSellInsideCompoundInputs());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.currentInputs)(\"ngForTrackBy\", ctx_r4.trackByInputName);\n  }\n}\nfunction StepperModalComponent_div_21_div_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 87);\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_6_small_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 79);\n    i0.ɵɵtext(1, \" Get personalized location recommendations based on your preferences \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_6_small_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 79);\n    i0.ɵɵtext(1, \" Receive budget optimization suggestions from our experts \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_6_small_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 79);\n    i0.ɵɵtext(1, \" Get market-based pricing recommendations for your property \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_6_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelement(1, \"i\", 82);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const input_r21 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"id\", input_r21.name + \"_help\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", input_r21.label, \" is required to proceed\");\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵelement(1, \"input\", 103);\n    i0.ɵɵelementStart(2, \"label\", 104)(3, \"div\", 75);\n    i0.ɵɵelement(4, \"i\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 77)(6, \"span\", 78);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, StepperModalComponent_div_21_div_1_div_6_small_8_Template, 2, 0, \"small\", 105)(9, StepperModalComponent_div_21_div_1_div_6_small_9_Template, 2, 0, \"small\", 105)(10, StepperModalComponent_div_21_div_1_div_6_small_10_Template, 2, 0, \"small\", 105);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, StepperModalComponent_div_21_div_1_div_6_div_11_Template, 4, 2, \"div\", 106);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_8_0;\n    let tmp_14_0;\n    const input_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"has-error\", ((tmp_4_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_4_0.touched) && ((tmp_4_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_4_0.invalid));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", input_r21.name)(\"id\", input_r21.name);\n    i0.ɵɵattribute(\"aria-describedby\", input_r21.name + \"_help\")(\"aria-invalid\", ((tmp_8_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_8_0.touched) && ((tmp_8_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_8_0.invalid) ? \"true\" : \"false\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", input_r21.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(input_r21.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", input_r21.name === \"locationSuggestions\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", input_r21.name === \"budgetSuggestions\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", input_r21.name === \"rentPriceSuggestions\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_14_0.touched) && ((tmp_14_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_14_0.invalid));\n  }\n}\nfunction StepperModalComponent_div_21_div_1_input_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 108);\n  }\n  if (rf & 2) {\n    let tmp_6_0;\n    const input_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formControlName\", input_r21.name)(\"placeholder\", \"Enter \" + input_r21.label)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_6_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_6_0.touched) && ((tmp_6_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_6_0.invalid)));\n  }\n}\nfunction StepperModalComponent_div_21_div_1_input_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 109);\n  }\n  if (rf & 2) {\n    let tmp_6_0;\n    const input_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formControlName\", input_r21.name)(\"placeholder\", \"Enter \" + input_r21.label)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_6_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_6_0.touched) && ((tmp_6_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_6_0.invalid)));\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_9_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 69);\n    i0.ɵɵlistener(\"click\", function StepperModalComponent_div_21_div_1_div_9_li_6_Template_a_click_1_listener() {\n      const option_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      const input_r21 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.select(ctx_r4.getCurrentForm(), input_r21.name, option_r23.value));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r23 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r23.key);\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"button\", 111)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ul\", 112);\n    i0.ɵɵtemplate(6, StepperModalComponent_div_21_div_1_div_9_li_6_Template, 3, 1, \"li\", 52);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    const input_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", input_r21.name + \"Dropdown\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getText(input_r21.options, (tmp_5_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_5_0.value) || \"Select \" + input_r21.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", input_r21.options && input_r21.options.length > 10 ? i0.ɵɵpureFunction0(5, _c2) : i0.ɵɵpureFunction0(6, _c3));\n    i0.ɵɵattribute(\"aria-labelledby\", input_r21.name + \"Dropdown\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", input_r21.options);\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_10_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 133);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const input_r21 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getSelectedCount(input_r21.name), \" \");\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_10_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 134)(1, \"div\", 126)(2, \"input\", 135);\n    i0.ɵɵlistener(\"change\", function StepperModalComponent_div_21_div_1_div_10_div_17_Template_input_change_2_listener() {\n      const option_r26 = i0.ɵɵrestoreView(_r25).$implicit;\n      const input_r21 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(input_r21.name === \"otherAccessories\" ? ctx_r4.toggleAccessory(option_r26.value) : input_r21.name === \"otherExpenses\" ? ctx_r4.toggleOtherExpense(option_r26.value) : ctx_r4.toggleMultiSelect(input_r21.name, option_r26.value));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 136);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const option_r26 = ctx.$implicit;\n    const input_r21 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", input_r21.name + \"_\" + option_r26.value)(\"checked\", input_r21.name === \"otherAccessories\" ? ctx_r4.isAccessorySelected(option_r26.value) : input_r21.name === \"otherExpenses\" ? ctx_r4.isOtherExpenseSelected(option_r26.value) : ctx_r4.isMultiSelectOptionSelected(input_r21.name, option_r26.value));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"fw-bold\", ctx_r4.isMultiSelectOptionSelected(input_r21.name, option_r26.value));\n    i0.ɵɵproperty(\"for\", input_r21.name + \"_\" + option_r26.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r26.key, \" \");\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_10_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137);\n    i0.ɵɵelement(1, \"i\", 138);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"No options found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"small\");\n    i0.ɵɵtext(5, \"Try adjusting your search\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"div\", 48)(2, \"button\", 118)(3, \"div\", 119)(4, \"span\", 120);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, StepperModalComponent_div_21_div_1_div_10_span_6_Template, 2, 1, \"span\", 121);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"i\", 122);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 123)(9, \"div\", 124)(10, \"div\", 125)(11, \"div\", 126)(12, \"input\", 127);\n    i0.ɵɵlistener(\"change\", function StepperModalComponent_div_21_div_1_div_10_Template_input_change_12_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const input_r21 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.toggleSelectAll(input_r21.name));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"label\", 128);\n    i0.ɵɵtext(14, \" Select All \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 129)(16, \"div\", 130);\n    i0.ɵɵtemplate(17, StepperModalComponent_div_21_div_1_div_10_div_17_Template, 5, 6, \"div\", 131)(18, StepperModalComponent_div_21_div_1_div_10_div_18_Template, 6, 0, \"div\", 132);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const input_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"has-selections\", ctx_r4.getSelectedCount(input_r21.name) > 0);\n    i0.ɵɵproperty(\"id\", input_r21.name + \"Dropdown\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(input_r21.name === \"otherAccessories\" ? ctx_r4.getSelectedAccessoriesText() : input_r21.name === \"otherExpenses\" ? ctx_r4.getSelectedOtherExpensesText() : ctx_r4.getSelectedText(input_r21.name, input_r21.options || i0.ɵɵpureFunction0(12, _c4)) || \"Select \" + input_r21.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getSelectedCount(input_r21.name) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-labelledby\", input_r21.name + \"Dropdown\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"id\", input_r21.name + \"_selectAll\")(\"checked\", ctx_r4.areAllOptionsSelected(input_r21.name))(\"indeterminate\", ctx_r4.areSomeOptionsSelected(input_r21.name) && !ctx_r4.areAllOptionsSelected(input_r21.name));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", input_r21.name + \"_selectAll\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getFilteredOptions(input_r21.name, input_r21.options || i0.ɵɵpureFunction0(13, _c4)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getFilteredOptions(input_r21.name, input_r21.options || i0.ɵɵpureFunction0(14, _c4)).length === 0);\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_11_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 149);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const input_r21 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Click to upload \", input_r21.name === \"video\" ? \"videos\" : \"images\", \" \");\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_11_p_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 149);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const input_r21 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getFileCount(input_r21.name), \" file(s) uploaded successfully \");\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_11_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 150);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const input_r21 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getFileCount(input_r21.name), \" file(s) \");\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 139)(1, \"div\", 140)(2, \"label\", 141)(3, \"div\", 142);\n    i0.ɵɵelement(4, \"i\", 143);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 144)(6, \"h6\", 145);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, StepperModalComponent_div_21_div_1_div_11_p_8_Template, 2, 1, \"p\", 146)(9, StepperModalComponent_div_21_div_1_div_11_p_9_Template, 2, 1, \"p\", 146)(10, StepperModalComponent_div_21_div_1_div_11_span_10_Template, 2, 1, \"span\", 147);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 148);\n    i0.ɵɵlistener(\"change\", function StepperModalComponent_div_21_div_1_div_11_Template_input_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const input_r21 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onFileChange($event, input_r21.name));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const input_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-files\", ctx_r4.getFileCount(input_r21.name) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", input_r21.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"fa-cloud-upload-alt\", ctx_r4.getFileCount(input_r21.name) === 0)(\"fa-check-circle\", ctx_r4.getFileCount(input_r21.name) > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(input_r21.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getFileCount(input_r21.name) === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getFileCount(input_r21.name) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getFileCount(input_r21.name) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", input_r21.name)(\"multiple\", input_r21.name !== \"mainImage\")(\"accept\", input_r21.name === \"video\" ? \"video/*\" : \"image/*\");\n  }\n}\nfunction StepperModalComponent_div_21_div_1_textarea_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"textarea\", 151);\n  }\n  if (rf & 2) {\n    let tmp_6_0;\n    const input_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formControlName\", input_r21.name)(\"placeholder\", \"Enter \" + input_r21.label)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_6_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_6_0.touched) && ((tmp_6_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_6_0.invalid)));\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const input_r21 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", input_r21.label, \" is required. \");\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_13_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    const input_r21 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", input_r21.label, \" must be greater than or equal to \", (tmp_5_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_5_0.errors == null ? null : tmp_5_0.errors[\"min\"] == null ? null : tmp_5_0.errors[\"min\"].min, \". \");\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_13_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const input_r21 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Please enter a valid \", input_r21.label.toLowerCase(), \". \");\n  }\n}\nfunction StepperModalComponent_div_21_div_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, StepperModalComponent_div_21_div_1_div_13_div_1_Template, 2, 1, \"div\", 15)(2, StepperModalComponent_div_21_div_1_div_13_div_2_Template, 2, 2, \"div\", 15)(3, StepperModalComponent_div_21_div_1_div_13_div_3_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    const input_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_4_0.errors == null ? null : tmp_4_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_5_0.errors == null ? null : tmp_5_0.errors[\"min\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_6_0.errors == null ? null : tmp_6_0.errors[\"pattern\"]);\n  }\n}\nfunction StepperModalComponent_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"label\", 94)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, StepperModalComponent_div_21_div_1_span_4_Template, 1, 0, \"span\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerStart(5, 96);\n    i0.ɵɵtemplate(6, StepperModalComponent_div_21_div_1_div_6_Template, 12, 12, \"div\", 97)(7, StepperModalComponent_div_21_div_1_input_7_Template, 1, 5, \"input\", 98)(8, StepperModalComponent_div_21_div_1_input_8_Template, 1, 5, \"input\", 99)(9, StepperModalComponent_div_21_div_1_div_9_Template, 7, 7, \"div\", 101)(10, StepperModalComponent_div_21_div_1_div_10_Template, 19, 15, \"div\", 114)(11, StepperModalComponent_div_21_div_1_div_11_Template, 12, 14, \"div\", 115)(12, StepperModalComponent_div_21_div_1_textarea_12_Template, 1, 5, \"textarea\", 116);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(13, StepperModalComponent_div_21_div_1_div_13_Template, 4, 3, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_13_0;\n    const input_r21 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(input_r21.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isInputRequired(input_r21));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", input_r21.type);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"checkbox\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"text\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"number\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"select\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"multiSelect\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"file\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"textarea\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_13_0.touched) && ((tmp_13_0 = ctx_r4.getCurrentForm().get(input_r21.name)) == null ? null : tmp_13_0.invalid));\n  }\n}\nfunction StepperModalComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, StepperModalComponent_div_21_div_1_Template, 14, 11, \"div\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.currentInputs)(\"ngForTrackBy\", ctx_r4.trackByInputName);\n  }\n}\nfunction StepperModalComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 152)(1, \"div\", 24);\n    i0.ɵɵelement(2, \"i\", 153);\n    i0.ɵɵelementStart(3, \"div\")(4, \"h6\", 154);\n    i0.ɵɵtext(5, \"No Files to Upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 155);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r4.getStep4InfoMessage());\n  }\n}\nfunction StepperModalComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function StepperModalComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.prevStep());\n    });\n    i0.ɵɵelement(1, \"i\", 157);\n    i0.ɵɵtext(2, \" Back \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 158);\n  }\n}\nfunction StepperModalComponent_button_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 159);\n    i0.ɵɵlistener(\"click\", function StepperModalComponent_button_26_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.nextStep());\n    });\n    i0.ɵɵelementStart(1, \"span\", 160);\n    i0.ɵɵtext(2, \" Next \");\n    i0.ɵɵelement(3, \"i\", 161);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r4.isCurrentFormValid());\n  }\n}\nfunction StepperModalComponent_button_27_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 160);\n    i0.ɵɵelement(1, \"i\", 165);\n    i0.ɵɵtext(2, \" Submit \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_button_27_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵelement(1, \"span\", 167);\n    i0.ɵɵtext(2, \" Submitting... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StepperModalComponent_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 162);\n    i0.ɵɵlistener(\"click\", function StepperModalComponent_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.submitForm());\n    });\n    i0.ɵɵtemplate(1, StepperModalComponent_button_27_span_1_Template, 3, 0, \"span\", 163)(2, StepperModalComponent_button_27_span_2_Template, 3, 0, \"span\", 164);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r4.isCurrentFormValid() || ctx_r4.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isSubmitting);\n  }\n}\nexport class StepperModalComponent {\n  fb;\n  createRequestService;\n  propertyService;\n  cdr;\n  router;\n  stepperInputConfigService;\n  totalSteps = STEPPER_CONFIG.TOTAL_STEPS;\n  currentStep = STEPPER_CONFIG.INITIAL_STEP;\n  userId;\n  userRole;\n  stepForms = [];\n  currentInputs = [];\n  validationErrors = [];\n  showErrorList = false;\n  citiesSubject = new BehaviorSubject([]);\n  areasSubject = new BehaviorSubject([]);\n  subAreasSubject = new BehaviorSubject([]);\n  cities$ = this.citiesSubject.asObservable();\n  areas$ = this.areasSubject.asObservable();\n  subAreas$ = this.subAreasSubject.asObservable();\n  selectedCityId = null;\n  selectedCityName = '';\n  selectedAreaId = null;\n  selectedAreaName = '';\n  selectedSubAreaName = '';\n  isLoadingCities = false;\n  isSubmitting = false;\n  specializationScopeOptions = SPECIALIZATION_SCOPE_OPTIONS;\n  typeOptions = TYPE_OPTIONS;\n  unitTypeOptions = [];\n  fieldToStepMap = {};\n  stepNames = STEPPER_CONFIG.STEP_NAMES;\n  // Enhanced multiselect properties\n  searchQueries = {};\n  constructor(fb, createRequestService, propertyService, cdr, router, stepperInputConfigService) {\n    this.fb = fb;\n    this.createRequestService = createRequestService;\n    this.propertyService = propertyService;\n    this.cdr = cdr;\n    this.router = router;\n    this.stepperInputConfigService = stepperInputConfigService;\n  }\n  ngOnInit() {\n    const userJson = localStorage.getItem('currentUser');\n    const user = userJson ? JSON.parse(userJson) : null;\n    this.userId = user?.id;\n    this.userRole = user?.role;\n    this.initForms();\n    this.loadInitialData();\n    this.loadStepInputs();\n  }\n  initForms() {\n    this.stepForms = Array(this.totalSteps).fill(null).map(() => this.fb.group({}));\n    this.stepForms[0] = this.fb.group({\n      specializationScope: ['', Validators.required],\n      type: ['', Validators.required],\n      unitType: ['', Validators.required]\n    });\n  }\n  loadInitialData() {\n    this.isLoadingCities = true;\n    forkJoin({\n      cities: this.propertyService.getCities().pipe(map(response => {\n        const cities = response.data || response;\n        console.log('Cities response:', response);\n        return cities.map(city => ({\n          key: city.name_en,\n          value: city.id\n        }));\n      }), catchError(error => {\n        console.error('Error fetching cities:', error);\n        Swal.fire('Error', 'Failed to fetch cities.', 'error');\n        return of([]);\n      })),\n      unitTypes: this.propertyService.getUnitTypes().pipe(map(response => {\n        const unitTypes = response.data || response;\n        console.log('Unit types response:', response);\n        return Object.entries(unitTypes).map(([key, value]) => ({\n          key,\n          value: value\n        }));\n      }), catchError(error => {\n        console.error('Error fetching unit types:', error);\n        Swal.fire('Error', 'Failed to fetch unit types.', 'error');\n        return of([]);\n      }))\n    }).subscribe({\n      next: ({\n        cities,\n        unitTypes\n      }) => {\n        this.citiesSubject.next(cities);\n        this.unitTypeOptions = unitTypes;\n        this.isLoadingCities = false;\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoadingCities = false;\n        console.error('Error loading initial data:', err);\n        Swal.fire('Error', 'Failed to load initial data.', 'error');\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  loadStepInputs() {\n    if (this.currentStep > 1) {\n      this.currentInputs = this.stepperInputConfigService.getInputsForKey(this.getConfigKey(), this.currentStep, this);\n      const formControls = this.currentInputs.reduce((acc, input) => {\n        acc[input.name] = [input.type === 'multiSelect' ? [] : '', input.validators || []];\n        return acc;\n      }, {});\n      this.stepForms[this.currentStep - 1] = this.fb.group(formControls);\n      if (this.currentStep === 2) {\n        this.stepForms[1].addControl('cityId', this.fb.control('', Validators.required));\n        this.stepForms[1].addControl('areaId', this.fb.control('', Validators.required));\n        this.stepForms[1].addControl('subAreaId', this.fb.control(''));\n        this.stepForms[1].addControl('compoundName', this.fb.control('', this.getInsideCompoundPrivilege() ? Validators.required : null));\n        // Add additional fields for outside compound scenarios\n        if (!this.getInsideCompoundPrivilege()) {\n          // Add detailedAddress and addressLink for rent-out and sell outside compound scenarios\n          if (this.getRentOutsideCompoundInputs() || this.getSellOutsideCompoundInputs()) {\n            this.stepForms[1].addControl('detailedAddress', this.fb.control('', Validators.required));\n            this.stepForms[1].addControl('addressLink', this.fb.control(''));\n          }\n          // Add locationSuggestion for purchase outside compound scenarios\n          if (this.getPurchaseOutsideCompoundInputs()) {\n            this.stepForms[1].addControl('locationSuggestions', this.fb.control(''));\n          }\n          // For rent-in outside compound, locationSuggestions is handled by the configuration service\n        }\n      }\n    } else {\n      this.currentInputs = [{\n        name: 'specializationScope',\n        type: 'select',\n        label: 'Specialization Scope',\n        options: this.specializationScopeOptions,\n        validators: [Validators.required]\n      }, {\n        name: 'type',\n        type: 'select',\n        label: 'Type',\n        options: this.getFilteredTypeOptions(),\n        validators: [Validators.required]\n      }, {\n        name: 'unitType',\n        type: 'select',\n        label: 'Unit Type',\n        options: this.getFilteredUnitTypeOptions(),\n        validators: [Validators.required]\n      }];\n    }\n    this.cdr.markForCheck();\n  }\n  getSellInsideCompoundInputs() {\n    const scope = this.stepForms[0].get('specializationScope')?.value;\n    const type = this.stepForms[0].get('type')?.value;\n    const targetScopes = ['resale_inside_compound', 'primary_inside_compound'];\n    const typeScopes = ['sell', 'purchasing'];\n    return targetScopes.includes(scope) && typeScopes.includes(type);\n  }\n  getRentOutInsideCompoundInputs() {\n    const scope = this.stepForms[0].get('specializationScope')?.value;\n    const type = this.stepForms[0].get('type')?.value;\n    const targetScopes = ['rentals_inside_compound'];\n    const typeScopes = ['rent_out'];\n    return targetScopes.includes(scope) && typeScopes.includes(type);\n  }\n  getRentOutsideCompoundInputs() {\n    const scope = this.stepForms[0].get('specializationScope')?.value;\n    const type = this.stepForms[0].get('type')?.value;\n    const targetScopes = ['rentals_outside_compound'];\n    const typeScopes = ['rent_out'];\n    return targetScopes.includes(scope) && typeScopes.includes(type);\n  }\n  getRentInOutsideCompoundInputs() {\n    const scope = this.stepForms[0].get('specializationScope')?.value;\n    const type = this.stepForms[0].get('type')?.value;\n    const targetScopes = ['rentals_outside_compound'];\n    const typeScopes = ['rent_in'];\n    return targetScopes.includes(scope) && typeScopes.includes(type);\n  }\n  getSellOutsideCompoundInputs() {\n    const scope = this.stepForms[0].get('specializationScope')?.value;\n    const type = this.stepForms[0].get('type')?.value;\n    const targetScopes = ['purchase_sell_outside_compound'];\n    const typeScopes = ['sell'];\n    return targetScopes.includes(scope) && typeScopes.includes(type);\n  }\n  getPurchaseOutsideCompoundInputs() {\n    const scope = this.stepForms[0].get('specializationScope')?.value;\n    const type = this.stepForms[0].get('type')?.value;\n    const targetScopes = ['purchase_sell_outside_compound'];\n    const typeScopes = ['purchasing'];\n    return targetScopes.includes(scope) && typeScopes.includes(type);\n  }\n  getMallPrivilege() {\n    const type = this.stepForms[0].get('type')?.value;\n    const unitType = this.stepForms[0].get('unitType')?.value;\n    const mallUnitTypes = ['administrative_units', 'medical_clinics', 'pharmacies', 'commercial_stores'];\n    const hasTargetType = ['sell', 'rent_out'].includes(type);\n    return mallUnitTypes.includes(unitType) && hasTargetType && !this.getInsideCompoundPrivilege();\n  }\n  getUnitAndBuildingNumber() {\n    const type = this.stepForms[0].get('type')?.value;\n    return ['sell', 'rent_out'].includes(type);\n  }\n  getVillageName() {\n    const unitType = this.stepForms[0].get('unitType')?.value;\n    const targetUnitTypes = ['vacation_villa', 'chalets'];\n    return targetUnitTypes.includes(unitType);\n  }\n  getSelectedAccessoriesText() {\n    const input = this.currentInputs.find(i => i.name === 'otherAccessories');\n    if (!input) return '';\n    return this.getSelectedText('otherAccessories', input.options);\n  }\n  isAccessorySelected(accessoryValue) {\n    return this.isMultiSelectOptionSelected('otherAccessories', accessoryValue);\n  }\n  toggleAccessory(accessoryValue) {\n    this.toggleMultiSelect('otherAccessories', accessoryValue);\n  }\n  getSelectedOtherExpensesText() {\n    const input = this.currentInputs.find(i => i.name === 'otherExpenses');\n    if (!input) return '';\n    return this.getSelectedText('otherExpenses', input.options);\n  }\n  isOtherExpenseSelected(expenseValue) {\n    return this.isMultiSelectOptionSelected('otherExpenses', expenseValue);\n  }\n  toggleOtherExpense(expenseValue) {\n    this.toggleMultiSelect('otherExpenses', expenseValue);\n  }\n  onAllAccessoriesChange(event) {\n    const input = this.currentInputs.find(i => i.name === 'otherAccessories');\n    if (!input) return;\n    const isChecked = event.target.checked;\n    const updatedValues = isChecked ? input.options.map(opt => opt.value) : [];\n    this.stepForms[this.currentStep - 1].patchValue({\n      otherAccessories: updatedValues\n    });\n    this.cdr.markForCheck();\n  }\n  getSelectedText(fieldName, options) {\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\n    const selectedOptions = options.filter(opt => currentValues.includes(opt.value));\n    if (selectedOptions.length === 0) return '';\n    if (selectedOptions.length === 1) return selectedOptions[0].key;\n    return `${selectedOptions.length} items selected`;\n  }\n  isMultiSelectOptionSelected(fieldName, value) {\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\n    return currentValues.includes(value);\n  }\n  toggleMultiSelect(fieldName, value) {\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\n    let updatedValues;\n    if (currentValues.includes(value)) {\n      updatedValues = currentValues.filter(item => item !== value);\n    } else {\n      updatedValues = [...currentValues, value];\n    }\n    this.getCurrentForm().patchValue({\n      [fieldName]: updatedValues\n    });\n    this.cdr.markForCheck();\n  }\n  // Enhanced multiselect methods\n  getSelectedCount(fieldName) {\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\n    return currentValues.length;\n  }\n  getSearchQuery(fieldName) {\n    return this.searchQueries[fieldName] || '';\n  }\n  updateSearchQuery(fieldName, event) {\n    this.searchQueries[fieldName] = event.target.value;\n    this.cdr.markForCheck();\n  }\n  clearSearch(fieldName) {\n    this.searchQueries[fieldName] = '';\n    this.cdr.markForCheck();\n  }\n  getFilteredOptions(fieldName, options) {\n    const searchQuery = this.getSearchQuery(fieldName).toLowerCase();\n    if (!searchQuery) {\n      return options;\n    }\n    return options.filter(option => option.key.toLowerCase().includes(searchQuery));\n  }\n  areAllOptionsSelected(fieldName) {\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\n    const visibleOptions = this.getFilteredOptions(fieldName, this.getOptionsForField(fieldName));\n    return visibleOptions.length > 0 && visibleOptions.every(option => currentValues.includes(option.value));\n  }\n  areSomeOptionsSelected(fieldName) {\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\n    const visibleOptions = this.getFilteredOptions(fieldName, this.getOptionsForField(fieldName));\n    return visibleOptions.some(option => currentValues.includes(option.value));\n  }\n  toggleSelectAll(fieldName) {\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\n    const visibleOptions = this.getFilteredOptions(fieldName, this.getOptionsForField(fieldName));\n    if (this.areAllOptionsSelected(fieldName)) {\n      // Deselect all visible options\n      const updatedValues = currentValues.filter(value => !visibleOptions.some(option => option.value === value));\n      this.getCurrentForm().patchValue({\n        [fieldName]: updatedValues\n      });\n    } else {\n      // Select all visible options\n      const newValues = visibleOptions.map(option => option.value);\n      const updatedValues = [...new Set([...currentValues, ...newValues])];\n      this.getCurrentForm().patchValue({\n        [fieldName]: updatedValues\n      });\n    }\n    this.cdr.markForCheck();\n  }\n  clearAllSelections(fieldName) {\n    this.getCurrentForm().patchValue({\n      [fieldName]: []\n    });\n    this.cdr.markForCheck();\n  }\n  getSelectedOptions(fieldName, options) {\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\n    return options.filter(option => currentValues.includes(option.value));\n  }\n  getOptionsForField(fieldName) {\n    const input = this.currentInputs.find(inp => inp.name === fieldName);\n    return input?.options || [];\n  }\n  getText(options, value) {\n    const item = options.find(item => item.value === value);\n    return item ? item.key : '';\n  }\n  select(form, field, value) {\n    form.patchValue({\n      [field]: value\n    });\n    form.get(field)?.markAsTouched();\n    form.get(field)?.updateValueAndValidity();\n    this.cdr.markForCheck();\n    // Special handling for specialization scope changes\n    if (field === 'specializationScope') {\n      // Reset type and unitType when scope changes since filtered options may change\n      form.patchValue({\n        type: '',\n        unitType: ''\n      });\n      form.get('type')?.markAsUntouched();\n      form.get('unitType')?.markAsUntouched();\n      // Refresh the step inputs to update the unit type options\n      this.loadStepInputs();\n    } else if (field === 'type') {\n      // Reset unitType when type changes\n      form.patchValue({\n        unitType: ''\n      });\n      form.get('unitType')?.markAsUntouched();\n    }\n  }\n  onSelectChange(fieldName, value, name) {\n    if (fieldName === 'cityId') {\n      this.selectCity(value, name);\n    } else if (fieldName === 'areaId') {\n      this.selectArea(value, name);\n    } else if (fieldName === 'subAreaId') {\n      this.selectSubArea(value, name);\n    }\n  }\n  fileValidator(options) {\n    return control => {\n      const file = control.value;\n      if (!file) return null;\n      if (file.size > options.maxSize) {\n        return {\n          maxSize: true\n        };\n      }\n      if (!options.allowedTypes.includes(file.type)) {\n        return {\n          invalidType: true\n        };\n      }\n      return null;\n    };\n  }\n  fileArrayValidator(options) {\n    return control => {\n      const files = control.value;\n      if (!Array.isArray(files)) {\n        return {\n          notArray: true\n        };\n      }\n      for (const file of files) {\n        if (file.size > options.maxSize) {\n          return {\n            maxSize: true\n          };\n        }\n        if (!options.allowedTypes.includes(file.type)) {\n          return {\n            invalidType: true\n          };\n        }\n      }\n      return null;\n    };\n  }\n  isClient() {\n    return this.userRole == 'client';\n  }\n  getInsideCompoundPrivilege() {\n    const scope = this.stepForms[0].get('specializationScope')?.value;\n    const targetScopes = ['primary_inside_compound', 'resale_inside_compound', 'rentals_inside_compound'];\n    return targetScopes.includes(scope);\n  }\n  getOutsideCompoundPrivilege() {\n    const scope = this.stepForms[0].get('specializationScope')?.value;\n    const targetScopes = ['purchase_sell_outside_compound', 'rentals_outside_compound'];\n    return targetScopes.includes(scope);\n  }\n  selectCity(cityId, cityName) {\n    this.selectedCityId = cityId;\n    this.selectedCityName = cityName;\n    this.getCurrentForm().patchValue({\n      cityId,\n      areaId: '',\n      subAreaId: ''\n    });\n    this.areasSubject.next([]);\n    this.subAreasSubject.next([]);\n    this.selectedAreaId = null;\n    this.selectedAreaName = '';\n    this.selectedSubAreaName = '';\n    this.propertyService.getAreas(cityId).pipe(map(response => {\n      const areas = response.data || response;\n      return areas.map(area => ({\n        key: area.name_en,\n        value: area.id\n      }));\n    }), catchError(error => {\n      console.error('Error fetching areas:', error);\n      Swal.fire('Error', 'Failed to fetch areas.', 'error');\n      return of([]);\n    })).subscribe(areas => {\n      console.log('Fetched areas:', areas);\n      this.areasSubject.next(areas);\n      this.cdr.markForCheck();\n    });\n  }\n  selectArea(areaId, areaName) {\n    this.selectedAreaId = areaId;\n    this.selectedAreaName = areaName;\n    this.getCurrentForm().patchValue({\n      areaId,\n      subAreaId: ''\n    });\n    this.subAreasSubject.next([]);\n    this.selectedSubAreaName = '';\n    this.propertyService.getSubAreas(areaId).pipe(map(response => {\n      const subAreas = response.data || response;\n      return subAreas.map(subArea => ({\n        key: subArea.name_en,\n        value: subArea.id\n      }));\n    }), catchError(error => {\n      console.error('Error fetching sub-areas:', error);\n      Swal.fire('Error', 'Failed to fetch sub-areas.', 'error');\n      return of([]);\n    })).subscribe(subAreas => {\n      this.subAreasSubject.next(subAreas);\n      this.cdr.markForCheck();\n    });\n  }\n  selectSubArea(subAreaId, subAreaName) {\n    this.selectedSubAreaName = subAreaName;\n    this.getCurrentForm().patchValue({\n      subAreaId\n    });\n    this.cdr.markForCheck();\n  }\n  getConfigKey() {\n    const step1Values = this.stepForms[0].value;\n    return `${step1Values.specializationScope}_${step1Values.type}_${step1Values.unitType}`;\n  }\n  getFilteredTypeOptions() {\n    const scope = this.stepForms[0].get('specializationScope')?.value;\n    if (scope?.includes('rentals')) {\n      return this.typeOptions.filter(t => ['rent_out', 'rent_in'].includes(t.value));\n    } else if (scope?.includes('purchase_sell') || scope?.includes('primary') || scope?.includes('resale')) {\n      return this.typeOptions.filter(t => ['purchasing', 'sell'].includes(t.value));\n    }\n    return this.typeOptions;\n  }\n  getFilteredUnitTypeOptions() {\n    const scope = this.stepForms[0].get('specializationScope')?.value;\n    // If no scope is selected, return empty array\n    if (!scope) {\n      return [];\n    }\n    // Inside compound unit types (primary and resale)\n    if (scope === 'primary_inside_compound' || scope === 'resale_inside_compound') {\n      return this.unitTypeOptions.filter(unitType => ['apartments', 'duplexes', 'studios', 'penthouses', 'villas', 'twin_houses', 'town_houses', 'standalone_villas', 'administrative_units',\n      // 'commercial_units',\n      'medical_clinics',\n      // 'commercial_stores',\n      'pharmacies', 'commercial_administrative_buildings', 'shops'].includes(unitType.value));\n    }\n    //  if (selectedScope === 'purchase_sell_outside_compound' ||\n    //     // selectedScope === 'purchase_sell_inside_compound' ||\n    //     selectedScope === 'primary_inside_compound' ||\n    //     selectedScope === 'resale_inside_compound') {\n    //    return this.Type.filter(type =>\n    //     type.value === 'sell' || type.value === 'purchasing'\n    // Outside compound unit types (purchase-sell)\n    if (scope === 'purchase_sell_outside_compound') {\n      return this.unitTypeOptions.filter(unitType => ['apartments', 'duplexes', 'penthouses', 'studios', 'basements', 'roofs', 'administrative_units', 'medical_clinics', 'pharmacies', 'commercial_stores', 'standalone_villas', 'factory_lands', 'warehouses', 'residential_buildings', 'commercial_administrative_buildings'].includes(unitType.value));\n    }\n    // Rental unit types (both inside and outside compound)\n    if (scope === 'rentals_inside_compound') {\n      return this.unitTypeOptions.filter(unitType => ['apartments', 'duplexes', 'studios', 'penthouses', 'villas', 'town_houses', 'twin_houses',\n      // 'basements',\n      // 'roofs',\n      // 'residential_buildings',\n      'standalone_villas', 'administrative_units', 'commercial_stores', 'medical_clinics', 'pharmacies', 'commercial_administrative_buildings'].includes(unitType.value));\n    }\n    if (scope === 'rentals_outside_compound') {\n      return this.unitTypeOptions.filter(unitType => ['apartments', 'duplexes', 'studios', 'penthouses',\n      // 'villas',\n      // 'town_houses',\n      // 'twin_houses',\n      'basements', 'roofs',\n      // 'residential_buildings',\n      'standalone_villas', 'administrative_units', 'commercial_stores', 'medical_clinics', 'pharmacies', 'factory_lands', 'warehouses', 'commercial_administrative_buildings'].includes(unitType.value));\n    }\n    // For other scopes, return all unit types\n    return this.unitTypeOptions;\n  }\n  getCurrentForm() {\n    return this.stepForms[this.currentStep - 1];\n  }\n  nextStep() {\n    if (this.getCurrentForm().valid && this.currentStep < this.totalSteps) {\n      this.currentStep++;\n      this.loadStepInputs();\n    }\n  }\n  prevStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n      this.loadStepInputs();\n    }\n  }\n  // Check if current step has inputs\n  hasStepInputs() {\n    return this.currentInputs && this.currentInputs.length > 0;\n  }\n  // Check if input is required\n  isInputRequired(input) {\n    return input.validators && input.validators.some(validator => validator === Validators.required || validator && validator.toString && validator.toString().includes('required'));\n  }\n  // Get step 4 info message\n  getStep4InfoMessage() {\n    const configKey = this.getConfigKey();\n    if (configKey.includes('rent_in')) {\n      return 'No image uploads are required for rent-in requests.';\n    }\n    return 'No image uploads are available for this property type.';\n  }\n  trackByInputName(index, input) {\n    return input.name;\n  }\n  navigateToErrorStep(stepNumber) {\n    this.currentStep = stepNumber;\n    this.loadStepInputs();\n    this.cdr.markForCheck();\n  }\n  clearValidationErrors() {\n    this.validationErrors = [];\n    this.showErrorList = false;\n    this.cdr.markForCheck();\n  }\n  /**\n   * Get total count of all validation errors across all steps\n   */\n  getTotalErrorCount() {\n    return this.validationErrors.reduce((total, stepError) => {\n      return total + stepError.errors.length;\n    }, 0);\n  }\n  /**\n   * Navigate to the first step that has validation errors\n   */\n  navigateToFirstErrorStep() {\n    if (this.validationErrors.length > 0) {\n      const firstErrorStep = Math.min(...this.validationErrors.map(error => error.step));\n      this.navigateToErrorStep(firstErrorStep);\n    }\n  }\n  isCurrentFormValid() {\n    return this.getCurrentForm().valid;\n  }\n  // Debug method to show current form errors\n  getCurrentFormErrors() {\n    const form = this.getCurrentForm();\n    const errors = [];\n    Object.keys(form.controls).forEach(key => {\n      const control = form.get(key);\n      if (control && control.invalid) {\n        const controlErrors = control.errors;\n        if (controlErrors) {\n          Object.keys(controlErrors).forEach(errorKey => {\n            errors.push(`${key}: ${errorKey}`);\n          });\n        }\n      }\n    });\n    return errors.length > 0 ? errors.join(', ') : 'No errors';\n  }\n  getFileCount(fieldName) {\n    const files = this.getCurrentForm().get(fieldName)?.value;\n    return files && Array.isArray(files) ? files.length : 0;\n  }\n  onFileChange(event, fieldName) {\n    if (event.target.files?.length) {\n      this.stepForms[this.currentStep - 1].patchValue({\n        [fieldName]: Array.from(event.target.files)\n      });\n      this.cdr.markForCheck();\n    }\n  }\n  submitForm() {\n    if (this.stepForms.every(form => form.valid)) {\n      this.isSubmitting = true;\n      const formData = new FormData();\n      formData.append('userId', this.userId.toString());\n      // Add step 1 values\n      const step1Values = this.stepForms[0].value;\n      Object.keys(step1Values).forEach(key => {\n        formData.append(key, step1Values[key] || '');\n      });\n      // Add location data from step 2\n      const step2Values = this.stepForms[1].value;\n      if (step2Values.cityId) {\n        formData.append('locations[0][city]', step2Values.cityId.toString());\n      }\n      if (step2Values.areaId) {\n        formData.append('locations[0][areas][0][id]', step2Values.areaId.toString());\n      }\n      if (step2Values.subAreaId) {\n        formData.append('locations[0][areas][0][subAreas][0]', step2Values.subAreaId.toString());\n      }\n      // Add other form values\n      this.stepForms.slice(1).forEach(form => {\n        Object.keys(form.value).forEach(key => {\n          if (['cityId', 'areaId', 'subAreaId'].includes(key)) {\n            return;\n          }\n          const value = form.value[key];\n          if (value !== null && value !== undefined) {\n            if (Array.isArray(value)) {\n              value.forEach((item, i) => formData.append(`attributes[${key}][${i}]`, item));\n            } else if (typeof value === 'boolean') {\n              formData.append(`attributes[${key}]`, value ? '1' : '0');\n            } else if (value !== '') {\n              formData.append(`attributes[${key}]`, value.toString());\n            }\n          }\n        });\n      });\n      // Handle file uploads\n      const fileFields = ['mainImage', 'galleryImages', 'video', 'unitInMasterPlanImage'];\n      fileFields.forEach(field => {\n        const files = this.stepForms[3]?.get(field)?.value;\n        if (files && Array.isArray(files) && files.length > 0) {\n          const isMultiple = ['galleryImages', 'video'].includes(field);\n          if (isMultiple) {\n            files.forEach((file, index) => {\n              if (file instanceof File) {\n                formData.append(`attributes[${field}][${index}]`, file);\n              }\n            });\n          } else {\n            if (files[0] instanceof File) {\n              formData.append(`attributes[${field}]`, files[0]);\n            }\n          }\n        }\n      });\n      this.createRequestService.createRequest(formData).subscribe({\n        next: res => {\n          this.isSubmitting = false;\n          Swal.fire('Success', 'Request created successfully!', 'success').then(() => {\n            this.router.navigate([`/requests/assign-to`, res.data?.id || res.id]);\n          });\n        },\n        error: err => {\n          this.isSubmitting = false;\n          this.handleSubmissionError(err);\n        }\n      });\n    }\n  }\n  handleSubmissionError(error) {\n    this.validationErrors = [];\n    if (error?.error?.errors) {\n      Object.keys(error.error.errors).forEach(field => {\n        const cleanField = field.replace(/^attributes\\./g, '').replace(/\\[\\d+\\].*/g, '');\n        const step = this.currentInputs.find(input => input.name === cleanField)?.step || 1;\n        let stepGroup = this.validationErrors.find(v => v.step === step);\n        if (!stepGroup) {\n          stepGroup = {\n            step,\n            stepName: this.stepNames[step] || `Step ${step}`,\n            errors: []\n          };\n          this.validationErrors.push(stepGroup);\n        }\n        stepGroup.errors.push({\n          field: cleanField,\n          messages: Array.isArray(error.error.errors[field]) ? error.error.errors[field] : [error.error.errors[field]]\n        });\n      });\n      this.showErrorList = true;\n      this.cdr.markForCheck();\n    } else {\n      Swal.fire('Error', error?.error?.message || 'An unknown error occurred.', 'error');\n    }\n  }\n  static ɵfac = function StepperModalComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || StepperModalComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.createrequestservice), i0.ɵɵdirectiveInject(i3.PropertyService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.StepperInputConfigService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: StepperModalComponent,\n    selectors: [[\"app-stepper-modal\"]],\n    decls: 28,\n    vars: 16,\n    consts: [[1, \"mb-5\", \"mt-0\"], [3, \"showCreateButton\"], [1, \"card\", \"rounded-4\"], [1, \"card-body\", \"p-10\"], [\"id\", \"hotel_unit_rental_stepper\", 1, \"stepper\", \"stepper-pills\", \"d-flex\", \"flex-column\"], [1, \"mb-5\", \"text-center\"], [1, \"fw-bold\", \"text-dark-blue\", \"mb-2\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"mb-2\"], [1, \"text-success\", \"fw-bold\"], [1, \"text-muted\", \"mx-1\"], [1, \"text-muted\"], [1, \"progress\", \"h-8px\", \"bg-light-success\", \"w-75\", \"mx-auto\"], [\"role\", \"progressbar\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", 1, \"progress-bar\", \"bg-success\"], [\"class\", \"alert alert-danger border-0 shadow-sm mb-5\", \"style\", \"border-radius: 12px;\", 4, \"ngIf\"], [1, \"mx-auto\", \"w-100\", \"pt-5\", \"pb-10\", 3, \"formGroup\"], [4, \"ngIf\"], [\"class\", \"alert alert-info mt-4\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"pt-10\"], [\"type\", \"button\", \"class\", \"btn btn-lg btn-outline-success btn-light-success px-6 py-3 rounded-pill\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"invisible\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-lg btn-navy px-6 py-3 rounded-pill\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-lg btn-navy px-6 py-3 rounded-pill d-flex align-items-center\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"alert\", \"alert-danger\", \"border-0\", \"shadow-sm\", \"mb-5\", 2, \"border-radius\", \"12px\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\", \"mb-4\"], [1, \"d-flex\", \"align-items-center\"], [1, \"bg-danger\", \"rounded-circle\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"me-3\", 2, \"width\", \"40px\", \"height\", \"40px\"], [1, \"fas\", \"fa-exclamation-triangle\", \"text-white\"], [1, \"mb-1\", \"text-danger\", \"fw-bold\"], [1, \"mb-0\", \"text-muted\", \"small\"], [1, \"error-steps-container\"], [\"class\", \"error-step-card mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"error-step-card\", \"mb-3\"], [1, \"card\", \"border-0\", \"shadow-sm\", 2, \"border-radius\", \"8px\"], [1, \"card-body\", \"pt-8\"], [1, \"error-list\"], [\"class\", \"error-item d-flex align-items-start p-3 mb-2 bg-light rounded\", 3, \"border-start\", \"border-danger\", \"border-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"error-item\", \"d-flex\", \"align-items-start\", \"p-3\", \"mb-2\", \"bg-light\", \"rounded\"], [1, \"error-icon-container\", \"me-3\", \"mt-1\"], [1, \"fas\", \"fa-times-circle\", \"text-danger\"], [1, \"error-content\", \"flex-grow-1\"], [1, \"d-flex\", \"align-items-center\", \"mb-1\"], [1, \"text-dark\", \"me-2\"], [1, \"badge\", \"bg-danger\", \"bg-opacity-10\", \"text-danger\", \"small\"], [1, \"error-messages\"], [\"class\", \"mb-0 text-muted small\", 4, \"ngFor\", \"ngForOf\"], [1, \"fas\", \"fa-chevron-right\", \"me-1\", 2, \"font-size\", \"10px\"], [1, \"mb-10\"], [1, \"form-label\", \"fw-bold\", \"text-start\", \"d-block\"], [1, \"dropdown\"], [\"type\", \"button\", \"id\", \"specializationScopeDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"fas\", \"fa-chevron-down\"], [\"aria-labelledby\", \"specializationScopeDropdown\", 1, \"dropdown-menu\", \"w-100\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"invalid-feedback d-block\", 4, \"ngIf\"], [\"type\", \"button\", \"id\", \"typeDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"typeDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"type\", \"button\", \"id\", \"unitTypeDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [\"aria-labelledby\", \"unitTypeDropdown\", 1, \"dropdown-menu\", \"w-100\"], [\"href\", \"javascript:void(0)\", 1, \"dropdown-item\", 3, \"click\"], [1, \"invalid-feedback\", \"d-block\"], [\"type\", \"button\", \"id\", \"cityIdDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 3, \"disabled\"], [\"aria-labelledby\", \"cityIdDropdown\", 1, \"dropdown-menu\", \"w-100\", 2, \"max-height\", \"200px\", \"overflow-y\", \"auto\"], [\"class\", \"text-muted mt-1\", 4, \"ngIf\"], [\"type\", \"button\", \"id\", \"areaIdDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 3, \"disabled\"], [\"aria-labelledby\", \"areaIdDropdown\", 1, \"dropdown-menu\", \"w-100\", 2, \"max-height\", \"200px\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"id\", \"subAreaIdDropdown\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 3, \"disabled\"], [\"aria-labelledby\", \"subAreaIdDropdown\", 1, \"dropdown-menu\", \"w-100\", 2, \"max-height\", \"200px\", \"overflow-y\", \"auto\"], [\"class\", \"mb-10\", 4, \"ngIf\"], [\"class\", \"mb-7\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"dropdown-item\", 3, \"click\"], [1, \"text-muted\", \"mt-1\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"me-1\"], [1, \"custom-checkbox-container\"], [\"type\", \"checkbox\", \"id\", \"locationSuggestions\", \"formControlName\", \"locationSuggestions\", \"aria-describedby\", \"locationSuggestions_help\", 1, \"custom-checkbox-input\"], [\"for\", \"locationSuggestions\", 1, \"custom-checkbox-label\"], [1, \"custom-checkbox-box\"], [1, \"fas\", \"fa-check\", \"custom-checkbox-icon\"], [1, \"custom-checkbox-content\"], [1, \"custom-checkbox-text\"], [1, \"custom-checkbox-description\"], [\"class\", \"checkbox-error-feedback\", \"id\", \"locationSuggestions_help\", \"role\", \"alert\", 4, \"ngIf\"], [\"id\", \"locationSuggestions_help\", \"role\", \"alert\", 1, \"checkbox-error-feedback\"], [1, \"fas\", \"fa-exclamation-circle\", \"me-1\"], [\"type\", \"checkbox\", \"id\", \"locationSuggestionsOutside\", \"formControlName\", \"locationSuggestions\", \"aria-describedby\", \"locationSuggestionsOutside_help\", 1, \"custom-checkbox-input\"], [\"for\", \"locationSuggestionsOutside\", 1, \"custom-checkbox-label\"], [\"class\", \"checkbox-error-feedback\", \"id\", \"locationSuggestionsOutside_help\", \"role\", \"alert\", 4, \"ngIf\"], [\"id\", \"locationSuggestionsOutside_help\", \"role\", \"alert\", 1, \"checkbox-error-feedback\"], [1, \"required\"], [\"type\", \"text\", \"formControlName\", \"compoundName\", \"placeholder\", \"Enter Compound Name\", 1, \"form-control\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"detailedAddress\", \"placeholder\", \"Enter Detailed Address\", 1, \"form-control\", 3, \"ngClass\"], [\"type\", \"url\", \"formControlName\", \"addressLink\", \"placeholder\", \"Enter Address Link (https://...)\", 1, \"form-control\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"projectManagement\", \"placeholder\", \"Enter Project Management\", 1, \"form-control\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"projectConstructor\", \"placeholder\", \"Enter Project Constructor\", 1, \"form-control\", 3, \"ngClass\"], [1, \"mb-7\"], [1, \"form-label\", \"fw-bold\", \"text-start\", \"d-flex\", \"align-items-center\", \"flex-wrap\", \"gap-2\", \"mb-3\"], [\"class\", \"required\", 4, \"ngIf\"], [3, \"ngSwitch\"], [\"class\", \"custom-checkbox-container\", 3, \"has-error\", 4, \"ngSwitchCase\"], [\"type\", \"text\", \"class\", \"form-control\", 3, \"formControlName\", \"placeholder\", \"ngClass\", 4, \"ngSwitchCase\"], [\"type\", \"number\", \"class\", \"form-control\", \"min\", \"0\", 3, \"formControlName\", \"placeholder\", \"ngClass\", 4, \"ngSwitchCase\"], [\"type\", \"url\", \"class\", \"form-control\", 3, \"formControlName\", \"placeholder\", \"ngClass\", 4, \"ngSwitchCase\"], [\"class\", \"dropdown\", 4, \"ngSwitchCase\"], [\"class\", \"form-control\", \"rows\", \"3\", 3, \"formControlName\", \"placeholder\", \"ngClass\", 4, \"ngSwitchCase\"], [\"type\", \"checkbox\", 1, \"custom-checkbox-input\", 3, \"formControlName\", \"id\"], [1, \"custom-checkbox-label\", 3, \"for\"], [\"class\", \"custom-checkbox-description\", 4, \"ngIf\"], [\"class\", \"checkbox-error-feedback\", \"role\", \"alert\", 3, \"id\", 4, \"ngIf\"], [\"role\", \"alert\", 1, \"checkbox-error-feedback\", 3, \"id\"], [\"type\", \"text\", 1, \"form-control\", 3, \"formControlName\", \"placeholder\", \"ngClass\"], [\"type\", \"number\", \"min\", \"0\", 1, \"form-control\", 3, \"formControlName\", \"placeholder\", \"ngClass\"], [\"type\", \"url\", 1, \"form-control\", 3, \"formControlName\", \"placeholder\", \"ngClass\"], [\"type\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 3, \"id\"], [1, \"dropdown-menu\", \"w-100\", 3, \"ngStyle\"], [\"rows\", \"3\", 1, \"form-control\", 3, \"formControlName\", \"placeholder\", \"ngClass\"], [\"class\", \"enhanced-multiselect\", 4, \"ngSwitchCase\"], [\"class\", \"mb-4 upload-card-container\", 4, \"ngSwitchCase\"], [\"class\", \"form-control\", \"rows\", \"4\", 3, \"formControlName\", \"placeholder\", \"ngClass\", 4, \"ngSwitchCase\"], [1, \"enhanced-multiselect\"], [\"type\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"btn\", \"btn-outline-secondary\", \"dropdown-toggle\", \"w-100\", \"text-start\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"enhanced-multiselect-button\", 3, \"id\"], [1, \"multiselect-display\"], [1, \"multiselect-text\"], [\"class\", \"badge bg-primary ms-2\", 4, \"ngIf\"], [1, \"fas\", \"fa-chevron-down\", \"multiselect-icon\"], [1, \"dropdown-menu\", \"w-150\", \"enhanced-multiselect-menu\"], [1, \"multiselect-controls\", \"p-2\", \"border-bottom\", \"bg-light\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"form-check\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"id\", \"checked\", \"indeterminate\"], [1, \"form-check-label\", \"fw-bold\", \"text-primary\", 3, \"for\"], [1, \"multiselect-options-container\", 2, \"max-height\", \"250px\", \"overflow-y\", \"auto\"], [1, \"p-2\"], [\"class\", \"multiselect-option mb-1\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-muted py-3\", 4, \"ngIf\"], [1, \"badge\", \"bg-primary\", \"ms-2\"], [1, \"multiselect-option\", \"mb-1\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\", \"id\", \"checked\"], [1, \"form-check-label\", \"text-start\", \"multiselect-option-label\", 3, \"for\"], [1, \"text-center\", \"text-muted\", \"py-3\"], [1, \"fas\", \"fa-search\", \"mb-2\"], [1, \"mb-4\", \"upload-card-container\"], [1, \"card\", \"cursor-pointer\", \"upload-card\"], [1, \"card-body\", \"text-center\", 3, \"for\"], [1, \"upload-icon\"], [1, \"fas\", \"fa-2x\"], [1, \"upload-content\"], [1, \"upload-title\", \"mb-2\"], [\"class\", \"upload-subtitle mb-0\", 4, \"ngIf\"], [\"class\", \"badge bg-success mt-2\", 4, \"ngIf\"], [\"type\", \"file\", 1, \"d-none\", 3, \"change\", \"id\", \"multiple\", \"accept\"], [1, \"upload-subtitle\", \"mb-0\"], [1, \"badge\", \"bg-success\", \"mt-2\"], [\"rows\", \"4\", 1, \"form-control\", 3, \"formControlName\", \"placeholder\", \"ngClass\"], [1, \"alert\", \"alert-info\", \"mt-4\"], [1, \"fas\", \"fa-info-circle\", \"fa-2x\", \"me-3\", \"text-info\"], [1, \"alert-heading\", \"mb-1\"], [1, \"mb-0\"], [\"type\", \"button\", 1, \"btn\", \"btn-lg\", \"btn-outline-success\", \"btn-light-success\", \"px-6\", \"py-3\", \"rounded-pill\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [1, \"invisible\"], [\"type\", \"button\", 1, \"btn\", \"btn-lg\", \"btn-navy\", \"px-6\", \"py-3\", \"rounded-pill\", 3, \"click\", \"disabled\"], [1, \"indicator-label\", \"text-white\"], [1, \"fas\", \"fa-arrow-right\", \"ms-2\", \"text-white\"], [\"type\", \"button\", 1, \"btn\", \"btn-lg\", \"btn-navy\", \"px-6\", \"py-3\", \"rounded-pill\", \"d-flex\", \"align-items-center\", 3, \"click\", \"disabled\"], [\"class\", \"indicator-label text-white\", 4, \"ngIf\"], [\"class\", \"d-flex align-items-center text-white\", 4, \"ngIf\"], [1, \"fas\", \"fa-check\", \"me-2\"], [1, \"d-flex\", \"align-items-center\", \"text-white\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n    template: function StepperModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"app-broker-title\", 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"h2\", 6);\n        i0.ɵɵtext(7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"span\", 8);\n        i0.ɵɵtext(10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"span\", 9);\n        i0.ɵɵtext(12, \"of\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"span\", 10);\n        i0.ɵɵtext(14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"div\", 11);\n        i0.ɵɵelement(16, \"div\", 12);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(17, StepperModalComponent_div_17_Template, 12, 2, \"div\", 13);\n        i0.ɵɵelementStart(18, \"form\", 14);\n        i0.ɵɵtemplate(19, StepperModalComponent_div_19_Template, 34, 9, \"div\", 15)(20, StepperModalComponent_div_20_Template, 45, 27, \"div\", 15)(21, StepperModalComponent_div_21_Template, 2, 2, \"div\", 15)(22, StepperModalComponent_div_22_Template, 8, 1, \"div\", 16);\n        i0.ɵɵelementStart(23, \"div\", 17);\n        i0.ɵɵtemplate(24, StepperModalComponent_button_24_Template, 3, 0, \"button\", 18)(25, StepperModalComponent_div_25_Template, 1, 0, \"div\", 19)(26, StepperModalComponent_button_26_Template, 4, 1, \"button\", 20)(27, StepperModalComponent_button_27_Template, 3, 3, \"button\", 21);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"showCreateButton\", false);\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(ctx.stepNames[ctx.currentStep]);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\"Step \", ctx.currentStep, \"\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.totalSteps);\n        i0.ɵɵadvance(2);\n        i0.ɵɵstyleProp(\"width\", ctx.currentStep / ctx.totalSteps * 100 + \"%\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showErrorList && ctx.validationErrors.length > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"formGroup\", ctx.getCurrentForm());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 2);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4 && !ctx.hasStepInputs());\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep !== ctx.totalSteps);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === ctx.totalSteps);\n      }\n    },\n    dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i6.NgStyle, i6.NgSwitch, i6.NgSwitchCase, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.FormGroupDirective, i1.FormControlName, i7.BrokerTitleComponent, i6.AsyncPipe, i6.TitleCasePipe],\n    styles: [\".card[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  margin: 0 auto;\\n  border-radius: 15px;\\n  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);\\n  border: none;\\n}\\n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n\\n.text-dark-blue[_ngcontent-%COMP%] {\\n  color: #1e1e2d;\\n}\\n\\n.btn-dark-blue[_ngcontent-%COMP%] {\\n  background-color: #1e1e2d;\\n  color: #ffffff;\\n}\\n\\n.btn-navy[_ngcontent-%COMP%] {\\n  background-color: #1e1e7c;\\n  color: #ffffff;\\n  border: none;\\n}\\n.btn-navy[_ngcontent-%COMP%]:hover {\\n  background-color: #16165a;\\n}\\n.btn-navy[_ngcontent-%COMP%]:disabled {\\n  background-color: #9999c9;\\n}\\n\\n.progress[_ngcontent-%COMP%] {\\n  border-radius: 30px;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  border-radius: 30px;\\n}\\n\\n.cursor-pointer[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 0.75rem 1rem;\\n}\\n\\n.form-select[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 0.75rem 1rem;\\n}\\n\\n.dropdown[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  border: 1px solid #e4e6ef;\\n  background-color: #f5f8fa;\\n  color: #5e6278;\\n  padding: 0.75rem 1rem;\\n}\\n.dropdown[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:hover, .dropdown[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:focus {\\n  background-color: #f5f8fa;\\n  border-color: #e4e6ef;\\n}\\n.dropdown[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]::after {\\n  display: none;\\n}\\n.dropdown[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);\\n  padding: 0.5rem 0;\\n}\\n.dropdown[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1.25rem;\\n  cursor: pointer;\\n}\\n.dropdown[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f8fa;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  border-radius: 30px;\\n  background-color: #3f51b5;\\n  border-color: #3f51b5;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #303f9f;\\n  border-color: #303f9f;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:disabled {\\n  background-color: #9fa8da;\\n  border-color: #9fa8da;\\n}\\n\\n.upload-card-container[_ngcontent-%COMP%]   .upload-card[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  border-radius: 15px;\\n  border: 2px dashed #e4e6ef;\\n  background-color: #f8f9fa;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .upload-card[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  margin-bottom: 0;\\n  padding: 2rem 1rem;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .upload-card[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .upload-card[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  transition: color 0.3s ease;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .upload-card[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .upload-content[_ngcontent-%COMP%]   .upload-title[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .upload-card[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .upload-content[_ngcontent-%COMP%]   .upload-subtitle[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .upload-card[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .upload-content[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.4rem 0.8rem;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .upload-card[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff;\\n  background-color: #f0f8ff;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.15);\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .upload-card[_ngcontent-%COMP%]:hover   label[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .upload-card[_ngcontent-%COMP%]:hover   label[_ngcontent-%COMP%]   .upload-content[_ngcontent-%COMP%]   .upload-title[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .upload-card.has-files[_ngcontent-%COMP%] {\\n  border-color: #28a745;\\n  background-color: #f8fff9;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .upload-card.has-files[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.upload-card-container[_ngcontent-%COMP%]   .upload-card.has-files[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .upload-content[_ngcontent-%COMP%]   .upload-title[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n\\n.custom-checkbox-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n  position: relative;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-input[_ngcontent-%COMP%] {\\n  position: absolute;\\n  opacity: 0;\\n  cursor: pointer;\\n  width: 0;\\n  height: 0;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-input[_ngcontent-%COMP%]:checked    + .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-box[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  border-color: #007bff;\\n  transform: scale(1.05);\\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-input[_ngcontent-%COMP%]:checked    + .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-box[_ngcontent-%COMP%]   .custom-checkbox-icon[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: scale(1) rotate(0deg);\\n  animation: _ngcontent-%COMP%_checkmarkBounce 0.3s ease-out;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-input[_ngcontent-%COMP%]:checked    + .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-text[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-weight: 600;\\n  transform: translateX(2px);\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-input[_ngcontent-%COMP%]:checked    + .custom-checkbox-label[_ngcontent-%COMP%]::before {\\n  opacity: 1;\\n  transform: scale(1);\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-input[_ngcontent-%COMP%]:focus    + .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-box[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25), 0 2px 8px rgba(0, 123, 255, 0.15);\\n  border-color: #007bff;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-input[_ngcontent-%COMP%]:focus    + .custom-checkbox-label[_ngcontent-%COMP%]::before {\\n  opacity: 0.7;\\n  transform: scale(1);\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-input[_ngcontent-%COMP%]:disabled    + .custom-checkbox-label[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-input[_ngcontent-%COMP%]:disabled    + .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-box[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-color: #dee2e6;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  padding: 0.5rem 0;\\n  border-radius: 8px;\\n  width: 100%;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -0.5rem;\\n  right: -0.5rem;\\n  bottom: 0;\\n  background: linear-gradient(90deg, rgba(0, 123, 255, 0.05) 0%, rgba(0, 123, 255, 0.02) 100%);\\n  border-radius: 8px;\\n  opacity: 0;\\n  transform: scale(0.95);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  z-index: -1;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-box[_ngcontent-%COMP%] {\\n  width: 22px;\\n  height: 22px;\\n  border: 2px solid #dee2e6;\\n  border-radius: 6px;\\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 14px;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n  flex-shrink: 0;\\n  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-box[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  width: 0;\\n  height: 0;\\n  background: rgba(0, 123, 255, 0.3);\\n  border-radius: 50%;\\n  transform: translate(-50%, -50%);\\n  transition: all 0.3s ease;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-box[_ngcontent-%COMP%]   .custom-checkbox-icon[_ngcontent-%COMP%] {\\n  color: #fff;\\n  font-size: 13px;\\n  opacity: 0;\\n  transform: scale(0.3) rotate(-45deg);\\n  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);\\n  z-index: 2;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-text[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 15px;\\n  font-weight: 500;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  line-height: 1.4;\\n  margin-bottom: 0.25rem;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-description[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 13px;\\n  line-height: 1.3;\\n  margin-top: 0.125rem;\\n  transition: color 0.3s ease;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n  transform: scale(1);\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]:hover   .custom-checkbox-box[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);\\n  transform: translateY(-1px);\\n  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 123, 255, 0.15);\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]:hover   .custom-checkbox-box[_ngcontent-%COMP%]::after {\\n  width: 100%;\\n  height: 100%;\\n  opacity: 0.1;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]:hover   .custom-checkbox-text[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  transform: translateX(2px);\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]:hover   .custom-checkbox-description[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n.custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]:active   .custom-checkbox-box[_ngcontent-%COMP%] {\\n  transform: scale(0.95);\\n}\\n.custom-checkbox-container.has-error[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-box[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n  background: linear-gradient(135deg, #fff5f5 0%, #ffeaea 100%);\\n  box-shadow: inset 0 1px 3px rgba(220, 53, 69, 0.1);\\n}\\n.custom-checkbox-container.has-error[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-text[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.custom-checkbox-container.has-error[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-description[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  opacity: 0.8;\\n}\\n.custom-checkbox-container.has-error[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]:hover   .custom-checkbox-box[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n  background: linear-gradient(135deg, #fff0f0 0%, #ffe5e5 100%);\\n  box-shadow: inset 0 1px 3px rgba(220, 53, 69, 0.15), 0 2px 8px rgba(220, 53, 69, 0.15);\\n}\\n\\n.checkbox-error-feedback[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-top: 0.5rem;\\n  padding: 0.5rem 0.75rem;\\n  background: linear-gradient(90deg, rgba(220, 53, 69, 0.05) 0%, rgba(220, 53, 69, 0.02) 100%);\\n  border: 1px solid rgba(220, 53, 69, 0.2);\\n  border-radius: 6px;\\n  color: #dc3545;\\n  font-size: 0.875rem;\\n  animation: _ngcontent-%COMP%_errorSlideIn 0.3s ease-out;\\n}\\n.checkbox-error-feedback[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  margin-right: 0.25rem;\\n}\\n.checkbox-error-feedback[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n@keyframes _ngcontent-%COMP%_errorSlideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_checkmarkBounce {\\n  0% {\\n    transform: scale(0.3) rotate(-45deg);\\n    opacity: 0;\\n  }\\n  50% {\\n    transform: scale(1.2) rotate(0deg);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(1) rotate(0deg);\\n    opacity: 1;\\n  }\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-controls[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  padding: 0.75rem 1rem;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-controls[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 123, 255, 0.05);\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-controls[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  margin-top: 0.125rem;\\n  border: 2px solid #dee2e6;\\n  border-radius: 4px;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-controls[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%]:checked {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  border-color: #007bff;\\n  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-controls[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%]:indeterminate {\\n  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);\\n  border-color: #6c757d;\\n  box-shadow: 0 2px 6px rgba(108, 117, 125, 0.3);\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-controls[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-controls[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff;\\n  transform: scale(1.05);\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-controls[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #007bff;\\n  margin-left: 0.5rem;\\n  cursor: pointer;\\n  transition: color 0.2s ease;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-controls[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-options-container[_ngcontent-%COMP%]   .multiselect-option[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  border-radius: 6px;\\n  margin: 0.125rem 0.5rem;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-options-container[_ngcontent-%COMP%]   .multiselect-option[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(90deg, rgba(0, 123, 255, 0.08) 0%, rgba(0, 123, 255, 0.03) 100%);\\n  transform: translateX(2px);\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-options-container[_ngcontent-%COMP%]   .multiselect-option[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  padding: 0.6rem 0.75rem;\\n  border-radius: 6px;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-options-container[_ngcontent-%COMP%]   .multiselect-option[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  width: 17px;\\n  height: 17px;\\n  margin-top: 0.125rem;\\n  border: 2px solid #dee2e6;\\n  border-radius: 4px;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-options-container[_ngcontent-%COMP%]   .multiselect-option[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%]:checked {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  border-color: #007bff;\\n  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.25);\\n  transform: scale(1.05);\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-options-container[_ngcontent-%COMP%]   .multiselect-option[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-options-container[_ngcontent-%COMP%]   .multiselect-option[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff;\\n  background-color: rgba(0, 123, 255, 0.05);\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-options-container[_ngcontent-%COMP%]   .multiselect-option[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .multiselect-option-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  margin-left: 0.6rem;\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: all 0.2s ease;\\n  line-height: 1.4;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-options-container[_ngcontent-%COMP%]   .multiselect-option[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .multiselect-option-label.fw-bold[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-weight: 600;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .multiselect-options-container[_ngcontent-%COMP%]   .multiselect-option[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%]   .multiselect-option-label[_ngcontent-%COMP%]:hover {\\n  color: #007bff;\\n  transform: translateX(1px);\\n}\\n\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-button[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  border: 1px solid #e4e6ef;\\n  background-color: #f5f8fa;\\n  color: #5e6278;\\n  padding: 0.75rem 1rem;\\n  transition: all 0.2s ease;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-button[_ngcontent-%COMP%]:hover, .enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-button[_ngcontent-%COMP%]:focus {\\n  background-color: #f5f8fa;\\n  border-color: #e4e6ef;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-button.has-selections[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  background-color: #f0f8ff;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-button.has-selections[_ngcontent-%COMP%]   .multiselect-text[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-weight: 500;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-button[_ngcontent-%COMP%]::after {\\n  display: none;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-button[_ngcontent-%COMP%]   .multiselect-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  min-width: 0;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-button[_ngcontent-%COMP%]   .multiselect-display[_ngcontent-%COMP%]   .multiselect-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-overflow: ellipsis;\\n  overflow: hidden;\\n  white-space: nowrap;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-button[_ngcontent-%COMP%]   .multiselect-display[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-button[_ngcontent-%COMP%]   .multiselect-icon[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n  margin-left: 0.5rem;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-button[aria-expanded=true][_ngcontent-%COMP%]   .multiselect-icon[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-menu[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);\\n  padding: 0;\\n  border: 1px solid #e4e6ef;\\n  min-width: 100%;\\n  max-width: none;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-menu[_ngcontent-%COMP%]   .multiselect-search-container[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-bottom: 1px solid #e4e6ef;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-menu[_ngcontent-%COMP%]   .multiselect-search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-text[_ngcontent-%COMP%] {\\n  border-radius: 6px 0 0 6px;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-menu[_ngcontent-%COMP%]   .multiselect-search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-radius: 0 6px 6px 0;\\n  border: none;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-menu[_ngcontent-%COMP%]   .multiselect-search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: transparent;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-menu[_ngcontent-%COMP%]   .multiselect-search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 0 6px 6px 0;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-menu[_ngcontent-%COMP%]   .multiselect-footer[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-top: 1px solid #e4e6ef;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-menu[_ngcontent-%COMP%]   .multiselect-footer[_ngcontent-%COMP%]   .selected-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.25rem;\\n  max-width: 200px;\\n  overflow: hidden;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-menu[_ngcontent-%COMP%]   .multiselect-footer[_ngcontent-%COMP%]   .selected-tags[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  max-width: 120px;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-menu[_ngcontent-%COMP%]   .multiselect-footer[_ngcontent-%COMP%]   .selected-tags[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]:nth-child(n+4) {\\n  display: none;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-menu[_ngcontent-%COMP%]   .multiselect-footer[_ngcontent-%COMP%]   .selected-tags[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%] {\\n  margin-left: 0.25rem;\\n  padding: 0;\\n  width: 0.6em;\\n  height: 0.6em;\\n  opacity: 0.7;\\n}\\n.enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-menu[_ngcontent-%COMP%]   .multiselect-footer[_ngcontent-%COMP%]   .selected-tags[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n\\n.enhanced-multiselect[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideDown 0.2s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@media (max-width: 576px) {\\n  .enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-menu[_ngcontent-%COMP%]   .multiselect-footer[_ngcontent-%COMP%]   .selected-tags[_ngcontent-%COMP%] {\\n    max-width: 150px;\\n  }\\n  .enhanced-multiselect[_ngcontent-%COMP%]   .enhanced-multiselect-menu[_ngcontent-%COMP%]   .multiselect-footer[_ngcontent-%COMP%]   .selected-tags[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n    max-width: 80px;\\n    font-size: 0.65em;\\n  }\\n  .custom-checkbox-container[_ngcontent-%COMP%] {\\n    margin-bottom: 1.25rem;\\n  }\\n  .custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%] {\\n    padding: 0.375rem 0;\\n  }\\n  .custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-box[_ngcontent-%COMP%] {\\n    width: 20px;\\n    height: 20px;\\n    margin-right: 12px;\\n  }\\n  .custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-box[_ngcontent-%COMP%]   .custom-checkbox-icon[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-text[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-description[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .checkbox-error-feedback[_ngcontent-%COMP%] {\\n    padding: 0.375rem 0.5rem;\\n    font-size: 0.8125rem;\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  .custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%] {\\n    transition: none;\\n  }\\n  .custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]::before {\\n    transition: opacity 0.2s ease;\\n  }\\n  .custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-box[_ngcontent-%COMP%] {\\n    transition: border-color 0.2s ease, background 0.2s ease;\\n  }\\n  .custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-box[_ngcontent-%COMP%]::after {\\n    transition: none;\\n  }\\n  .custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-box[_ngcontent-%COMP%]   .custom-checkbox-icon[_ngcontent-%COMP%] {\\n    transition: opacity 0.2s ease;\\n  }\\n  .custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-text[_ngcontent-%COMP%], \\n   .custom-checkbox-container[_ngcontent-%COMP%]   .custom-checkbox-label[_ngcontent-%COMP%]   .custom-checkbox-description[_ngcontent-%COMP%] {\\n    transition: color 0.2s ease;\\n  }\\n  @keyframes checkmarkBounce {\\n    to {\\n      transform: scale(1) rotate(0deg);\\n      opacity: 1;\\n    }\\n  }\\n  @keyframes errorSlideIn {\\n    to {\\n      opacity: 1;\\n      transform: translateY(0);\\n    }\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "BehaviorSubject", "fork<PERSON><PERSON>n", "of", "catchError", "map", "<PERSON><PERSON>", "STEPPER_CONFIG", "SPECIALIZATION_SCOPE_OPTIONS", "TYPE_OPTIONS", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "StepperModalComponent_div_17_div_11_div_4_p_11_span_3_Template", "ɵɵadvance", "ɵɵtextInterpolate1", "message_r1", "ɵɵproperty", "last_r2", "StepperModalComponent_div_17_div_11_div_4_p_11_Template", "ɵɵclassProp", "ɵɵtextInterpolate", "ɵɵpipeBind1", "error_r3", "field", "messages", "StepperModalComponent_div_17_div_11_div_4_Template", "stepError_r4", "errors", "StepperModalComponent_div_17_div_11_Template", "ctx_r4", "validationErrors", "length", "ɵɵlistener", "StepperModalComponent_div_19_li_10_Template_a_click_1_listener", "tmp_5_0", "scope_r7", "ɵɵrestoreView", "_r6", "$implicit", "ɵɵnextContext", "select", "getCurrentForm", "value", "ɵɵresetView", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "key", "StepperModalComponent_div_19_li_21_Template_a_click_1_listener", "type_r9", "_r8", "StepperModalComponent_div_19_li_32_Template_a_click_1_listener", "unitType_r11", "_r10", "StepperModalComponent_div_19_li_10_Template", "StepperModalComponent_div_19_div_11_Template", "StepperModalComponent_div_19_li_21_Template", "StepperModalComponent_div_19_div_22_Template", "StepperModalComponent_div_19_li_32_Template", "StepperModalComponent_div_19_div_33_Template", "getText", "specializationScopeOptions", "tmp_1_0", "tmp_3_0", "touched", "invalid", "getFilteredTypeOptions", "tmp_4_0", "tmp_6_0", "getFilteredUnitTypeOptions", "tmp_7_0", "tmp_9_0", "StepperModalComponent_div_20_li_10_Template_a_click_1_listener", "city_r13", "_r12", "onSelectChange", "StepperModalComponent_div_20_li_23_Template_a_click_1_listener", "area_r15", "_r14", "StepperModalComponent_div_20_li_35_Template_a_click_1_listener", "subArea_r17", "_r16", "StepperModalComponent_div_20_div_37_div_11_Template", "tmp_2_0", "StepperModalComponent_div_20_div_38_div_11_Template", "StepperModalComponent_div_20_div_39_div_5_Template", "ɵɵpureFunction1", "_c0", "StepperModalComponent_div_20_div_40_div_5_Template", "ɵɵelementContainerStart", "StepperModalComponent_div_20_div_41_div_4_ng_container_1_Template", "StepperModalComponent_div_20_div_41_div_4_Template", "StepperModalComponent_div_20_div_42_div_4_Template", "StepperModalComponent_div_20_div_43_div_4_Template", "input_r18", "name", "label", "StepperModalComponent_div_20_div_44_ng_container_1_div_6_small_8_Template", "StepperModalComponent_div_20_div_44_ng_container_1_div_6_small_9_Template", "StepperModalComponent_div_20_div_44_ng_container_1_div_6_small_10_Template", "StepperModalComponent_div_20_div_44_ng_container_1_div_6_small_11_Template", "StepperModalComponent_div_20_div_44_ng_container_1_div_6_div_12_Template", "tmp_16_0", "StepperModalComponent_div_20_div_44_ng_container_1_div_10_li_6_Template_a_click_1_listener", "option_r20", "_r19", "StepperModalComponent_div_20_div_44_ng_container_1_div_10_li_6_Template", "options", "ɵɵpureFunction0", "_c2", "_c3", "ɵɵtextInterpolate2", "min", "StepperModalComponent_div_20_div_44_ng_container_1_div_12_ng_container_1_Template", "StepperModalComponent_div_20_div_44_ng_container_1_div_12_ng_container_2_Template", "StepperModalComponent_div_20_div_44_ng_container_1_div_12_ng_container_3_Template", "StepperModalComponent_div_20_div_44_ng_container_1_span_4_Template", "StepperModalComponent_div_20_div_44_ng_container_1_div_6_Template", "StepperModalComponent_div_20_div_44_ng_container_1_input_7_Template", "StepperModalComponent_div_20_div_44_ng_container_1_input_8_Template", "StepperModalComponent_div_20_div_44_ng_container_1_input_9_Template", "StepperModalComponent_div_20_div_44_ng_container_1_div_10_Template", "StepperModalComponent_div_20_div_44_ng_container_1_textarea_11_Template", "StepperModalComponent_div_20_div_44_ng_container_1_div_12_Template", "isInputRequired", "type", "tmp_13_0", "StepperModalComponent_div_20_div_44_ng_container_1_Template", "_c1", "includes", "StepperModalComponent_div_20_li_10_Template", "StepperModalComponent_div_20_div_12_Template", "StepperModalComponent_div_20_div_13_Template", "StepperModalComponent_div_20_li_23_Template", "StepperModalComponent_div_20_div_25_Template", "StepperModalComponent_div_20_li_35_Template", "StepperModalComponent_div_20_div_37_Template", "StepperModalComponent_div_20_div_38_Template", "StepperModalComponent_div_20_div_39_Template", "StepperModalComponent_div_20_div_40_Template", "StepperModalComponent_div_20_div_41_Template", "StepperModalComponent_div_20_div_42_Template", "StepperModalComponent_div_20_div_43_Template", "StepperModalComponent_div_20_div_44_Template", "isLoadingCities", "selectedCityName", "cities$", "selectedCityId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "areas$", "selectedAreaId", "selectedSubAreaName", "subAreas$", "getInsideCompoundPrivilege", "isClient", "getSellInsideCompoundInputs", "getRentOutInsideCompoundInputs", "getRentOutsideCompoundInputs", "getSellOutsideCompoundInputs", "currentInputs", "trackByInputName", "input_r21", "StepperModalComponent_div_21_div_1_div_6_small_8_Template", "StepperModalComponent_div_21_div_1_div_6_small_9_Template", "StepperModalComponent_div_21_div_1_div_6_small_10_Template", "StepperModalComponent_div_21_div_1_div_6_div_11_Template", "tmp_14_0", "StepperModalComponent_div_21_div_1_div_9_li_6_Template_a_click_1_listener", "option_r23", "_r22", "StepperModalComponent_div_21_div_1_div_9_li_6_Template", "getSelectedCount", "StepperModalComponent_div_21_div_1_div_10_div_17_Template_input_change_2_listener", "option_r26", "_r25", "toggleAccessory", "toggleOtherExpense", "toggleMultiSelect", "isAccessorySelected", "isOtherExpenseSelected", "isMultiSelectOptionSelected", "StepperModalComponent_div_21_div_1_div_10_span_6_Template", "StepperModalComponent_div_21_div_1_div_10_Template_input_change_12_listener", "_r24", "toggleSelectAll", "StepperModalComponent_div_21_div_1_div_10_div_17_Template", "StepperModalComponent_div_21_div_1_div_10_div_18_Template", "getSelectedAccessoriesText", "getSelectedOtherExpensesText", "getSelectedText", "_c4", "areAllOptionsSelected", "areSomeOptionsSelected", "getFilteredOptions", "getFileCount", "StepperModalComponent_div_21_div_1_div_11_p_8_Template", "StepperModalComponent_div_21_div_1_div_11_p_9_Template", "StepperModalComponent_div_21_div_1_div_11_span_10_Template", "StepperModalComponent_div_21_div_1_div_11_Template_input_change_11_listener", "$event", "_r27", "onFileChange", "toLowerCase", "StepperModalComponent_div_21_div_1_div_13_div_1_Template", "StepperModalComponent_div_21_div_1_div_13_div_2_Template", "StepperModalComponent_div_21_div_1_div_13_div_3_Template", "StepperModalComponent_div_21_div_1_span_4_Template", "StepperModalComponent_div_21_div_1_div_6_Template", "StepperModalComponent_div_21_div_1_input_7_Template", "StepperModalComponent_div_21_div_1_input_8_Template", "StepperModalComponent_div_21_div_1_div_9_Template", "StepperModalComponent_div_21_div_1_div_10_Template", "StepperModalComponent_div_21_div_1_div_11_Template", "StepperModalComponent_div_21_div_1_textarea_12_Template", "StepperModalComponent_div_21_div_1_div_13_Template", "StepperModalComponent_div_21_div_1_Template", "getStep4InfoMessage", "StepperModalComponent_button_24_Template_button_click_0_listener", "_r28", "prevStep", "StepperModalComponent_button_26_Template_button_click_0_listener", "_r29", "nextStep", "isCurrentFormValid", "StepperModalComponent_button_27_Template_button_click_0_listener", "_r30", "submitForm", "StepperModalComponent_button_27_span_1_Template", "StepperModalComponent_button_27_span_2_Template", "isSubmitting", "StepperModalComponent", "fb", "createRequestService", "propertyService", "cdr", "router", "stepperInputConfigService", "totalSteps", "TOTAL_STEPS", "currentStep", "INITIAL_STEP", "userId", "userRole", "stepForms", "showErrorList", "citiesSubject", "areasSubject", "subAreasSubject", "asObservable", "typeOptions", "unitTypeOptions", "fieldToStepMap", "<PERSON><PERSON><PERSON><PERSON>", "STEP_NAMES", "searchQueries", "constructor", "ngOnInit", "userJson", "localStorage", "getItem", "user", "JSON", "parse", "id", "role", "initForms", "loadInitialData", "loadStepInputs", "Array", "fill", "group", "specializationScope", "required", "unitType", "cities", "getCities", "pipe", "response", "data", "console", "log", "city", "name_en", "error", "fire", "unitTypes", "getUnitTypes", "Object", "entries", "subscribe", "next", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "getInputsForKey", "getConfigKey", "formControls", "reduce", "acc", "input", "validators", "addControl", "control", "getPurchaseOutsideCompoundInputs", "scope", "targetScopes", "typeScopes", "getRentInOutsideCompoundInputs", "getMallPrivilege", "mallUnitTypes", "hasTargetType", "getUnitAndBuildingNumber", "getVillageName", "targetUnitTypes", "find", "i", "accessoryValue", "expenseValue", "onAllAccessoriesChange", "event", "isChecked", "target", "checked", "updatedValues", "opt", "patchValue", "otherAccessories", "fieldName", "currentV<PERSON>ues", "selectedOptions", "filter", "item", "getSearch<PERSON>uery", "updateSearchQuery", "clearSearch", "searchQuery", "option", "visibleOptions", "getOptionsForField", "every", "some", "newValues", "Set", "clearAllSelections", "getSelectedOptions", "inp", "form", "updateValueAndValidity", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectCity", "selectArea", "selectSubArea", "fileValidator", "file", "size", "maxSize", "allowedTypes", "invalidType", "fileArrayValidator", "files", "isArray", "notArray", "getOutsideCompoundPrivilege", "cityId", "cityName", "areaId", "subAreaId", "<PERSON><PERSON><PERSON><PERSON>", "areas", "area", "areaName", "getSubAreas", "subAreas", "subArea", "subAreaName", "step1<PERSON><PERSON><PERSON>", "t", "valid", "hasStepInputs", "validator", "toString", "config<PERSON><PERSON>", "index", "navigateToErrorStep", "<PERSON><PERSON><PERSON><PERSON>", "clearValidationErrors", "getTotalErrorCount", "total", "step<PERSON><PERSON>r", "navigateToFirstErrorStep", "firstErrorStep", "Math", "step", "getCurrentFormErrors", "keys", "controls", "for<PERSON>ach", "controlErrors", "<PERSON><PERSON><PERSON>", "push", "join", "from", "formData", "FormData", "append", "step2V<PERSON><PERSON>", "slice", "undefined", "fileFields", "isMultiple", "File", "createRequest", "res", "then", "navigate", "handleSubmissionError", "cleanField", "replace", "stepGroup", "v", "<PERSON><PERSON><PERSON>", "message", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "createrequestservice", "i3", "PropertyService", "ChangeDetectorRef", "i4", "Router", "i5", "StepperInputConfigService", "selectors", "decls", "vars", "consts", "template", "StepperModalComponent_Template", "rf", "ctx", "StepperModalComponent_div_17_Template", "StepperModalComponent_div_19_Template", "StepperModalComponent_div_20_Template", "StepperModalComponent_div_21_Template", "StepperModalComponent_div_22_Template", "StepperModalComponent_button_24_Template", "StepperModalComponent_div_25_Template", "StepperModalComponent_button_26_Template", "StepperModalComponent_button_27_Template", "ɵɵstyleProp"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\shared\\stepper-modal\\stepper-modal.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\shared\\stepper-modal\\stepper-modal.component.html"], "sourcesContent": ["import { Component, OnInit, ChangeDetectorRef } from '@angular/core';\r\nimport { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';\r\nimport { createrequestservice } from '../../services/createrequest.service';\r\nimport { PropertyService } from '../../services/property.service';\r\nimport { Router } from '@angular/router';\r\nimport { BehaviorSubject, forkJoin, Observable, of } from 'rxjs';\r\nimport { catchError, map, tap } from 'rxjs/operators';\r\nimport Swal from 'sweetalert2';\r\nimport {\r\n  STEPPER_CONFIG,\r\n  SPECIALIZATION_SCOPE_OPTIONS,\r\n  TYPE_OPTIONS,\r\n  FILE_VALIDATION_CONFIG,\r\n  OptionItem\r\n} from './stepper-modal.constants';\r\nimport { StepperInputConfigService } from './stepper-input-config.service';\r\n\r\n@Component({\r\n  selector: 'app-stepper-modal',\r\n  templateUrl: './stepper-modal.component.html',\r\n  styleUrls: ['./stepper-modal.component.scss'],\r\n})\r\nexport class StepperModalComponent implements OnInit {\r\n  totalSteps = STEPPER_CONFIG.TOTAL_STEPS;\r\n  currentStep = STEPPER_CONFIG.INITIAL_STEP;\r\n  userId: number;\r\n  userRole: string;\r\n  stepForms: FormGroup[] = [];\r\n  currentInputs: any[] = [];\r\n  validationErrors: any[] = [];\r\n  showErrorList = false;\r\n\r\n  private citiesSubject = new BehaviorSubject<{ key: string; value: number }[]>([]);\r\n  private areasSubject = new BehaviorSubject<{ key: string; value: number }[]>([]);\r\n  private subAreasSubject = new BehaviorSubject<{ key: string; value: number }[]>([]);\r\n\r\n  cities$ = this.citiesSubject.asObservable();\r\n  areas$ = this.areasSubject.asObservable();\r\n  subAreas$ = this.subAreasSubject.asObservable();\r\n\r\n  selectedCityId: number | null = null;\r\n  selectedCityName: string = '';\r\n  selectedAreaId: number | null = null;\r\n  selectedAreaName: string = '';\r\n  selectedSubAreaName: string = '';\r\n  isLoadingCities = false;\r\n  isSubmitting = false;\r\n\r\n  specializationScopeOptions = SPECIALIZATION_SCOPE_OPTIONS;\r\n  typeOptions = TYPE_OPTIONS;\r\n  unitTypeOptions: OptionItem[] = [];\r\n  fieldToStepMap: { [key: string]: number } = {};\r\n  stepNames = STEPPER_CONFIG.STEP_NAMES;\r\n\r\n  // Enhanced multiselect properties\r\n  private searchQueries: { [fieldName: string]: string } = {};\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private createRequestService: createrequestservice,\r\n    private propertyService: PropertyService,\r\n    private cdr: ChangeDetectorRef,\r\n    private router: Router,\r\n    private stepperInputConfigService: StepperInputConfigService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const userJson = localStorage.getItem('currentUser');\r\n    const user = userJson ? JSON.parse(userJson) : null;\r\n    this.userId = user?.id;\r\n    this.userRole = user?.role;\r\n\r\n    this.initForms();\r\n    this.loadInitialData();\r\n    this.loadStepInputs();\r\n  }\r\n\r\n  initForms(): void {\r\n    this.stepForms = Array(this.totalSteps)\r\n      .fill(null)\r\n      .map(() => this.fb.group({}));\r\n    this.stepForms[0] = this.fb.group({\r\n      specializationScope: ['', Validators.required],\r\n      type: ['', Validators.required],\r\n      unitType: ['', Validators.required],\r\n    });\r\n  }\r\n\r\n  loadInitialData(): void {\r\n    this.isLoadingCities = true;\r\n    forkJoin({\r\n      cities: this.propertyService.getCities().pipe(\r\n        map((response: any) => {\r\n          const cities = response.data || response;\r\n          console.log('Cities response:', response);\r\n          return cities.map((city: any) => ({\r\n            key: city.name_en,\r\n            value: city.id,\r\n          }));\r\n        }),\r\n        catchError((error) => {\r\n          console.error('Error fetching cities:', error);\r\n          Swal.fire('Error', 'Failed to fetch cities.', 'error');\r\n          return of([]);\r\n        })\r\n      ),\r\n      unitTypes: this.propertyService.getUnitTypes().pipe(\r\n        map((response: any) => {\r\n          const unitTypes = response.data || response;\r\n          console.log('Unit types response:', response);\r\n          return Object.entries(unitTypes).map(([key, value]) => ({\r\n            key,\r\n            value: value as string,\r\n          }));\r\n        }),\r\n        catchError((error) => {\r\n          console.error('Error fetching unit types:', error);\r\n          Swal.fire('Error', 'Failed to fetch unit types.', 'error');\r\n          return of([]);\r\n        })\r\n      ),\r\n    }).subscribe({\r\n      next: ({ cities, unitTypes }) => {\r\n        this.citiesSubject.next(cities);\r\n        this.unitTypeOptions = unitTypes;\r\n        this.isLoadingCities = false;\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err) => {\r\n        this.isLoadingCities = false;\r\n        console.error('Error loading initial data:', err);\r\n        Swal.fire('Error', 'Failed to load initial data.', 'error');\r\n        this.cdr.markForCheck();\r\n      },\r\n    });\r\n  }\r\n\r\n  loadStepInputs(): void {\r\n    if (this.currentStep > 1) {\r\n      this.currentInputs = this.stepperInputConfigService.getInputsForKey(this.getConfigKey(), this.currentStep, this);\r\n      const formControls = this.currentInputs.reduce((acc: any, input: any) => {\r\n        acc[input.name] = [input.type === 'multiSelect' ? [] : '', input.validators || []];\r\n        return acc;\r\n      }, {});\r\n      this.stepForms[this.currentStep - 1] = this.fb.group(formControls);\r\n      if (this.currentStep === 2) {\r\n        this.stepForms[1].addControl('cityId', this.fb.control('', Validators.required));\r\n        this.stepForms[1].addControl('areaId', this.fb.control('', Validators.required));\r\n        this.stepForms[1].addControl('subAreaId', this.fb.control(''));\r\n        this.stepForms[1].addControl(\r\n          'compoundName',\r\n          this.fb.control('', this.getInsideCompoundPrivilege() ? Validators.required : null)\r\n        );\r\n\r\n        // Add additional fields for outside compound scenarios\r\n        if (!this.getInsideCompoundPrivilege()) {\r\n          // Add detailedAddress and addressLink for rent-out and sell outside compound scenarios\r\n          if (this.getRentOutsideCompoundInputs() || this.getSellOutsideCompoundInputs()) {\r\n            this.stepForms[1].addControl('detailedAddress', this.fb.control('', Validators.required));\r\n            this.stepForms[1].addControl('addressLink', this.fb.control(''));\r\n          }\r\n          // Add locationSuggestion for purchase outside compound scenarios\r\n          if (this.getPurchaseOutsideCompoundInputs()) {\r\n            this.stepForms[1].addControl('locationSuggestions', this.fb.control(''));\r\n          }\r\n          // For rent-in outside compound, locationSuggestions is handled by the configuration service\r\n        }\r\n      }\r\n    } else {\r\n      this.currentInputs = [\r\n        {\r\n          name: 'specializationScope',\r\n          type: 'select',\r\n          label: 'Specialization Scope',\r\n          options: this.specializationScopeOptions,\r\n          validators: [Validators.required],\r\n        },\r\n        {\r\n          name: 'type',\r\n          type: 'select',\r\n          label: 'Type',\r\n          options: this.getFilteredTypeOptions(),\r\n          validators: [Validators.required],\r\n        },\r\n        {\r\n          name: 'unitType',\r\n          type: 'select',\r\n          label: 'Unit Type',\r\n          options: this.getFilteredUnitTypeOptions(),\r\n          validators: [Validators.required],\r\n        },\r\n      ];\r\n    }\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  getSellInsideCompoundInputs(): boolean {\r\n\r\n    const scope = this.stepForms[0].get('specializationScope')?.value;\r\n    const type = this.stepForms[0].get('type')?.value;\r\n    const targetScopes = [\r\n      'resale_inside_compound',\r\n      'primary_inside_compound',\r\n    ];\r\n    const typeScopes = [\r\n      'sell',\r\n      'purchasing',\r\n    ];\r\n\r\n    return targetScopes.includes(scope) && typeScopes.includes(type);\r\n  }\r\n\r\n  getRentOutInsideCompoundInputs(): boolean {\r\n\r\n    const scope = this.stepForms[0].get('specializationScope')?.value;\r\n    const type = this.stepForms[0].get('type')?.value;\r\n    const targetScopes = [\r\n      'rentals_inside_compound',\r\n    ];\r\n    const typeScopes = [\r\n      'rent_out',\r\n    ];\r\n\r\n    return targetScopes.includes(scope) && typeScopes.includes(type);\r\n  }\r\n\r\n  getRentOutsideCompoundInputs(): boolean {\r\n    const scope = this.stepForms[0].get('specializationScope')?.value;\r\n    const type = this.stepForms[0].get('type')?.value;\r\n    const targetScopes = [\r\n      'rentals_outside_compound',\r\n    ];\r\n    const typeScopes = [\r\n      'rent_out',\r\n    ];\r\n\r\n    return targetScopes.includes(scope) && typeScopes.includes(type);\r\n  }\r\n\r\n  getRentInOutsideCompoundInputs(): boolean {\r\n    const scope = this.stepForms[0].get('specializationScope')?.value;\r\n    const type = this.stepForms[0].get('type')?.value;\r\n    const targetScopes = [\r\n      'rentals_outside_compound',\r\n    ];\r\n    const typeScopes = [\r\n      'rent_in',\r\n    ];\r\n\r\n    return targetScopes.includes(scope) && typeScopes.includes(type);\r\n  }\r\n\r\n  getSellOutsideCompoundInputs(): boolean {\r\n    const scope = this.stepForms[0].get('specializationScope')?.value;\r\n    const type = this.stepForms[0].get('type')?.value;\r\n    const targetScopes = [\r\n      'purchase_sell_outside_compound',\r\n    ];\r\n    const typeScopes = [\r\n      'sell',\r\n    ];\r\n\r\n    return targetScopes.includes(scope) && typeScopes.includes(type);\r\n  }\r\n\r\n  getPurchaseOutsideCompoundInputs(): boolean {\r\n    const scope = this.stepForms[0].get('specializationScope')?.value;\r\n    const type = this.stepForms[0].get('type')?.value;\r\n    const targetScopes = [\r\n      'purchase_sell_outside_compound',\r\n    ];\r\n    const typeScopes = [\r\n      'purchasing',\r\n    ];\r\n\r\n    return targetScopes.includes(scope) && typeScopes.includes(type);\r\n  }\r\n\r\n  getMallPrivilege(): boolean {\r\n    const type = this.stepForms[0].get('type')?.value;\r\n    const unitType = this.stepForms[0].get('unitType')?.value;\r\n    const mallUnitTypes = ['administrative_units', 'medical_clinics', 'pharmacies', 'commercial_stores'];\r\n    const hasTargetType = ['sell', 'rent_out'].includes(type);\r\n    return mallUnitTypes.includes(unitType) && hasTargetType && !this.getInsideCompoundPrivilege();\r\n  }\r\n\r\n  getUnitAndBuildingNumber(): boolean {\r\n    const type = this.stepForms[0].get('type')?.value;\r\n    return ['sell', 'rent_out'].includes(type);\r\n  }\r\n\r\n  getVillageName(): boolean {\r\n    const unitType = this.stepForms[0].get('unitType')?.value;\r\n    const targetUnitTypes = ['vacation_villa', 'chalets'];\r\n    return targetUnitTypes.includes(unitType);\r\n  }\r\n\r\n  getSelectedAccessoriesText(): string {\r\n    const input = this.currentInputs.find((i) => i.name === 'otherAccessories');\r\n    if (!input) return '';\r\n    return this.getSelectedText('otherAccessories', input.options);\r\n  }\r\n\r\n  isAccessorySelected(accessoryValue: string): boolean {\r\n    return this.isMultiSelectOptionSelected('otherAccessories', accessoryValue);\r\n  }\r\n\r\n  toggleAccessory(accessoryValue: string): void {\r\n    this.toggleMultiSelect('otherAccessories', accessoryValue);\r\n  }\r\n\r\n  getSelectedOtherExpensesText(): string {\r\n    const input = this.currentInputs.find((i) => i.name === 'otherExpenses');\r\n    if (!input) return '';\r\n    return this.getSelectedText('otherExpenses', input.options);\r\n  }\r\n\r\n  isOtherExpenseSelected(expenseValue: string): boolean {\r\n    return this.isMultiSelectOptionSelected('otherExpenses', expenseValue);\r\n  }\r\n\r\n  toggleOtherExpense(expenseValue: string): void {\r\n    this.toggleMultiSelect('otherExpenses', expenseValue);\r\n  }\r\n\r\n  onAllAccessoriesChange(event: any): void {\r\n    const input = this.currentInputs.find((i) => i.name === 'otherAccessories');\r\n    if (!input) return;\r\n    const isChecked = event.target.checked;\r\n    const updatedValues = isChecked ? input.options.map((opt: any) => opt.value) : [];\r\n    this.stepForms[this.currentStep - 1].patchValue({ otherAccessories: updatedValues });\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n    getSelectedText(fieldName: string, options: any[]): string {\r\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\r\n    const selectedOptions = options.filter((opt) => currentValues.includes(opt.value));\r\n    if (selectedOptions.length === 0) return '';\r\n    if (selectedOptions.length === 1) return selectedOptions[0].key;\r\n    return `${selectedOptions.length} items selected`;\r\n  }\r\n\r\n  isMultiSelectOptionSelected(fieldName: string, value: string): boolean {\r\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\r\n    return currentValues.includes(value);\r\n  }\r\n\r\n  toggleMultiSelect(fieldName: string, value: string): void {\r\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\r\n    let updatedValues;\r\n\r\n    if (currentValues.includes(value)) {\r\n      updatedValues = currentValues.filter((item: string) => item !== value);\r\n    } else {\r\n      updatedValues = [...currentValues, value];\r\n    }\r\n\r\n    this.getCurrentForm().patchValue({ [fieldName]: updatedValues });\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  // Enhanced multiselect methods\r\n  getSelectedCount(fieldName: string): number {\r\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\r\n    return currentValues.length;\r\n  }\r\n\r\n  getSearchQuery(fieldName: string): string {\r\n    return this.searchQueries[fieldName] || '';\r\n  }\r\n\r\n  updateSearchQuery(fieldName: string, event: any): void {\r\n    this.searchQueries[fieldName] = event.target.value;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  clearSearch(fieldName: string): void {\r\n    this.searchQueries[fieldName] = '';\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  getFilteredOptions(fieldName: string, options: any[]): any[] {\r\n    const searchQuery = this.getSearchQuery(fieldName).toLowerCase();\r\n    if (!searchQuery) {\r\n      return options;\r\n    }\r\n    return options.filter(option =>\r\n      option.key.toLowerCase().includes(searchQuery)\r\n    );\r\n  }\r\n\r\n  areAllOptionsSelected(fieldName: string): boolean {\r\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\r\n    const visibleOptions = this.getFilteredOptions(fieldName, this.getOptionsForField(fieldName));\r\n    return visibleOptions.length > 0 && visibleOptions.every(option => currentValues.includes(option.value));\r\n  }\r\n\r\n  areSomeOptionsSelected(fieldName: string): boolean {\r\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\r\n    const visibleOptions = this.getFilteredOptions(fieldName, this.getOptionsForField(fieldName));\r\n    return visibleOptions.some(option => currentValues.includes(option.value));\r\n  }\r\n\r\n  toggleSelectAll(fieldName: string): void {\r\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\r\n    const visibleOptions = this.getFilteredOptions(fieldName, this.getOptionsForField(fieldName));\r\n\r\n    if (this.areAllOptionsSelected(fieldName)) {\r\n      // Deselect all visible options\r\n      const updatedValues = currentValues.filter((value: string) =>\r\n        !visibleOptions.some(option => option.value === value)\r\n      );\r\n      this.getCurrentForm().patchValue({ [fieldName]: updatedValues });\r\n    } else {\r\n      // Select all visible options\r\n      const newValues = visibleOptions.map(option => option.value);\r\n      const updatedValues = [...new Set([...currentValues, ...newValues])];\r\n      this.getCurrentForm().patchValue({ [fieldName]: updatedValues });\r\n    }\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  clearAllSelections(fieldName: string): void {\r\n    this.getCurrentForm().patchValue({ [fieldName]: [] });\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  getSelectedOptions(fieldName: string, options: any[]): any[] {\r\n    const currentValues = this.getCurrentForm().get(fieldName)?.value || [];\r\n    return options.filter(option => currentValues.includes(option.value));\r\n  }\r\n\r\n  private getOptionsForField(fieldName: string): any[] {\r\n    const input = this.currentInputs.find(inp => inp.name === fieldName);\r\n    return input?.options || [];\r\n  }\r\n\r\n  getText(options: { key: string; value: string }[], value: string): string {\r\n    const item = options.find((item) => item.value === value);\r\n    return item ? item.key : '';\r\n  }\r\n\r\n  select(form: FormGroup, field: string, value: string): void {\r\n    form.patchValue({ [field]: value });\r\n    form.get(field)?.markAsTouched();\r\n    form.get(field)?.updateValueAndValidity();\r\n    this.cdr.markForCheck();\r\n\r\n    // Special handling for specialization scope changes\r\n    if (field === 'specializationScope') {\r\n      // Reset type and unitType when scope changes since filtered options may change\r\n      form.patchValue({ type: '', unitType: '' });\r\n      form.get('type')?.markAsUntouched();\r\n      form.get('unitType')?.markAsUntouched();\r\n      // Refresh the step inputs to update the unit type options\r\n      this.loadStepInputs();\r\n    } else if (field === 'type') {\r\n      // Reset unitType when type changes\r\n      form.patchValue({ unitType: '' });\r\n      form.get('unitType')?.markAsUntouched();\r\n    }\r\n  }\r\n\r\n  onSelectChange(fieldName: string, value: number, name: string): void {\r\n    if (fieldName === 'cityId') {\r\n      this.selectCity(value, name);\r\n    } else if (fieldName === 'areaId') {\r\n      this.selectArea(value, name);\r\n    } else if (fieldName === 'subAreaId') {\r\n      this.selectSubArea(value, name);\r\n    }\r\n  }\r\n\r\n  fileValidator(options: { allowedTypes: string[]; maxSize: number }): ValidatorFn {\r\n    return (control: AbstractControl): ValidationErrors | null => {\r\n      const file = control.value;\r\n      if (!file) return null;\r\n\r\n      if (file.size > options.maxSize) {\r\n        return { maxSize: true };\r\n      }\r\n\r\n      if (!options.allowedTypes.includes(file.type)) {\r\n        return { invalidType: true };\r\n      }\r\n\r\n      return null;\r\n    };\r\n  }\r\n\r\n  fileArrayValidator(options: { allowedTypes: string[], maxSize: number }): ValidatorFn {\r\n    return (control: AbstractControl): ValidationErrors | null => {\r\n      const files = control.value;\r\n\r\n      if (!Array.isArray(files)) {\r\n        return { notArray: true };\r\n      }\r\n\r\n      for (const file of files) {\r\n        if (file.size > options.maxSize) {\r\n          return { maxSize: true };\r\n        }\r\n        if (!options.allowedTypes.includes(file.type)) {\r\n          return { invalidType: true };\r\n        }\r\n      }\r\n\r\n      return null;\r\n    };\r\n  }\r\n\r\n  isClient() {\r\n    return this.userRole == 'client';\r\n  }\r\n\r\n  getInsideCompoundPrivilege(): boolean {\r\n    const scope = this.stepForms[0].get('specializationScope')?.value;\r\n    const targetScopes = [\r\n      'primary_inside_compound',\r\n      'resale_inside_compound',\r\n      'rentals_inside_compound',\r\n    ];\r\n    return targetScopes.includes(scope);\r\n  }\r\n\r\n  getOutsideCompoundPrivilege(): boolean {\r\n    const scope = this.stepForms[0].get('specializationScope')?.value;\r\n    const targetScopes = [\r\n      'purchase_sell_outside_compound',\r\n      'rentals_outside_compound',\r\n    ];\r\n    return targetScopes.includes(scope);\r\n  }\r\n\r\n  selectCity(cityId: number, cityName: string): void {\r\n    this.selectedCityId = cityId;\r\n    this.selectedCityName = cityName;\r\n    this.getCurrentForm().patchValue({ cityId, areaId: '', subAreaId: '' });\r\n    this.areasSubject.next([]);\r\n    this.subAreasSubject.next([]);\r\n    this.selectedAreaId = null;\r\n    this.selectedAreaName = '';\r\n    this.selectedSubAreaName = '';\r\n\r\n    this.propertyService\r\n      .getAreas(cityId)\r\n      .pipe(\r\n        map((response: any) => {\r\n          const areas = response.data || response;\r\n          return areas.map((area: any) => ({\r\n            key: area.name_en,\r\n            value: area.id,\r\n          }));\r\n        }),\r\n        catchError((error) => {\r\n          console.error('Error fetching areas:', error);\r\n          Swal.fire('Error', 'Failed to fetch areas.', 'error');\r\n          return of([]);\r\n        })\r\n      )\r\n      .subscribe((areas) => {\r\n        console.log('Fetched areas:', areas);\r\n        this.areasSubject.next(areas);\r\n        this.cdr.markForCheck();\r\n      });\r\n  }\r\n\r\n  selectArea(areaId: number, areaName: string): void {\r\n    this.selectedAreaId = areaId;\r\n    this.selectedAreaName = areaName;\r\n    this.getCurrentForm().patchValue({ areaId, subAreaId: '' });\r\n    this.subAreasSubject.next([]);\r\n    this.selectedSubAreaName = '';\r\n\r\n    this.propertyService\r\n      .getSubAreas(areaId)\r\n      .pipe(\r\n        map((response: any) => {\r\n          const subAreas = response.data || response;\r\n          return subAreas.map((subArea: any) => ({\r\n            key: subArea.name_en,\r\n            value: subArea.id,\r\n          }));\r\n        }),\r\n        catchError((error) => {\r\n          console.error('Error fetching sub-areas:', error);\r\n          Swal.fire('Error', 'Failed to fetch sub-areas.', 'error');\r\n          return of([]);\r\n        })\r\n      )\r\n      .subscribe((subAreas) => {\r\n        this.subAreasSubject.next(subAreas);\r\n        this.cdr.markForCheck();\r\n      });\r\n  }\r\n\r\n  selectSubArea(subAreaId: number, subAreaName: string): void {\r\n    this.selectedSubAreaName = subAreaName;\r\n    this.getCurrentForm().patchValue({ subAreaId });\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  getConfigKey(): string {\r\n    const step1Values = this.stepForms[0].value;\r\n    return `${step1Values.specializationScope}_${step1Values.type}_${step1Values.unitType}`;\r\n  }\r\n\r\n  getFilteredTypeOptions(): OptionItem[] {\r\n    const scope = this.stepForms[0].get('specializationScope')?.value;\r\n    if (scope?.includes('rentals')) {\r\n      return this.typeOptions.filter((t) => ['rent_out', 'rent_in'].includes(t.value));\r\n    } else if (scope?.includes('purchase_sell') || scope?.includes('primary') || scope?.includes('resale')) {\r\n      return this.typeOptions.filter((t) => ['purchasing', 'sell'].includes(t.value));\r\n    }\r\n    return this.typeOptions;\r\n  }\r\n\r\n    getFilteredUnitTypeOptions(): OptionItem[] {\r\n    const scope = this.stepForms[0].get('specializationScope')?.value;\r\n\r\n    // If no scope is selected, return empty array\r\n    if (!scope) {\r\n      return [];\r\n    }\r\n\r\n    // Inside compound unit types (primary and resale)\r\n    if (scope === 'primary_inside_compound' || scope === 'resale_inside_compound') {\r\n      return this.unitTypeOptions.filter(unitType =>\r\n        [\r\n          'apartments',\r\n          'duplexes',\r\n          'studios',\r\n          'penthouses',\r\n          'villas',\r\n          'twin_houses',\r\n          'town_houses',\r\n          'standalone_villas',\r\n          'administrative_units',\r\n          // 'commercial_units',\r\n          'medical_clinics',\r\n          // 'commercial_stores',\r\n          'pharmacies',\r\n          'commercial_administrative_buildings',\r\n          'shops',\r\n        ].includes(unitType.value)\r\n      );\r\n    }\r\n\r\n    //  if (selectedScope === 'purchase_sell_outside_compound' ||\r\n    //     // selectedScope === 'purchase_sell_inside_compound' ||\r\n    //     selectedScope === 'primary_inside_compound' ||\r\n    //     selectedScope === 'resale_inside_compound') {\r\n    //    return this.Type.filter(type =>\r\n    //     type.value === 'sell' || type.value === 'purchasing'\r\n    // Outside compound unit types (purchase-sell)\r\n    if (scope === 'purchase_sell_outside_compound') {\r\n      return this.unitTypeOptions.filter(unitType =>\r\n        [\r\n          'apartments',\r\n          'duplexes',\r\n          'penthouses',\r\n          'studios',\r\n          'basements',\r\n          'roofs',\r\n          'administrative_units',\r\n          'medical_clinics',\r\n          'pharmacies',\r\n          'commercial_stores',\r\n          'standalone_villas',\r\n          'factory_lands',\r\n          'warehouses',\r\n          'residential_buildings',\r\n          'commercial_administrative_buildings'\r\n        ].includes(unitType.value)\r\n      );\r\n    }\r\n\r\n    // Rental unit types (both inside and outside compound)\r\n    if (scope === 'rentals_inside_compound') {\r\n      return this.unitTypeOptions.filter(unitType =>\r\n        [\r\n          'apartments',\r\n          'duplexes',\r\n          'studios',\r\n          'penthouses',\r\n          'villas',\r\n          'town_houses',\r\n          'twin_houses',\r\n          // 'basements',\r\n          // 'roofs',\r\n          // 'residential_buildings',\r\n          'standalone_villas',\r\n          'administrative_units',\r\n          'commercial_stores',\r\n          'medical_clinics',\r\n          'pharmacies',\r\n          'commercial_administrative_buildings'\r\n        ].includes(unitType.value)\r\n      );\r\n    }\r\n\r\n    if (scope === 'rentals_outside_compound') {\r\n      return this.unitTypeOptions.filter(unitType =>\r\n        [\r\n          'apartments',\r\n          'duplexes',\r\n          'studios',\r\n          'penthouses',\r\n          // 'villas',\r\n          // 'town_houses',\r\n          // 'twin_houses',\r\n          'basements',\r\n          'roofs',\r\n          // 'residential_buildings',\r\n          'standalone_villas',\r\n          'administrative_units',\r\n          'commercial_stores',\r\n          'medical_clinics',\r\n          'pharmacies',\r\n          'factory_lands',\r\n          'warehouses',\r\n          'commercial_administrative_buildings'\r\n        ].includes(unitType.value)\r\n      );\r\n    }\r\n\r\n    // For other scopes, return all unit types\r\n    return this.unitTypeOptions;\r\n  }\r\n\r\n  getCurrentForm(): FormGroup {\r\n    return this.stepForms[this.currentStep - 1];\r\n  }\r\n\r\n  nextStep(): void {\r\n    if (this.getCurrentForm().valid && this.currentStep < this.totalSteps) {\r\n      this.currentStep++;\r\n      this.loadStepInputs();\r\n    }\r\n  }\r\n\r\n  prevStep(): void {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n      this.loadStepInputs();\r\n    }\r\n  }\r\n\r\n  // Check if current step has inputs\r\n  hasStepInputs(): boolean {\r\n    return this.currentInputs && this.currentInputs.length > 0;\r\n  }\r\n\r\n  // Check if input is required\r\n  isInputRequired(input: any): boolean {\r\n    return input.validators && input.validators.some((validator: any) =>\r\n      validator === Validators.required ||\r\n      (validator && validator.toString && validator.toString().includes('required'))\r\n    );\r\n  }\r\n\r\n  // Get step 4 info message\r\n  getStep4InfoMessage(): string {\r\n    const configKey = this.getConfigKey();\r\n    if (configKey.includes('rent_in')) {\r\n      return 'No image uploads are required for rent-in requests.';\r\n    }\r\n    return 'No image uploads are available for this property type.';\r\n  }\r\n\r\n  trackByInputName(index: number, input: any): string {\r\n    return input.name;\r\n  }\r\n\r\n  navigateToErrorStep(stepNumber: number): void {\r\n    this.currentStep = stepNumber;\r\n    this.loadStepInputs();\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  clearValidationErrors(): void {\r\n    this.validationErrors = [];\r\n    this.showErrorList = false;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  /**\r\n   * Get total count of all validation errors across all steps\r\n   */\r\n  getTotalErrorCount(): number {\r\n    return this.validationErrors.reduce((total, stepError) => {\r\n      return total + stepError.errors.length;\r\n    }, 0);\r\n  }\r\n\r\n  /**\r\n   * Navigate to the first step that has validation errors\r\n   */\r\n  navigateToFirstErrorStep(): void {\r\n    if (this.validationErrors.length > 0) {\r\n      const firstErrorStep = Math.min(...this.validationErrors.map(error => error.step));\r\n      this.navigateToErrorStep(firstErrorStep);\r\n    }\r\n  }\r\n\r\n  isCurrentFormValid(): boolean {\r\n    return this.getCurrentForm().valid;\r\n  }\r\n\r\n  // Debug method to show current form errors\r\n  getCurrentFormErrors(): string {\r\n    const form = this.getCurrentForm();\r\n    const errors: string[] = [];\r\n\r\n    Object.keys(form.controls).forEach(key => {\r\n      const control = form.get(key);\r\n      if (control && control.invalid) {\r\n        const controlErrors = control.errors;\r\n        if (controlErrors) {\r\n          Object.keys(controlErrors).forEach(errorKey => {\r\n            errors.push(`${key}: ${errorKey}`);\r\n          });\r\n        }\r\n      }\r\n    });\r\n\r\n    return errors.length > 0 ? errors.join(', ') : 'No errors';\r\n  }\r\n\r\n  getFileCount(fieldName: string): number {\r\n    const files = this.getCurrentForm().get(fieldName)?.value;\r\n    return files && Array.isArray(files) ? files.length : 0;\r\n  }\r\n\r\n  onFileChange(event: any, fieldName: string): void {\r\n    if (event.target.files?.length) {\r\n      this.stepForms[this.currentStep - 1].patchValue({\r\n        [fieldName]: Array.from(event.target.files)\r\n      });\r\n      this.cdr.markForCheck();\r\n    }\r\n  }\r\n\r\n  submitForm(): void {\r\n    if (this.stepForms.every((form) => form.valid)) {\r\n      this.isSubmitting = true;\r\n      const formData = new FormData();\r\n      formData.append('userId', this.userId.toString());\r\n\r\n      // Add step 1 values\r\n      const step1Values = this.stepForms[0].value;\r\n      Object.keys(step1Values).forEach((key) => {\r\n        formData.append(key, step1Values[key] || '');\r\n      });\r\n\r\n      // Add location data from step 2\r\n      const step2Values = this.stepForms[1].value;\r\n      if (step2Values.cityId) {\r\n        formData.append('locations[0][city]', step2Values.cityId.toString());\r\n      }\r\n      if (step2Values.areaId) {\r\n        formData.append('locations[0][areas][0][id]', step2Values.areaId.toString());\r\n      }\r\n      if (step2Values.subAreaId) {\r\n        formData.append('locations[0][areas][0][subAreas][0]', step2Values.subAreaId.toString());\r\n      }\r\n\r\n      // Add other form values\r\n      this.stepForms.slice(1).forEach((form) => {\r\n        Object.keys(form.value).forEach((key) => {\r\n          if (['cityId', 'areaId', 'subAreaId'].includes(key)) {\r\n            return;\r\n          }\r\n          const value = form.value[key];\r\n          if (value !== null && value !== undefined) {\r\n            if (Array.isArray(value)) {\r\n              value.forEach((item: any, i: number) =>\r\n                formData.append(`attributes[${key}][${i}]`, item)\r\n              );\r\n            } else if (typeof value === 'boolean') {\r\n              formData.append(`attributes[${key}]`, value ? '1' : '0');\r\n            } else if (value !== '') {\r\n              formData.append(`attributes[${key}]`, value.toString());\r\n            }\r\n          }\r\n        });\r\n      });\r\n\r\n      // Handle file uploads\r\n      const fileFields = ['mainImage', 'galleryImages', 'video', 'unitInMasterPlanImage'];\r\n      fileFields.forEach((field) => {\r\n        const files = this.stepForms[3]?.get(field)?.value;\r\n        if (files && Array.isArray(files) && files.length > 0) {\r\n          const isMultiple = ['galleryImages', 'video'].includes(field);\r\n\r\n          if (isMultiple) {\r\n            files.forEach((file: File, index: number) => {\r\n              if (file instanceof File) {\r\n                formData.append(`attributes[${field}][${index}]`, file);\r\n              }\r\n            });\r\n          } else {\r\n            if (files[0] instanceof File) {\r\n              formData.append(`attributes[${field}]`, files[0]);\r\n            }\r\n          }\r\n        }\r\n      });\r\n\r\n      this.createRequestService.createRequest(formData).subscribe({\r\n        next: (res) => {\r\n          this.isSubmitting = false;\r\n          Swal.fire('Success', 'Request created successfully!', 'success').then(() => {\r\n            this.router.navigate([`/requests/assign-to`, res.data?.id || res.id]);\r\n          });\r\n        },\r\n        error: (err) => {\r\n          this.isSubmitting = false;\r\n          this.handleSubmissionError(err);\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  handleSubmissionError(error: any): void {\r\n    this.validationErrors = [];\r\n    if (error?.error?.errors) {\r\n      Object.keys(error.error.errors).forEach((field) => {\r\n        const cleanField = field.replace(/^attributes\\./g, '').replace(/\\[\\d+\\].*/g, '');\r\n        const step = this.currentInputs.find((input) => input.name === cleanField)?.step || 1;\r\n        let stepGroup = this.validationErrors.find((v) => v.step === step);\r\n        if (!stepGroup) {\r\n          stepGroup = {\r\n            step,\r\n            stepName: this.stepNames[step] || `Step ${step}`,\r\n            errors: []\r\n          };\r\n          this.validationErrors.push(stepGroup);\r\n        }\r\n        stepGroup.errors.push({\r\n          field: cleanField,\r\n          messages: Array.isArray(error.error.errors[field])\r\n            ? error.error.errors[field]\r\n            : [error.error.errors[field]],\r\n        });\r\n      });\r\n      this.showErrorList = true;\r\n      this.cdr.markForCheck();\r\n    } else {\r\n      Swal.fire('Error', error?.error?.message || 'An unknown error occurred.', 'error');\r\n    }\r\n  }\r\n}\r\n", "<div class=\"mb-5 mt-0\">\r\n  <app-broker-title [showCreateButton]=\"false\"></app-broker-title>\r\n</div>\r\n\r\n<div class=\"card rounded-4\">\r\n  <div class=\"card-body p-10\">\r\n    <div class=\"stepper stepper-pills d-flex flex-column\" id=\"hotel_unit_rental_stepper\">\r\n      <!-- Header with Progress Bar -->\r\n      <div class=\"mb-5 text-center\">\r\n        <h2 class=\"fw-bold text-dark-blue mb-2\">{{ stepNames[currentStep] }}</h2>\r\n        <div class=\"d-flex justify-content-center align-items-center mb-2\">\r\n          <span class=\"text-success fw-bold\">Step {{ currentStep }}</span>\r\n          <span class=\"text-muted mx-1\">of</span>\r\n          <span class=\"text-muted\">{{ totalSteps }}</span>\r\n        </div>\r\n        <div class=\"progress h-8px bg-light-success w-75 mx-auto\">\r\n          <div class=\"progress-bar bg-success\" role=\"progressbar\" [style.width]=\"(currentStep / totalSteps) * 100 + '%'\"\r\n            aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Enhanced Error List Display -->\r\n      <div *ngIf=\"showErrorList && validationErrors.length > 0\" class=\"alert alert-danger border-0 shadow-sm mb-5\"\r\n        style=\"border-radius: 12px;\">\r\n        <!-- Error Header -->\r\n        <div class=\"d-flex align-items-center justify-content-between mb-4\">\r\n          <div class=\"d-flex align-items-center\">\r\n            <div class=\"bg-danger rounded-circle d-flex align-items-center justify-content-center me-3\"\r\n              style=\"width: 40px; height: 40px;\">\r\n              <i class=\"fas fa-exclamation-triangle text-white\"></i>\r\n            </div>\r\n            <div>\r\n              <h5 class=\"mb-1 text-danger fw-bold\">Validation Errors Found</h5>\r\n              <p class=\"mb-0 text-muted small\">Please correct the following {{ validationErrors.length }} issue(s)\r\n                before proceeding</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Error Details by Step -->\r\n        <div class=\"error-steps-container\">\r\n          <div *ngFor=\"let stepError of validationErrors; let stepIndex = index\" class=\"error-step-card mb-3\">\r\n            <!-- Step Header -->\r\n            <div class=\"card border-0 shadow-sm\" style=\"border-radius: 8px;\">\r\n\r\n              <!-- Error Details -->\r\n              <div class=\"card-body pt-8\">\r\n                <div class=\"error-list\">\r\n                  <div *ngFor=\"let error of stepError.errors; let errorIndex = index\"\r\n                    class=\"error-item d-flex align-items-start p-3 mb-2 bg-light rounded\" [class.border-start]=\"true\"\r\n                    [class.border-danger]=\"true\" [class.border-4]=\"true\">\r\n                    <!-- Error Icon -->\r\n                    <div class=\"error-icon-container me-3 mt-1\">\r\n                      <i class=\"fas fa-times-circle text-danger\"></i>\r\n                    </div>\r\n                    <!-- Error Content -->\r\n                    <div class=\"error-content flex-grow-1\">\r\n                      <div class=\"d-flex align-items-center mb-1\">\r\n                        <strong class=\"text-dark me-2\">{{ error.field | titlecase }}</strong>\r\n                        <span class=\"badge bg-danger bg-opacity-10 text-danger small\">Required</span>\r\n                      </div>\r\n                      <div class=\"error-messages\">\r\n                        <p class=\"mb-0 text-muted small\" *ngFor=\"let message of error.messages; let last = last\">\r\n                          <i class=\"fas fa-chevron-right me-1\" style=\"font-size: 10px;\"></i>\r\n                          {{ message }}<span *ngIf=\"!last\">;</span>\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Form Content -->\r\n      <form class=\"mx-auto w-100 pt-5 pb-10\" [formGroup]=\"getCurrentForm()\">\r\n        <!-- Step 1: Basic Request Settings -->\r\n        <div *ngIf=\"currentStep === 1\">\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Specialization Scope</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"specializationScopeDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  getText(specializationScopeOptions, getCurrentForm().get('specializationScope')?.value) ||\r\n                  'Select Specialization Scope'\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"specializationScopeDropdown\">\r\n                <li *ngFor=\"let scope of specializationScopeOptions\">\r\n                  <a class=\"dropdown-item\" href=\"javascript:void(0)\"\r\n                    (click)=\"select(getCurrentForm(), 'specializationScope', scope.value); getCurrentForm().get('specializationScope')?.markAsTouched()\">{{\r\n                    scope.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n            <div\r\n              *ngIf=\"getCurrentForm().get('specializationScope')?.touched && getCurrentForm().get('specializationScope')?.invalid\"\r\n              class=\"invalid-feedback d-block\">\r\n              Specialization Scope is required.\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Type</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"typeDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  getText(getFilteredTypeOptions(), getCurrentForm().get('type')?.value) ||\r\n                  'Select Type'\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"typeDropdown\">\r\n                <li *ngFor=\"let type of getFilteredTypeOptions()\">\r\n                  <a class=\"dropdown-item\" href=\"javascript:void(0)\"\r\n                    (click)=\"select(getCurrentForm(), 'type', type.value); getCurrentForm().get('type')?.markAsTouched()\">{{\r\n                    type.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n            <div *ngIf=\"getCurrentForm().get('type')?.touched && getCurrentForm().get('type')?.invalid\"\r\n              class=\"invalid-feedback d-block\">\r\n              Type is required.\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Unit Type</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"unitTypeDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                <span>{{\r\n                  getText(getFilteredUnitTypeOptions(), getCurrentForm().get('unitType')?.value) ||\r\n                  'Select Unit Type'\r\n                  }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"unitTypeDropdown\">\r\n                <li *ngFor=\"let unitType of getFilteredUnitTypeOptions()\">\r\n                  <a class=\"dropdown-item\" href=\"javascript:void(0)\"\r\n                    (click)=\"select(getCurrentForm(), 'unitType', unitType.value); getCurrentForm().get('unitType')?.markAsTouched()\">{{\r\n                    unitType.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n            <div *ngIf=\"getCurrentForm().get('unitType')?.touched && getCurrentForm().get('unitType')?.invalid\"\r\n              class=\"invalid-feedback d-block\">\r\n              Unit Type is required.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Step 2: Location Information -->\r\n        <div *ngIf=\"currentStep === 2\">\r\n          <!-- City Dropdown -->\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">City</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"cityIdDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\"\r\n                [disabled]=\"isLoadingCities\">\r\n                <span>{{ selectedCityName || 'Select City' }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"cityIdDropdown\"\r\n                style=\"max-height: 200px; overflow-y: auto;\">\r\n                <li *ngFor=\"let city of cities$ | async\">\r\n                  <a class=\"dropdown-item\" (click)=\"onSelectChange('cityId', city.value, city.key)\">{{ city.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n            <div *ngIf=\"getCurrentForm().get('cityId')?.touched && getCurrentForm().get('cityId')?.invalid\"\r\n              class=\"invalid-feedback d-block\">\r\n              City is required.\r\n            </div>\r\n            <div *ngIf=\"isLoadingCities\" class=\"text-muted mt-1\">\r\n              <i class=\"fas fa-spinner fa-spin me-1\"></i>\r\n              Loading cities...\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Area Dropdown -->\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Area</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"areaIdDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\"\r\n                [disabled]=\"!selectedCityId\">\r\n                <span>{{ selectedAreaName || 'Select Area' }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"areaIdDropdown\"\r\n                style=\"max-height: 200px; overflow-y: auto;\">\r\n                <li *ngFor=\"let area of areas$ | async\">\r\n                  <a class=\"dropdown-item\" (click)=\"onSelectChange('areaId', area.value, area.key)\">{{ area.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n            <div *ngIf=\"getCurrentForm().get('areaId')?.touched && getCurrentForm().get('areaId')?.invalid\"\r\n              class=\"invalid-feedback d-block\">\r\n              Area is required.\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Sub-Area Dropdown -->\r\n          <div class=\"mb-10\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Sub Area</label>\r\n            <div class=\"dropdown\">\r\n              <button\r\n                class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                type=\"button\" id=\"subAreaIdDropdown\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\"\r\n                [disabled]=\"!selectedAreaId\">\r\n                <span>{{ selectedSubAreaName || 'Select Sub Area' }}</span>\r\n                <i class=\"fas fa-chevron-down\"></i>\r\n              </button>\r\n              <ul class=\"dropdown-menu w-100\" aria-labelledby=\"subAreaIdDropdown\"\r\n                style=\"max-height: 200px; overflow-y: auto;\">\r\n                <li *ngFor=\"let subArea of subAreas$ | async\">\r\n                  <a class=\"dropdown-item\" (click)=\"onSelectChange('subAreaId', subArea.value, subArea.key)\">{{\r\n                    subArea.key }}</a>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Enhanced Location Suggestion for Inside Compound -->\r\n          <div *ngIf=\"getInsideCompoundPrivilege() && isClient()\" class=\"mb-10\">\r\n            <div class=\"custom-checkbox-container\"\r\n              [class.has-error]=\"getCurrentForm().get('locationSuggestions')?.touched && getCurrentForm().get('locationSuggestions')?.invalid\">\r\n              <input class=\"custom-checkbox-input\" type=\"checkbox\" id=\"locationSuggestions\"\r\n                formControlName=\"locationSuggestions\" aria-describedby=\"locationSuggestions_help\"\r\n                [attr.aria-invalid]=\"getCurrentForm().get('locationSuggestions')?.touched && getCurrentForm().get('locationSuggestions')?.invalid ? 'true' : 'false'\" />\r\n              <label class=\"custom-checkbox-label\" for=\"locationSuggestions\">\r\n                <div class=\"custom-checkbox-box\">\r\n                  <i class=\"fas fa-check custom-checkbox-icon\"></i>\r\n                </div>\r\n                <div class=\"custom-checkbox-content\">\r\n                  <span class=\"custom-checkbox-text\">Location Suggestions</span>\r\n                  <small class=\"custom-checkbox-description\">\r\n                    Get personalized location recommendations based on your preferences\r\n                  </small>\r\n                </div>\r\n              </label>\r\n              <!-- Enhanced validation feedback -->\r\n              <div\r\n                *ngIf=\"getCurrentForm().get('locationSuggestions')?.touched && getCurrentForm().get('locationSuggestions')?.invalid\"\r\n                class=\"checkbox-error-feedback\" id=\"locationSuggestions_help\" role=\"alert\">\r\n                <i class=\"fas fa-exclamation-circle me-1\"></i>\r\n                <span>Location Suggestions selection is required to proceed</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Enhanced Location Suggestion for Outside Compound -->\r\n          <div *ngIf=\"!getInsideCompoundPrivilege() && isClient() && getCurrentForm().get('locationSuggestions')\"\r\n            class=\"mb-10\">\r\n            <div class=\"custom-checkbox-container\"\r\n              [class.has-error]=\"getCurrentForm().get('locationSuggestions')?.touched && getCurrentForm().get('locationSuggestions')?.invalid\">\r\n              <input class=\"custom-checkbox-input\" type=\"checkbox\" id=\"locationSuggestionsOutside\"\r\n                formControlName=\"locationSuggestions\" aria-describedby=\"locationSuggestionsOutside_help\"\r\n                [attr.aria-invalid]=\"getCurrentForm().get('locationSuggestions')?.touched && getCurrentForm().get('locationSuggestions')?.invalid ? 'true' : 'false'\" />\r\n              <label class=\"custom-checkbox-label\" for=\"locationSuggestionsOutside\">\r\n                <div class=\"custom-checkbox-box\">\r\n                  <i class=\"fas fa-check custom-checkbox-icon\"></i>\r\n                </div>\r\n                <div class=\"custom-checkbox-content\">\r\n                  <span class=\"custom-checkbox-text\">Location Suggestions</span>\r\n                  <small class=\"custom-checkbox-description\">\r\n                    Get personalized location recommendations based on your preferences\r\n                  </small>\r\n                </div>\r\n              </label>\r\n              <!-- Enhanced validation feedback -->\r\n              <div\r\n                *ngIf=\"getCurrentForm().get('locationSuggestions')?.touched && getCurrentForm().get('locationSuggestions')?.invalid\"\r\n                class=\"checkbox-error-feedback\" id=\"locationSuggestionsOutside_help\" role=\"alert\">\r\n                <i class=\"fas fa-exclamation-circle me-1\"></i>\r\n                <span>Location Suggestions selection is required to proceed</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n\r\n\r\n          <!-- Compound Name -->\r\n          <div class=\"mb-10\" *ngIf=\"getInsideCompoundPrivilege()\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Compound Name\r\n              <span class=\"required\"></span>\r\n            </label>\r\n            <input type=\"text\" class=\"form-control\" formControlName=\"compoundName\" placeholder=\"Enter Compound Name\"\r\n              [ngClass]=\"{\r\n                'is-invalid': getCurrentForm().get('compoundName')?.touched && getCurrentForm().get('compoundName')?.invalid\r\n              }\" />\r\n            <div *ngIf=\"getCurrentForm().get('compoundName')?.touched && getCurrentForm().get('compoundName')?.invalid\"\r\n              class=\"invalid-feedback d-block\">\r\n              Compound Name is required.\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Detailed Address -->\r\n          <div class=\"mb-10\"\r\n            *ngIf=\"getSellInsideCompoundInputs() || getRentOutInsideCompoundInputs() || getRentOutsideCompoundInputs() || getSellOutsideCompoundInputs()\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Detailed Address\r\n              <span class=\"required\"></span>\r\n            </label>\r\n            <input type=\"text\" class=\"form-control\" formControlName=\"detailedAddress\"\r\n              placeholder=\"Enter Detailed Address\" [ngClass]=\"{\r\n                'is-invalid': getCurrentForm().get('detailedAddress')?.touched && getCurrentForm().get('detailedAddress')?.invalid\r\n              }\" />\r\n            <div\r\n              *ngIf=\"getCurrentForm().get('detailedAddress')?.touched && getCurrentForm().get('detailedAddress')?.invalid\"\r\n              class=\"invalid-feedback d-block\">\r\n              Detailed Address is required.\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Detailed Address Link -->\r\n          <div class=\"mb-10\"\r\n            *ngIf=\"getSellInsideCompoundInputs() || getRentOutInsideCompoundInputs() || getRentOutsideCompoundInputs() || getSellOutsideCompoundInputs()\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Detailed Address Link</label>\r\n            <input type=\"url\" class=\"form-control\" formControlName=\"addressLink\"\r\n              placeholder=\"Enter Address Link (https://...)\" [ngClass]=\"{\r\n                'is-invalid': getCurrentForm().get('addressLink')?.touched && getCurrentForm().get('addressLink')?.invalid\r\n              }\" />\r\n            <div *ngIf=\"getCurrentForm().get('addressLink')?.touched && getCurrentForm().get('addressLink')?.invalid\"\r\n              class=\"invalid-feedback d-block\">\r\n              <ng-container *ngIf=\"getCurrentForm().get('addressLink')?.errors?.['pattern']\">\r\n                Please enter a valid URL.\r\n              </ng-container>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Project Management -->\r\n          <div class=\"mb-10\" *ngIf=\"getSellInsideCompoundInputs()\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Project Management</label>\r\n            <input type=\"text\" class=\"form-control\" formControlName=\"projectManagement\"\r\n              placeholder=\"Enter Project Management\" [ngClass]=\"{\r\n                'is-invalid': getCurrentForm().get('projectManagement')?.touched && getCurrentForm().get('projectManagement')?.invalid\r\n              }\" />\r\n            <div\r\n              *ngIf=\"getCurrentForm().get('projectManagement')?.touched && getCurrentForm().get('projectManagement')?.invalid\"\r\n              class=\"invalid-feedback d-block\">\r\n              Project Management is required.\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Project Constructor -->\r\n          <div class=\"mb-10\" *ngIf=\"getSellInsideCompoundInputs()\">\r\n            <label class=\"form-label fw-bold text-start d-block\">Project Constructor</label>\r\n            <input type=\"text\" class=\"form-control\" formControlName=\"projectConstructor\"\r\n              placeholder=\"Enter Project Constructor\" [ngClass]=\"{\r\n                'is-invalid': getCurrentForm().get('projectConstructor')?.touched && getCurrentForm().get('projectConstructor')?.invalid\r\n              }\" />\r\n            <div\r\n              *ngIf=\"getCurrentForm().get('projectConstructor')?.touched && getCurrentForm().get('projectConstructor')?.invalid\"\r\n              class=\"invalid-feedback d-block\">\r\n              Project Constructor is required.\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Dynamic Inputs for Step 2 -->\r\n          <div *ngFor=\"let input of currentInputs; trackBy: trackByInputName\" class=\"mb-7\">\r\n            <!-- Skip the hardcoded fields to avoid duplication -->\r\n            <ng-container\r\n              *ngIf=\"!['cityId', 'areaId', 'subAreaId', 'compoundName', 'detailedAddress', 'addressLink', 'projectManagement', 'projectConstructor', 'locationSuggestions'].includes(input.name)\">\r\n              <label class=\"form-label fw-bold text-start d-flex align-items-center flex-wrap gap-2 mb-3\">\r\n                <span>{{ input.label }}</span>\r\n                <span *ngIf=\"isInputRequired(input)\" class=\"required\"></span>\r\n              </label>\r\n              <ng-container [ngSwitch]=\"input.type\">\r\n                <!-- Enhanced Checkbox Input -->\r\n                <div *ngSwitchCase=\"'checkbox'\" class=\"custom-checkbox-container\"\r\n                  [class.has-error]=\"getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid\">\r\n                  <input type=\"checkbox\" class=\"custom-checkbox-input\" [formControlName]=\"input.name\" [id]=\"input.name\"\r\n                    [attr.aria-describedby]=\"input.name + '_help'\"\r\n                    [attr.aria-invalid]=\"getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid ? 'true' : 'false'\" />\r\n                  <label class=\"custom-checkbox-label\" [for]=\"input.name\">\r\n                    <div class=\"custom-checkbox-box\">\r\n                      <i class=\"fas fa-check custom-checkbox-icon\"></i>\r\n                    </div>\r\n                    <div class=\"custom-checkbox-content\">\r\n                      <span class=\"custom-checkbox-text\">{{ input.label }}</span>\r\n                      <small *ngIf=\"input.name === 'locationSuggestions'\" class=\"custom-checkbox-description\">\r\n                        Get personalized location recommendations based on your preferences\r\n                      </small>\r\n                      <small *ngIf=\"input.name === 'locationSuggestion'\" class=\"custom-checkbox-description\">\r\n                        Get personalized location recommendations for your purchase requirements\r\n                      </small>\r\n                      <small *ngIf=\"input.name === 'budgetSuggestions'\" class=\"custom-checkbox-description\">\r\n                        Receive budget optimization suggestions from our experts\r\n                      </small>\r\n                      <small *ngIf=\"input.name === 'rentPriceSuggestions'\" class=\"custom-checkbox-description\">\r\n                        Get market-based pricing recommendations for your property\r\n                      </small>\r\n                    </div>\r\n                  </label>\r\n                  <!-- Enhanced validation feedback -->\r\n                  <div *ngIf=\"getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid\"\r\n                    class=\"checkbox-error-feedback\" [id]=\"input.name + '_help'\" role=\"alert\">\r\n                    <i class=\"fas fa-exclamation-circle me-1\"></i>\r\n                    <span>{{ input.label }} is required to proceed</span>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Text Input -->\r\n                <input *ngSwitchCase=\"'text'\" type=\"text\" class=\"form-control\" [formControlName]=\"input.name\"\r\n                  [placeholder]=\"'Enter ' + input.label\" [ngClass]=\"{\r\n                    'is-invalid': getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid\r\n                  }\" />\r\n\r\n                <!-- Number Input -->\r\n                <input *ngSwitchCase=\"'number'\" type=\"number\" class=\"form-control\" [formControlName]=\"input.name\"\r\n                  [placeholder]=\"'Enter ' + input.label\" min=\"0\" [ngClass]=\"{\r\n                    'is-invalid': getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid\r\n                  }\" />\r\n\r\n                <!-- URL Input -->\r\n                <input *ngSwitchCase=\"'url'\" type=\"url\" class=\"form-control\" [formControlName]=\"input.name\"\r\n                  [placeholder]=\"'Enter ' + input.label\" [ngClass]=\"{\r\n                    'is-invalid': getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid\r\n                  }\" />\r\n\r\n                <!-- Select Input -->\r\n                <div *ngSwitchCase=\"'select'\" class=\"dropdown\">\r\n                  <button\r\n                    class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                    type=\"button\" [id]=\"input.name + 'Dropdown'\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                    <span>{{\r\n                      getText(input.options, getCurrentForm().get(input.name)?.value) || 'Select ' + input.label\r\n                      }}</span>\r\n                    <i class=\"fas fa-chevron-down\"></i>\r\n                  </button>\r\n                  <ul class=\"dropdown-menu w-100\" [attr.aria-labelledby]=\"input.name + 'Dropdown'\"\r\n                    [ngStyle]=\"input.options && input.options.length > 10 ? { 'max-height': '200px', 'overflow-y': 'auto' } : {}\">\r\n                    <li *ngFor=\"let option of input.options\">\r\n                      <a class=\"dropdown-item\" (click)=\"select(getCurrentForm(), input.name, option.value)\">{{\r\n                        option.key }}</a>\r\n                    </li>\r\n                  </ul>\r\n                </div>\r\n\r\n                <!-- Textarea Input -->\r\n                <textarea *ngSwitchCase=\"'textarea'\" class=\"form-control\" [formControlName]=\"input.name\"\r\n                  [placeholder]=\"'Enter ' + input.label\" rows=\"3\" [ngClass]=\"{\r\n                    'is-invalid': getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid\r\n                  }\"></textarea>\r\n              </ng-container>\r\n\r\n              <!-- Error message for dynamic inputs -->\r\n              <div *ngIf=\"getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid\"\r\n                class=\"invalid-feedback d-block\">\r\n                <ng-container *ngIf=\"getCurrentForm().get(input.name)?.errors?.['required']\">\r\n                  {{ input.label }} is required.\r\n                </ng-container>\r\n                <ng-container *ngIf=\"getCurrentForm().get(input.name)?.errors?.['pattern']\">\r\n                  Please enter a valid {{ input.label }}.\r\n                </ng-container>\r\n                <ng-container *ngIf=\"getCurrentForm().get(input.name)?.errors?.['min']\">\r\n                  {{ input.label }} must be greater than or equal to {{\r\n                  getCurrentForm().get(input.name)?.errors?.['min']?.min }}.\r\n                </ng-container>\r\n              </div>\r\n            </ng-container>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Steps 3–5: Dynamic Inputs from Service -->\r\n        <div *ngIf=\"currentStep > 2\">\r\n          <div *ngFor=\"let input of currentInputs; trackBy: trackByInputName\" class=\"mb-7\">\r\n            <label class=\"form-label fw-bold text-start d-flex align-items-center flex-wrap gap-2 mb-3\">\r\n              <span>{{ input.label }}</span>\r\n              <span *ngIf=\"isInputRequired(input)\" class=\"required\"></span>\r\n            </label>\r\n            <ng-container [ngSwitch]=\"input.type\">\r\n              <!-- Enhanced Checkbox Input -->\r\n              <div *ngSwitchCase=\"'checkbox'\" class=\"custom-checkbox-container\"\r\n                [class.has-error]=\"getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid\">\r\n                <input type=\"checkbox\" class=\"custom-checkbox-input\" [formControlName]=\"input.name\" [id]=\"input.name\"\r\n                  [attr.aria-describedby]=\"input.name + '_help'\"\r\n                  [attr.aria-invalid]=\"getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid ? 'true' : 'false'\" />\r\n                <label class=\"custom-checkbox-label\" [for]=\"input.name\">\r\n                  <div class=\"custom-checkbox-box\">\r\n                    <i class=\"fas fa-check custom-checkbox-icon\"></i>\r\n                  </div>\r\n                  <div class=\"custom-checkbox-content\">\r\n                    <span class=\"custom-checkbox-text\">{{ input.label }}</span>\r\n                    <small *ngIf=\"input.name === 'locationSuggestions'\" class=\"custom-checkbox-description\">\r\n                      Get personalized location recommendations based on your preferences\r\n                    </small>\r\n                    <small *ngIf=\"input.name === 'budgetSuggestions'\" class=\"custom-checkbox-description\">\r\n                      Receive budget optimization suggestions from our experts\r\n                    </small>\r\n                    <small *ngIf=\"input.name === 'rentPriceSuggestions'\" class=\"custom-checkbox-description\">\r\n                      Get market-based pricing recommendations for your property\r\n                    </small>\r\n                  </div>\r\n                </label>\r\n                <!-- Enhanced validation feedback -->\r\n                <div *ngIf=\"getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid\"\r\n                  class=\"checkbox-error-feedback\" [id]=\"input.name + '_help'\" role=\"alert\">\r\n                  <i class=\"fas fa-exclamation-circle me-1\"></i>\r\n                  <span>{{ input.label }} is required to proceed</span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Text Input -->\r\n              <input *ngSwitchCase=\"'text'\" type=\"text\" class=\"form-control\" [formControlName]=\"input.name\"\r\n                [placeholder]=\"'Enter ' + input.label\" [ngClass]=\"{\r\n                  'is-invalid': getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid\r\n                }\" />\r\n\r\n              <!-- Number Input -->\r\n              <input *ngSwitchCase=\"'number'\" type=\"number\" class=\"form-control\" [formControlName]=\"input.name\"\r\n                [placeholder]=\"'Enter ' + input.label\" min=\"0\" [ngClass]=\"{\r\n                  'is-invalid': getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid\r\n                }\" />\r\n\r\n              <!-- Select Input -->\r\n              <div *ngSwitchCase=\"'select'\" class=\"dropdown\">\r\n                <button\r\n                  class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center\"\r\n                  type=\"button\" [id]=\"input.name + 'Dropdown'\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                  <span>{{\r\n                    getText(input.options, getCurrentForm().get(input.name)?.value) || 'Select ' + input.label\r\n                    }}</span>\r\n                  <i class=\"fas fa-chevron-down\"></i>\r\n                </button>\r\n                <ul class=\"dropdown-menu w-100\" [attr.aria-labelledby]=\"input.name + 'Dropdown'\"\r\n                  [ngStyle]=\"input.options && input.options.length > 10 ? { 'max-height': '200px', 'overflow-y': 'auto' } : {}\">\r\n                  <li *ngFor=\"let option of input.options\">\r\n                    <a class=\"dropdown-item\" (click)=\"select(getCurrentForm(), input.name, option.value)\">{{ option.key\r\n                      }}</a>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n\r\n              <!-- Enhanced MultiSelect Input -->\r\n              <div *ngSwitchCase=\"'multiSelect'\" class=\"enhanced-multiselect\">\r\n                <div class=\"dropdown\">\r\n                  <button\r\n                    class=\"btn btn-outline-secondary dropdown-toggle w-100 text-start d-flex justify-content-between align-items-center enhanced-multiselect-button\"\r\n                    type=\"button\" [id]=\"input.name + 'Dropdown'\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\"\r\n                    [class.has-selections]=\"getSelectedCount(input.name) > 0\">\r\n                    <div class=\"multiselect-display\">\r\n                      <span class=\"multiselect-text\">{{\r\n                        input.name === 'otherAccessories'\r\n                        ? getSelectedAccessoriesText()\r\n                        : input.name === 'otherExpenses'\r\n                        ? getSelectedOtherExpensesText()\r\n                        : getSelectedText(input.name, input.options || []) || 'Select ' + input.label\r\n                        }}</span>\r\n                      <span *ngIf=\"getSelectedCount(input.name) > 0\" class=\"badge bg-primary ms-2\">\r\n                        {{ getSelectedCount(input.name) }}\r\n                      </span>\r\n                    </div>\r\n                    <i class=\"fas fa-chevron-down multiselect-icon\"></i>\r\n                  </button>\r\n\r\n                  <div class=\"dropdown-menu w-150 enhanced-multiselect-menu\"\r\n                    [attr.aria-labelledby]=\"input.name + 'Dropdown'\">\r\n\r\n                    <!-- Select All / Clear All Controls -->\r\n                    <div class=\"multiselect-controls p-2 border-bottom bg-light\">\r\n                      <div class=\"d-flex justify-content-between align-items-center\">\r\n                        <div class=\"form-check\">\r\n                          <input class=\"form-check-input\" type=\"checkbox\" [id]=\"input.name + '_selectAll'\"\r\n                            [checked]=\"areAllOptionsSelected(input.name)\"\r\n                            [indeterminate]=\"areSomeOptionsSelected(input.name) && !areAllOptionsSelected(input.name)\"\r\n                            (change)=\"toggleSelectAll(input.name)\">\r\n                          <label class=\"form-check-label fw-bold text-primary\" [for]=\"input.name + '_selectAll'\">\r\n                            Select All\r\n                          </label>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <!-- Options List -->\r\n                    <div class=\"multiselect-options-container\" style=\"max-height: 250px; overflow-y: auto;\">\r\n                      <div class=\"p-2\">\r\n                        <div *ngFor=\"let option of getFilteredOptions(input.name, input.options || [])\"\r\n                          class=\"multiselect-option mb-1\">\r\n                          <div class=\"form-check\">\r\n                            <input class=\"form-check-input\" type=\"checkbox\" [id]=\"input.name + '_' + option.value\"\r\n                              [checked]=\"\r\n                                input.name === 'otherAccessories'\r\n                                  ? isAccessorySelected(option.value)\r\n                                  : input.name === 'otherExpenses'\r\n                                  ? isOtherExpenseSelected(option.value)\r\n                                  : isMultiSelectOptionSelected(input.name, option.value)\r\n                              \" (change)=\"\r\n                                input.name === 'otherAccessories'\r\n                                  ? toggleAccessory(option.value)\r\n                                  : input.name === 'otherExpenses'\r\n                                  ? toggleOtherExpense(option.value)\r\n                                  : toggleMultiSelect(input.name, option.value)\r\n                              \">\r\n                            <label class=\"form-check-label text-start multiselect-option-label\"\r\n                              [for]=\"input.name + '_' + option.value\"\r\n                              [class.fw-bold]=\"isMultiSelectOptionSelected(input.name, option.value)\">\r\n                              {{ option.key }}\r\n                            </label>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <!-- No Results Message -->\r\n                        <div *ngIf=\"getFilteredOptions(input.name, input.options || []).length === 0\"\r\n                          class=\"text-center text-muted py-3\">\r\n                          <i class=\"fas fa-search mb-2\"></i>\r\n                          <div>No options found</div>\r\n                          <small>Try adjusting your search</small>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- File Input -->\r\n              <div *ngSwitchCase=\"'file'\" class=\"mb-4 upload-card-container\">\r\n                <div class=\"card cursor-pointer upload-card\" [class.has-files]=\"getFileCount(input.name) > 0\">\r\n                  <label [for]=\"input.name\" class=\"card-body text-center\">\r\n                    <div class=\"upload-icon\">\r\n                      <i class=\"fas fa-2x\" [class.fa-cloud-upload-alt]=\"getFileCount(input.name) === 0\"\r\n                        [class.fa-check-circle]=\"getFileCount(input.name) > 0\"></i>\r\n                    </div>\r\n                    <div class=\"upload-content\">\r\n                      <h6 class=\"upload-title mb-2\">{{ input.label }}</h6>\r\n                      <p class=\"upload-subtitle mb-0\" *ngIf=\"getFileCount(input.name) === 0\">\r\n                        Click to upload {{ input.name === 'video' ? 'videos' : 'images' }}\r\n                      </p>\r\n                      <p class=\"upload-subtitle mb-0\" *ngIf=\"getFileCount(input.name) > 0\">\r\n                        {{ getFileCount(input.name) }} file(s) uploaded successfully\r\n                      </p>\r\n                      <span *ngIf=\"getFileCount(input.name) > 0\" class=\"badge bg-success mt-2\">\r\n                        {{ getFileCount(input.name) }} file(s)\r\n                      </span>\r\n                    </div>\r\n                    <input type=\"file\" [id]=\"input.name\" class=\"d-none\" (change)=\"onFileChange($event, input.name)\"\r\n                      [multiple]=\"input.name !== 'mainImage'\"\r\n                      [accept]=\"input.name === 'video' ? 'video/*' : 'image/*'\" />\r\n                  </label>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Textarea for Notes -->\r\n              <textarea *ngSwitchCase=\"'textarea'\" class=\"form-control\" [formControlName]=\"input.name\" rows=\"4\"\r\n                [placeholder]=\"'Enter ' + input.label\" [ngClass]=\"{\r\n                  'is-invalid': getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid\r\n                }\"></textarea>\r\n            </ng-container>\r\n\r\n            <!-- Enhanced Error Display -->\r\n            <div *ngIf=\"getCurrentForm().get(input.name)?.touched && getCurrentForm().get(input.name)?.invalid\"\r\n              class=\"invalid-feedback d-block\">\r\n              <div *ngIf=\"getCurrentForm().get(input.name)?.errors?.['required']\">\r\n                {{ input.label }} is required.\r\n              </div>\r\n              <div *ngIf=\"getCurrentForm().get(input.name)?.errors?.['min']\">\r\n                {{ input.label }} must be greater than or equal to {{\r\n                getCurrentForm().get(input.name)?.errors?.['min']?.min }}.\r\n              </div>\r\n              <div *ngIf=\"getCurrentForm().get(input.name)?.errors?.['pattern']\">\r\n                Please enter a valid {{ input.label.toLowerCase() }}.\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Step 4 No Inputs Info Card -->\r\n        <div *ngIf=\"currentStep === 4 && !hasStepInputs()\" class=\"alert alert-info mt-4\">\r\n          <div class=\"d-flex align-items-center\">\r\n            <i class=\"fas fa-info-circle fa-2x me-3 text-info\"></i>\r\n            <div>\r\n              <h6 class=\"alert-heading mb-1\">No Files to Upload</h6>\r\n              <p class=\"mb-0\">{{ getStep4InfoMessage() }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Navigation Buttons -->\r\n        <div class=\"d-flex justify-content-between align-items-center pt-10\">\r\n          <!-- Back Button -->\r\n          <button *ngIf=\"currentStep > 1\" type=\"button\"\r\n            class=\"btn btn-lg btn-outline-success btn-light-success px-6 py-3 rounded-pill\" (click)=\"prevStep()\">\r\n            <i class=\"fas fa-arrow-left me-2\"></i>\r\n            Back\r\n          </button>\r\n          <div *ngIf=\"currentStep === 1\" class=\"invisible\"></div>\r\n\r\n          <!-- Next/Submit Button -->\r\n          <button *ngIf=\"currentStep !== totalSteps\" type=\"button\" class=\"btn btn-lg btn-navy px-6 py-3 rounded-pill\"\r\n            [disabled]=\"!isCurrentFormValid()\" (click)=\"nextStep()\">\r\n            <span class=\"indicator-label text-white\">\r\n              Next\r\n              <i class=\"fas fa-arrow-right ms-2 text-white\"></i>\r\n            </span>\r\n          </button>\r\n\r\n          <button *ngIf=\"currentStep === totalSteps\" type=\"button\"\r\n            class=\"btn btn-lg btn-navy px-6 py-3 rounded-pill d-flex align-items-center\"\r\n            [disabled]=\"!isCurrentFormValid() || isSubmitting\" (click)=\"submitForm()\">\r\n            <span *ngIf=\"!isSubmitting\" class=\"indicator-label text-white\">\r\n              <i class=\"fas fa-check me-2\"></i>\r\n              Submit\r\n            </span>\r\n            <span *ngIf=\"isSubmitting\" class=\"d-flex align-items-center text-white\">\r\n              <span class=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n              Submitting...\r\n            </span>\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AACA,SAAiFA,UAAU,QAAQ,gBAAgB;AAInH,SAASC,eAAe,EAAEC,QAAQ,EAAcC,EAAE,QAAQ,MAAM;AAChE,SAASC,UAAU,EAAEC,GAAG,QAAa,gBAAgB;AACrD,OAAOC,IAAI,MAAM,aAAa;AAC9B,SACEC,cAAc,EACdC,4BAA4B,EAC5BC,YAAY,QAGP,2BAA2B;;;;;;;;;;;;;;;;;;;;;ICkDKC,EAAA,CAAAC,cAAA,WAAoB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF3CH,EAAA,CAAAC,cAAA,YAAyF;IACvFD,EAAA,CAAAI,SAAA,YAAkE;IAClEJ,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAK,UAAA,IAAAC,8DAAA,mBAAoB;IACnCN,EAAA,CAAAG,YAAA,EAAI;;;;;IADFH,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAQ,kBAAA,MAAAC,UAAA,KAAa;IAAOT,EAAA,CAAAO,SAAA,EAAW;IAAXP,EAAA,CAAAU,UAAA,UAAAC,OAAA,CAAW;;;;;IAZrCX,EAJF,CAAAC,cAAA,cAEuD,cAET;IAC1CD,EAAA,CAAAI,SAAA,YAA+C;IACjDJ,EAAA,CAAAG,YAAA,EAAM;IAIFH,EAFJ,CAAAC,cAAA,cAAuC,cACO,iBACX;IAAAD,EAAA,CAAAE,MAAA,GAA6B;;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrEH,EAAA,CAAAC,cAAA,eAA8D;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IACxEF,EADwE,CAAAG,YAAA,EAAO,EACzE;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAK,UAAA,KAAAO,uDAAA,gBAAyF;IAM/FZ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAlByBH,EADyC,CAAAa,WAAA,sBAA2B,uBACrE,kBAAwB;IAQjBb,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAc,iBAAA,CAAAd,EAAA,CAAAe,WAAA,OAAAC,QAAA,CAAAC,KAAA,EAA6B;IAIPjB,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAU,UAAA,YAAAM,QAAA,CAAAE,QAAA,CAAmB;;;;;IAfhFlB,EANN,CAAAC,cAAA,cAAoG,cAEjC,cAGnC,cACF;IACtBD,EAAA,CAAAK,UAAA,IAAAc,kDAAA,oBAEuD;IAsB/DnB,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;;IAxByBH,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAU,UAAA,YAAAU,YAAA,CAAAC,MAAA,CAAqB;;;;;IArBlDrB,EALN,CAAAC,cAAA,cAC+B,cAEuC,cAC3B,cAEA;IACnCD,EAAA,CAAAI,SAAA,YAAsD;IACxDJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,UAAK,aACkC;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjEH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAE,MAAA,GACd;IAGzBF,EAHyB,CAAAG,YAAA,EAAI,EACnB,EACF,EACF;IAGNH,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAK,UAAA,KAAAiB,4CAAA,kBAAoG;IAiCxGtB,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAzCmCH,EAAA,CAAAO,SAAA,GACd;IADcP,EAAA,CAAAQ,kBAAA,kCAAAe,MAAA,CAAAC,gBAAA,CAAAC,MAAA,gCACd;IAOIzB,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAU,UAAA,YAAAa,MAAA,CAAAC,gBAAA,CAAqB;;;;;;IAqDxCxB,EADF,CAAAC,cAAA,SAAqD,YAEoF;IAArID,EAAA,CAAA0B,UAAA,mBAAAC,+DAAA;MAAA,IAAAC,OAAA;MAAA,MAAAC,QAAA,GAAA7B,EAAA,CAAA8B,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAvB,EAAA,CAAAiC,aAAA;MAASV,MAAA,CAAAW,MAAA,CAAOX,MAAA,CAAAY,cAAA,EAAgB,EAAE,qBAAqB,EAAAN,QAAA,CAAAO,KAAA,CAAc;MAAA,OAAApC,EAAA,CAAAqC,WAAA,EAAAT,OAAA,GAAEL,MAAA,CAAAY,cAAA,EAAgB,CAAAG,GAAA,CAAK,qBAAqB,CAAC,mBAAAV,OAAA,CAAAW,aAAA;IAAA,EAAkB;IAACvC,EAAA,CAAAE,MAAA,GACzH;IAChBF,EADgB,CAAAG,YAAA,EAAI,EACf;;;;IAFoIH,EAAA,CAAAO,SAAA,GACzH;IADyHP,EAAA,CAAAc,iBAAA,CAAAe,QAAA,CAAAW,GAAA,CACzH;;;;;IAIpBxC,EAAA,CAAAC,cAAA,cAEmC;IACjCD,EAAA,CAAAE,MAAA,0CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAiBAH,EADF,CAAAC,cAAA,SAAkD,YAEwD;IAAtGD,EAAA,CAAA0B,UAAA,mBAAAe,+DAAA;MAAA,IAAAb,OAAA;MAAA,MAAAc,OAAA,GAAA1C,EAAA,CAAA8B,aAAA,CAAAa,GAAA,EAAAX,SAAA;MAAA,MAAAT,MAAA,GAAAvB,EAAA,CAAAiC,aAAA;MAASV,MAAA,CAAAW,MAAA,CAAOX,MAAA,CAAAY,cAAA,EAAgB,EAAE,MAAM,EAAAO,OAAA,CAAAN,KAAA,CAAa;MAAA,OAAApC,EAAA,CAAAqC,WAAA,EAAAT,OAAA,GAAEL,MAAA,CAAAY,cAAA,EAAgB,CAAAG,GAAA,CAAK,MAAM,CAAC,mBAAAV,OAAA,CAAAW,aAAA;IAAA,EAAkB;IAACvC,EAAA,CAAAE,MAAA,GAC3F;IACfF,EADe,CAAAG,YAAA,EAAI,EACd;;;;IAFqGH,EAAA,CAAAO,SAAA,GAC3F;IAD2FP,EAAA,CAAAc,iBAAA,CAAA4B,OAAA,CAAAF,GAAA,CAC3F;;;;;IAInBxC,EAAA,CAAAC,cAAA,cACmC;IACjCD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAiBAH,EADF,CAAAC,cAAA,SAA0D,YAE4D;IAAlHD,EAAA,CAAA0B,UAAA,mBAAAkB,+DAAA;MAAA,IAAAhB,OAAA;MAAA,MAAAiB,YAAA,GAAA7C,EAAA,CAAA8B,aAAA,CAAAgB,IAAA,EAAAd,SAAA;MAAA,MAAAT,MAAA,GAAAvB,EAAA,CAAAiC,aAAA;MAASV,MAAA,CAAAW,MAAA,CAAOX,MAAA,CAAAY,cAAA,EAAgB,EAAE,UAAU,EAAAU,YAAA,CAAAT,KAAA,CAAiB;MAAA,OAAApC,EAAA,CAAAqC,WAAA,EAAAT,OAAA,GAAEL,MAAA,CAAAY,cAAA,EAAgB,CAAAG,GAAA,CAAK,UAAU,CAAC,mBAAAV,OAAA,CAAAW,aAAA;IAAA,EAAkB;IAACvC,EAAA,CAAAE,MAAA,GACnG;IACnBF,EADmB,CAAAG,YAAA,EAAI,EAClB;;;;IAFiHH,EAAA,CAAAO,SAAA,GACnG;IADmGP,EAAA,CAAAc,iBAAA,CAAA+B,YAAA,CAAAL,GAAA,CACnG;;;;;IAIvBxC,EAAA,CAAAC,cAAA,cACmC;IACjCD,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA3ENH,EAFJ,CAAAC,cAAA,UAA+B,cACV,gBACoC;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAK7EH,EAJJ,CAAAC,cAAA,cAAsB,iBAG6E,WACzF;IAAAD,EAAA,CAAAE,MAAA,GAGF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACXH,EAAA,CAAAI,SAAA,YAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,aAA8E;IAC5ED,EAAA,CAAAK,UAAA,KAAA0C,2CAAA,iBAAqD;IAMzD/C,EADE,CAAAG,YAAA,EAAK,EACD;IACNH,EAAA,CAAAK,UAAA,KAAA2C,4CAAA,kBAEmC;IAGrChD,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAAmB,iBACoC;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAK7DH,EAJJ,CAAAC,cAAA,eAAsB,kBAG8D,YAC1E;IAAAD,EAAA,CAAAE,MAAA,IAGF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACXH,EAAA,CAAAI,SAAA,aAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAK,UAAA,KAAA4C,2CAAA,iBAAkD;IAMtDjD,EADE,CAAAG,YAAA,EAAK,EACD;IACNH,EAAA,CAAAK,UAAA,KAAA6C,4CAAA,kBACmC;IAGrClD,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAAmB,iBACoC;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAKlEH,EAJJ,CAAAC,cAAA,eAAsB,kBAGkE,YAC9E;IAAAD,EAAA,CAAAE,MAAA,IAGF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACXH,EAAA,CAAAI,SAAA,aAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAK,UAAA,KAAA8C,2CAAA,iBAA0D;IAM9DnD,EADE,CAAAG,YAAA,EAAK,EACD;IACNH,EAAA,CAAAK,UAAA,KAAA+C,4CAAA,kBACmC;IAIvCpD,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;;;;;IAxEQH,EAAA,CAAAO,SAAA,GAGF;IAHEP,EAAA,CAAAc,iBAAA,CAAAS,MAAA,CAAA8B,OAAA,CAAA9B,MAAA,CAAA+B,0BAAA,GAAAC,OAAA,GAAAhC,MAAA,CAAAY,cAAA,GAAAG,GAAA,0CAAAiB,OAAA,CAAAnB,KAAA,mCAGF;IAIkBpC,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAU,UAAA,YAAAa,MAAA,CAAA+B,0BAAA,CAA6B;IAQpDtD,EAAA,CAAAO,SAAA,EAAkH;IAAlHP,EAAA,CAAAU,UAAA,WAAA8C,OAAA,GAAAjC,MAAA,CAAAY,cAAA,GAAAG,GAAA,0CAAAkB,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAjC,MAAA,CAAAY,cAAA,GAAAG,GAAA,0CAAAkB,OAAA,CAAAE,OAAA,EAAkH;IAY3G1D,EAAA,CAAAO,SAAA,GAGF;IAHEP,EAAA,CAAAc,iBAAA,CAAAS,MAAA,CAAA8B,OAAA,CAAA9B,MAAA,CAAAoC,sBAAA,KAAAC,OAAA,GAAArC,MAAA,CAAAY,cAAA,GAAAG,GAAA,2BAAAsB,OAAA,CAAAxB,KAAA,mBAGF;IAIiBpC,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAU,UAAA,YAAAa,MAAA,CAAAoC,sBAAA,GAA2B;IAO9C3D,EAAA,CAAAO,SAAA,EAAoF;IAApFP,EAAA,CAAAU,UAAA,WAAAmD,OAAA,GAAAtC,MAAA,CAAAY,cAAA,GAAAG,GAAA,2BAAAuB,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAtC,MAAA,CAAAY,cAAA,GAAAG,GAAA,2BAAAuB,OAAA,CAAAH,OAAA,EAAoF;IAYhF1D,EAAA,CAAAO,SAAA,GAGF;IAHEP,EAAA,CAAAc,iBAAA,CAAAS,MAAA,CAAA8B,OAAA,CAAA9B,MAAA,CAAAuC,0BAAA,KAAAC,OAAA,GAAAxC,MAAA,CAAAY,cAAA,GAAAG,GAAA,+BAAAyB,OAAA,CAAA3B,KAAA,wBAGF;IAIqBpC,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAU,UAAA,YAAAa,MAAA,CAAAuC,0BAAA,GAA+B;IAOtD9D,EAAA,CAAAO,SAAA,EAA4F;IAA5FP,EAAA,CAAAU,UAAA,WAAAsD,OAAA,GAAAzC,MAAA,CAAAY,cAAA,GAAAG,GAAA,+BAAA0B,OAAA,CAAAP,OAAA,OAAAO,OAAA,GAAAzC,MAAA,CAAAY,cAAA,GAAAG,GAAA,+BAAA0B,OAAA,CAAAN,OAAA,EAA4F;;;;;;IAuB5F1D,EADF,CAAAC,cAAA,SAAyC,YAC2C;IAAzDD,EAAA,CAAA0B,UAAA,mBAAAuC,+DAAA;MAAA,MAAAC,QAAA,GAAAlE,EAAA,CAAA8B,aAAA,CAAAqC,IAAA,EAAAnC,SAAA;MAAA,MAAAT,MAAA,GAAAvB,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAqC,WAAA,CAASd,MAAA,CAAA6C,cAAA,CAAe,QAAQ,EAAAF,QAAA,CAAA9B,KAAA,EAAA8B,QAAA,CAAA1B,GAAA,CAAuB;IAAA,EAAC;IAACxC,EAAA,CAAAE,MAAA,GAAc;IAClGF,EADkG,CAAAG,YAAA,EAAI,EACjG;;;;IAD+EH,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAc,iBAAA,CAAAoD,QAAA,CAAA1B,GAAA,CAAc;;;;;IAItGxC,EAAA,CAAAC,cAAA,cACmC;IACjCD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAI,SAAA,YAA2C;IAC3CJ,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAiBAH,EADF,CAAAC,cAAA,SAAwC,YAC4C;IAAzDD,EAAA,CAAA0B,UAAA,mBAAA2C,+DAAA;MAAA,MAAAC,QAAA,GAAAtE,EAAA,CAAA8B,aAAA,CAAAyC,IAAA,EAAAvC,SAAA;MAAA,MAAAT,MAAA,GAAAvB,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAqC,WAAA,CAASd,MAAA,CAAA6C,cAAA,CAAe,QAAQ,EAAAE,QAAA,CAAAlC,KAAA,EAAAkC,QAAA,CAAA9B,GAAA,CAAuB;IAAA,EAAC;IAACxC,EAAA,CAAAE,MAAA,GAAc;IAClGF,EADkG,CAAAG,YAAA,EAAI,EACjG;;;;IAD+EH,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAc,iBAAA,CAAAwD,QAAA,CAAA9B,GAAA,CAAc;;;;;IAItGxC,EAAA,CAAAC,cAAA,cACmC;IACjCD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAiBAH,EADF,CAAAC,cAAA,SAA8C,YAC+C;IAAlED,EAAA,CAAA0B,UAAA,mBAAA8C,+DAAA;MAAA,MAAAC,WAAA,GAAAzE,EAAA,CAAA8B,aAAA,CAAA4C,IAAA,EAAA1C,SAAA;MAAA,MAAAT,MAAA,GAAAvB,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAqC,WAAA,CAASd,MAAA,CAAA6C,cAAA,CAAe,WAAW,EAAAK,WAAA,CAAArC,KAAA,EAAAqC,WAAA,CAAAjC,GAAA,CAA6B;IAAA,EAAC;IAACxC,EAAA,CAAAE,MAAA,GAC3E;IAClBF,EADkB,CAAAG,YAAA,EAAI,EACjB;;;;IAFwFH,EAAA,CAAAO,SAAA,GAC3E;IAD2EP,EAAA,CAAAc,iBAAA,CAAA2D,WAAA,CAAAjC,GAAA,CAC3E;;;;;IAyBpBxC,EAAA,CAAAC,cAAA,cAE6E;IAC3ED,EAAA,CAAAI,SAAA,YAA8C;IAC9CJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,4DAAqD;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;;;;;IAtBRH,EADF,CAAAC,cAAA,cAAsE,cAE+D;IACjID,EAAA,CAAAI,SAAA,gBAE0J;IAExJJ,EADF,CAAAC,cAAA,gBAA+D,cAC5B;IAC/BD,EAAA,CAAAI,SAAA,YAAiD;IACnDJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAqC,eACA;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,gBAA2C;IACzCD,EAAA,CAAAE,MAAA,6EACF;IAEJF,EAFI,CAAAG,YAAA,EAAQ,EACJ,EACA;IAERH,EAAA,CAAAK,UAAA,KAAAsE,mDAAA,kBAE6E;IAKjF3E,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;;IAvBFH,EAAA,CAAAO,SAAA,EAAgI;IAAhIP,EAAA,CAAAa,WAAA,gBAAA+D,OAAA,GAAArD,MAAA,CAAAY,cAAA,GAAAG,GAAA,0CAAAsC,OAAA,CAAAnB,OAAA,OAAAmB,OAAA,GAAArD,MAAA,CAAAY,cAAA,GAAAG,GAAA,0CAAAsC,OAAA,CAAAlB,OAAA,EAAgI;IAG9H1D,EAAA,CAAAO,SAAA,EAAqJ;;IAcpJP,EAAA,CAAAO,SAAA,GAAkH;IAAlHP,EAAA,CAAAU,UAAA,WAAAkD,OAAA,GAAArC,MAAA,CAAAY,cAAA,GAAAG,GAAA,0CAAAsB,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAArC,MAAA,CAAAY,cAAA,GAAAG,GAAA,0CAAAsB,OAAA,CAAAF,OAAA,EAAkH;;;;;IA4BrH1D,EAAA,CAAAC,cAAA,cAEoF;IAClFD,EAAA,CAAAI,SAAA,YAA8C;IAC9CJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,4DAAqD;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;;;;;IAtBRH,EAFF,CAAAC,cAAA,cACgB,cAEqH;IACjID,EAAA,CAAAI,SAAA,gBAE0J;IAExJJ,EADF,CAAAC,cAAA,gBAAsE,cACnC;IAC/BD,EAAA,CAAAI,SAAA,YAAiD;IACnDJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAqC,eACA;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,gBAA2C;IACzCD,EAAA,CAAAE,MAAA,6EACF;IAEJF,EAFI,CAAAG,YAAA,EAAQ,EACJ,EACA;IAERH,EAAA,CAAAK,UAAA,KAAAwE,mDAAA,kBAEoF;IAKxF7E,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;;IAvBFH,EAAA,CAAAO,SAAA,EAAgI;IAAhIP,EAAA,CAAAa,WAAA,gBAAA+D,OAAA,GAAArD,MAAA,CAAAY,cAAA,GAAAG,GAAA,0CAAAsC,OAAA,CAAAnB,OAAA,OAAAmB,OAAA,GAAArD,MAAA,CAAAY,cAAA,GAAAG,GAAA,0CAAAsC,OAAA,CAAAlB,OAAA,EAAgI;IAG9H1D,EAAA,CAAAO,SAAA,EAAqJ;;IAcpJP,EAAA,CAAAO,SAAA,GAAkH;IAAlHP,EAAA,CAAAU,UAAA,WAAAkD,OAAA,GAAArC,MAAA,CAAAY,cAAA,GAAAG,GAAA,0CAAAsB,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAArC,MAAA,CAAAY,cAAA,GAAAG,GAAA,0CAAAsB,OAAA,CAAAF,OAAA,EAAkH;;;;;IAmBvH1D,EAAA,CAAAC,cAAA,cACmC;IACjCD,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAVNH,EADF,CAAAC,cAAA,cAAwD,gBACD;IAAAD,EAAA,CAAAE,MAAA,qBACnD;IAAAF,EAAA,CAAAI,SAAA,eAA8B;IAChCJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAI,SAAA,gBAGO;IACPJ,EAAA,CAAAK,UAAA,IAAAyE,kDAAA,kBACmC;IAGrC9E,EAAA,CAAAG,YAAA,EAAM;;;;;;IAPFH,EAAA,CAAAO,SAAA,GAEE;IAFFP,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA+E,eAAA,IAAAC,GAAA,IAAAJ,OAAA,GAAArD,MAAA,CAAAY,cAAA,GAAAG,GAAA,mCAAAsC,OAAA,CAAAnB,OAAA,OAAAmB,OAAA,GAAArD,MAAA,CAAAY,cAAA,GAAAG,GAAA,mCAAAsC,OAAA,CAAAlB,OAAA,GAEE;IACE1D,EAAA,CAAAO,SAAA,EAAoG;IAApGP,EAAA,CAAAU,UAAA,WAAA8C,OAAA,GAAAjC,MAAA,CAAAY,cAAA,GAAAG,GAAA,mCAAAkB,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAjC,MAAA,CAAAY,cAAA,GAAAG,GAAA,mCAAAkB,OAAA,CAAAE,OAAA,EAAoG;;;;;IAgB1G1D,EAAA,CAAAC,cAAA,cAEmC;IACjCD,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAXNH,EAFF,CAAAC,cAAA,cACgJ,gBACzF;IAAAD,EAAA,CAAAE,MAAA,wBACnD;IAAAF,EAAA,CAAAI,SAAA,eAA8B;IAChCJ,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAI,SAAA,gBAGO;IACPJ,EAAA,CAAAK,UAAA,IAAA4E,kDAAA,kBAEmC;IAGrCjF,EAAA,CAAAG,YAAA,EAAM;;;;;;IARmCH,EAAA,CAAAO,SAAA,GAEnC;IAFmCP,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA+E,eAAA,IAAAC,GAAA,IAAAJ,OAAA,GAAArD,MAAA,CAAAY,cAAA,GAAAG,GAAA,sCAAAsC,OAAA,CAAAnB,OAAA,OAAAmB,OAAA,GAAArD,MAAA,CAAAY,cAAA,GAAAG,GAAA,sCAAAsC,OAAA,CAAAlB,OAAA,GAEnC;IAED1D,EAAA,CAAAO,SAAA,EAA0G;IAA1GP,EAAA,CAAAU,UAAA,WAAA8C,OAAA,GAAAjC,MAAA,CAAAY,cAAA,GAAAG,GAAA,sCAAAkB,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAjC,MAAA,CAAAY,cAAA,GAAAG,GAAA,sCAAAkB,OAAA,CAAAE,OAAA,EAA0G;;;;;IAgB3G1D,EAAA,CAAAkF,uBAAA,GAA+E;IAC7ElF,EAAA,CAAAE,MAAA,kCACF;;;;;;IAJFF,EAAA,CAAAC,cAAA,cACmC;IACjCD,EAAA,CAAAK,UAAA,IAAA8E,iEAAA,2BAA+E;IAGjFnF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHWH,EAAA,CAAAO,SAAA,EAA8D;IAA9DP,EAAA,CAAAU,UAAA,UAAA8C,OAAA,GAAAjC,MAAA,CAAAY,cAAA,GAAAG,GAAA,kCAAAkB,OAAA,CAAAnC,MAAA,kBAAAmC,OAAA,CAAAnC,MAAA,YAA8D;;;;;IAP/ErB,EAFF,CAAAC,cAAA,cACgJ,gBACzF;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClFH,EAAA,CAAAI,SAAA,gBAGO;IACPJ,EAAA,CAAAK,UAAA,IAAA+E,kDAAA,kBACmC;IAKrCpF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAT6CH,EAAA,CAAAO,SAAA,GAE7C;IAF6CP,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA+E,eAAA,IAAAC,GAAA,IAAAJ,OAAA,GAAArD,MAAA,CAAAY,cAAA,GAAAG,GAAA,kCAAAsC,OAAA,CAAAnB,OAAA,OAAAmB,OAAA,GAAArD,MAAA,CAAAY,cAAA,GAAAG,GAAA,kCAAAsC,OAAA,CAAAlB,OAAA,GAE7C;IACE1D,EAAA,CAAAO,SAAA,EAAkG;IAAlGP,EAAA,CAAAU,UAAA,WAAA8C,OAAA,GAAAjC,MAAA,CAAAY,cAAA,GAAAG,GAAA,kCAAAkB,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAjC,MAAA,CAAAY,cAAA,GAAAG,GAAA,kCAAAkB,OAAA,CAAAE,OAAA,EAAkG;;;;;IAexG1D,EAAA,CAAAC,cAAA,cAEmC;IACjCD,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IATNH,EADF,CAAAC,cAAA,cAAyD,gBACF;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/EH,EAAA,CAAAI,SAAA,gBAGO;IACPJ,EAAA,CAAAK,UAAA,IAAAgF,kDAAA,kBAEmC;IAGrCrF,EAAA,CAAAG,YAAA,EAAM;;;;;;IARqCH,EAAA,CAAAO,SAAA,GAErC;IAFqCP,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA+E,eAAA,IAAAC,GAAA,IAAAJ,OAAA,GAAArD,MAAA,CAAAY,cAAA,GAAAG,GAAA,wCAAAsC,OAAA,CAAAnB,OAAA,OAAAmB,OAAA,GAAArD,MAAA,CAAAY,cAAA,GAAAG,GAAA,wCAAAsC,OAAA,CAAAlB,OAAA,GAErC;IAED1D,EAAA,CAAAO,SAAA,EAA8G;IAA9GP,EAAA,CAAAU,UAAA,WAAA8C,OAAA,GAAAjC,MAAA,CAAAY,cAAA,GAAAG,GAAA,wCAAAkB,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAjC,MAAA,CAAAY,cAAA,GAAAG,GAAA,wCAAAkB,OAAA,CAAAE,OAAA,EAA8G;;;;;IAajH1D,EAAA,CAAAC,cAAA,cAEmC;IACjCD,EAAA,CAAAE,MAAA,yCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IATNH,EADF,CAAAC,cAAA,cAAyD,gBACF;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChFH,EAAA,CAAAI,SAAA,gBAGO;IACPJ,EAAA,CAAAK,UAAA,IAAAiF,kDAAA,kBAEmC;IAGrCtF,EAAA,CAAAG,YAAA,EAAM;;;;;;IARsCH,EAAA,CAAAO,SAAA,GAEtC;IAFsCP,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAA+E,eAAA,IAAAC,GAAA,IAAAJ,OAAA,GAAArD,MAAA,CAAAY,cAAA,GAAAG,GAAA,yCAAAsC,OAAA,CAAAnB,OAAA,OAAAmB,OAAA,GAAArD,MAAA,CAAAY,cAAA,GAAAG,GAAA,yCAAAsC,OAAA,CAAAlB,OAAA,GAEtC;IAED1D,EAAA,CAAAO,SAAA,EAAgH;IAAhHP,EAAA,CAAAU,UAAA,WAAA8C,OAAA,GAAAjC,MAAA,CAAAY,cAAA,GAAAG,GAAA,yCAAAkB,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAjC,MAAA,CAAAY,cAAA,GAAAG,GAAA,yCAAAkB,OAAA,CAAAE,OAAA,EAAgH;;;;;IAa/G1D,EAAA,CAAAI,SAAA,eAA6D;;;;;IAevDJ,EAAA,CAAAC,cAAA,gBAAwF;IACtFD,EAAA,CAAAE,MAAA,4EACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACRH,EAAA,CAAAC,cAAA,gBAAuF;IACrFD,EAAA,CAAAE,MAAA,iFACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACRH,EAAA,CAAAC,cAAA,gBAAsF;IACpFD,EAAA,CAAAE,MAAA,iEACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACRH,EAAA,CAAAC,cAAA,gBAAyF;IACvFD,EAAA,CAAAE,MAAA,mEACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAIZH,EAAA,CAAAC,cAAA,eAC2E;IACzED,EAAA,CAAAI,SAAA,YAA8C;IAC9CJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAwC;IAChDF,EADgD,CAAAG,YAAA,EAAO,EACjD;;;;IAH4BH,EAAA,CAAAU,UAAA,OAAA6E,SAAA,CAAAC,IAAA,WAA2B;IAErDxF,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAQ,kBAAA,KAAA+E,SAAA,CAAAE,KAAA,4BAAwC;;;;;IA7BlDzF,EAAA,CAAAC,cAAA,cAC6G;IAC3GD,EAAA,CAAAI,SAAA,iBAEoI;IAElIJ,EADF,CAAAC,cAAA,iBAAwD,cACrB;IAC/BD,EAAA,CAAAI,SAAA,YAAiD;IACnDJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAqC,eACA;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAU3DH,EATA,CAAAK,UAAA,IAAAqF,yEAAA,qBAAwF,IAAAC,yEAAA,qBAGD,KAAAC,0EAAA,qBAGD,KAAAC,0EAAA,qBAGG;IAI7F7F,EADE,CAAAG,YAAA,EAAM,EACA;IAERH,EAAA,CAAAK,UAAA,KAAAyF,wEAAA,mBAC2E;IAI7E9F,EAAA,CAAAG,YAAA,EAAM;;;;;;;;IA9BJH,EAAA,CAAAa,WAAA,gBAAAe,OAAA,GAAAL,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAA5D,OAAA,CAAA6B,OAAA,OAAA7B,OAAA,GAAAL,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAA5D,OAAA,CAAA8B,OAAA,EAA0G;IACrD1D,EAAA,CAAAO,SAAA,EAA8B;IAACP,EAA/B,CAAAU,UAAA,oBAAA6E,SAAA,CAAAC,IAAA,CAA8B,OAAAD,SAAA,CAAAC,IAAA,CAAkB;;IAGhExF,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAU,UAAA,QAAA6E,SAAA,CAAAC,IAAA,CAAkB;IAKhBxF,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAc,iBAAA,CAAAyE,SAAA,CAAAE,KAAA,CAAiB;IAC5CzF,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAAU,UAAA,SAAA6E,SAAA,CAAAC,IAAA,2BAA0C;IAG1CxF,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAAU,UAAA,SAAA6E,SAAA,CAAAC,IAAA,0BAAyC;IAGzCxF,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAU,UAAA,SAAA6E,SAAA,CAAAC,IAAA,yBAAwC;IAGxCxF,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAAU,UAAA,SAAA6E,SAAA,CAAAC,IAAA,4BAA2C;IAMjDxF,EAAA,CAAAO,SAAA,EAA4F;IAA5FP,EAAA,CAAAU,UAAA,WAAAqF,QAAA,GAAAxE,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAAO,QAAA,CAAAtC,OAAA,OAAAsC,QAAA,GAAAxE,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAAO,QAAA,CAAArC,OAAA,EAA4F;;;;;IAQpG1D,EAAA,CAAAI,SAAA,iBAGO;;;;;;IAFkCJ,EADsB,CAAAU,UAAA,oBAAA6E,SAAA,CAAAC,IAAA,CAA8B,2BAAAD,SAAA,CAAAE,KAAA,CACrD,YAAAzF,EAAA,CAAA+E,eAAA,IAAAC,GAAA,IAAAjB,OAAA,GAAAxC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAAzB,OAAA,CAAAN,OAAA,OAAAM,OAAA,GAAAxC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAAzB,OAAA,CAAAL,OAAA,GAEpC;;;;;IAGJ1D,EAAA,CAAAI,SAAA,iBAGO;;;;;;IAF0CJ,EADkB,CAAAU,UAAA,oBAAA6E,SAAA,CAAAC,IAAA,CAA8B,2BAAAD,SAAA,CAAAE,KAAA,CACzD,YAAAzF,EAAA,CAAA+E,eAAA,IAAAC,GAAA,IAAAjB,OAAA,GAAAxC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAAzB,OAAA,CAAAN,OAAA,OAAAM,OAAA,GAAAxC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAAzB,OAAA,CAAAL,OAAA,GAEpC;;;;;IAGJ1D,EAAA,CAAAI,SAAA,iBAGO;;;;;;IAFkCJ,EADoB,CAAAU,UAAA,oBAAA6E,SAAA,CAAAC,IAAA,CAA8B,2BAAAD,SAAA,CAAAE,KAAA,CACnD,YAAAzF,EAAA,CAAA+E,eAAA,IAAAC,GAAA,IAAAjB,OAAA,GAAAxC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAAzB,OAAA,CAAAN,OAAA,OAAAM,OAAA,GAAAxC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAAzB,OAAA,CAAAL,OAAA,GAEpC;;;;;;IAeE1D,EADF,CAAAC,cAAA,SAAyC,YAC+C;IAA7DD,EAAA,CAAA0B,UAAA,mBAAAsE,2FAAA;MAAA,MAAAC,UAAA,GAAAjG,EAAA,CAAA8B,aAAA,CAAAoE,IAAA,EAAAlE,SAAA;MAAA,MAAAuD,SAAA,GAAAvF,EAAA,CAAAiC,aAAA,IAAAD,SAAA;MAAA,MAAAT,MAAA,GAAAvB,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAqC,WAAA,CAASd,MAAA,CAAAW,MAAA,CAAOX,MAAA,CAAAY,cAAA,EAAgB,EAAAoD,SAAA,CAAAC,IAAA,EAAAS,UAAA,CAAA7D,KAAA,CAA2B;IAAA,EAAC;IAACpC,EAAA,CAAAE,MAAA,GACvE;IACjBF,EADiB,CAAAG,YAAA,EAAI,EAChB;;;;IAFmFH,EAAA,CAAAO,SAAA,GACvE;IADuEP,EAAA,CAAAc,iBAAA,CAAAmF,UAAA,CAAAzD,GAAA,CACvE;;;;;IATjBxC,EAJJ,CAAAC,cAAA,cAA+C,kBAGkD,WACvF;IAAAD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACXH,EAAA,CAAAI,SAAA,YAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,cACgH;IAC9GD,EAAA,CAAAK,UAAA,IAAA8F,uEAAA,iBAAyC;IAK7CnG,EADE,CAAAG,YAAA,EAAK,EACD;;;;;;IAbYH,EAAA,CAAAO,SAAA,EAA8B;IAA9BP,EAAA,CAAAU,UAAA,OAAA6E,SAAA,CAAAC,IAAA,cAA8B;IACtCxF,EAAA,CAAAO,SAAA,GAEF;IAFEP,EAAA,CAAAc,iBAAA,CAAAS,MAAA,CAAA8B,OAAA,CAAAkC,SAAA,CAAAa,OAAA,GAAAvC,OAAA,GAAAtC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAA3B,OAAA,CAAAzB,KAAA,iBAAAmD,SAAA,CAAAE,KAAA,CAEF;IAIJzF,EAAA,CAAAO,SAAA,GAA6G;IAA7GP,EAAA,CAAAU,UAAA,YAAA6E,SAAA,CAAAa,OAAA,IAAAb,SAAA,CAAAa,OAAA,CAAA3E,MAAA,QAAAzB,EAAA,CAAAqG,eAAA,IAAAC,GAAA,IAAAtG,EAAA,CAAAqG,eAAA,IAAAE,GAAA,EAA6G;;IACtFvG,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAU,UAAA,YAAA6E,SAAA,CAAAa,OAAA,CAAgB;;;;;IAQ3CpG,EAAA,CAAAI,SAAA,oBAGgB;;;;;;IAFkCJ,EADQ,CAAAU,UAAA,oBAAA6E,SAAA,CAAAC,IAAA,CAA8B,2BAAAD,SAAA,CAAAE,KAAA,CAChD,YAAAzF,EAAA,CAAA+E,eAAA,IAAAC,GAAA,IAAAjB,OAAA,GAAAxC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAAzB,OAAA,CAAAN,OAAA,OAAAM,OAAA,GAAAxC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAAzB,OAAA,CAAAL,OAAA,GAEpC;;;;;IAMJ1D,EAAA,CAAAkF,uBAAA,GAA6E;IAC3ElF,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA+E,SAAA,CAAAE,KAAA,mBACF;;;;;IACAzF,EAAA,CAAAkF,uBAAA,GAA4E;IAC1ElF,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,2BAAA+E,SAAA,CAAAE,KAAA,OACF;;;;;IACAzF,EAAA,CAAAkF,uBAAA,GAAwE;IACtElF,EAAA,CAAAE,MAAA,GAEF;;;;;;;IAFEF,EAAA,CAAAO,SAAA,EAEF;IAFEP,EAAA,CAAAwG,kBAAA,MAAAjB,SAAA,CAAAE,KAAA,yCAAA5B,OAAA,GAAAtC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAA3B,OAAA,CAAAxC,MAAA,kBAAAwC,OAAA,CAAAxC,MAAA,yBAAAwC,OAAA,CAAAxC,MAAA,QAAAoF,GAAA,OAEF;;;;;IAXFzG,EAAA,CAAAC,cAAA,cACmC;IAOjCD,EANA,CAAAK,UAAA,IAAAqG,iFAAA,2BAA6E,IAAAC,iFAAA,2BAGD,IAAAC,iFAAA,2BAGJ;IAI1E5G,EAAA,CAAAG,YAAA,EAAM;;;;;;;;IAVWH,EAAA,CAAAO,SAAA,EAA4D;IAA5DP,EAAA,CAAAU,UAAA,UAAAkB,OAAA,GAAAL,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAA5D,OAAA,CAAAP,MAAA,kBAAAO,OAAA,CAAAP,MAAA,aAA4D;IAG5DrB,EAAA,CAAAO,SAAA,EAA2D;IAA3DP,EAAA,CAAAU,UAAA,UAAAmD,OAAA,GAAAtC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAA3B,OAAA,CAAAxC,MAAA,kBAAAwC,OAAA,CAAAxC,MAAA,YAA2D;IAG3DrB,EAAA,CAAAO,SAAA,EAAuD;IAAvDP,EAAA,CAAAU,UAAA,UAAAqD,OAAA,GAAAxC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAAzB,OAAA,CAAA1C,MAAA,kBAAA0C,OAAA,CAAA1C,MAAA,QAAuD;;;;;IA9F1ErB,EAAA,CAAAkF,uBAAA,GACsL;IAElLlF,EADF,CAAAC,cAAA,gBAA4F,WACpF;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9BH,EAAA,CAAAK,UAAA,IAAAwG,kEAAA,mBAAsD;IACxD7G,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAkF,uBAAA,OAAsC;IAyEpClF,EAvEA,CAAAK,UAAA,IAAAyG,iEAAA,oBAC6G,IAAAC,mEAAA,oBAoCtG,IAAAC,mEAAA,oBAMA,IAAAC,mEAAA,qBAMA,KAAAC,kEAAA,mBAGwC,KAAAC,uEAAA,wBAsB1C;;IAIPnH,EAAA,CAAAK,UAAA,KAAA+G,kEAAA,kBACmC;;;;;;;IApF3BpH,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAc,iBAAA,CAAAyE,SAAA,CAAAE,KAAA,CAAiB;IAChBzF,EAAA,CAAAO,SAAA,EAA4B;IAA5BP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAA8F,eAAA,CAAA9B,SAAA,EAA4B;IAEvBvF,EAAA,CAAAO,SAAA,EAAuB;IAAvBP,EAAA,CAAAU,UAAA,aAAA6E,SAAA,CAAA+B,IAAA,CAAuB;IAE7BtH,EAAA,CAAAO,SAAA,EAAwB;IAAxBP,EAAA,CAAAU,UAAA,4BAAwB;IAkCtBV,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAU,UAAA,wBAAoB;IAMpBV,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAU,UAAA,0BAAsB;IAMtBV,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAU,UAAA,uBAAmB;IAMrBV,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAU,UAAA,0BAAsB;IAmBjBV,EAAA,CAAAO,SAAA,EAAwB;IAAxBP,EAAA,CAAAU,UAAA,4BAAwB;IAO/BV,EAAA,CAAAO,SAAA,EAA4F;IAA5FP,EAAA,CAAAU,UAAA,WAAA6G,QAAA,GAAAhG,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAA+B,QAAA,CAAA9D,OAAA,OAAA8D,QAAA,GAAAhG,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAiD,SAAA,CAAAC,IAAA,oBAAA+B,QAAA,CAAA7D,OAAA,EAA4F;;;;;IAxFtG1D,EAAA,CAAAC,cAAA,cAAiF;IAE/ED,EAAA,CAAAK,UAAA,IAAAmH,2DAAA,6BACsL;IAmGxLxH,EAAA,CAAAG,YAAA,EAAM;;;;IAnGDH,EAAA,CAAAO,SAAA,EAAiL;IAAjLP,EAAA,CAAAU,UAAA,UAAAV,EAAA,CAAAqG,eAAA,IAAAoB,GAAA,EAAAC,QAAA,CAAAnC,SAAA,CAAAC,IAAA,EAAiL;;;;;IAlNpLxF,EAHJ,CAAAC,cAAA,UAA+B,cAEV,gBACoC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAM7DH,EALJ,CAAAC,cAAA,cAAsB,iBAIW,WACvB;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpDH,EAAA,CAAAI,SAAA,YAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,aAC+C;IAC7CD,EAAA,CAAAK,UAAA,KAAAsH,2CAAA,iBAAyC;;IAI7C3H,EADE,CAAAG,YAAA,EAAK,EACD;IAKNH,EAJA,CAAAK,UAAA,KAAAuH,4CAAA,kBACmC,KAAAC,4CAAA,kBAGkB;IAIvD7H,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAAmB,iBACoC;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAM7DH,EALJ,CAAAC,cAAA,eAAsB,kBAIW,YACvB;IAAAD,EAAA,CAAAE,MAAA,IAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpDH,EAAA,CAAAI,SAAA,aAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,cAC+C;IAC7CD,EAAA,CAAAK,UAAA,KAAAyH,2CAAA,iBAAwC;;IAI5C9H,EADE,CAAAG,YAAA,EAAK,EACD;IACNH,EAAA,CAAAK,UAAA,KAAA0H,4CAAA,kBACmC;IAGrC/H,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAAmB,iBACoC;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAMjEH,EALJ,CAAAC,cAAA,eAAsB,kBAIW,YACvB;IAAAD,EAAA,CAAAE,MAAA,IAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAI,SAAA,aAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,cAC+C;IAC7CD,EAAA,CAAAK,UAAA,KAAA2H,2CAAA,iBAA8C;;IAMpDhI,EAFI,CAAAG,YAAA,EAAK,EACD,EACF;IA0INH,EAvIA,CAAAK,UAAA,KAAA4H,4CAAA,mBAAsE,KAAAC,4CAAA,mBA6BtD,KAAAC,4CAAA,kBA8BwC,KAAAC,4CAAA,kBAgBwF,KAAAC,4CAAA,kBAiBA,KAAAC,4CAAA,kBAevF,KAAAC,4CAAA,kBAcA,KAAAC,4CAAA,kBAcwB;IAuGnFxI,EAAA,CAAAG,YAAA,EAAM;;;;;;IAjTEH,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAU,UAAA,aAAAa,MAAA,CAAAkH,eAAA,CAA4B;IACtBzI,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAc,iBAAA,CAAAS,MAAA,CAAAmH,gBAAA,kBAAuC;IAKxB1I,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAe,WAAA,SAAAQ,MAAA,CAAAoH,OAAA,EAAkB;IAKrC3I,EAAA,CAAAO,SAAA,GAAwF;IAAxFP,EAAA,CAAAU,UAAA,WAAAkD,OAAA,GAAArC,MAAA,CAAAY,cAAA,GAAAG,GAAA,6BAAAsB,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAArC,MAAA,CAAAY,cAAA,GAAAG,GAAA,6BAAAsB,OAAA,CAAAF,OAAA,EAAwF;IAIxF1D,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAAkH,eAAA,CAAqB;IAavBzI,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAU,UAAA,cAAAa,MAAA,CAAAqH,cAAA,CAA4B;IACtB5I,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAc,iBAAA,CAAAS,MAAA,CAAAsH,gBAAA,kBAAuC;IAKxB7I,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAe,WAAA,SAAAQ,MAAA,CAAAuH,MAAA,EAAiB;IAKpC9I,EAAA,CAAAO,SAAA,GAAwF;IAAxFP,EAAA,CAAAU,UAAA,WAAAsD,OAAA,GAAAzC,MAAA,CAAAY,cAAA,GAAAG,GAAA,6BAAA0B,OAAA,CAAAP,OAAA,OAAAO,OAAA,GAAAzC,MAAA,CAAAY,cAAA,GAAAG,GAAA,6BAAA0B,OAAA,CAAAN,OAAA,EAAwF;IAa1F1D,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAU,UAAA,cAAAa,MAAA,CAAAwH,cAAA,CAA4B;IACtB/I,EAAA,CAAAO,SAAA,GAA8C;IAA9CP,EAAA,CAAAc,iBAAA,CAAAS,MAAA,CAAAyH,mBAAA,sBAA8C;IAK5BhJ,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAe,WAAA,SAAAQ,MAAA,CAAA0H,SAAA,EAAoB;IAS5CjJ,EAAA,CAAAO,SAAA,GAAgD;IAAhDP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAA2H,0BAAA,MAAA3H,MAAA,CAAA4H,QAAA,GAAgD;IA4BhDnJ,EAAA,CAAAO,SAAA,EAAgG;IAAhGP,EAAA,CAAAU,UAAA,UAAAa,MAAA,CAAA2H,0BAAA,MAAA3H,MAAA,CAAA4H,QAAA,MAAA5H,MAAA,CAAAY,cAAA,GAAAG,GAAA,wBAAgG;IA+BlFtC,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAA2H,0BAAA,GAAkC;IAgBnDlJ,EAAA,CAAAO,SAAA,EAA2I;IAA3IP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAA6H,2BAAA,MAAA7H,MAAA,CAAA8H,8BAAA,MAAA9H,MAAA,CAAA+H,4BAAA,MAAA/H,MAAA,CAAAgI,4BAAA,GAA2I;IAiB3IvJ,EAAA,CAAAO,SAAA,EAA2I;IAA3IP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAA6H,2BAAA,MAAA7H,MAAA,CAAA8H,8BAAA,MAAA9H,MAAA,CAAA+H,4BAAA,MAAA/H,MAAA,CAAAgI,4BAAA,GAA2I;IAe1HvJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAA6H,2BAAA,GAAmC;IAcnCpJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAA6H,2BAAA,GAAmC;IAchCpJ,EAAA,CAAAO,SAAA,EAAkB;IAAAP,EAAlB,CAAAU,UAAA,YAAAa,MAAA,CAAAiI,aAAA,CAAkB,iBAAAjI,MAAA,CAAAkI,gBAAA,CAAyB;;;;;IA8G9DzJ,EAAA,CAAAI,SAAA,eAA6D;;;;;IAevDJ,EAAA,CAAAC,cAAA,gBAAwF;IACtFD,EAAA,CAAAE,MAAA,4EACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACRH,EAAA,CAAAC,cAAA,gBAAsF;IACpFD,EAAA,CAAAE,MAAA,iEACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACRH,EAAA,CAAAC,cAAA,gBAAyF;IACvFD,EAAA,CAAAE,MAAA,mEACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAIZH,EAAA,CAAAC,cAAA,eAC2E;IACzED,EAAA,CAAAI,SAAA,YAA8C;IAC9CJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAwC;IAChDF,EADgD,CAAAG,YAAA,EAAO,EACjD;;;;IAH4BH,EAAA,CAAAU,UAAA,OAAAgJ,SAAA,CAAAlE,IAAA,WAA2B;IAErDxF,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAQ,kBAAA,KAAAkJ,SAAA,CAAAjE,KAAA,4BAAwC;;;;;IA1BlDzF,EAAA,CAAAC,cAAA,cAC6G;IAC3GD,EAAA,CAAAI,SAAA,iBAEoI;IAElIJ,EADF,CAAAC,cAAA,iBAAwD,cACrB;IAC/BD,EAAA,CAAAI,SAAA,YAAiD;IACnDJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAqC,eACA;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAO3DH,EANA,CAAAK,UAAA,IAAAsJ,yDAAA,qBAAwF,IAAAC,yDAAA,qBAGF,KAAAC,0DAAA,qBAGG;IAI7F7J,EADE,CAAAG,YAAA,EAAM,EACA;IAERH,EAAA,CAAAK,UAAA,KAAAyJ,wDAAA,mBAC2E;IAI7E9J,EAAA,CAAAG,YAAA,EAAM;;;;;;;;IA3BJH,EAAA,CAAAa,WAAA,gBAAA+C,OAAA,GAAArC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA5B,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAArC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA5B,OAAA,CAAAF,OAAA,EAA0G;IACrD1D,EAAA,CAAAO,SAAA,EAA8B;IAACP,EAA/B,CAAAU,UAAA,oBAAAgJ,SAAA,CAAAlE,IAAA,CAA8B,OAAAkE,SAAA,CAAAlE,IAAA,CAAkB;;IAGhExF,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAU,UAAA,QAAAgJ,SAAA,CAAAlE,IAAA,CAAkB;IAKhBxF,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAc,iBAAA,CAAA4I,SAAA,CAAAjE,KAAA,CAAiB;IAC5CzF,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAAU,UAAA,SAAAgJ,SAAA,CAAAlE,IAAA,2BAA0C;IAG1CxF,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAU,UAAA,SAAAgJ,SAAA,CAAAlE,IAAA,yBAAwC;IAGxCxF,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAAU,UAAA,SAAAgJ,SAAA,CAAAlE,IAAA,4BAA2C;IAMjDxF,EAAA,CAAAO,SAAA,EAA4F;IAA5FP,EAAA,CAAAU,UAAA,WAAAqJ,QAAA,GAAAxI,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAAuE,QAAA,CAAAtG,OAAA,OAAAsG,QAAA,GAAAxI,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAAuE,QAAA,CAAArG,OAAA,EAA4F;;;;;IAQpG1D,EAAA,CAAAI,SAAA,iBAGO;;;;;;IAFkCJ,EADsB,CAAAU,UAAA,oBAAAgJ,SAAA,CAAAlE,IAAA,CAA8B,2BAAAkE,SAAA,CAAAjE,KAAA,CACrD,YAAAzF,EAAA,CAAA+E,eAAA,IAAAC,GAAA,IAAAnB,OAAA,GAAAtC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA3B,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAtC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA3B,OAAA,CAAAH,OAAA,GAEpC;;;;;IAGJ1D,EAAA,CAAAI,SAAA,iBAGO;;;;;;IAF0CJ,EADkB,CAAAU,UAAA,oBAAAgJ,SAAA,CAAAlE,IAAA,CAA8B,2BAAAkE,SAAA,CAAAjE,KAAA,CACzD,YAAAzF,EAAA,CAAA+E,eAAA,IAAAC,GAAA,IAAAnB,OAAA,GAAAtC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA3B,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAtC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA3B,OAAA,CAAAH,OAAA,GAEpC;;;;;;IAeE1D,EADF,CAAAC,cAAA,SAAyC,YAC+C;IAA7DD,EAAA,CAAA0B,UAAA,mBAAAsI,0EAAA;MAAA,MAAAC,UAAA,GAAAjK,EAAA,CAAA8B,aAAA,CAAAoI,IAAA,EAAAlI,SAAA;MAAA,MAAA0H,SAAA,GAAA1J,EAAA,CAAAiC,aAAA,IAAAD,SAAA;MAAA,MAAAT,MAAA,GAAAvB,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAqC,WAAA,CAASd,MAAA,CAAAW,MAAA,CAAOX,MAAA,CAAAY,cAAA,EAAgB,EAAAuH,SAAA,CAAAlE,IAAA,EAAAyE,UAAA,CAAA7H,KAAA,CAA2B;IAAA,EAAC;IAACpC,EAAA,CAAAE,MAAA,GAClF;IACNF,EADM,CAAAG,YAAA,EAAI,EACL;;;;IAFmFH,EAAA,CAAAO,SAAA,GAClF;IADkFP,EAAA,CAAAc,iBAAA,CAAAmJ,UAAA,CAAAzH,GAAA,CAClF;;;;;IATNxC,EAJJ,CAAAC,cAAA,cAA+C,kBAGkD,WACvF;IAAAD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACXH,EAAA,CAAAI,SAAA,YAAmC;IACrCJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,cACgH;IAC9GD,EAAA,CAAAK,UAAA,IAAA8J,sDAAA,iBAAyC;IAK7CnK,EADE,CAAAG,YAAA,EAAK,EACD;;;;;;IAbYH,EAAA,CAAAO,SAAA,EAA8B;IAA9BP,EAAA,CAAAU,UAAA,OAAAgJ,SAAA,CAAAlE,IAAA,cAA8B;IACtCxF,EAAA,CAAAO,SAAA,GAEF;IAFEP,EAAA,CAAAc,iBAAA,CAAAS,MAAA,CAAA8B,OAAA,CAAAqG,SAAA,CAAAtD,OAAA,GAAAxE,OAAA,GAAAL,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA5D,OAAA,CAAAQ,KAAA,iBAAAsH,SAAA,CAAAjE,KAAA,CAEF;IAIJzF,EAAA,CAAAO,SAAA,GAA6G;IAA7GP,EAAA,CAAAU,UAAA,YAAAgJ,SAAA,CAAAtD,OAAA,IAAAsD,SAAA,CAAAtD,OAAA,CAAA3E,MAAA,QAAAzB,EAAA,CAAAqG,eAAA,IAAAC,GAAA,IAAAtG,EAAA,CAAAqG,eAAA,IAAAE,GAAA,EAA6G;;IACtFvG,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAU,UAAA,YAAAgJ,SAAA,CAAAtD,OAAA,CAAgB;;;;;IAsBnCpG,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAe,MAAA,CAAA6I,gBAAA,CAAAV,SAAA,CAAAlE,IAAA,OACF;;;;;;IA6BMxF,EAHJ,CAAAC,cAAA,eACkC,eACR,iBAclB;IANAD,EAAA,CAAA0B,UAAA,oBAAA2I,kFAAA;MAAA,MAAAC,UAAA,GAAAtK,EAAA,CAAA8B,aAAA,CAAAyI,IAAA,EAAAvI,SAAA;MAAA,MAAA0H,SAAA,GAAA1J,EAAA,CAAAiC,aAAA,IAAAD,SAAA;MAAA,MAAAT,MAAA,GAAAvB,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAqC,WAAA,CAAAqH,SAAA,CAAAlE,IAAA,KAEnB,kBAAkB,GAC9BjE,MAAA,CAAAiJ,eAAA,CAAAF,UAAA,CAAAlI,KAAA,CAA6B,GAAAsH,SAAA,CAAAlE,IAAA,KAChB,eAAe,GAAqCjE,MAAA,CAAAkJ,kBAAA,CAAAH,UAAA,CAAAlI,KAAA,CACtC,GAAqCb,MAAA,CAAAmJ,iBAAA,CAAAhB,SAAA,CAAAlE,IAAA,EAAA8E,UAAA,CAAAlI,KAAA,CACtC;IAAA,EAAI;IAbHpC,EAAA,CAAAG,YAAA,EAaI;IACJH,EAAA,CAAAC,cAAA,iBAE0E;IACxED,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAQ,EACJ,EACF;;;;;;IApB8CH,EAAA,CAAAO,SAAA,GAAsC;IACpFP,EAD8C,CAAAU,UAAA,OAAAgJ,SAAA,CAAAlE,IAAA,SAAA8E,UAAA,CAAAlI,KAAA,CAAsC,YAAAsH,SAAA,CAAAlE,IAAA,0BAAAjE,MAAA,CAAAoJ,mBAAA,CAAAL,UAAA,CAAAlI,KAAA,IAAAsH,SAAA,CAAAlE,IAAA,uBAAAjE,MAAA,CAAAqJ,sBAAA,CAAAN,UAAA,CAAAlI,KAAA,IAAAb,MAAA,CAAAsJ,2BAAA,CAAAnB,SAAA,CAAAlE,IAAA,EAAA8E,UAAA,CAAAlI,KAAA,EAOnF;IASDpC,EAAA,CAAAO,SAAA,EAAuE;IAAvEP,EAAA,CAAAa,WAAA,YAAAU,MAAA,CAAAsJ,2BAAA,CAAAnB,SAAA,CAAAlE,IAAA,EAAA8E,UAAA,CAAAlI,KAAA,EAAuE;IADvEpC,EAAA,CAAAU,UAAA,QAAAgJ,SAAA,CAAAlE,IAAA,SAAA8E,UAAA,CAAAlI,KAAA,CAAuC;IAEvCpC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA8J,UAAA,CAAA9H,GAAA,MACF;;;;;IAKJxC,EAAA,CAAAC,cAAA,eACsC;IACpCD,EAAA,CAAAI,SAAA,aAAkC;IAClCJ,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3BH,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAClCF,EADkC,CAAAG,YAAA,EAAQ,EACpC;;;;;;IAlERH,EAPR,CAAAC,cAAA,eAAgE,cACxC,kBAIwC,eACzB,gBACA;IAAAD,EAAA,CAAAE,MAAA,GAM3B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACXH,EAAA,CAAAK,UAAA,IAAAyK,yDAAA,oBAA6E;IAG/E9K,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,SAAA,aAAoD;IACtDJ,EAAA,CAAAG,YAAA,EAAS;IASDH,EAPR,CAAAC,cAAA,eACmD,eAGY,gBACI,gBACrC,kBAImB;IAAvCD,EAAA,CAAA0B,UAAA,oBAAAqJ,4EAAA;MAAA/K,EAAA,CAAA8B,aAAA,CAAAkJ,IAAA;MAAA,MAAAtB,SAAA,GAAA1J,EAAA,CAAAiC,aAAA,GAAAD,SAAA;MAAA,MAAAT,MAAA,GAAAvB,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAqC,WAAA,CAAUd,MAAA,CAAA0J,eAAA,CAAAvB,SAAA,CAAAlE,IAAA,CAA2B;IAAA,EAAC;IAHxCxF,EAAA,CAAAG,YAAA,EAGyC;IACzCH,EAAA,CAAAC,cAAA,kBAAuF;IACrFD,EAAA,CAAAE,MAAA,oBACF;IAGNF,EAHM,CAAAG,YAAA,EAAQ,EACJ,EACF,EACF;IAIJH,EADF,CAAAC,cAAA,gBAAwF,gBACrE;IA2BfD,EA1BA,CAAAK,UAAA,KAAA6K,yDAAA,mBACkC,KAAAC,yDAAA,mBA0BI;IAUhDnL,EALQ,CAAAG,YAAA,EAAM,EACF,EAEF,EACF,EACF;;;;;IA1EAH,EAAA,CAAAO,SAAA,GAAyD;IAAzDP,EAAA,CAAAa,WAAA,mBAAAU,MAAA,CAAA6I,gBAAA,CAAAV,SAAA,CAAAlE,IAAA,MAAyD;IAD3CxF,EAAA,CAAAU,UAAA,OAAAgJ,SAAA,CAAAlE,IAAA,cAA8B;IAGXxF,EAAA,CAAAO,SAAA,GAM3B;IAN2BP,EAAA,CAAAc,iBAAA,CAAA4I,SAAA,CAAAlE,IAAA,0BAAAjE,MAAA,CAAA6J,0BAAA,KAAA1B,SAAA,CAAAlE,IAAA,uBAAAjE,MAAA,CAAA8J,4BAAA,KAAA9J,MAAA,CAAA+J,eAAA,CAAA5B,SAAA,CAAAlE,IAAA,EAAAkE,SAAA,CAAAtD,OAAA,IAAApG,EAAA,CAAAqG,eAAA,KAAAkF,GAAA,kBAAA7B,SAAA,CAAAjE,KAAA,CAM3B;IACGzF,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAA6I,gBAAA,CAAAV,SAAA,CAAAlE,IAAA,MAAsC;IAQ/CxF,EAAA,CAAAO,SAAA,GAAgD;;IAMMP,EAAA,CAAAO,SAAA,GAAgC;IAE9EP,EAF8C,CAAAU,UAAA,OAAAgJ,SAAA,CAAAlE,IAAA,gBAAgC,YAAAjE,MAAA,CAAAiK,qBAAA,CAAA9B,SAAA,CAAAlE,IAAA,EACjC,kBAAAjE,MAAA,CAAAkK,sBAAA,CAAA/B,SAAA,CAAAlE,IAAA,MAAAjE,MAAA,CAAAiK,qBAAA,CAAA9B,SAAA,CAAAlE,IAAA,EAC6C;IAEvCxF,EAAA,CAAAO,SAAA,EAAiC;IAAjCP,EAAA,CAAAU,UAAA,QAAAgJ,SAAA,CAAAlE,IAAA,gBAAiC;IAUhExF,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAU,UAAA,YAAAa,MAAA,CAAAmK,kBAAA,CAAAhC,SAAA,CAAAlE,IAAA,EAAAkE,SAAA,CAAAtD,OAAA,IAAApG,EAAA,CAAAqG,eAAA,KAAAkF,GAAA,GAAsD;IA0BxEvL,EAAA,CAAAO,SAAA,EAAsE;IAAtEP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAAmK,kBAAA,CAAAhC,SAAA,CAAAlE,IAAA,EAAAkE,SAAA,CAAAtD,OAAA,IAAApG,EAAA,CAAAqG,eAAA,KAAAkF,GAAA,GAAA9J,MAAA,OAAsE;;;;;IAuB9EzB,EAAA,CAAAC,cAAA,aAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,sBAAAkJ,SAAA,CAAAlE,IAAA,wCACF;;;;;IACAxF,EAAA,CAAAC,cAAA,aAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IADFH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAe,MAAA,CAAAoK,YAAA,CAAAjC,SAAA,CAAAlE,IAAA,qCACF;;;;;IACAxF,EAAA,CAAAC,cAAA,gBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAe,MAAA,CAAAoK,YAAA,CAAAjC,SAAA,CAAAlE,IAAA,eACF;;;;;;IAdFxF,EAHN,CAAAC,cAAA,eAA+D,eACiC,iBACpC,eAC7B;IACvBD,EAAA,CAAAI,SAAA,aAC6D;IAC/DJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA4B,cACI;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAOpDH,EANA,CAAAK,UAAA,IAAAuL,sDAAA,iBAAuE,IAAAC,sDAAA,iBAGF,KAAAC,0DAAA,oBAGI;IAG3E9L,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAE8D;IAFVD,EAAA,CAAA0B,UAAA,oBAAAqK,4EAAAC,MAAA;MAAAhM,EAAA,CAAA8B,aAAA,CAAAmK,IAAA;MAAA,MAAAvC,SAAA,GAAA1J,EAAA,CAAAiC,aAAA,GAAAD,SAAA;MAAA,MAAAT,MAAA,GAAAvB,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAqC,WAAA,CAAUd,MAAA,CAAA2K,YAAA,CAAAF,MAAA,EAAAtC,SAAA,CAAAlE,IAAA,CAAgC;IAAA,EAAC;IAKrGxF,EALM,CAAAG,YAAA,EAE8D,EACxD,EACJ,EACF;;;;;IAvByCH,EAAA,CAAAO,SAAA,EAAgD;IAAhDP,EAAA,CAAAa,WAAA,cAAAU,MAAA,CAAAoK,YAAA,CAAAjC,SAAA,CAAAlE,IAAA,MAAgD;IACpFxF,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAU,UAAA,QAAAgJ,SAAA,CAAAlE,IAAA,CAAkB;IAEAxF,EAAA,CAAAO,SAAA,GAA4D;IAC/EP,EADmB,CAAAa,WAAA,wBAAAU,MAAA,CAAAoK,YAAA,CAAAjC,SAAA,CAAAlE,IAAA,QAA4D,oBAAAjE,MAAA,CAAAoK,YAAA,CAAAjC,SAAA,CAAAlE,IAAA,MACzB;IAG1BxF,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAc,iBAAA,CAAA4I,SAAA,CAAAjE,KAAA,CAAiB;IACdzF,EAAA,CAAAO,SAAA,EAAoC;IAApCP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAAoK,YAAA,CAAAjC,SAAA,CAAAlE,IAAA,QAAoC;IAGpCxF,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAAoK,YAAA,CAAAjC,SAAA,CAAAlE,IAAA,MAAkC;IAG5DxF,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAAoK,YAAA,CAAAjC,SAAA,CAAAlE,IAAA,MAAkC;IAIxBxF,EAAA,CAAAO,SAAA,EAAiB;IAElCP,EAFiB,CAAAU,UAAA,OAAAgJ,SAAA,CAAAlE,IAAA,CAAiB,aAAAkE,SAAA,CAAAlE,IAAA,iBACK,WAAAkE,SAAA,CAAAlE,IAAA,qCACkB;;;;;IAMjExF,EAAA,CAAAI,SAAA,oBAGgB;;;;;;IAFyBJ,EADiB,CAAAU,UAAA,oBAAAgJ,SAAA,CAAAlE,IAAA,CAA8B,2BAAAkE,SAAA,CAAAjE,KAAA,CAChD,YAAAzF,EAAA,CAAA+E,eAAA,IAAAC,GAAA,IAAAnB,OAAA,GAAAtC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA3B,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAtC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA3B,OAAA,CAAAH,OAAA,GAEpC;;;;;IAMJ1D,EAAA,CAAAC,cAAA,UAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAkJ,SAAA,CAAAjE,KAAA,mBACF;;;;;IACAzF,EAAA,CAAAC,cAAA,UAA+D;IAC7DD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFJH,EAAA,CAAAO,SAAA,EAEF;IAFEP,EAAA,CAAAwG,kBAAA,MAAAkD,SAAA,CAAAjE,KAAA,yCAAA7D,OAAA,GAAAL,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA5D,OAAA,CAAAP,MAAA,kBAAAO,OAAA,CAAAP,MAAA,yBAAAO,OAAA,CAAAP,MAAA,QAAAoF,GAAA,OAEF;;;;;IACAzG,EAAA,CAAAC,cAAA,UAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,2BAAAkJ,SAAA,CAAAjE,KAAA,CAAA0G,WAAA,SACF;;;;;IAXFnM,EAAA,CAAAC,cAAA,cACmC;IAQjCD,EAPA,CAAAK,UAAA,IAAA+L,wDAAA,kBAAoE,IAAAC,wDAAA,kBAGL,IAAAC,wDAAA,kBAII;IAGrEtM,EAAA,CAAAG,YAAA,EAAM;;;;;;;;IAVEH,EAAA,CAAAO,SAAA,EAA4D;IAA5DP,EAAA,CAAAU,UAAA,UAAAkD,OAAA,GAAArC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA5B,OAAA,CAAAvC,MAAA,kBAAAuC,OAAA,CAAAvC,MAAA,aAA4D;IAG5DrB,EAAA,CAAAO,SAAA,EAAuD;IAAvDP,EAAA,CAAAU,UAAA,UAAAkB,OAAA,GAAAL,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA5D,OAAA,CAAAP,MAAA,kBAAAO,OAAA,CAAAP,MAAA,QAAuD;IAIvDrB,EAAA,CAAAO,SAAA,EAA2D;IAA3DP,EAAA,CAAAU,UAAA,UAAAmD,OAAA,GAAAtC,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA3B,OAAA,CAAAxC,MAAA,kBAAAwC,OAAA,CAAAxC,MAAA,YAA2D;;;;;IAhMjErB,EAFJ,CAAAC,cAAA,cAAiF,gBACa,WACpF;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9BH,EAAA,CAAAK,UAAA,IAAAkM,kDAAA,mBAAsD;IACxDvM,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAkF,uBAAA,OAAsC;IA6KpClF,EA3KA,CAAAK,UAAA,IAAAmM,iDAAA,oBAC6G,IAAAC,mDAAA,oBAiCtG,IAAAC,mDAAA,oBAMA,IAAAC,iDAAA,mBAGwC,KAAAC,kDAAA,qBAmBiB,KAAAC,kDAAA,qBAkFD,KAAAC,uDAAA,wBA8B1D;;IAIP9M,EAAA,CAAAK,UAAA,KAAA0M,kDAAA,kBACmC;IAYrC/M,EAAA,CAAAG,YAAA,EAAM;;;;;;IApMIH,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAc,iBAAA,CAAA4I,SAAA,CAAAjE,KAAA,CAAiB;IAChBzF,EAAA,CAAAO,SAAA,EAA4B;IAA5BP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAA8F,eAAA,CAAAqC,SAAA,EAA4B;IAEvB1J,EAAA,CAAAO,SAAA,EAAuB;IAAvBP,EAAA,CAAAU,UAAA,aAAAgJ,SAAA,CAAApC,IAAA,CAAuB;IAE7BtH,EAAA,CAAAO,SAAA,EAAwB;IAAxBP,EAAA,CAAAU,UAAA,4BAAwB;IA+BtBV,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAU,UAAA,wBAAoB;IAMpBV,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAU,UAAA,0BAAsB;IAMxBV,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAU,UAAA,0BAAsB;IAmBtBV,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAU,UAAA,+BAA2B;IAkF3BV,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAU,UAAA,wBAAoB;IA2BfV,EAAA,CAAAO,SAAA,EAAwB;IAAxBP,EAAA,CAAAU,UAAA,4BAAwB;IAO/BV,EAAA,CAAAO,SAAA,EAA4F;IAA5FP,EAAA,CAAAU,UAAA,WAAA6G,QAAA,GAAAhG,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA+B,QAAA,CAAA9D,OAAA,OAAA8D,QAAA,GAAAhG,MAAA,CAAAY,cAAA,GAAAG,GAAA,CAAAoH,SAAA,CAAAlE,IAAA,oBAAA+B,QAAA,CAAA7D,OAAA,EAA4F;;;;;IA1LtG1D,EAAA,CAAAC,cAAA,UAA6B;IAC3BD,EAAA,CAAAK,UAAA,IAAA2M,2CAAA,oBAAiF;IAuMnFhN,EAAA,CAAAG,YAAA,EAAM;;;;IAvMmBH,EAAA,CAAAO,SAAA,EAAkB;IAAAP,EAAlB,CAAAU,UAAA,YAAAa,MAAA,CAAAiI,aAAA,CAAkB,iBAAAjI,MAAA,CAAAkI,gBAAA,CAAyB;;;;;IA2MlEzJ,EADF,CAAAC,cAAA,eAAiF,cACxC;IACrCD,EAAA,CAAAI,SAAA,aAAuD;IAErDJ,EADF,CAAAC,cAAA,UAAK,cAC4B;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtDH,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAGjDF,EAHiD,CAAAG,YAAA,EAAI,EAC3C,EACF,EACF;;;;IAHgBH,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAc,iBAAA,CAAAS,MAAA,CAAA0L,mBAAA,GAA2B;;;;;;IAQ/CjN,EAAA,CAAAC,cAAA,kBACuG;IAArBD,EAAA,CAAA0B,UAAA,mBAAAwL,iEAAA;MAAAlN,EAAA,CAAA8B,aAAA,CAAAqL,IAAA;MAAA,MAAA5L,MAAA,GAAAvB,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAqC,WAAA,CAASd,MAAA,CAAA6L,QAAA,EAAU;IAAA,EAAC;IACpGpN,EAAA,CAAAI,SAAA,aAAsC;IACtCJ,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IACTH,EAAA,CAAAI,SAAA,eAAuD;;;;;;IAGvDJ,EAAA,CAAAC,cAAA,kBAC0D;IAArBD,EAAA,CAAA0B,UAAA,mBAAA2L,iEAAA;MAAArN,EAAA,CAAA8B,aAAA,CAAAwL,IAAA;MAAA,MAAA/L,MAAA,GAAAvB,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAqC,WAAA,CAASd,MAAA,CAAAgM,QAAA,EAAU;IAAA,EAAC;IACvDvN,EAAA,CAAAC,cAAA,gBAAyC;IACvCD,EAAA,CAAAE,MAAA,aACA;IAAAF,EAAA,CAAAI,SAAA,aAAkD;IAEtDJ,EADE,CAAAG,YAAA,EAAO,EACA;;;;IALPH,EAAA,CAAAU,UAAA,cAAAa,MAAA,CAAAiM,kBAAA,GAAkC;;;;;IAUlCxN,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAI,SAAA,aAAiC;IACjCJ,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,gBAAwE;IACtED,EAAA,CAAAI,SAAA,gBAA4F;IAC5FJ,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAVTH,EAAA,CAAAC,cAAA,kBAE4E;IAAvBD,EAAA,CAAA0B,UAAA,mBAAA+L,iEAAA;MAAAzN,EAAA,CAAA8B,aAAA,CAAA4L,IAAA;MAAA,MAAAnM,MAAA,GAAAvB,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAqC,WAAA,CAASd,MAAA,CAAAoM,UAAA,EAAY;IAAA,EAAC;IAKzE3N,EAJA,CAAAK,UAAA,IAAAuN,+CAAA,oBAA+D,IAAAC,+CAAA,oBAIS;IAI1E7N,EAAA,CAAAG,YAAA,EAAS;;;;IATPH,EAAA,CAAAU,UAAA,cAAAa,MAAA,CAAAiM,kBAAA,MAAAjM,MAAA,CAAAuM,YAAA,CAAkD;IAC3C9N,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAU,UAAA,UAAAa,MAAA,CAAAuM,YAAA,CAAmB;IAInB9N,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAU,UAAA,SAAAa,MAAA,CAAAuM,YAAA,CAAkB;;;ADtrBrC,OAAM,MAAOC,qBAAqB;EAoCtBC,EAAA;EACAC,oBAAA;EACAC,eAAA;EACAC,GAAA;EACAC,MAAA;EACAC,yBAAA;EAxCVC,UAAU,GAAGzO,cAAc,CAAC0O,WAAW;EACvCC,WAAW,GAAG3O,cAAc,CAAC4O,YAAY;EACzCC,MAAM;EACNC,QAAQ;EACRC,SAAS,GAAgB,EAAE;EAC3BpF,aAAa,GAAU,EAAE;EACzBhI,gBAAgB,GAAU,EAAE;EAC5BqN,aAAa,GAAG,KAAK;EAEbC,aAAa,GAAG,IAAIvP,eAAe,CAAmC,EAAE,CAAC;EACzEwP,YAAY,GAAG,IAAIxP,eAAe,CAAmC,EAAE,CAAC;EACxEyP,eAAe,GAAG,IAAIzP,eAAe,CAAmC,EAAE,CAAC;EAEnFoJ,OAAO,GAAG,IAAI,CAACmG,aAAa,CAACG,YAAY,EAAE;EAC3CnG,MAAM,GAAG,IAAI,CAACiG,YAAY,CAACE,YAAY,EAAE;EACzChG,SAAS,GAAG,IAAI,CAAC+F,eAAe,CAACC,YAAY,EAAE;EAE/CrG,cAAc,GAAkB,IAAI;EACpCF,gBAAgB,GAAW,EAAE;EAC7BK,cAAc,GAAkB,IAAI;EACpCF,gBAAgB,GAAW,EAAE;EAC7BG,mBAAmB,GAAW,EAAE;EAChCP,eAAe,GAAG,KAAK;EACvBqF,YAAY,GAAG,KAAK;EAEpBxK,0BAA0B,GAAGxD,4BAA4B;EACzDoP,WAAW,GAAGnP,YAAY;EAC1BoP,eAAe,GAAiB,EAAE;EAClCC,cAAc,GAA8B,EAAE;EAC9CC,SAAS,GAAGxP,cAAc,CAACyP,UAAU;EAErC;EACQC,aAAa,GAAoC,EAAE;EAE3DC,YACUxB,EAAe,EACfC,oBAA0C,EAC1CC,eAAgC,EAChCC,GAAsB,EACtBC,MAAc,EACdC,yBAAoD;IALpD,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,yBAAyB,GAAzBA,yBAAyB;EAChC;EAEHoB,QAAQA,CAAA;IACN,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACpD,MAAMC,IAAI,GAAGH,QAAQ,GAAGI,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC,GAAG,IAAI;IACnD,IAAI,CAAChB,MAAM,GAAGmB,IAAI,EAAEG,EAAE;IACtB,IAAI,CAACrB,QAAQ,GAAGkB,IAAI,EAAEI,IAAI;IAE1B,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAF,SAASA,CAAA;IACP,IAAI,CAACtB,SAAS,GAAGyB,KAAK,CAAC,IAAI,CAAC/B,UAAU,CAAC,CACpCgC,IAAI,CAAC,IAAI,CAAC,CACV3Q,GAAG,CAAC,MAAM,IAAI,CAACqO,EAAE,CAACuC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC/B,IAAI,CAAC3B,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAACZ,EAAE,CAACuC,KAAK,CAAC;MAChCC,mBAAmB,EAAE,CAAC,EAAE,EAAElR,UAAU,CAACmR,QAAQ,CAAC;MAC9CnJ,IAAI,EAAE,CAAC,EAAE,EAAEhI,UAAU,CAACmR,QAAQ,CAAC;MAC/BC,QAAQ,EAAE,CAAC,EAAE,EAAEpR,UAAU,CAACmR,QAAQ;KACnC,CAAC;EACJ;EAEAN,eAAeA,CAAA;IACb,IAAI,CAAC1H,eAAe,GAAG,IAAI;IAC3BjJ,QAAQ,CAAC;MACPmR,MAAM,EAAE,IAAI,CAACzC,eAAe,CAAC0C,SAAS,EAAE,CAACC,IAAI,CAC3ClR,GAAG,CAAEmR,QAAa,IAAI;QACpB,MAAMH,MAAM,GAAGG,QAAQ,CAACC,IAAI,IAAID,QAAQ;QACxCE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEH,QAAQ,CAAC;QACzC,OAAOH,MAAM,CAAChR,GAAG,CAAEuR,IAAS,KAAM;UAChC1O,GAAG,EAAE0O,IAAI,CAACC,OAAO;UACjB/O,KAAK,EAAE8O,IAAI,CAAClB;SACb,CAAC,CAAC;MACL,CAAC,CAAC,EACFtQ,UAAU,CAAE0R,KAAK,IAAI;QACnBJ,OAAO,CAACI,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CxR,IAAI,CAACyR,IAAI,CAAC,OAAO,EAAE,yBAAyB,EAAE,OAAO,CAAC;QACtD,OAAO5R,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;MACD6R,SAAS,EAAE,IAAI,CAACpD,eAAe,CAACqD,YAAY,EAAE,CAACV,IAAI,CACjDlR,GAAG,CAAEmR,QAAa,IAAI;QACpB,MAAMQ,SAAS,GAAGR,QAAQ,CAACC,IAAI,IAAID,QAAQ;QAC3CE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,QAAQ,CAAC;QAC7C,OAAOU,MAAM,CAACC,OAAO,CAACH,SAAS,CAAC,CAAC3R,GAAG,CAAC,CAAC,CAAC6C,GAAG,EAAEJ,KAAK,CAAC,MAAM;UACtDI,GAAG;UACHJ,KAAK,EAAEA;SACR,CAAC,CAAC;MACL,CAAC,CAAC,EACF1C,UAAU,CAAE0R,KAAK,IAAI;QACnBJ,OAAO,CAACI,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDxR,IAAI,CAACyR,IAAI,CAAC,OAAO,EAAE,6BAA6B,EAAE,OAAO,CAAC;QAC1D,OAAO5R,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC;KAEL,CAAC,CAACiS,SAAS,CAAC;MACXC,IAAI,EAAEA,CAAC;QAAEhB,MAAM;QAAEW;MAAS,CAAE,KAAI;QAC9B,IAAI,CAACxC,aAAa,CAAC6C,IAAI,CAAChB,MAAM,CAAC;QAC/B,IAAI,CAACxB,eAAe,GAAGmC,SAAS;QAChC,IAAI,CAAC7I,eAAe,GAAG,KAAK;QAC5B,IAAI,CAAC0F,GAAG,CAACyD,YAAY,EAAE;MACzB,CAAC;MACDR,KAAK,EAAGS,GAAG,IAAI;QACb,IAAI,CAACpJ,eAAe,GAAG,KAAK;QAC5BuI,OAAO,CAACI,KAAK,CAAC,6BAA6B,EAAES,GAAG,CAAC;QACjDjS,IAAI,CAACyR,IAAI,CAAC,OAAO,EAAE,8BAA8B,EAAE,OAAO,CAAC;QAC3D,IAAI,CAAClD,GAAG,CAACyD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEAxB,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC5B,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAAChF,aAAa,GAAG,IAAI,CAAC6E,yBAAyB,CAACyD,eAAe,CAAC,IAAI,CAACC,YAAY,EAAE,EAAE,IAAI,CAACvD,WAAW,EAAE,IAAI,CAAC;MAChH,MAAMwD,YAAY,GAAG,IAAI,CAACxI,aAAa,CAACyI,MAAM,CAAC,CAACC,GAAQ,EAAEC,KAAU,KAAI;QACtED,GAAG,CAACC,KAAK,CAAC3M,IAAI,CAAC,GAAG,CAAC2M,KAAK,CAAC7K,IAAI,KAAK,aAAa,GAAG,EAAE,GAAG,EAAE,EAAE6K,KAAK,CAACC,UAAU,IAAI,EAAE,CAAC;QAClF,OAAOF,GAAG;MACZ,CAAC,EAAE,EAAE,CAAC;MACN,IAAI,CAACtD,SAAS,CAAC,IAAI,CAACJ,WAAW,GAAG,CAAC,CAAC,GAAG,IAAI,CAACR,EAAE,CAACuC,KAAK,CAACyB,YAAY,CAAC;MAClE,IAAI,IAAI,CAACxD,WAAW,KAAK,CAAC,EAAE;QAC1B,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,CAACyD,UAAU,CAAC,QAAQ,EAAE,IAAI,CAACrE,EAAE,CAACsE,OAAO,CAAC,EAAE,EAAEhT,UAAU,CAACmR,QAAQ,CAAC,CAAC;QAChF,IAAI,CAAC7B,SAAS,CAAC,CAAC,CAAC,CAACyD,UAAU,CAAC,QAAQ,EAAE,IAAI,CAACrE,EAAE,CAACsE,OAAO,CAAC,EAAE,EAAEhT,UAAU,CAACmR,QAAQ,CAAC,CAAC;QAChF,IAAI,CAAC7B,SAAS,CAAC,CAAC,CAAC,CAACyD,UAAU,CAAC,WAAW,EAAE,IAAI,CAACrE,EAAE,CAACsE,OAAO,CAAC,EAAE,CAAC,CAAC;QAC9D,IAAI,CAAC1D,SAAS,CAAC,CAAC,CAAC,CAACyD,UAAU,CAC1B,cAAc,EACd,IAAI,CAACrE,EAAE,CAACsE,OAAO,CAAC,EAAE,EAAE,IAAI,CAACpJ,0BAA0B,EAAE,GAAG5J,UAAU,CAACmR,QAAQ,GAAG,IAAI,CAAC,CACpF;QAED;QACA,IAAI,CAAC,IAAI,CAACvH,0BAA0B,EAAE,EAAE;UACtC;UACA,IAAI,IAAI,CAACI,4BAA4B,EAAE,IAAI,IAAI,CAACC,4BAA4B,EAAE,EAAE;YAC9E,IAAI,CAACqF,SAAS,CAAC,CAAC,CAAC,CAACyD,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAACrE,EAAE,CAACsE,OAAO,CAAC,EAAE,EAAEhT,UAAU,CAACmR,QAAQ,CAAC,CAAC;YACzF,IAAI,CAAC7B,SAAS,CAAC,CAAC,CAAC,CAACyD,UAAU,CAAC,aAAa,EAAE,IAAI,CAACrE,EAAE,CAACsE,OAAO,CAAC,EAAE,CAAC,CAAC;UAClE;UACA;UACA,IAAI,IAAI,CAACC,gCAAgC,EAAE,EAAE;YAC3C,IAAI,CAAC3D,SAAS,CAAC,CAAC,CAAC,CAACyD,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAACrE,EAAE,CAACsE,OAAO,CAAC,EAAE,CAAC,CAAC;UAC1E;UACA;QACF;MACF;IACF,CAAC,MAAM;MACL,IAAI,CAAC9I,aAAa,GAAG,CACnB;QACEhE,IAAI,EAAE,qBAAqB;QAC3B8B,IAAI,EAAE,QAAQ;QACd7B,KAAK,EAAE,sBAAsB;QAC7BW,OAAO,EAAE,IAAI,CAAC9C,0BAA0B;QACxC8O,UAAU,EAAE,CAAC9S,UAAU,CAACmR,QAAQ;OACjC,EACD;QACEjL,IAAI,EAAE,MAAM;QACZ8B,IAAI,EAAE,QAAQ;QACd7B,KAAK,EAAE,MAAM;QACbW,OAAO,EAAE,IAAI,CAACzC,sBAAsB,EAAE;QACtCyO,UAAU,EAAE,CAAC9S,UAAU,CAACmR,QAAQ;OACjC,EACD;QACEjL,IAAI,EAAE,UAAU;QAChB8B,IAAI,EAAE,QAAQ;QACd7B,KAAK,EAAE,WAAW;QAClBW,OAAO,EAAE,IAAI,CAACtC,0BAA0B,EAAE;QAC1CsO,UAAU,EAAE,CAAC9S,UAAU,CAACmR,QAAQ;OACjC,CACF;IACH;IACA,IAAI,CAACtC,GAAG,CAACyD,YAAY,EAAE;EACzB;EAEAxI,2BAA2BA,CAAA;IAEzB,MAAMoJ,KAAK,GAAG,IAAI,CAAC5D,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,qBAAqB,CAAC,EAAEF,KAAK;IACjE,MAAMkF,IAAI,GAAG,IAAI,CAACsH,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,MAAM,CAAC,EAAEF,KAAK;IACjD,MAAMqQ,YAAY,GAAG,CACnB,wBAAwB,EACxB,yBAAyB,CAC1B;IACD,MAAMC,UAAU,GAAG,CACjB,MAAM,EACN,YAAY,CACb;IAED,OAAOD,YAAY,CAAC/K,QAAQ,CAAC8K,KAAK,CAAC,IAAIE,UAAU,CAAChL,QAAQ,CAACJ,IAAI,CAAC;EAClE;EAEA+B,8BAA8BA,CAAA;IAE5B,MAAMmJ,KAAK,GAAG,IAAI,CAAC5D,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,qBAAqB,CAAC,EAAEF,KAAK;IACjE,MAAMkF,IAAI,GAAG,IAAI,CAACsH,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,MAAM,CAAC,EAAEF,KAAK;IACjD,MAAMqQ,YAAY,GAAG,CACnB,yBAAyB,CAC1B;IACD,MAAMC,UAAU,GAAG,CACjB,UAAU,CACX;IAED,OAAOD,YAAY,CAAC/K,QAAQ,CAAC8K,KAAK,CAAC,IAAIE,UAAU,CAAChL,QAAQ,CAACJ,IAAI,CAAC;EAClE;EAEAgC,4BAA4BA,CAAA;IAC1B,MAAMkJ,KAAK,GAAG,IAAI,CAAC5D,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,qBAAqB,CAAC,EAAEF,KAAK;IACjE,MAAMkF,IAAI,GAAG,IAAI,CAACsH,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,MAAM,CAAC,EAAEF,KAAK;IACjD,MAAMqQ,YAAY,GAAG,CACnB,0BAA0B,CAC3B;IACD,MAAMC,UAAU,GAAG,CACjB,UAAU,CACX;IAED,OAAOD,YAAY,CAAC/K,QAAQ,CAAC8K,KAAK,CAAC,IAAIE,UAAU,CAAChL,QAAQ,CAACJ,IAAI,CAAC;EAClE;EAEAqL,8BAA8BA,CAAA;IAC5B,MAAMH,KAAK,GAAG,IAAI,CAAC5D,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,qBAAqB,CAAC,EAAEF,KAAK;IACjE,MAAMkF,IAAI,GAAG,IAAI,CAACsH,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,MAAM,CAAC,EAAEF,KAAK;IACjD,MAAMqQ,YAAY,GAAG,CACnB,0BAA0B,CAC3B;IACD,MAAMC,UAAU,GAAG,CACjB,SAAS,CACV;IAED,OAAOD,YAAY,CAAC/K,QAAQ,CAAC8K,KAAK,CAAC,IAAIE,UAAU,CAAChL,QAAQ,CAACJ,IAAI,CAAC;EAClE;EAEAiC,4BAA4BA,CAAA;IAC1B,MAAMiJ,KAAK,GAAG,IAAI,CAAC5D,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,qBAAqB,CAAC,EAAEF,KAAK;IACjE,MAAMkF,IAAI,GAAG,IAAI,CAACsH,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,MAAM,CAAC,EAAEF,KAAK;IACjD,MAAMqQ,YAAY,GAAG,CACnB,gCAAgC,CACjC;IACD,MAAMC,UAAU,GAAG,CACjB,MAAM,CACP;IAED,OAAOD,YAAY,CAAC/K,QAAQ,CAAC8K,KAAK,CAAC,IAAIE,UAAU,CAAChL,QAAQ,CAACJ,IAAI,CAAC;EAClE;EAEAiL,gCAAgCA,CAAA;IAC9B,MAAMC,KAAK,GAAG,IAAI,CAAC5D,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,qBAAqB,CAAC,EAAEF,KAAK;IACjE,MAAMkF,IAAI,GAAG,IAAI,CAACsH,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,MAAM,CAAC,EAAEF,KAAK;IACjD,MAAMqQ,YAAY,GAAG,CACnB,gCAAgC,CACjC;IACD,MAAMC,UAAU,GAAG,CACjB,YAAY,CACb;IAED,OAAOD,YAAY,CAAC/K,QAAQ,CAAC8K,KAAK,CAAC,IAAIE,UAAU,CAAChL,QAAQ,CAACJ,IAAI,CAAC;EAClE;EAEAsL,gBAAgBA,CAAA;IACd,MAAMtL,IAAI,GAAG,IAAI,CAACsH,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,MAAM,CAAC,EAAEF,KAAK;IACjD,MAAMsO,QAAQ,GAAG,IAAI,CAAC9B,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,UAAU,CAAC,EAAEF,KAAK;IACzD,MAAMyQ,aAAa,GAAG,CAAC,sBAAsB,EAAE,iBAAiB,EAAE,YAAY,EAAE,mBAAmB,CAAC;IACpG,MAAMC,aAAa,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAACpL,QAAQ,CAACJ,IAAI,CAAC;IACzD,OAAOuL,aAAa,CAACnL,QAAQ,CAACgJ,QAAQ,CAAC,IAAIoC,aAAa,IAAI,CAAC,IAAI,CAAC5J,0BAA0B,EAAE;EAChG;EAEA6J,wBAAwBA,CAAA;IACtB,MAAMzL,IAAI,GAAG,IAAI,CAACsH,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,MAAM,CAAC,EAAEF,KAAK;IACjD,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAACsF,QAAQ,CAACJ,IAAI,CAAC;EAC5C;EAEA0L,cAAcA,CAAA;IACZ,MAAMtC,QAAQ,GAAG,IAAI,CAAC9B,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,UAAU,CAAC,EAAEF,KAAK;IACzD,MAAM6Q,eAAe,GAAG,CAAC,gBAAgB,EAAE,SAAS,CAAC;IACrD,OAAOA,eAAe,CAACvL,QAAQ,CAACgJ,QAAQ,CAAC;EAC3C;EAEAtF,0BAA0BA,CAAA;IACxB,MAAM+G,KAAK,GAAG,IAAI,CAAC3I,aAAa,CAAC0J,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC3N,IAAI,KAAK,kBAAkB,CAAC;IAC3E,IAAI,CAAC2M,KAAK,EAAE,OAAO,EAAE;IACrB,OAAO,IAAI,CAAC7G,eAAe,CAAC,kBAAkB,EAAE6G,KAAK,CAAC/L,OAAO,CAAC;EAChE;EAEAuE,mBAAmBA,CAACyI,cAAsB;IACxC,OAAO,IAAI,CAACvI,2BAA2B,CAAC,kBAAkB,EAAEuI,cAAc,CAAC;EAC7E;EAEA5I,eAAeA,CAAC4I,cAAsB;IACpC,IAAI,CAAC1I,iBAAiB,CAAC,kBAAkB,EAAE0I,cAAc,CAAC;EAC5D;EAEA/H,4BAA4BA,CAAA;IAC1B,MAAM8G,KAAK,GAAG,IAAI,CAAC3I,aAAa,CAAC0J,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC3N,IAAI,KAAK,eAAe,CAAC;IACxE,IAAI,CAAC2M,KAAK,EAAE,OAAO,EAAE;IACrB,OAAO,IAAI,CAAC7G,eAAe,CAAC,eAAe,EAAE6G,KAAK,CAAC/L,OAAO,CAAC;EAC7D;EAEAwE,sBAAsBA,CAACyI,YAAoB;IACzC,OAAO,IAAI,CAACxI,2BAA2B,CAAC,eAAe,EAAEwI,YAAY,CAAC;EACxE;EAEA5I,kBAAkBA,CAAC4I,YAAoB;IACrC,IAAI,CAAC3I,iBAAiB,CAAC,eAAe,EAAE2I,YAAY,CAAC;EACvD;EAEAC,sBAAsBA,CAACC,KAAU;IAC/B,MAAMpB,KAAK,GAAG,IAAI,CAAC3I,aAAa,CAAC0J,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC3N,IAAI,KAAK,kBAAkB,CAAC;IAC3E,IAAI,CAAC2M,KAAK,EAAE;IACZ,MAAMqB,SAAS,GAAGD,KAAK,CAACE,MAAM,CAACC,OAAO;IACtC,MAAMC,aAAa,GAAGH,SAAS,GAAGrB,KAAK,CAAC/L,OAAO,CAACzG,GAAG,CAAEiU,GAAQ,IAAKA,GAAG,CAACxR,KAAK,CAAC,GAAG,EAAE;IACjF,IAAI,CAACwM,SAAS,CAAC,IAAI,CAACJ,WAAW,GAAG,CAAC,CAAC,CAACqF,UAAU,CAAC;MAAEC,gBAAgB,EAAEH;IAAa,CAAE,CAAC;IACpF,IAAI,CAACxF,GAAG,CAACyD,YAAY,EAAE;EACzB;EAEEtG,eAAeA,CAACyI,SAAiB,EAAE3N,OAAc;IACjD,MAAM4N,aAAa,GAAG,IAAI,CAAC7R,cAAc,EAAE,CAACG,GAAG,CAACyR,SAAS,CAAC,EAAE3R,KAAK,IAAI,EAAE;IACvE,MAAM6R,eAAe,GAAG7N,OAAO,CAAC8N,MAAM,CAAEN,GAAG,IAAKI,aAAa,CAACtM,QAAQ,CAACkM,GAAG,CAACxR,KAAK,CAAC,CAAC;IAClF,IAAI6R,eAAe,CAACxS,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAC3C,IAAIwS,eAAe,CAACxS,MAAM,KAAK,CAAC,EAAE,OAAOwS,eAAe,CAAC,CAAC,CAAC,CAACzR,GAAG;IAC/D,OAAO,GAAGyR,eAAe,CAACxS,MAAM,iBAAiB;EACnD;EAEAoJ,2BAA2BA,CAACkJ,SAAiB,EAAE3R,KAAa;IAC1D,MAAM4R,aAAa,GAAG,IAAI,CAAC7R,cAAc,EAAE,CAACG,GAAG,CAACyR,SAAS,CAAC,EAAE3R,KAAK,IAAI,EAAE;IACvE,OAAO4R,aAAa,CAACtM,QAAQ,CAACtF,KAAK,CAAC;EACtC;EAEAsI,iBAAiBA,CAACqJ,SAAiB,EAAE3R,KAAa;IAChD,MAAM4R,aAAa,GAAG,IAAI,CAAC7R,cAAc,EAAE,CAACG,GAAG,CAACyR,SAAS,CAAC,EAAE3R,KAAK,IAAI,EAAE;IACvE,IAAIuR,aAAa;IAEjB,IAAIK,aAAa,CAACtM,QAAQ,CAACtF,KAAK,CAAC,EAAE;MACjCuR,aAAa,GAAGK,aAAa,CAACE,MAAM,CAAEC,IAAY,IAAKA,IAAI,KAAK/R,KAAK,CAAC;IACxE,CAAC,MAAM;MACLuR,aAAa,GAAG,CAAC,GAAGK,aAAa,EAAE5R,KAAK,CAAC;IAC3C;IAEA,IAAI,CAACD,cAAc,EAAE,CAAC0R,UAAU,CAAC;MAAE,CAACE,SAAS,GAAGJ;IAAa,CAAE,CAAC;IAChE,IAAI,CAACxF,GAAG,CAACyD,YAAY,EAAE;EACzB;EAEA;EACAxH,gBAAgBA,CAAC2J,SAAiB;IAChC,MAAMC,aAAa,GAAG,IAAI,CAAC7R,cAAc,EAAE,CAACG,GAAG,CAACyR,SAAS,CAAC,EAAE3R,KAAK,IAAI,EAAE;IACvE,OAAO4R,aAAa,CAACvS,MAAM;EAC7B;EAEA2S,cAAcA,CAACL,SAAiB;IAC9B,OAAO,IAAI,CAACxE,aAAa,CAACwE,SAAS,CAAC,IAAI,EAAE;EAC5C;EAEAM,iBAAiBA,CAACN,SAAiB,EAAER,KAAU;IAC7C,IAAI,CAAChE,aAAa,CAACwE,SAAS,CAAC,GAAGR,KAAK,CAACE,MAAM,CAACrR,KAAK;IAClD,IAAI,CAAC+L,GAAG,CAACyD,YAAY,EAAE;EACzB;EAEA0C,WAAWA,CAACP,SAAiB;IAC3B,IAAI,CAACxE,aAAa,CAACwE,SAAS,CAAC,GAAG,EAAE;IAClC,IAAI,CAAC5F,GAAG,CAACyD,YAAY,EAAE;EACzB;EAEAlG,kBAAkBA,CAACqI,SAAiB,EAAE3N,OAAc;IAClD,MAAMmO,WAAW,GAAG,IAAI,CAACH,cAAc,CAACL,SAAS,CAAC,CAAC5H,WAAW,EAAE;IAChE,IAAI,CAACoI,WAAW,EAAE;MAChB,OAAOnO,OAAO;IAChB;IACA,OAAOA,OAAO,CAAC8N,MAAM,CAACM,MAAM,IAC1BA,MAAM,CAAChS,GAAG,CAAC2J,WAAW,EAAE,CAACzE,QAAQ,CAAC6M,WAAW,CAAC,CAC/C;EACH;EAEA/I,qBAAqBA,CAACuI,SAAiB;IACrC,MAAMC,aAAa,GAAG,IAAI,CAAC7R,cAAc,EAAE,CAACG,GAAG,CAACyR,SAAS,CAAC,EAAE3R,KAAK,IAAI,EAAE;IACvE,MAAMqS,cAAc,GAAG,IAAI,CAAC/I,kBAAkB,CAACqI,SAAS,EAAE,IAAI,CAACW,kBAAkB,CAACX,SAAS,CAAC,CAAC;IAC7F,OAAOU,cAAc,CAAChT,MAAM,GAAG,CAAC,IAAIgT,cAAc,CAACE,KAAK,CAACH,MAAM,IAAIR,aAAa,CAACtM,QAAQ,CAAC8M,MAAM,CAACpS,KAAK,CAAC,CAAC;EAC1G;EAEAqJ,sBAAsBA,CAACsI,SAAiB;IACtC,MAAMC,aAAa,GAAG,IAAI,CAAC7R,cAAc,EAAE,CAACG,GAAG,CAACyR,SAAS,CAAC,EAAE3R,KAAK,IAAI,EAAE;IACvE,MAAMqS,cAAc,GAAG,IAAI,CAAC/I,kBAAkB,CAACqI,SAAS,EAAE,IAAI,CAACW,kBAAkB,CAACX,SAAS,CAAC,CAAC;IAC7F,OAAOU,cAAc,CAACG,IAAI,CAACJ,MAAM,IAAIR,aAAa,CAACtM,QAAQ,CAAC8M,MAAM,CAACpS,KAAK,CAAC,CAAC;EAC5E;EAEA6I,eAAeA,CAAC8I,SAAiB;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAAC7R,cAAc,EAAE,CAACG,GAAG,CAACyR,SAAS,CAAC,EAAE3R,KAAK,IAAI,EAAE;IACvE,MAAMqS,cAAc,GAAG,IAAI,CAAC/I,kBAAkB,CAACqI,SAAS,EAAE,IAAI,CAACW,kBAAkB,CAACX,SAAS,CAAC,CAAC;IAE7F,IAAI,IAAI,CAACvI,qBAAqB,CAACuI,SAAS,CAAC,EAAE;MACzC;MACA,MAAMJ,aAAa,GAAGK,aAAa,CAACE,MAAM,CAAE9R,KAAa,IACvD,CAACqS,cAAc,CAACG,IAAI,CAACJ,MAAM,IAAIA,MAAM,CAACpS,KAAK,KAAKA,KAAK,CAAC,CACvD;MACD,IAAI,CAACD,cAAc,EAAE,CAAC0R,UAAU,CAAC;QAAE,CAACE,SAAS,GAAGJ;MAAa,CAAE,CAAC;IAClE,CAAC,MAAM;MACL;MACA,MAAMkB,SAAS,GAAGJ,cAAc,CAAC9U,GAAG,CAAC6U,MAAM,IAAIA,MAAM,CAACpS,KAAK,CAAC;MAC5D,MAAMuR,aAAa,GAAG,CAAC,GAAG,IAAImB,GAAG,CAAC,CAAC,GAAGd,aAAa,EAAE,GAAGa,SAAS,CAAC,CAAC,CAAC;MACpE,IAAI,CAAC1S,cAAc,EAAE,CAAC0R,UAAU,CAAC;QAAE,CAACE,SAAS,GAAGJ;MAAa,CAAE,CAAC;IAClE;IACA,IAAI,CAACxF,GAAG,CAACyD,YAAY,EAAE;EACzB;EAEAmD,kBAAkBA,CAAChB,SAAiB;IAClC,IAAI,CAAC5R,cAAc,EAAE,CAAC0R,UAAU,CAAC;MAAE,CAACE,SAAS,GAAG;IAAE,CAAE,CAAC;IACrD,IAAI,CAAC5F,GAAG,CAACyD,YAAY,EAAE;EACzB;EAEAoD,kBAAkBA,CAACjB,SAAiB,EAAE3N,OAAc;IAClD,MAAM4N,aAAa,GAAG,IAAI,CAAC7R,cAAc,EAAE,CAACG,GAAG,CAACyR,SAAS,CAAC,EAAE3R,KAAK,IAAI,EAAE;IACvE,OAAOgE,OAAO,CAAC8N,MAAM,CAACM,MAAM,IAAIR,aAAa,CAACtM,QAAQ,CAAC8M,MAAM,CAACpS,KAAK,CAAC,CAAC;EACvE;EAEQsS,kBAAkBA,CAACX,SAAiB;IAC1C,MAAM5B,KAAK,GAAG,IAAI,CAAC3I,aAAa,CAAC0J,IAAI,CAAC+B,GAAG,IAAIA,GAAG,CAACzP,IAAI,KAAKuO,SAAS,CAAC;IACpE,OAAO5B,KAAK,EAAE/L,OAAO,IAAI,EAAE;EAC7B;EAEA/C,OAAOA,CAAC+C,OAAyC,EAAEhE,KAAa;IAC9D,MAAM+R,IAAI,GAAG/N,OAAO,CAAC8M,IAAI,CAAEiB,IAAI,IAAKA,IAAI,CAAC/R,KAAK,KAAKA,KAAK,CAAC;IACzD,OAAO+R,IAAI,GAAGA,IAAI,CAAC3R,GAAG,GAAG,EAAE;EAC7B;EAEAN,MAAMA,CAACgT,IAAe,EAAEjU,KAAa,EAAEmB,KAAa;IAClD8S,IAAI,CAACrB,UAAU,CAAC;MAAE,CAAC5S,KAAK,GAAGmB;IAAK,CAAE,CAAC;IACnC8S,IAAI,CAAC5S,GAAG,CAACrB,KAAK,CAAC,EAAEsB,aAAa,EAAE;IAChC2S,IAAI,CAAC5S,GAAG,CAACrB,KAAK,CAAC,EAAEkU,sBAAsB,EAAE;IACzC,IAAI,CAAChH,GAAG,CAACyD,YAAY,EAAE;IAEvB;IACA,IAAI3Q,KAAK,KAAK,qBAAqB,EAAE;MACnC;MACAiU,IAAI,CAACrB,UAAU,CAAC;QAAEvM,IAAI,EAAE,EAAE;QAAEoJ,QAAQ,EAAE;MAAE,CAAE,CAAC;MAC3CwE,IAAI,CAAC5S,GAAG,CAAC,MAAM,CAAC,EAAE8S,eAAe,EAAE;MACnCF,IAAI,CAAC5S,GAAG,CAAC,UAAU,CAAC,EAAE8S,eAAe,EAAE;MACvC;MACA,IAAI,CAAChF,cAAc,EAAE;IACvB,CAAC,MAAM,IAAInP,KAAK,KAAK,MAAM,EAAE;MAC3B;MACAiU,IAAI,CAACrB,UAAU,CAAC;QAAEnD,QAAQ,EAAE;MAAE,CAAE,CAAC;MACjCwE,IAAI,CAAC5S,GAAG,CAAC,UAAU,CAAC,EAAE8S,eAAe,EAAE;IACzC;EACF;EAEAhR,cAAcA,CAAC2P,SAAiB,EAAE3R,KAAa,EAAEoD,IAAY;IAC3D,IAAIuO,SAAS,KAAK,QAAQ,EAAE;MAC1B,IAAI,CAACsB,UAAU,CAACjT,KAAK,EAAEoD,IAAI,CAAC;IAC9B,CAAC,MAAM,IAAIuO,SAAS,KAAK,QAAQ,EAAE;MACjC,IAAI,CAACuB,UAAU,CAAClT,KAAK,EAAEoD,IAAI,CAAC;IAC9B,CAAC,MAAM,IAAIuO,SAAS,KAAK,WAAW,EAAE;MACpC,IAAI,CAACwB,aAAa,CAACnT,KAAK,EAAEoD,IAAI,CAAC;IACjC;EACF;EAEAgQ,aAAaA,CAACpP,OAAoD;IAChE,OAAQkM,OAAwB,IAA6B;MAC3D,MAAMmD,IAAI,GAAGnD,OAAO,CAAClQ,KAAK;MAC1B,IAAI,CAACqT,IAAI,EAAE,OAAO,IAAI;MAEtB,IAAIA,IAAI,CAACC,IAAI,GAAGtP,OAAO,CAACuP,OAAO,EAAE;QAC/B,OAAO;UAAEA,OAAO,EAAE;QAAI,CAAE;MAC1B;MAEA,IAAI,CAACvP,OAAO,CAACwP,YAAY,CAAClO,QAAQ,CAAC+N,IAAI,CAACnO,IAAI,CAAC,EAAE;QAC7C,OAAO;UAAEuO,WAAW,EAAE;QAAI,CAAE;MAC9B;MAEA,OAAO,IAAI;IACb,CAAC;EACH;EAEAC,kBAAkBA,CAAC1P,OAAoD;IACrE,OAAQkM,OAAwB,IAA6B;MAC3D,MAAMyD,KAAK,GAAGzD,OAAO,CAAClQ,KAAK;MAE3B,IAAI,CAACiO,KAAK,CAAC2F,OAAO,CAACD,KAAK,CAAC,EAAE;QACzB,OAAO;UAAEE,QAAQ,EAAE;QAAI,CAAE;MAC3B;MAEA,KAAK,MAAMR,IAAI,IAAIM,KAAK,EAAE;QACxB,IAAIN,IAAI,CAACC,IAAI,GAAGtP,OAAO,CAACuP,OAAO,EAAE;UAC/B,OAAO;YAAEA,OAAO,EAAE;UAAI,CAAE;QAC1B;QACA,IAAI,CAACvP,OAAO,CAACwP,YAAY,CAAClO,QAAQ,CAAC+N,IAAI,CAACnO,IAAI,CAAC,EAAE;UAC7C,OAAO;YAAEuO,WAAW,EAAE;UAAI,CAAE;QAC9B;MACF;MAEA,OAAO,IAAI;IACb,CAAC;EACH;EAEA1M,QAAQA,CAAA;IACN,OAAO,IAAI,CAACwF,QAAQ,IAAI,QAAQ;EAClC;EAEAzF,0BAA0BA,CAAA;IACxB,MAAMsJ,KAAK,GAAG,IAAI,CAAC5D,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,qBAAqB,CAAC,EAAEF,KAAK;IACjE,MAAMqQ,YAAY,GAAG,CACnB,yBAAyB,EACzB,wBAAwB,EACxB,yBAAyB,CAC1B;IACD,OAAOA,YAAY,CAAC/K,QAAQ,CAAC8K,KAAK,CAAC;EACrC;EAEA0D,2BAA2BA,CAAA;IACzB,MAAM1D,KAAK,GAAG,IAAI,CAAC5D,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,qBAAqB,CAAC,EAAEF,KAAK;IACjE,MAAMqQ,YAAY,GAAG,CACnB,gCAAgC,EAChC,0BAA0B,CAC3B;IACD,OAAOA,YAAY,CAAC/K,QAAQ,CAAC8K,KAAK,CAAC;EACrC;EAEA6C,UAAUA,CAACc,MAAc,EAAEC,QAAgB;IACzC,IAAI,CAACxN,cAAc,GAAGuN,MAAM;IAC5B,IAAI,CAACzN,gBAAgB,GAAG0N,QAAQ;IAChC,IAAI,CAACjU,cAAc,EAAE,CAAC0R,UAAU,CAAC;MAAEsC,MAAM;MAAEE,MAAM,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAE,CAAE,CAAC;IACvE,IAAI,CAACvH,YAAY,CAAC4C,IAAI,CAAC,EAAE,CAAC;IAC1B,IAAI,CAAC3C,eAAe,CAAC2C,IAAI,CAAC,EAAE,CAAC;IAC7B,IAAI,CAAC5I,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACF,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACG,mBAAmB,GAAG,EAAE;IAE7B,IAAI,CAACkF,eAAe,CACjBqI,QAAQ,CAACJ,MAAM,CAAC,CAChBtF,IAAI,CACHlR,GAAG,CAAEmR,QAAa,IAAI;MACpB,MAAM0F,KAAK,GAAG1F,QAAQ,CAACC,IAAI,IAAID,QAAQ;MACvC,OAAO0F,KAAK,CAAC7W,GAAG,CAAE8W,IAAS,KAAM;QAC/BjU,GAAG,EAAEiU,IAAI,CAACtF,OAAO;QACjB/O,KAAK,EAAEqU,IAAI,CAACzG;OACb,CAAC,CAAC;IACL,CAAC,CAAC,EACFtQ,UAAU,CAAE0R,KAAK,IAAI;MACnBJ,OAAO,CAACI,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CxR,IAAI,CAACyR,IAAI,CAAC,OAAO,EAAE,wBAAwB,EAAE,OAAO,CAAC;MACrD,OAAO5R,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAiS,SAAS,CAAE8E,KAAK,IAAI;MACnBxF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEuF,KAAK,CAAC;MACpC,IAAI,CAACzH,YAAY,CAAC4C,IAAI,CAAC6E,KAAK,CAAC;MAC7B,IAAI,CAACrI,GAAG,CAACyD,YAAY,EAAE;IACzB,CAAC,CAAC;EACN;EAEA0D,UAAUA,CAACe,MAAc,EAAEK,QAAgB;IACzC,IAAI,CAAC3N,cAAc,GAAGsN,MAAM;IAC5B,IAAI,CAACxN,gBAAgB,GAAG6N,QAAQ;IAChC,IAAI,CAACvU,cAAc,EAAE,CAAC0R,UAAU,CAAC;MAAEwC,MAAM;MAAEC,SAAS,EAAE;IAAE,CAAE,CAAC;IAC3D,IAAI,CAACtH,eAAe,CAAC2C,IAAI,CAAC,EAAE,CAAC;IAC7B,IAAI,CAAC3I,mBAAmB,GAAG,EAAE;IAE7B,IAAI,CAACkF,eAAe,CACjByI,WAAW,CAACN,MAAM,CAAC,CACnBxF,IAAI,CACHlR,GAAG,CAAEmR,QAAa,IAAI;MACpB,MAAM8F,QAAQ,GAAG9F,QAAQ,CAACC,IAAI,IAAID,QAAQ;MAC1C,OAAO8F,QAAQ,CAACjX,GAAG,CAAEkX,OAAY,KAAM;QACrCrU,GAAG,EAAEqU,OAAO,CAAC1F,OAAO;QACpB/O,KAAK,EAAEyU,OAAO,CAAC7G;OAChB,CAAC,CAAC;IACL,CAAC,CAAC,EACFtQ,UAAU,CAAE0R,KAAK,IAAI;MACnBJ,OAAO,CAACI,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDxR,IAAI,CAACyR,IAAI,CAAC,OAAO,EAAE,4BAA4B,EAAE,OAAO,CAAC;MACzD,OAAO5R,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAiS,SAAS,CAAEkF,QAAQ,IAAI;MACtB,IAAI,CAAC5H,eAAe,CAAC2C,IAAI,CAACiF,QAAQ,CAAC;MACnC,IAAI,CAACzI,GAAG,CAACyD,YAAY,EAAE;IACzB,CAAC,CAAC;EACN;EAEA2D,aAAaA,CAACe,SAAiB,EAAEQ,WAAmB;IAClD,IAAI,CAAC9N,mBAAmB,GAAG8N,WAAW;IACtC,IAAI,CAAC3U,cAAc,EAAE,CAAC0R,UAAU,CAAC;MAAEyC;IAAS,CAAE,CAAC;IAC/C,IAAI,CAACnI,GAAG,CAACyD,YAAY,EAAE;EACzB;EAEAG,YAAYA,CAAA;IACV,MAAMgF,WAAW,GAAG,IAAI,CAACnI,SAAS,CAAC,CAAC,CAAC,CAACxM,KAAK;IAC3C,OAAO,GAAG2U,WAAW,CAACvG,mBAAmB,IAAIuG,WAAW,CAACzP,IAAI,IAAIyP,WAAW,CAACrG,QAAQ,EAAE;EACzF;EAEA/M,sBAAsBA,CAAA;IACpB,MAAM6O,KAAK,GAAG,IAAI,CAAC5D,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,qBAAqB,CAAC,EAAEF,KAAK;IACjE,IAAIoQ,KAAK,EAAE9K,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACwH,WAAW,CAACgF,MAAM,CAAE8C,CAAC,IAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAACtP,QAAQ,CAACsP,CAAC,CAAC5U,KAAK,CAAC,CAAC;IAClF,CAAC,MAAM,IAAIoQ,KAAK,EAAE9K,QAAQ,CAAC,eAAe,CAAC,IAAI8K,KAAK,EAAE9K,QAAQ,CAAC,SAAS,CAAC,IAAI8K,KAAK,EAAE9K,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACtG,OAAO,IAAI,CAACwH,WAAW,CAACgF,MAAM,CAAE8C,CAAC,IAAK,CAAC,YAAY,EAAE,MAAM,CAAC,CAACtP,QAAQ,CAACsP,CAAC,CAAC5U,KAAK,CAAC,CAAC;IACjF;IACA,OAAO,IAAI,CAAC8M,WAAW;EACzB;EAEEpL,0BAA0BA,CAAA;IAC1B,MAAM0O,KAAK,GAAG,IAAI,CAAC5D,SAAS,CAAC,CAAC,CAAC,CAACtM,GAAG,CAAC,qBAAqB,CAAC,EAAEF,KAAK;IAEjE;IACA,IAAI,CAACoQ,KAAK,EAAE;MACV,OAAO,EAAE;IACX;IAEA;IACA,IAAIA,KAAK,KAAK,yBAAyB,IAAIA,KAAK,KAAK,wBAAwB,EAAE;MAC7E,OAAO,IAAI,CAACrD,eAAe,CAAC+E,MAAM,CAACxD,QAAQ,IACzC,CACE,YAAY,EACZ,UAAU,EACV,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,aAAa,EACb,aAAa,EACb,mBAAmB,EACnB,sBAAsB;MACtB;MACA,iBAAiB;MACjB;MACA,YAAY,EACZ,qCAAqC,EACrC,OAAO,CACR,CAAChJ,QAAQ,CAACgJ,QAAQ,CAACtO,KAAK,CAAC,CAC3B;IACH;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIoQ,KAAK,KAAK,gCAAgC,EAAE;MAC9C,OAAO,IAAI,CAACrD,eAAe,CAAC+E,MAAM,CAACxD,QAAQ,IACzC,CACE,YAAY,EACZ,UAAU,EACV,YAAY,EACZ,SAAS,EACT,WAAW,EACX,OAAO,EACP,sBAAsB,EACtB,iBAAiB,EACjB,YAAY,EACZ,mBAAmB,EACnB,mBAAmB,EACnB,eAAe,EACf,YAAY,EACZ,uBAAuB,EACvB,qCAAqC,CACtC,CAAChJ,QAAQ,CAACgJ,QAAQ,CAACtO,KAAK,CAAC,CAC3B;IACH;IAEA;IACA,IAAIoQ,KAAK,KAAK,yBAAyB,EAAE;MACvC,OAAO,IAAI,CAACrD,eAAe,CAAC+E,MAAM,CAACxD,QAAQ,IACzC,CACE,YAAY,EACZ,UAAU,EACV,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,aAAa,EACb,aAAa;MACb;MACA;MACA;MACA,mBAAmB,EACnB,sBAAsB,EACtB,mBAAmB,EACnB,iBAAiB,EACjB,YAAY,EACZ,qCAAqC,CACtC,CAAChJ,QAAQ,CAACgJ,QAAQ,CAACtO,KAAK,CAAC,CAC3B;IACH;IAEA,IAAIoQ,KAAK,KAAK,0BAA0B,EAAE;MACxC,OAAO,IAAI,CAACrD,eAAe,CAAC+E,MAAM,CAACxD,QAAQ,IACzC,CACE,YAAY,EACZ,UAAU,EACV,SAAS,EACT,YAAY;MACZ;MACA;MACA;MACA,WAAW,EACX,OAAO;MACP;MACA,mBAAmB,EACnB,sBAAsB,EACtB,mBAAmB,EACnB,iBAAiB,EACjB,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,qCAAqC,CACtC,CAAChJ,QAAQ,CAACgJ,QAAQ,CAACtO,KAAK,CAAC,CAC3B;IACH;IAEA;IACA,OAAO,IAAI,CAAC+M,eAAe;EAC7B;EAEAhN,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACyM,SAAS,CAAC,IAAI,CAACJ,WAAW,GAAG,CAAC,CAAC;EAC7C;EAEAjB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACpL,cAAc,EAAE,CAAC8U,KAAK,IAAI,IAAI,CAACzI,WAAW,GAAG,IAAI,CAACF,UAAU,EAAE;MACrE,IAAI,CAACE,WAAW,EAAE;MAClB,IAAI,CAAC4B,cAAc,EAAE;IACvB;EACF;EAEAhD,QAAQA,CAAA;IACN,IAAI,IAAI,CAACoB,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;MAClB,IAAI,CAAC4B,cAAc,EAAE;IACvB;EACF;EAEA;EACA8G,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC1N,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC/H,MAAM,GAAG,CAAC;EAC5D;EAEA;EACA4F,eAAeA,CAAC8K,KAAU;IACxB,OAAOA,KAAK,CAACC,UAAU,IAAID,KAAK,CAACC,UAAU,CAACwC,IAAI,CAAEuC,SAAc,IAC9DA,SAAS,KAAK7X,UAAU,CAACmR,QAAQ,IAChC0G,SAAS,IAAIA,SAAS,CAACC,QAAQ,IAAID,SAAS,CAACC,QAAQ,EAAE,CAAC1P,QAAQ,CAAC,UAAU,CAAE,CAC/E;EACH;EAEA;EACAuF,mBAAmBA,CAAA;IACjB,MAAMoK,SAAS,GAAG,IAAI,CAACtF,YAAY,EAAE;IACrC,IAAIsF,SAAS,CAAC3P,QAAQ,CAAC,SAAS,CAAC,EAAE;MACjC,OAAO,qDAAqD;IAC9D;IACA,OAAO,wDAAwD;EACjE;EAEA+B,gBAAgBA,CAAC6N,KAAa,EAAEnF,KAAU;IACxC,OAAOA,KAAK,CAAC3M,IAAI;EACnB;EAEA+R,mBAAmBA,CAACC,UAAkB;IACpC,IAAI,CAAChJ,WAAW,GAAGgJ,UAAU;IAC7B,IAAI,CAACpH,cAAc,EAAE;IACrB,IAAI,CAACjC,GAAG,CAACyD,YAAY,EAAE;EACzB;EAEA6F,qBAAqBA,CAAA;IACnB,IAAI,CAACjW,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACqN,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACV,GAAG,CAACyD,YAAY,EAAE;EACzB;EAEA;;;EAGA8F,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAClW,gBAAgB,CAACyQ,MAAM,CAAC,CAAC0F,KAAK,EAAEC,SAAS,KAAI;MACvD,OAAOD,KAAK,GAAGC,SAAS,CAACvW,MAAM,CAACI,MAAM;IACxC,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;;;EAGAoW,wBAAwBA,CAAA;IACtB,IAAI,IAAI,CAACrW,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;MACpC,MAAMqW,cAAc,GAAGC,IAAI,CAACtR,GAAG,CAAC,GAAG,IAAI,CAACjF,gBAAgB,CAAC7B,GAAG,CAACyR,KAAK,IAAIA,KAAK,CAAC4G,IAAI,CAAC,CAAC;MAClF,IAAI,CAACT,mBAAmB,CAACO,cAAc,CAAC;IAC1C;EACF;EAEAtK,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACrL,cAAc,EAAE,CAAC8U,KAAK;EACpC;EAEA;EACAgB,oBAAoBA,CAAA;IAClB,MAAM/C,IAAI,GAAG,IAAI,CAAC/S,cAAc,EAAE;IAClC,MAAMd,MAAM,GAAa,EAAE;IAE3BmQ,MAAM,CAAC0G,IAAI,CAAChD,IAAI,CAACiD,QAAQ,CAAC,CAACC,OAAO,CAAC5V,GAAG,IAAG;MACvC,MAAM8P,OAAO,GAAG4C,IAAI,CAAC5S,GAAG,CAACE,GAAG,CAAC;MAC7B,IAAI8P,OAAO,IAAIA,OAAO,CAAC5O,OAAO,EAAE;QAC9B,MAAM2U,aAAa,GAAG/F,OAAO,CAACjR,MAAM;QACpC,IAAIgX,aAAa,EAAE;UACjB7G,MAAM,CAAC0G,IAAI,CAACG,aAAa,CAAC,CAACD,OAAO,CAACE,QAAQ,IAAG;YAC5CjX,MAAM,CAACkX,IAAI,CAAC,GAAG/V,GAAG,KAAK8V,QAAQ,EAAE,CAAC;UACpC,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;IAEF,OAAOjX,MAAM,CAACI,MAAM,GAAG,CAAC,GAAGJ,MAAM,CAACmX,IAAI,CAAC,IAAI,CAAC,GAAG,WAAW;EAC5D;EAEA7M,YAAYA,CAACoI,SAAiB;IAC5B,MAAMgC,KAAK,GAAG,IAAI,CAAC5T,cAAc,EAAE,CAACG,GAAG,CAACyR,SAAS,CAAC,EAAE3R,KAAK;IACzD,OAAO2T,KAAK,IAAI1F,KAAK,CAAC2F,OAAO,CAACD,KAAK,CAAC,GAAGA,KAAK,CAACtU,MAAM,GAAG,CAAC;EACzD;EAEAyK,YAAYA,CAACqH,KAAU,EAAEQ,SAAiB;IACxC,IAAIR,KAAK,CAACE,MAAM,CAACsC,KAAK,EAAEtU,MAAM,EAAE;MAC9B,IAAI,CAACmN,SAAS,CAAC,IAAI,CAACJ,WAAW,GAAG,CAAC,CAAC,CAACqF,UAAU,CAAC;QAC9C,CAACE,SAAS,GAAG1D,KAAK,CAACoI,IAAI,CAAClF,KAAK,CAACE,MAAM,CAACsC,KAAK;OAC3C,CAAC;MACF,IAAI,CAAC5H,GAAG,CAACyD,YAAY,EAAE;IACzB;EACF;EAEAjE,UAAUA,CAAA;IACR,IAAI,IAAI,CAACiB,SAAS,CAAC+F,KAAK,CAAEO,IAAI,IAAKA,IAAI,CAAC+B,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACnJ,YAAY,GAAG,IAAI;MACxB,MAAM4K,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAClK,MAAM,CAAC0I,QAAQ,EAAE,CAAC;MAEjD;MACA,MAAML,WAAW,GAAG,IAAI,CAACnI,SAAS,CAAC,CAAC,CAAC,CAACxM,KAAK;MAC3CoP,MAAM,CAAC0G,IAAI,CAACnB,WAAW,CAAC,CAACqB,OAAO,CAAE5V,GAAG,IAAI;QACvCkW,QAAQ,CAACE,MAAM,CAACpW,GAAG,EAAEuU,WAAW,CAACvU,GAAG,CAAC,IAAI,EAAE,CAAC;MAC9C,CAAC,CAAC;MAEF;MACA,MAAMqW,WAAW,GAAG,IAAI,CAACjK,SAAS,CAAC,CAAC,CAAC,CAACxM,KAAK;MAC3C,IAAIyW,WAAW,CAAC1C,MAAM,EAAE;QACtBuC,QAAQ,CAACE,MAAM,CAAC,oBAAoB,EAAEC,WAAW,CAAC1C,MAAM,CAACiB,QAAQ,EAAE,CAAC;MACtE;MACA,IAAIyB,WAAW,CAACxC,MAAM,EAAE;QACtBqC,QAAQ,CAACE,MAAM,CAAC,4BAA4B,EAAEC,WAAW,CAACxC,MAAM,CAACe,QAAQ,EAAE,CAAC;MAC9E;MACA,IAAIyB,WAAW,CAACvC,SAAS,EAAE;QACzBoC,QAAQ,CAACE,MAAM,CAAC,qCAAqC,EAAEC,WAAW,CAACvC,SAAS,CAACc,QAAQ,EAAE,CAAC;MAC1F;MAEA;MACA,IAAI,CAACxI,SAAS,CAACkK,KAAK,CAAC,CAAC,CAAC,CAACV,OAAO,CAAElD,IAAI,IAAI;QACvC1D,MAAM,CAAC0G,IAAI,CAAChD,IAAI,CAAC9S,KAAK,CAAC,CAACgW,OAAO,CAAE5V,GAAG,IAAI;UACtC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,CAACkF,QAAQ,CAAClF,GAAG,CAAC,EAAE;YACnD;UACF;UACA,MAAMJ,KAAK,GAAG8S,IAAI,CAAC9S,KAAK,CAACI,GAAG,CAAC;UAC7B,IAAIJ,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK2W,SAAS,EAAE;YACzC,IAAI1I,KAAK,CAAC2F,OAAO,CAAC5T,KAAK,CAAC,EAAE;cACxBA,KAAK,CAACgW,OAAO,CAAC,CAACjE,IAAS,EAAEhB,CAAS,KACjCuF,QAAQ,CAACE,MAAM,CAAC,cAAcpW,GAAG,KAAK2Q,CAAC,GAAG,EAAEgB,IAAI,CAAC,CAClD;YACH,CAAC,MAAM,IAAI,OAAO/R,KAAK,KAAK,SAAS,EAAE;cACrCsW,QAAQ,CAACE,MAAM,CAAC,cAAcpW,GAAG,GAAG,EAAEJ,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;YAC1D,CAAC,MAAM,IAAIA,KAAK,KAAK,EAAE,EAAE;cACvBsW,QAAQ,CAACE,MAAM,CAAC,cAAcpW,GAAG,GAAG,EAAEJ,KAAK,CAACgV,QAAQ,EAAE,CAAC;YACzD;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF;MACA,MAAM4B,UAAU,GAAG,CAAC,WAAW,EAAE,eAAe,EAAE,OAAO,EAAE,uBAAuB,CAAC;MACnFA,UAAU,CAACZ,OAAO,CAAEnX,KAAK,IAAI;QAC3B,MAAM8U,KAAK,GAAG,IAAI,CAACnH,SAAS,CAAC,CAAC,CAAC,EAAEtM,GAAG,CAACrB,KAAK,CAAC,EAAEmB,KAAK;QAClD,IAAI2T,KAAK,IAAI1F,KAAK,CAAC2F,OAAO,CAACD,KAAK,CAAC,IAAIA,KAAK,CAACtU,MAAM,GAAG,CAAC,EAAE;UACrD,MAAMwX,UAAU,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,CAACvR,QAAQ,CAACzG,KAAK,CAAC;UAE7D,IAAIgY,UAAU,EAAE;YACdlD,KAAK,CAACqC,OAAO,CAAC,CAAC3C,IAAU,EAAE6B,KAAa,KAAI;cAC1C,IAAI7B,IAAI,YAAYyD,IAAI,EAAE;gBACxBR,QAAQ,CAACE,MAAM,CAAC,cAAc3X,KAAK,KAAKqW,KAAK,GAAG,EAAE7B,IAAI,CAAC;cACzD;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,IAAIM,KAAK,CAAC,CAAC,CAAC,YAAYmD,IAAI,EAAE;cAC5BR,QAAQ,CAACE,MAAM,CAAC,cAAc3X,KAAK,GAAG,EAAE8U,KAAK,CAAC,CAAC,CAAC,CAAC;YACnD;UACF;QACF;MACF,CAAC,CAAC;MAEF,IAAI,CAAC9H,oBAAoB,CAACkL,aAAa,CAACT,QAAQ,CAAC,CAAChH,SAAS,CAAC;QAC1DC,IAAI,EAAGyH,GAAG,IAAI;UACZ,IAAI,CAACtL,YAAY,GAAG,KAAK;UACzBlO,IAAI,CAACyR,IAAI,CAAC,SAAS,EAAE,+BAA+B,EAAE,SAAS,CAAC,CAACgI,IAAI,CAAC,MAAK;YACzE,IAAI,CAACjL,MAAM,CAACkL,QAAQ,CAAC,CAAC,qBAAqB,EAAEF,GAAG,CAACrI,IAAI,EAAEf,EAAE,IAAIoJ,GAAG,CAACpJ,EAAE,CAAC,CAAC;UACvE,CAAC,CAAC;QACJ,CAAC;QACDoB,KAAK,EAAGS,GAAG,IAAI;UACb,IAAI,CAAC/D,YAAY,GAAG,KAAK;UACzB,IAAI,CAACyL,qBAAqB,CAAC1H,GAAG,CAAC;QACjC;OACD,CAAC;IACJ;EACF;EAEA0H,qBAAqBA,CAACnI,KAAU;IAC9B,IAAI,CAAC5P,gBAAgB,GAAG,EAAE;IAC1B,IAAI4P,KAAK,EAAEA,KAAK,EAAE/P,MAAM,EAAE;MACxBmQ,MAAM,CAAC0G,IAAI,CAAC9G,KAAK,CAACA,KAAK,CAAC/P,MAAM,CAAC,CAAC+W,OAAO,CAAEnX,KAAK,IAAI;QAChD,MAAMuY,UAAU,GAAGvY,KAAK,CAACwY,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;QAChF,MAAMzB,IAAI,GAAG,IAAI,CAACxO,aAAa,CAAC0J,IAAI,CAAEf,KAAK,IAAKA,KAAK,CAAC3M,IAAI,KAAKgU,UAAU,CAAC,EAAExB,IAAI,IAAI,CAAC;QACrF,IAAI0B,SAAS,GAAG,IAAI,CAAClY,gBAAgB,CAAC0R,IAAI,CAAEyG,CAAC,IAAKA,CAAC,CAAC3B,IAAI,KAAKA,IAAI,CAAC;QAClE,IAAI,CAAC0B,SAAS,EAAE;UACdA,SAAS,GAAG;YACV1B,IAAI;YACJ4B,QAAQ,EAAE,IAAI,CAACvK,SAAS,CAAC2I,IAAI,CAAC,IAAI,QAAQA,IAAI,EAAE;YAChD3W,MAAM,EAAE;WACT;UACD,IAAI,CAACG,gBAAgB,CAAC+W,IAAI,CAACmB,SAAS,CAAC;QACvC;QACAA,SAAS,CAACrY,MAAM,CAACkX,IAAI,CAAC;UACpBtX,KAAK,EAAEuY,UAAU;UACjBtY,QAAQ,EAAEmP,KAAK,CAAC2F,OAAO,CAAC5E,KAAK,CAACA,KAAK,CAAC/P,MAAM,CAACJ,KAAK,CAAC,CAAC,GAC9CmQ,KAAK,CAACA,KAAK,CAAC/P,MAAM,CAACJ,KAAK,CAAC,GACzB,CAACmQ,KAAK,CAACA,KAAK,CAAC/P,MAAM,CAACJ,KAAK,CAAC;SAC/B,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAAC4N,aAAa,GAAG,IAAI;MACzB,IAAI,CAACV,GAAG,CAACyD,YAAY,EAAE;IACzB,CAAC,MAAM;MACLhS,IAAI,CAACyR,IAAI,CAAC,OAAO,EAAED,KAAK,EAAEA,KAAK,EAAEyI,OAAO,IAAI,4BAA4B,EAAE,OAAO,CAAC;IACpF;EACF;;qCAj6BW9L,qBAAqB,EAAA/N,EAAA,CAAA8Z,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAha,EAAA,CAAA8Z,iBAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAAla,EAAA,CAAA8Z,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAApa,EAAA,CAAA8Z,iBAAA,CAAA9Z,EAAA,CAAAqa,iBAAA,GAAAra,EAAA,CAAA8Z,iBAAA,CAAAQ,EAAA,CAAAC,MAAA,GAAAva,EAAA,CAAA8Z,iBAAA,CAAAU,EAAA,CAAAC,yBAAA;EAAA;;UAArB1M,qBAAqB;IAAA2M,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCtBlChb,EAAA,CAAAC,cAAA,aAAuB;QACrBD,EAAA,CAAAI,SAAA,0BAAgE;QAClEJ,EAAA,CAAAG,YAAA,EAAM;QAOEH,EALR,CAAAC,cAAA,aAA4B,aACE,aAC2D,aAErD,YACY;QAAAD,EAAA,CAAAE,MAAA,GAA4B;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEvEH,EADF,CAAAC,cAAA,aAAmE,cAC9B;QAAAD,EAAA,CAAAE,MAAA,IAAsB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAChEH,EAAA,CAAAC,cAAA,eAA8B;QAAAD,EAAA,CAAAE,MAAA,UAAE;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACvCH,EAAA,CAAAC,cAAA,gBAAyB;QAAAD,EAAA,CAAAE,MAAA,IAAgB;QAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;QACNH,EAAA,CAAAC,cAAA,eAA0D;QACxDD,EAAA,CAAAI,SAAA,eAC8C;QAElDJ,EADE,CAAAG,YAAA,EAAM,EACF;QAGNH,EAAA,CAAAK,UAAA,KAAA6a,qCAAA,mBAC+B;QAsD/Blb,EAAA,CAAAC,cAAA,gBAAsE;QA2lBpED,EAzlBA,CAAAK,UAAA,KAAA8a,qCAAA,mBAA+B,KAAAC,qCAAA,oBAkFA,KAAAC,qCAAA,kBA4TF,KAAAC,qCAAA,kBA2MoD;QAWjFtb,EAAA,CAAAC,cAAA,eAAqE;QAkBnED,EAhBA,CAAAK,UAAA,KAAAkb,wCAAA,qBACuG,KAAAC,qCAAA,kBAItD,KAAAC,wCAAA,qBAIS,KAAAC,wCAAA,qBASkB;QActF1b,EAJQ,CAAAG,YAAA,EAAM,EACD,EACH,EACF,EACF;;;QAptBcH,EAAA,CAAAO,SAAA,EAA0B;QAA1BP,EAAA,CAAAU,UAAA,2BAA0B;QAQEV,EAAA,CAAAO,SAAA,GAA4B;QAA5BP,EAAA,CAAAc,iBAAA,CAAAma,GAAA,CAAA5L,SAAA,CAAA4L,GAAA,CAAAzM,WAAA,EAA4B;QAE/BxO,EAAA,CAAAO,SAAA,GAAsB;QAAtBP,EAAA,CAAAQ,kBAAA,UAAAya,GAAA,CAAAzM,WAAA,KAAsB;QAEhCxO,EAAA,CAAAO,SAAA,GAAgB;QAAhBP,EAAA,CAAAc,iBAAA,CAAAma,GAAA,CAAA3M,UAAA,CAAgB;QAGetO,EAAA,CAAAO,SAAA,GAAsD;QAAtDP,EAAA,CAAA2b,WAAA,UAAAV,GAAA,CAAAzM,WAAA,GAAAyM,GAAA,CAAA3M,UAAA,aAAsD;QAM5GtO,EAAA,CAAAO,SAAA,EAAkD;QAAlDP,EAAA,CAAAU,UAAA,SAAAua,GAAA,CAAApM,aAAA,IAAAoM,GAAA,CAAAzZ,gBAAA,CAAAC,MAAA,KAAkD;QAuDjBzB,EAAA,CAAAO,SAAA,EAA8B;QAA9BP,EAAA,CAAAU,UAAA,cAAAua,GAAA,CAAA9Y,cAAA,GAA8B;QAE7DnC,EAAA,CAAAO,SAAA,EAAuB;QAAvBP,EAAA,CAAAU,UAAA,SAAAua,GAAA,CAAAzM,WAAA,OAAuB;QAkFvBxO,EAAA,CAAAO,SAAA,EAAuB;QAAvBP,EAAA,CAAAU,UAAA,SAAAua,GAAA,CAAAzM,WAAA,OAAuB;QA4TvBxO,EAAA,CAAAO,SAAA,EAAqB;QAArBP,EAAA,CAAAU,UAAA,SAAAua,GAAA,CAAAzM,WAAA,KAAqB;QA2MrBxO,EAAA,CAAAO,SAAA,EAA2C;QAA3CP,EAAA,CAAAU,UAAA,SAAAua,GAAA,CAAAzM,WAAA,WAAAyM,GAAA,CAAA/D,aAAA,GAA2C;QAatClX,EAAA,CAAAO,SAAA,GAAqB;QAArBP,EAAA,CAAAU,UAAA,SAAAua,GAAA,CAAAzM,WAAA,KAAqB;QAKxBxO,EAAA,CAAAO,SAAA,EAAuB;QAAvBP,EAAA,CAAAU,UAAA,SAAAua,GAAA,CAAAzM,WAAA,OAAuB;QAGpBxO,EAAA,CAAAO,SAAA,EAAgC;QAAhCP,EAAA,CAAAU,UAAA,SAAAua,GAAA,CAAAzM,WAAA,KAAAyM,GAAA,CAAA3M,UAAA,CAAgC;QAQhCtO,EAAA,CAAAO,SAAA,EAAgC;QAAhCP,EAAA,CAAAU,UAAA,SAAAua,GAAA,CAAAzM,WAAA,KAAAyM,GAAA,CAAA3M,UAAA,CAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}