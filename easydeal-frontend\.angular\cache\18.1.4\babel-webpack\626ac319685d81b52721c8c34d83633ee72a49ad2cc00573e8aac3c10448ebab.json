{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/broker.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"./components/pie-chart/pie-chart.component\";\nimport * as i4 from \"./components/dashboard-card/dashboard-card.component\";\nimport * as i5 from \"./components/analysis-card/analysis-card.component\";\nimport * as i6 from \"./components/new-requests/new-requests.component\";\nimport * as i7 from \"./components/new-request-card/new-request-card.component\";\nimport * as i8 from \"./components/specializations-filter/specializations-filter.component\";\nimport * as i9 from \"../shared/broker-title/broker-title.component\";\nconst _c0 = a0 => ({\n  type: \"svg\",\n  svgContent: a0\n});\nconst _c1 = () => ({\n  type: \"fontawesome\",\n  name: \"map\"\n});\nconst _c2 = () => ({\n  type: \"keenicon\",\n  name: \"social-media\",\n  iconType: \"outline\"\n});\nconst _c3 = a0 => ({\n  \"show\": a0\n});\nfunction BrokerDashboardComponent_div_12_app_pie_chart_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-pie-chart\", 30);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"newRequestsPercent\", ctx_r2.newRequestsPercent)(\"inProcessingRequestsPercent\", ctx_r2.inProcessingRequestsPercent)(\"finishedRequestsPercent\", ctx_r2.finishedRequestsPercent)(\"chartSize\", 150)(\"chartLine\", 30)(\"chartRotate\", 145);\n  }\n}\nfunction BrokerDashboardComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 8)(2, \"div\", 9)(3, \"div\", 3)(4, \"div\", 10)(5, \"h3\", 11)(6, \"span\", 12);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 13)(8, \"g\", 14);\n    i0.ɵɵelement(9, \"path\", 15)(10, \"path\", 16)(11, \"path\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"defs\")(13, \"clipPath\", 18);\n    i0.ɵɵelement(14, \"rect\", 19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtext(15, \" Requests and Sales Statistics \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"div\", 20);\n    i0.ɵɵelement(17, \"app-analysis-card\", 21)(18, \"app-analysis-card\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 3)(20, \"div\", 22)(21, \"h3\", 11)(22, \"span\", 12);\n    i0.ɵɵtext(23, \"Filter Requests\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 23);\n    i0.ɵɵtext(25, \"Filter requests by specializations\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 24)(27, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function BrokerDashboardComponent_div_12_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const specializationsFilter_r2 = i0.ɵɵreference(30);\n      return i0.ɵɵresetView(specializationsFilter_r2.toggleDropdown());\n    });\n    i0.ɵɵelement(28, \"i\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"app-specializations-filter\", 27, 0);\n    i0.ɵɵlistener(\"filterChanged\", function BrokerDashboardComponent_div_12_Template_app_specializations_filter_filterChanged_29_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFilterChanged($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 28)(32, \"div\", 2);\n    i0.ɵɵtemplate(33, BrokerDashboardComponent_div_12_app_pie_chart_33_Template, 1, 6, \"app-pie-chart\", 29);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const specializationsFilter_r2 = i0.ɵɵreference(30);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"backgroundColor\", \"success\")(\"title\", \"Finished Requests\")(\"totalRequests\", ctx_r2.allRequestsCount)(\"activeRequests\", ctx_r2.finishedRequestsCount);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"backgroundColor\", \"mid-blue\")(\"title\", \"In Processing Requests\")(\"totalRequests\", ctx_r2.allRequestsCount)(\"activeRequests\", ctx_r2.inProcessingRequestsCount);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c3, specializationsFilter_r2.isDropdownOpen));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.allRequestsCount > 0);\n  }\n}\nfunction BrokerDashboardComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"app-new-requests\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class BrokerDashboardComponent {\n  cd;\n  brokerService;\n  cssClass = '';\n  userId;\n  brokerId;\n  allRequestsCount = 0;\n  newRequestsCount = 0;\n  inProcessingRequestsCount = 0;\n  finishedRequestsCount = 0;\n  newRequestsPercent = 0;\n  inProcessingRequestsPercent = 0;\n  finishedRequestsPercent = 0;\n  totalContractRequests = 0;\n  acceptedContractsCount = 0;\n  maps = 0;\n  dataAndProperties = 0;\n  advertisements = 0;\n  loading = false;\n  errorMessage = '';\n  constructor(cd, brokerService) {\n    this.cd = cd;\n    this.brokerService = brokerService;\n  }\n  ngOnInit() {\n    const userJson = localStorage.getItem('currentUser');\n    let user = userJson ? JSON.parse(userJson) : null;\n    this.userId = user?.id;\n    this.brokerId = user?.brokerId;\n    this.fetchStatistics();\n    this.fetchDashboardStatistics();\n  }\n  propertySvg = `\n    <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n      <mask id=\"mask0_448_13442\" style=\"mask-type:luminance\" maskUnits=\"userSpaceOnUse\" x=\"0\" y=\"0\" width=\"20\" height=\"20\">\n        <path d=\"M19.5 19.5V0.5H0.5V19.5H19.5Z\" fill=\"white\" stroke=\"white\"/>\n      </mask>\n      <g mask=\"url(#mask0_448_13442)\">\n        <path d=\"M11.3698 19.259H2.64697V4.72903C2.64697 4.32169 2.90709 3.95989 3.29318 3.83013L11.3698 1.11528V19.259Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M16.2795 17.4364V19.259H11.3701V9.41489H16.7869C17.1459 9.41489 17.4365 9.70552 17.4365 10.0645V15.2754\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M11.3701 19.259H19.2667C19.51 19.259 19.7072 19.0618 19.7072 18.8186V17.4365H11.3701V19.259Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M17.9105 15.2046H18.0759C18.9767 15.2046 19.707 15.9349 19.707 16.8357V17.436H16.2793V16.8357C16.2793 15.9349 17.0096 15.2046 17.9105 15.2046Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M0.733438 19.259H2.64711V17.4365H0.292969V18.8186C0.292969 19.0618 0.490156 19.259 0.733438 19.259Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M2.4168 15.2046H2.64715V17.436H0.292969V17.3284C0.292969 16.1554 1.24387 15.2046 2.4168 15.2046Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M14.4034 9.41431H11.3701V1.11485L14.1142 3.76731C14.299 3.94599 14.4034 4.19208 14.4034 4.44919V8.03329\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M4.78076 3.32971V1.3512C4.78076 1.01487 5.05342 0.742212 5.39014 0.742212H5.45186C5.78818 0.742212 6.06084 1.01487 6.06084 1.3512V2.89924\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M11.3698 3.32935L4.78076 5.54419\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M11.3698 5.43787L4.78076 7.65271\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M11.3698 7.54651L4.78076 9.76135\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M11.3698 9.65552L4.78076 11.8704\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M5.8685 13.6125L4.78076 13.9781\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M11.37 11.7633L7.18066 13.1715\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M8.25287 19.259H4.78076V16.7702C4.78076 16.5772 4.90928 16.4079 5.09514 16.356L7.82197 15.5947C8.03846 15.5343 8.25287 15.697 8.25287 15.9218V19.259Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M15.8193 11.8313H12.9462C12.8352 11.8313 12.7451 11.7413 12.7451 11.6302V10.925C12.7451 10.8139 12.8352 10.7239 12.9462 10.7239H15.8193C15.9304 10.7239 16.0204 10.8139 16.0204 10.925V11.6302C16.0204 11.7413 15.9304 11.8313 15.8193 11.8313Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M15.8193 14.0582H12.9462C12.8352 14.0582 12.7451 13.9682 12.7451 13.8571V13.1519C12.7451 13.0408 12.8352 12.9508 12.9462 12.9508H15.8193C15.9304 12.9508 16.0204 13.0408 16.0204 13.1519V13.8571C16.0204 13.9682 15.9304 14.0582 15.8193 14.0582Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M13.8965 16.3091H12.9462C12.8352 16.3091 12.7451 16.219 12.7451 16.108V15.4028C12.7451 15.2917 12.8352 15.2017 12.9462 15.2017H13.8965C14.0076 15.2017 14.0977 15.2917 14.0977 15.4028V16.108C14.0977 16.219 14.0076 16.3091 13.8965 16.3091Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n        <path d=\"M6.51709 15.9591V19.259\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n      </g>\n    </svg>\n  `;\n  adSvg = `\n    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n      <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"#1a3c8b\"/>\n    </svg>\n  `;\n  developerSvg = `\n    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n      <g clip-path=\"url(#clip0_136_62239)\">\n        <path stroke=\"#0D47A1\" fill=\"#0D47A1\" stroke-width=\"0.5\" d=\"M19.4998 18.9999H18.9998V11H17.9998V10H16.9998V11H15.9998V13H14.9999V11V9.00003H13.9999V11H12.9999V8.00004H11.9999V11V13H10.9999V11H9.9999V9.00003H8.99991V11H7.99992V18.9999H5.99994V8.00004H6.99993C7.55227 8.00004 7.99992 7.55239 7.99992 7.00005V6.00006H16.9998V9.00003H17.9998V6.00006H18.9998C19.4635 6.00006 19.8665 5.68108 19.9731 5.22972C20.0798 4.7784 19.8618 4.31305 19.4471 4.10575L17.4471 3.10576C17.3082 3.03642 17.1551 3.00009 16.9998 3.00009H6.10928L4.89429 0.552773C4.7223 0.208792 4.3723 0.00012207 4.00031 0.00012207C3.92399 0.00012207 3.84699 0.00879386 3.77031 0.027114C3.31895 0.133441 2.99997 0.536445 2.99997 1.00011V3.00009H0.99999C0.447652 3.00009 0 3.44774 0 4.00008V5.00007C0 5.55241 0.447652 6.00006 0.99999 6.00006H2.99997V7.00005V18.9999H0.499995C0.22367 18.9999 0 19.2236 0 19.4999C0 19.7763 0.22367 19.9999 0.499995 19.9999H19.4998C19.7761 19.9999 19.9998 19.7763 19.9998 19.4999C19.9998 19.2236 19.7761 18.9999 19.4998 18.9999ZM2.99997 5.00007H0.99999V4.00008H2.99997V5.00007ZM16.9998 4.00008L18.9998 5.00007H7.99992C7.99992 4.73472 7.89457 4.48043 7.70692 4.29309L7.41426 4.00008H16.9998ZM3.99996 1.00011L4.99995 3.00009H3.99996V1.00011ZM3.99996 4.00008H5.99994L6.99993 5.00007V7.00005H3.99996V4.00008ZM4.99995 18.9999H3.99996V8.00004H4.99995V18.9999ZM17.9998 18.9999H8.99991V12H9.9999V14H12.9999V12H13.9999V14H16.9998V12H17.9998V18.9999Z\"/>\n      </g>\n      <defs>\n        <clipPath id=\"clip0_136_62239\">\n          <rect width=\"20\" height=\"20\" fill=\"white\"/>\n        </clipPath>\n      </defs>\n    </svg>\n  `;\n  onFilterChanged(payload) {\n    this.fetchStatistics(payload.specializationScope);\n  }\n  fetchStatistics(specializationScope) {\n    this.loading = true;\n    this.brokerService.getBrokerRequestStatistics(this.userId, specializationScope).subscribe({\n      next: response => {\n        this.allRequestsCount = response.data.allRequestsCount || 0;\n        this.newRequestsCount = response.data.newRequestsCount || 0;\n        this.inProcessingRequestsCount = response.data.inProcessingRequestsCount || 0;\n        this.finishedRequestsCount = response.data.finishedRequestsCount || 0;\n        const total = this.allRequestsCount || 1;\n        this.newRequestsPercent = parseFloat((this.newRequestsCount / total * 100).toFixed(1));\n        this.inProcessingRequestsPercent = parseFloat((this.inProcessingRequestsCount / total * 100).toFixed(1));\n        this.finishedRequestsPercent = parseFloat((this.finishedRequestsCount / total * 100).toFixed(1));\n        this.cd.detectChanges();\n        this.loading = false;\n      },\n      error: error => {\n        this.errorMessage = 'Failed to load requests';\n        console.error(error);\n        this.loading = false;\n      }\n    });\n  }\n  fetchDashboardStatistics() {\n    this.loading = true;\n    this.brokerService.getBrokerDashboardStatistics(this.brokerId).subscribe({\n      next: response => {\n        this.totalContractRequests = response.data.totalContractRequests || 0;\n        this.acceptedContractsCount = response.data.acceptedContractsCount || 0;\n        this.maps = response.data.maps || 0;\n        this.dataAndProperties = response.data.dataAndProperties || 0;\n        this.advertisements = response.data.advertisements || 0;\n        this.cd.detectChanges();\n        this.loading = false;\n      },\n      error: error => {\n        this.errorMessage = 'Failed to load broker statistics';\n        console.error(error);\n        this.loading = false;\n      }\n    });\n  }\n  static ɵfac = function BrokerDashboardComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BrokerDashboardComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.BrokerService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BrokerDashboardComponent,\n    selectors: [[\"app-broker-dashboard\"]],\n    inputs: {\n      cssClass: \"cssClass\"\n    },\n    decls: 14,\n    vars: 36,\n    consts: [[\"specializationsFilter\", \"\"], [1, \"mb-5\", \"mt-0\"], [1, \"row\"], [1, \"col-md-6\"], [3, \"title\", \"subTitle\", \"buttonTitle\", \"buttonIcon\"], [3, \"title\", \"subTitle\", \"icon\", \"buttonTitle\", \"buttonIcon\", \"buttonLink\"], [\"class\", \"col-md-6\", 4, \"ngIf\"], [\"class\", \"row mt-5 mb-5\", 4, \"ngIf\"], [1, \"card\", \"mb-4\"], [1, \"row\", \"g-0\"], [1, \"card-header\", \"border-0\", \"pb-1\"], [1, \"card-title\", \"align-items-start\", \"flex-column\"], [1, \"card-label\", \"fw-bolder\", \"fs-3\", \"mb-1\", \"text-dark-blue\"], [\"width\", \"19\", \"height\", \"19\", \"viewBox\", \"0 0 19 19\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"clip-path\", \"url(#clip0_24_2533)\"], [\"stroke\", \"#0D47A1\", \"stroke-width\", \"1\", \"d\", \"M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z\"], [\"stroke\", \"#0D47A1\", \"stroke-width\", \"1\", \"d\", \"M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z\"], [\"stroke\", \"#0D47A1\", \"stroke-width\", \"1\", \"d\", \"M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z\"], [\"id\", \"clip0_24_2533\"], [\"width\", \"19\", \"height\", \"19\", \"fill\", \"white\"], [1, \"card-body\", \"pt-0\", \"pb-1\"], [3, \"backgroundColor\", \"title\", \"totalRequests\", \"activeRequests\"], [1, \"card-header\", \"border-0\", \"mb-1\"], [1, \"text-muted\", \"fw-bold\", \"fs-8\"], [1, \"card-toolbar\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-icon\", \"btn-light-dark-blue\", \"btn-active-dark-blue\", 3, \"click\"], [1, \"fa-solid\", \"fa-filter\"], [3, \"filterChanged\", \"ngClass\"], [1, \"card-body\", \"py-3\"], [\"cssClass\", \"your-css-class\", 3, \"newRequestsPercent\", \"inProcessingRequestsPercent\", \"finishedRequestsPercent\", \"chartSize\", \"chartLine\", \"chartRotate\", 4, \"ngIf\"], [\"cssClass\", \"your-css-class\", 3, \"newRequestsPercent\", \"inProcessingRequestsPercent\", \"finishedRequestsPercent\", \"chartSize\", \"chartLine\", \"chartRotate\"], [1, \"row\", \"mt-5\", \"mb-5\"]],\n    template: function BrokerDashboardComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1);\n        i0.ɵɵelement(1, \"app-broker-title\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵelement(4, \"app-new-request-card\", 4);\n        i0.ɵɵelementStart(5, \"div\", 2)(6, \"div\", 3);\n        i0.ɵɵelement(7, \"app-dashboard-card\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 3);\n        i0.ɵɵelement(9, \"app-dashboard-card\", 5);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(10, \"app-dashboard-card\", 5)(11, \"app-dashboard-card\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(12, BrokerDashboardComponent_div_12_Template, 34, 12, \"div\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(13, BrokerDashboardComponent_div_13_Template, 2, 0, \"div\", 7);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"title\", \"Create New Request\")(\"subTitle\", \"Over \" + ctx.allRequestsCount + \" Request\" + (ctx.allRequestsCount === 1 ? \"\" : \"s\"))(\"buttonTitle\", \"Create\")(\"buttonIcon\", \"plus\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"title\", \"Developers\")(\"subTitle\", \"Over \" + ctx.acceptedContractsCount + \" developers\")(\"icon\", i0.ɵɵpureFunction1(30, _c0, ctx.developerSvg))(\"buttonTitle\", \"View all\")(\"buttonIcon\", \"angles-right\")(\"buttonLink\", \"/broker/developers\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"title\", \"My Maps\")(\"subTitle\", \"Over \" + ctx.maps + \" Maps\")(\"icon\", i0.ɵɵpureFunction0(32, _c1))(\"buttonTitle\", \"View all\")(\"buttonIcon\", \"angles-right\")(\"buttonLink\", \"/broker/Maps\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"title\", \"My Advertisements\")(\"subTitle\", \"Over \" + ctx.advertisements + \" Ads\")(\"icon\", i0.ɵɵpureFunction0(33, _c2))(\"buttonTitle\", \"View all\")(\"buttonIcon\", \"angles-right\")(\"buttonLink\", \"/broker/Adds\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"title\", \"Data and Properties\")(\"subTitle\", \"Over \" + ctx.dataAndProperties + \" Properties\")(\"icon\", i0.ɵɵpureFunction1(34, _c0, ctx.propertySvg))(\"buttonTitle\", \"View all\")(\"buttonIcon\", \"angles-right\")(\"buttonLink\", \"/broker/dataandproperties\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.allRequestsCount != 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.allRequestsCount != 0);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgIf, i3.PieChartComponent, i4.DashboardCardComponent, i5.AnalysisCardComponent, i6.NewRequestsComponent, i7.NewRequestCardComponent, i8.SpecializationsFilterComponent, i9.BrokerTitleComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelement", "ɵɵproperty", "ctx_r2", "newRequestsPercent", "inProcessingRequestsPercent", "finishedRequestsPercent", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "BrokerDashboardComponent_div_12_Template_button_click_27_listener", "ɵɵrestoreView", "_r1", "specializationsFilter_r2", "ɵɵreference", "ɵɵresetView", "toggleDropdown", "BrokerDashboardComponent_div_12_Template_app_specializations_filter_filterChanged_29_listener", "$event", "ɵɵnextContext", "onFilterChanged", "ɵɵtemplate", "BrokerDashboardComponent_div_12_app_pie_chart_33_Template", "ɵɵadvance", "allRequestsCount", "finishedRequestsCount", "inProcessingRequestsCount", "ɵɵpureFunction1", "_c3", "isDropdownOpen", "BrokerDashboardComponent", "cd", "brokerService", "cssClass", "userId", "brokerId", "newRequestsCount", "totalContractRequests", "acceptedContractsCount", "maps", "dataAndProperties", "advertisements", "loading", "errorMessage", "constructor", "ngOnInit", "userJson", "localStorage", "getItem", "user", "JSON", "parse", "id", "fetchStatistics", "fetchDashboardStatistics", "propertySvg", "adSvg", "developerSvg", "payload", "specializationScope", "getBrokerRequestStatistics", "subscribe", "next", "response", "data", "total", "parseFloat", "toFixed", "detectChanges", "error", "console", "getBrokerDashboardStatistics", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "BrokerService", "selectors", "inputs", "decls", "vars", "consts", "template", "BrokerDashboardComponent_Template", "rf", "ctx", "BrokerDashboardComponent_div_12_Template", "BrokerDashboardComponent_div_13_Template", "_c0", "ɵɵpureFunction0", "_c1", "_c2"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\broker-dashboard\\broker-dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\broker\\broker-dashboard\\broker-dashboard.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';\r\nimport { BrokerService } from '../services/broker.service';\r\nimport { BaseGridComponent } from '../../shared/base-grid/base-grid.component';\r\n\r\n@Component({\r\n  selector: 'app-broker-dashboard',\r\n  templateUrl: './broker-dashboard.component.html',\r\n  styleUrl: './broker-dashboard.component.scss',\r\n})\r\nexport class BrokerDashboardComponent implements OnInit {\r\n\r\n  @Input() cssClass: string = ''\r\n\r\n  userId: any;\r\n  brokerId :any;\r\n  allRequestsCount: number = 0;\r\n  newRequestsCount: number = 0;\r\n  inProcessingRequestsCount: number = 0;\r\n  finishedRequestsCount: number = 0;\r\n  newRequestsPercent: number = 0;\r\n  inProcessingRequestsPercent: number = 0;\r\n  finishedRequestsPercent: number = 0;\r\n\r\n  totalContractRequests: number = 0;\r\n  acceptedContractsCount: number = 0;\r\n  maps: number = 0;\r\n  dataAndProperties: number = 0;\r\n  advertisements: number = 0;\r\n  loading = false;\r\n  errorMessage = '';\r\n\r\n  constructor(protected cd: ChangeDetectorRef, private brokerService: BrokerService) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    const userJson = localStorage.getItem('currentUser');\r\n    let user = userJson ? JSON.parse(userJson) : null;\r\n    this.userId = user?.id;\r\n    this.brokerId = user?.brokerId;\r\n    this.fetchStatistics();\r\n    this.fetchDashboardStatistics();\r\n  }\r\n\r\n  propertySvg = `\r\n    <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <mask id=\"mask0_448_13442\" style=\"mask-type:luminance\" maskUnits=\"userSpaceOnUse\" x=\"0\" y=\"0\" width=\"20\" height=\"20\">\r\n        <path d=\"M19.5 19.5V0.5H0.5V19.5H19.5Z\" fill=\"white\" stroke=\"white\"/>\r\n      </mask>\r\n      <g mask=\"url(#mask0_448_13442)\">\r\n        <path d=\"M11.3698 19.259H2.64697V4.72903C2.64697 4.32169 2.90709 3.95989 3.29318 3.83013L11.3698 1.11528V19.259Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M16.2795 17.4364V19.259H11.3701V9.41489H16.7869C17.1459 9.41489 17.4365 9.70552 17.4365 10.0645V15.2754\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M11.3701 19.259H19.2667C19.51 19.259 19.7072 19.0618 19.7072 18.8186V17.4365H11.3701V19.259Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M17.9105 15.2046H18.0759C18.9767 15.2046 19.707 15.9349 19.707 16.8357V17.436H16.2793V16.8357C16.2793 15.9349 17.0096 15.2046 17.9105 15.2046Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M0.733438 19.259H2.64711V17.4365H0.292969V18.8186C0.292969 19.0618 0.490156 19.259 0.733438 19.259Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M2.4168 15.2046H2.64715V17.436H0.292969V17.3284C0.292969 16.1554 1.24387 15.2046 2.4168 15.2046Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M14.4034 9.41431H11.3701V1.11485L14.1142 3.76731C14.299 3.94599 14.4034 4.19208 14.4034 4.44919V8.03329\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M4.78076 3.32971V1.3512C4.78076 1.01487 5.05342 0.742212 5.39014 0.742212H5.45186C5.78818 0.742212 6.06084 1.01487 6.06084 1.3512V2.89924\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M11.3698 3.32935L4.78076 5.54419\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M11.3698 5.43787L4.78076 7.65271\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M11.3698 7.54651L4.78076 9.76135\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M11.3698 9.65552L4.78076 11.8704\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M5.8685 13.6125L4.78076 13.9781\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M11.37 11.7633L7.18066 13.1715\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M8.25287 19.259H4.78076V16.7702C4.78076 16.5772 4.90928 16.4079 5.09514 16.356L7.82197 15.5947C8.03846 15.5343 8.25287 15.697 8.25287 15.9218V19.259Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M15.8193 11.8313H12.9462C12.8352 11.8313 12.7451 11.7413 12.7451 11.6302V10.925C12.7451 10.8139 12.8352 10.7239 12.9462 10.7239H15.8193C15.9304 10.7239 16.0204 10.8139 16.0204 10.925V11.6302C16.0204 11.7413 15.9304 11.8313 15.8193 11.8313Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M15.8193 14.0582H12.9462C12.8352 14.0582 12.7451 13.9682 12.7451 13.8571V13.1519C12.7451 13.0408 12.8352 12.9508 12.9462 12.9508H15.8193C15.9304 12.9508 16.0204 13.0408 16.0204 13.1519V13.8571C16.0204 13.9682 15.9304 14.0582 15.8193 14.0582Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M13.8965 16.3091H12.9462C12.8352 16.3091 12.7451 16.219 12.7451 16.108V15.4028C12.7451 15.2917 12.8352 15.2017 12.9462 15.2017H13.8965C14.0076 15.2017 14.0977 15.2917 14.0977 15.4028V16.108C14.0977 16.219 14.0076 16.3091 13.8965 16.3091Z\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n        <path d=\"M6.51709 15.9591V19.259\" stroke=\"#0D47A1\" stroke-width=\"1\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n      </g>\r\n    </svg>\r\n  `;\r\n\r\n  adSvg = `\r\n    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"#1a3c8b\"/>\r\n    </svg>\r\n  `;\r\n\r\n  developerSvg = `\r\n    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <g clip-path=\"url(#clip0_136_62239)\">\r\n        <path stroke=\"#0D47A1\" fill=\"#0D47A1\" stroke-width=\"0.5\" d=\"M19.4998 18.9999H18.9998V11H17.9998V10H16.9998V11H15.9998V13H14.9999V11V9.00003H13.9999V11H12.9999V8.00004H11.9999V11V13H10.9999V11H9.9999V9.00003H8.99991V11H7.99992V18.9999H5.99994V8.00004H6.99993C7.55227 8.00004 7.99992 7.55239 7.99992 7.00005V6.00006H16.9998V9.00003H17.9998V6.00006H18.9998C19.4635 6.00006 19.8665 5.68108 19.9731 5.22972C20.0798 4.7784 19.8618 4.31305 19.4471 4.10575L17.4471 3.10576C17.3082 3.03642 17.1551 3.00009 16.9998 3.00009H6.10928L4.89429 0.552773C4.7223 0.208792 4.3723 0.00012207 4.00031 0.00012207C3.92399 0.00012207 3.84699 0.00879386 3.77031 0.027114C3.31895 0.133441 2.99997 0.536445 2.99997 1.00011V3.00009H0.99999C0.447652 3.00009 0 3.44774 0 4.00008V5.00007C0 5.55241 0.447652 6.00006 0.99999 6.00006H2.99997V7.00005V18.9999H0.499995C0.22367 18.9999 0 19.2236 0 19.4999C0 19.7763 0.22367 19.9999 0.499995 19.9999H19.4998C19.7761 19.9999 19.9998 19.7763 19.9998 19.4999C19.9998 19.2236 19.7761 18.9999 19.4998 18.9999ZM2.99997 5.00007H0.99999V4.00008H2.99997V5.00007ZM16.9998 4.00008L18.9998 5.00007H7.99992C7.99992 4.73472 7.89457 4.48043 7.70692 4.29309L7.41426 4.00008H16.9998ZM3.99996 1.00011L4.99995 3.00009H3.99996V1.00011ZM3.99996 4.00008H5.99994L6.99993 5.00007V7.00005H3.99996V4.00008ZM4.99995 18.9999H3.99996V8.00004H4.99995V18.9999ZM17.9998 18.9999H8.99991V12H9.9999V14H12.9999V12H13.9999V14H16.9998V12H17.9998V18.9999Z\"/>\r\n      </g>\r\n      <defs>\r\n        <clipPath id=\"clip0_136_62239\">\r\n          <rect width=\"20\" height=\"20\" fill=\"white\"/>\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  `;\r\n\r\n  onFilterChanged(payload: { specializationScope: string | null }): void {\r\n    this.fetchStatistics(payload.specializationScope);\r\n  }\r\n\r\n  fetchStatistics(specializationScope?: string | null): void {\r\n    this.loading = true;\r\n\r\n    this.brokerService.getBrokerRequestStatistics(this.userId, specializationScope).subscribe({\r\n      next: (response: any) => {\r\n        this.allRequestsCount = response.data.allRequestsCount || 0;\r\n        this.newRequestsCount = response.data.newRequestsCount || 0;\r\n        this.inProcessingRequestsCount = response.data.inProcessingRequestsCount || 0;\r\n        this.finishedRequestsCount = response.data.finishedRequestsCount || 0;\r\n\r\n        const total = this.allRequestsCount || 1;\r\n        this.newRequestsPercent = parseFloat(((this.newRequestsCount / total) * 100).toFixed(1));\r\n        this.inProcessingRequestsPercent = parseFloat(((this.inProcessingRequestsCount / total) * 100).toFixed(1));\r\n        this.finishedRequestsPercent = parseFloat(((this.finishedRequestsCount / total) * 100).toFixed(1));\r\n\r\n        this.cd.detectChanges();\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        this.errorMessage = 'Failed to load requests';\r\n        console.error(error);\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  fetchDashboardStatistics(): void {\r\n\r\n    this.loading = true;\r\n    this.brokerService.getBrokerDashboardStatistics(this.brokerId).subscribe({\r\n      next: (response: any) => {\r\n        this.totalContractRequests = response.data.totalContractRequests || 0;\r\n        this.acceptedContractsCount = response.data.acceptedContractsCount || 0;\r\n        this.maps = response.data.maps || 0;\r\n        this.dataAndProperties = response.data.dataAndProperties || 0;\r\n        this.advertisements = response.data.advertisements || 0;\r\n\r\n        this.cd.detectChanges();\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        this.errorMessage = 'Failed to load broker statistics';\r\n        console.error(error);\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<div class=\"mb-5 mt-0\">\r\n  <app-broker-title></app-broker-title>\r\n</div>\r\n\r\n<div class=\"row\">\r\n  <div class=\"col-md-6\">\r\n    <app-new-request-card [title]=\"'Create New Request'\"\r\n      [subTitle]=\"'Over ' + allRequestsCount + ' Request' + (allRequestsCount === 1 ? '' : 's')\"\r\n      [buttonTitle]=\"'Create'\"\r\n      [buttonIcon]=\"'plus'\"\r\n    ></app-new-request-card>\r\n\r\n    <div class=\"row\">\r\n      <div class=\"col-md-6\">\r\n        <app-dashboard-card\r\n          [title]=\"'Developers'\"\r\n          [subTitle]=\"'Over '  + acceptedContractsCount + ' developers'\"\r\n          [icon]=\"{ type: 'svg', svgContent: developerSvg }\"\r\n          [buttonTitle]=\"'View all'\"\r\n          [buttonIcon]=\"'angles-right'\"\r\n          [buttonLink]=\"'/broker/developers'\"\r\n        ></app-dashboard-card>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <app-dashboard-card\r\n          [title]=\"'My Maps'\"\r\n          [subTitle]=\"'Over ' + maps + ' Maps'\"\r\n          [icon]=\"{ type: 'fontawesome', name: 'map' }\"\r\n          [buttonTitle]=\"'View all'\"\r\n          [buttonIcon]=\"'angles-right'\"\r\n          [buttonLink]=\"'/broker/Maps'\"\r\n        ></app-dashboard-card>\r\n      </div>\r\n    </div>\r\n\r\n    <app-dashboard-card\r\n      [title]=\"'My Advertisements'\"\r\n      [subTitle]=\"'Over ' + advertisements + ' Ads'\"\r\n      [icon]=\"{ type: 'keenicon', name: 'social-media', iconType: 'outline' }\"\r\n      [buttonTitle]=\"'View all'\"\r\n      [buttonIcon]=\"'angles-right'\"\r\n      [buttonLink]=\"'/broker/Adds'\"\r\n    ></app-dashboard-card>\r\n\r\n    <app-dashboard-card\r\n          [title]=\"'Data and Properties'\"\r\n          [subTitle]=\"'Over ' + dataAndProperties + ' Properties'\"\r\n          [icon]=\"{ type: 'svg', svgContent: propertySvg }\"\r\n          [buttonTitle]=\"'View all'\"\r\n          [buttonIcon]=\"'angles-right'\"\r\n          [buttonLink]=\"'/broker/dataandproperties'\"\r\n      ></app-dashboard-card>\r\n  </div>\r\n\r\n  <div class=\"col-md-6\" *ngIf=\"allRequestsCount != 0\">\r\n    <div class=\"card mb-4\">\r\n      <div class=\"row g-0\">\r\n        <!-- Left Column -->\r\n        <div class=\"col-md-6\">\r\n          <div class=\"card-header border-0 pb-1\">\r\n            <h3 class=\"card-title align-items-start flex-column\">\r\n              <span class=\"card-label fw-bolder fs-3 mb-1 text-dark-blue\">\r\n                <svg width=\"19\" height=\"19\" viewBox=\"0 0 19 19\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <g clip-path=\"url(#clip0_24_2533)\">\r\n                    <path stroke=\"#0D47A1\" stroke-width=\"1\" d=\"M6.53125 10.0938C7.02313 10.0938 7.42188 9.695 7.42188 9.20312C7.42188 8.71125 7.02313 8.3125 6.53125 8.3125C6.03937 8.3125 5.64062 8.71125 5.64062 9.20312C5.64062 9.695 6.03937 10.0938 6.53125 10.0938Z\"/>\r\n                    <path stroke=\"#0D47A1\" stroke-width=\"1\" d=\"M7.125 7.125H5.9375V4.75H7.125C7.43994 4.75 7.74199 4.62489 7.96469 4.40219C8.18739 4.17949 8.3125 3.87744 8.3125 3.5625C8.3125 3.24756 8.18739 2.94551 7.96469 2.72281C7.74199 2.50011 7.43994 2.375 7.125 2.375H5.9375C5.62267 2.37536 5.32083 2.50059 5.09821 2.72321C4.87559 2.94583 4.75036 3.24767 4.75 3.5625V3.85938H3.5625V3.5625C3.56321 2.93283 3.81366 2.32915 4.2589 1.8839C4.70415 1.43866 5.30783 1.18821 5.9375 1.1875H7.125C7.75489 1.1875 8.35898 1.43772 8.80438 1.88312C9.24978 2.32852 9.5 2.93261 9.5 3.5625C9.5 4.19239 9.24978 4.79648 8.80438 5.24188C8.35898 5.68728 7.75489 5.9375 7.125 5.9375V7.125Z\"/>\r\n                    <path stroke=\"#0D47A1\" stroke-width=\"1\" d=\"M13.3284 12.4887C13.9224 11.7779 14.3579 10.9487 14.6059 10.0562C14.8539 9.1638 14.9088 8.22873 14.7668 7.31342C14.6248 6.39811 14.2892 5.52361 13.7825 4.74825C13.2758 3.9729 12.6095 3.31453 11.8282 2.81708L11.235 3.84427C12.0092 4.35075 12.6386 5.04962 13.0615 5.87244C13.4844 6.69526 13.6864 7.61381 13.6476 8.53814C13.6089 9.46247 13.3308 10.3609 12.8405 11.1454C12.3502 11.93 11.6645 12.5737 10.8506 13.0136C10.0368 13.4536 9.12262 13.6746 8.19768 13.655C7.27275 13.6355 6.36874 13.376 5.57419 12.9021C4.77965 12.4282 4.1218 11.7561 3.66508 10.9515C3.20836 10.147 2.96841 9.23762 2.96875 8.31247H1.78125C1.7803 9.55369 2.13332 10.7694 2.7989 11.8171C3.46449 12.8648 4.41503 13.7009 5.53904 14.2274C6.66304 14.754 7.91388 14.9491 9.14484 14.7898C10.3758 14.6306 11.5358 14.1236 12.4888 13.3284L16.9729 17.8125L17.8125 16.9728L13.3284 12.4887Z\"/>\r\n                  </g>\r\n                  <defs>\r\n                    <clipPath id=\"clip0_24_2533\">\r\n                      <rect width=\"19\" height=\"19\" fill=\"white\"/>\r\n                    </clipPath>\r\n                  </defs>\r\n                </svg>\r\n                Requests and Sales Statistics\r\n              </span>\r\n            </h3>\r\n          </div>\r\n          <div class=\"card-body pt-0 pb-1\">\r\n            <app-analysis-card\r\n              [backgroundColor]=\"'success'\"\r\n              [title]=\"'Finished Requests'\"\r\n              [totalRequests]=\"allRequestsCount\"\r\n              [activeRequests]=\"finishedRequestsCount\"\r\n            ></app-analysis-card>\r\n            <app-analysis-card\r\n              [backgroundColor]=\"'mid-blue'\"\r\n              [title]=\"'In Processing Requests'\"\r\n              [totalRequests]=\"allRequestsCount\"\r\n              [activeRequests]=\"inProcessingRequestsCount\"\r\n            ></app-analysis-card>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Right Column -->\r\n        <div class=\"col-md-6\">\r\n          <div class=\"card-header border-0 mb-1\">\r\n            <h3 class=\"card-title align-items-start flex-column\">\r\n              <span class=\"card-label fw-bolder fs-3 mb-1 text-dark-blue\">Filter Requests</span>\r\n              <span class=\"text-muted fw-bold fs-8\">Filter requests by specializations</span>\r\n            </h3>\r\n            <div class=\"card-toolbar\">\r\n              <button\r\n                type=\"button\"\r\n                class=\"btn btn-sm btn-icon btn-light-dark-blue btn-active-dark-blue\"\r\n                (click)=\"specializationsFilter.toggleDropdown()\"\r\n              >\r\n                <i class=\"fa-solid fa-filter\"></i>\r\n              </button>\r\n              <app-specializations-filter\r\n                #specializationsFilter\r\n                (filterChanged)=\"onFilterChanged($event)\"\r\n                [ngClass]=\"{'show': specializationsFilter.isDropdownOpen}\"\r\n              ></app-specializations-filter>\r\n            </div>\r\n          </div>\r\n          <div class=\"card-body py-3\">\r\n            <div class=\"row\">\r\n              <app-pie-chart\r\n                *ngIf=\"allRequestsCount > 0\"\r\n                [newRequestsPercent]=\"newRequestsPercent\"\r\n                [inProcessingRequestsPercent]=\"inProcessingRequestsPercent\"\r\n                [finishedRequestsPercent]=\"finishedRequestsPercent\"\r\n                [chartSize]=\"150\"\r\n                [chartLine]=\"30\"\r\n                [chartRotate]=\"145\"\r\n                cssClass=\"your-css-class\"\r\n              ></app-pie-chart>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"row mt-5 mb-5\" *ngIf=\"allRequestsCount != 0\">\r\n  <app-new-requests></app-new-requests>\r\n</div>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;ICsHcA,EAAA,CAAAC,SAAA,wBASiB;;;;IAFfD,EALA,CAAAE,UAAA,uBAAAC,MAAA,CAAAC,kBAAA,CAAyC,gCAAAD,MAAA,CAAAE,2BAAA,CACkB,4BAAAF,MAAA,CAAAG,uBAAA,CACR,kBAClC,iBACD,oBACG;;;;;;IAhErBN,EAPZ,CAAAO,cAAA,aAAoD,aAC3B,aACA,aAEG,cACmB,aACgB,eACS;;IAExDP,EADF,CAAAO,cAAA,cAA+F,YAC1D;IAGjCP,EAFA,CAAAC,SAAA,eAAwP,gBACuZ,gBAC4O;IAC73BD,EAAA,CAAAQ,YAAA,EAAI;IAEFR,EADF,CAAAO,cAAA,YAAM,oBACyB;IAC3BP,EAAA,CAAAC,SAAA,gBAA2C;IAGjDD,EAFI,CAAAQ,YAAA,EAAW,EACN,EACH;IACNR,EAAA,CAAAS,MAAA,uCACF;IAEJT,EAFI,CAAAQ,YAAA,EAAO,EACJ,EACD;;IACNR,EAAA,CAAAO,cAAA,eAAiC;IAO/BP,EANA,CAAAC,SAAA,6BAKqB,6BAMA;IAEzBD,EADE,CAAAQ,YAAA,EAAM,EACF;IAMAR,EAHN,CAAAO,cAAA,cAAsB,eACmB,cACgB,gBACS;IAAAP,EAAA,CAAAS,MAAA,uBAAe;IAAAT,EAAA,CAAAQ,YAAA,EAAO;IAClFR,EAAA,CAAAO,cAAA,gBAAsC;IAAAP,EAAA,CAAAS,MAAA,0CAAkC;IAC1ET,EAD0E,CAAAQ,YAAA,EAAO,EAC5E;IAEHR,EADF,CAAAO,cAAA,eAA0B,kBAKvB;IADCP,EAAA,CAAAU,UAAA,mBAAAC,kEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,wBAAA,GAAAd,EAAA,CAAAe,WAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,wBAAA,CAAAG,cAAA,EAAsC;IAAA,EAAC;IAEhDjB,EAAA,CAAAC,SAAA,aAAkC;IACpCD,EAAA,CAAAQ,YAAA,EAAS;IACTR,EAAA,CAAAO,cAAA,yCAIC;IAFCP,EAAA,CAAAU,UAAA,2BAAAQ,8FAAAC,MAAA;MAAAnB,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAH,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAgB,WAAA,CAAiBb,MAAA,CAAAkB,eAAA,CAAAF,MAAA,CAAuB;IAAA,EAAC;IAI/CnB,EAFK,CAAAQ,YAAA,EAA6B,EAC1B,EACF;IAEJR,EADF,CAAAO,cAAA,eAA4B,cACT;IACfP,EAAA,CAAAsB,UAAA,KAAAC,yDAAA,4BASC;IAMbvB,EALU,CAAAQ,YAAA,EAAM,EACF,EACF,EACF,EACF,EACF;;;;;IArDMR,EAAA,CAAAwB,SAAA,IAA6B;IAG7BxB,EAHA,CAAAE,UAAA,8BAA6B,8BACA,kBAAAC,MAAA,CAAAsB,gBAAA,CACK,mBAAAtB,MAAA,CAAAuB,qBAAA,CACM;IAGxC1B,EAAA,CAAAwB,SAAA,EAA8B;IAG9BxB,EAHA,CAAAE,UAAA,+BAA8B,mCACI,kBAAAC,MAAA,CAAAsB,gBAAA,CACA,mBAAAtB,MAAA,CAAAwB,yBAAA,CACU;IAuB1C3B,EAAA,CAAAwB,SAAA,IAA0D;IAA1DxB,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAA4B,eAAA,KAAAC,GAAA,EAAAf,wBAAA,CAAAgB,cAAA,EAA0D;IAOzD9B,EAAA,CAAAwB,SAAA,GAA0B;IAA1BxB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAsB,gBAAA,KAA0B;;;;;IAiB3CzB,EAAA,CAAAO,cAAA,cAAyD;IACvDP,EAAA,CAAAC,SAAA,uBAAqC;IACvCD,EAAA,CAAAQ,YAAA,EAAM;;;ADjIN,OAAM,MAAOuB,wBAAwB;EAsBbC,EAAA;EAA+BC,aAAA;EApB5CC,QAAQ,GAAW,EAAE;EAE9BC,MAAM;EACNC,QAAQ;EACRX,gBAAgB,GAAW,CAAC;EAC5BY,gBAAgB,GAAW,CAAC;EAC5BV,yBAAyB,GAAW,CAAC;EACrCD,qBAAqB,GAAW,CAAC;EACjCtB,kBAAkB,GAAW,CAAC;EAC9BC,2BAA2B,GAAW,CAAC;EACvCC,uBAAuB,GAAW,CAAC;EAEnCgC,qBAAqB,GAAW,CAAC;EACjCC,sBAAsB,GAAW,CAAC;EAClCC,IAAI,GAAW,CAAC;EAChBC,iBAAiB,GAAW,CAAC;EAC7BC,cAAc,GAAW,CAAC;EAC1BC,OAAO,GAAG,KAAK;EACfC,YAAY,GAAG,EAAE;EAEjBC,YAAsBb,EAAqB,EAAUC,aAA4B;IAA3D,KAAAD,EAAE,GAAFA,EAAE;IAA6B,KAAAC,aAAa,GAAbA,aAAa;EAClE;EAEAa,QAAQA,CAAA;IACN,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACpD,IAAIC,IAAI,GAAGH,QAAQ,GAAGI,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC,GAAG,IAAI;IACjD,IAAI,CAACZ,MAAM,GAAGe,IAAI,EAAEG,EAAE;IACtB,IAAI,CAACjB,QAAQ,GAAGc,IAAI,EAAEd,QAAQ;IAC9B,IAAI,CAACkB,eAAe,EAAE;IACtB,IAAI,CAACC,wBAAwB,EAAE;EACjC;EAEAC,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2Bb;EAEDC,KAAK,GAAG;;;;GAIP;EAEDC,YAAY,GAAG;;;;;;;;;;;GAWd;EAEDrC,eAAeA,CAACsC,OAA+C;IAC7D,IAAI,CAACL,eAAe,CAACK,OAAO,CAACC,mBAAmB,CAAC;EACnD;EAEAN,eAAeA,CAACM,mBAAmC;IACjD,IAAI,CAACjB,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACV,aAAa,CAAC4B,0BAA0B,CAAC,IAAI,CAAC1B,MAAM,EAAEyB,mBAAmB,CAAC,CAACE,SAAS,CAAC;MACxFC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACvC,gBAAgB,GAAGuC,QAAQ,CAACC,IAAI,CAACxC,gBAAgB,IAAI,CAAC;QAC3D,IAAI,CAACY,gBAAgB,GAAG2B,QAAQ,CAACC,IAAI,CAAC5B,gBAAgB,IAAI,CAAC;QAC3D,IAAI,CAACV,yBAAyB,GAAGqC,QAAQ,CAACC,IAAI,CAACtC,yBAAyB,IAAI,CAAC;QAC7E,IAAI,CAACD,qBAAqB,GAAGsC,QAAQ,CAACC,IAAI,CAACvC,qBAAqB,IAAI,CAAC;QAErE,MAAMwC,KAAK,GAAG,IAAI,CAACzC,gBAAgB,IAAI,CAAC;QACxC,IAAI,CAACrB,kBAAkB,GAAG+D,UAAU,CAAC,CAAE,IAAI,CAAC9B,gBAAgB,GAAG6B,KAAK,GAAI,GAAG,EAAEE,OAAO,CAAC,CAAC,CAAC,CAAC;QACxF,IAAI,CAAC/D,2BAA2B,GAAG8D,UAAU,CAAC,CAAE,IAAI,CAACxC,yBAAyB,GAAGuC,KAAK,GAAI,GAAG,EAAEE,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1G,IAAI,CAAC9D,uBAAuB,GAAG6D,UAAU,CAAC,CAAE,IAAI,CAACzC,qBAAqB,GAAGwC,KAAK,GAAI,GAAG,EAAEE,OAAO,CAAC,CAAC,CAAC,CAAC;QAElG,IAAI,CAACpC,EAAE,CAACqC,aAAa,EAAE;QACvB,IAAI,CAAC1B,OAAO,GAAG,KAAK;MACtB,CAAC;MACD2B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC1B,YAAY,GAAG,yBAAyB;QAC7C2B,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;QACpB,IAAI,CAAC3B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAY,wBAAwBA,CAAA;IAEtB,IAAI,CAACZ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACV,aAAa,CAACuC,4BAA4B,CAAC,IAAI,CAACpC,QAAQ,CAAC,CAAC0B,SAAS,CAAC;MACvEC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAC1B,qBAAqB,GAAG0B,QAAQ,CAACC,IAAI,CAAC3B,qBAAqB,IAAI,CAAC;QACrE,IAAI,CAACC,sBAAsB,GAAGyB,QAAQ,CAACC,IAAI,CAAC1B,sBAAsB,IAAI,CAAC;QACvE,IAAI,CAACC,IAAI,GAAGwB,QAAQ,CAACC,IAAI,CAACzB,IAAI,IAAI,CAAC;QACnC,IAAI,CAACC,iBAAiB,GAAGuB,QAAQ,CAACC,IAAI,CAACxB,iBAAiB,IAAI,CAAC;QAC7D,IAAI,CAACC,cAAc,GAAGsB,QAAQ,CAACC,IAAI,CAACvB,cAAc,IAAI,CAAC;QAEvD,IAAI,CAACV,EAAE,CAACqC,aAAa,EAAE;QACvB,IAAI,CAAC1B,OAAO,GAAG,KAAK;MACtB,CAAC;MACD2B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC1B,YAAY,GAAG,kCAAkC;QACtD2B,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;QACpB,IAAI,CAAC3B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;;qCApIWZ,wBAAwB,EAAA/B,EAAA,CAAAyE,iBAAA,CAAAzE,EAAA,CAAA0E,iBAAA,GAAA1E,EAAA,CAAAyE,iBAAA,CAAAE,EAAA,CAAAC,aAAA;EAAA;;UAAxB7C,wBAAwB;IAAA8C,SAAA;IAAAC,MAAA;MAAA5C,QAAA;IAAA;IAAA6C,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTrCpF,EAAA,CAAAO,cAAA,aAAuB;QACrBP,EAAA,CAAAC,SAAA,uBAAqC;QACvCD,EAAA,CAAAQ,YAAA,EAAM;QAGJR,EADF,CAAAO,cAAA,aAAiB,aACO;QACpBP,EAAA,CAAAC,SAAA,8BAIwB;QAGtBD,EADF,CAAAO,cAAA,aAAiB,aACO;QACpBP,EAAA,CAAAC,SAAA,4BAOsB;QACxBD,EAAA,CAAAQ,YAAA,EAAM;QACNR,EAAA,CAAAO,cAAA,aAAsB;QACpBP,EAAA,CAAAC,SAAA,4BAOsB;QAE1BD,EADE,CAAAQ,YAAA,EAAM,EACF;QAWNR,EATA,CAAAC,SAAA,6BAOsB,6BASE;QAC1BD,EAAA,CAAAQ,YAAA,EAAM;QAENR,EAAA,CAAAsB,UAAA,KAAAgE,wCAAA,mBAAoD;QAgFtDtF,EAAA,CAAAQ,YAAA,EAAM;QAENR,EAAA,CAAAsB,UAAA,KAAAiE,wCAAA,iBAAyD;;;QAlI/BvF,EAAA,CAAAwB,SAAA,GAA8B;QAGlDxB,EAHoB,CAAAE,UAAA,+BAA8B,uBAAAmF,GAAA,CAAA5D,gBAAA,iBAAA4D,GAAA,CAAA5D,gBAAA,mBACwC,yBAClE,sBACH;QAMjBzB,EAAA,CAAAwB,SAAA,GAAsB;QAKtBxB,EALA,CAAAE,UAAA,uBAAsB,uBAAAmF,GAAA,CAAA9C,sBAAA,iBACwC,SAAAvC,EAAA,CAAA4B,eAAA,KAAA4D,GAAA,EAAAH,GAAA,CAAA3B,YAAA,EACZ,2BACxB,8BACG,oCACM;QAKnC1D,EAAA,CAAAwB,SAAA,GAAmB;QAKnBxB,EALA,CAAAE,UAAA,oBAAmB,uBAAAmF,GAAA,CAAA7C,IAAA,WACkB,SAAAxC,EAAA,CAAAyF,eAAA,KAAAC,GAAA,EACQ,2BACnB,8BACG,8BACA;QAMjC1F,EAAA,CAAAwB,SAAA,EAA6B;QAK7BxB,EALA,CAAAE,UAAA,8BAA6B,uBAAAmF,GAAA,CAAA3C,cAAA,UACiB,SAAA1C,EAAA,CAAAyF,eAAA,KAAAE,GAAA,EAC0B,2BAC9C,8BACG,8BACA;QAIzB3F,EAAA,CAAAwB,SAAA,EAA+B;QAK/BxB,EALA,CAAAE,UAAA,gCAA+B,uBAAAmF,GAAA,CAAA5C,iBAAA,iBACyB,SAAAzC,EAAA,CAAA4B,eAAA,KAAA4D,GAAA,EAAAH,GAAA,CAAA7B,WAAA,EACP,2BACvB,8BACG,2CACa;QAI3BxD,EAAA,CAAAwB,SAAA,EAA2B;QAA3BxB,EAAA,CAAAE,UAAA,SAAAmF,GAAA,CAAA5D,gBAAA,MAA2B;QAkFxBzB,EAAA,CAAAwB,SAAA,EAA2B;QAA3BxB,EAAA,CAAAE,UAAA,SAAAmF,GAAA,CAAA5D,gBAAA,MAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}