{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class HomeComponent {\n  static ɵfac = function HomeComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HomeComponent)();\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomeComponent,\n    selectors: [[\"app-home\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 225,\n    vars: 0,\n    consts: [[1, \"home-header\"], [1, \"container-fluid\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\", \"py-3\"], [1, \"header-logo\"], [\"routerLink\", \"/dashboard\", 1, \"d-flex\", \"align-items-center\"], [\"alt\", \"Easy Deal Logo\", \"src\", \"./assets/media/easydeallogos/home-logo.png\", 1, \"h-40px\"], [1, \"header-nav\", \"d-none\", \"d-lg-flex\"], [1, \"nav-list\", \"d-flex\", \"align-items-center\", \"mb-0\"], [1, \"nav-item\"], [\"href\", \"#\", 1, \"nav-link\"], [1, \"mobile-menu-toggle\", \"d-lg-none\"], [\"type\", \"button\", \"data-bs-toggle\", \"collapse\", \"data-bs-target\", \"#mobileNav\", 1, \"btn\", \"btn-icon\"], [1, \"fas\", \"fa-bars\", \"fs-2\"], [\"id\", \"mobileNav\", 1, \"collapse\"], [1, \"mobile-nav\", \"py-3\"], [1, \"nav-list-mobile\"], [1, \"nav-item-mobile\"], [\"href\", \"#\", 1, \"nav-link-mobile\"], [1, \"hero-section\"], [1, \"hero-background\"], [1, \"hero-overlay\"], [1, \"container\"], [1, \"row\", \"min-vh-100\", \"align-items-center\"], [1, \"col-lg-6\"], [1, \"hero-content\"], [1, \"hero-title\"], [1, \"hero-subtitle\"], [1, \"hero-search-form\"], [1, \"search-tabs\"], [\"type\", \"button\", 1, \"search-tab\", \"active\"], [\"type\", \"button\", 1, \"search-tab\"], [1, \"search-inputs\"], [1, \"search-input-group\"], [1, \"fas\", \"fa-map-marker-alt\"], [1, \"form-select\"], [1, \"fas\", \"fa-home\"], [1, \"fas\", \"fa-dollar-sign\"], [1, \"btn\", \"btn-primary\", \"search-btn\"], [1, \"fas\", \"fa-search\"], [1, \"features-section\", \"py-5\"], [1, \"row\", \"text-center\", \"mb-5\"], [1, \"col-12\"], [1, \"section-title\"], [1, \"section-subtitle\"], [1, \"row\", \"g-4\"], [1, \"col-lg-4\", \"col-md-6\"], [1, \"feature-card\"], [1, \"feature-icon\"], [1, \"fas\", \"fa-search\", \"text-primary\"], [1, \"feature-title\"], [1, \"feature-description\"], [1, \"fas\", \"fa-shield-alt\", \"text-primary\"], [1, \"fas\", \"fa-headset\", \"text-primary\"], [1, \"featured-properties-section\", \"py-5\", \"bg-light\"], [1, \"property-card\"], [1, \"property-image\"], [\"src\", \"./assets/media/easydeallogos/empty-living-room-with-blue-sofa-plants-table-empty-white-wall-background-3d-rendering (1) 1.png\", \"alt\", \"Property\", 1, \"w-100\"], [1, \"property-badges\"], [1, \"badge\", \"bg-success\"], [1, \"property-content\"], [1, \"property-title\"], [1, \"property-location\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-1\"], [1, \"property-details\"], [1, \"property-detail\"], [1, \"fas\", \"fa-bed\"], [1, \"fas\", \"fa-bath\"], [1, \"fas\", \"fa-ruler-combined\"], [1, \"property-price\"], [1, \"price\"], [1, \"currency\"], [1, \"footer-section\", \"bg-dark\", \"text-white\", \"py-5\"], [1, \"row\"], [1, \"col-lg-4\", \"col-md-6\", \"mb-4\"], [1, \"footer-brand\"], [\"src\", \"./assets/media/easydeallogos/home-logo.png\", \"alt\", \"Easy Deal\", 1, \"h-40px\", \"mb-3\"], [1, \"footer-description\"], [1, \"col-lg-2\", \"col-md-6\", \"mb-4\"], [1, \"footer-title\"], [1, \"footer-links\"], [\"href\", \"#\"], [1, \"col-lg-3\", \"col-md-6\", \"mb-4\"], [1, \"footer-contact\"], [1, \"fas\", \"fa-phone\", \"me-2\"], [1, \"fas\", \"fa-envelope\", \"me-2\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-2\"], [1, \"footer-social\"], [\"href\", \"#\", 1, \"social-link\"], [1, \"fab\", \"fa-facebook-f\"], [1, \"fab\", \"fa-twitter\"], [1, \"fab\", \"fa-instagram\"], [1, \"fab\", \"fa-linkedin-in\"], [1, \"my-4\"], [1, \"col-12\", \"text-center\"], [1, \"footer-copyright\", \"mb-0\"]],\n    template: function HomeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"header\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4);\n        i0.ɵɵelement(5, \"img\", 5);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(6, \"nav\", 6)(7, \"ul\", 7)(8, \"li\", 8)(9, \"a\", 9);\n        i0.ɵɵtext(10, \"Home\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"li\", 8)(12, \"a\", 9);\n        i0.ɵɵtext(13, \"Properties\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"li\", 8)(15, \"a\", 9);\n        i0.ɵɵtext(16, \"New Projects\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"li\", 8)(18, \"a\", 9);\n        i0.ɵɵtext(19, \"About Us\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"li\", 8)(21, \"a\", 9);\n        i0.ɵɵtext(22, \"Contact\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(23, \"div\", 10)(24, \"button\", 11);\n        i0.ɵɵelement(25, \"i\", 12);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(26, \"div\", 13)(27, \"nav\", 14)(28, \"ul\", 15)(29, \"li\", 16)(30, \"a\", 17);\n        i0.ɵɵtext(31, \"Home\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"li\", 16)(33, \"a\", 17);\n        i0.ɵɵtext(34, \"Properties\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(35, \"li\", 16)(36, \"a\", 17);\n        i0.ɵɵtext(37, \"New Projects\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(38, \"li\", 16)(39, \"a\", 17);\n        i0.ɵɵtext(40, \"About Us\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(41, \"li\", 16)(42, \"a\", 17);\n        i0.ɵɵtext(43, \"Contact\");\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(44, \"section\", 18)(45, \"div\", 19);\n        i0.ɵɵelement(46, \"div\", 20);\n        i0.ɵɵelementStart(47, \"div\", 21)(48, \"div\", 22)(49, \"div\", 23)(50, \"div\", 24)(51, \"h1\", 25);\n        i0.ɵɵtext(52, \"Find Your Dream Property\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(53, \"p\", 26);\n        i0.ɵɵtext(54, \" Discover the perfect home or investment opportunity with Easy Deal. Browse thousands of properties and connect with trusted real estate professionals. \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(55, \"div\", 23)(56, \"div\", 27)(57, \"div\", 28)(58, \"button\", 29);\n        i0.ɵɵtext(59, \"Buy\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"button\", 30);\n        i0.ɵɵtext(61, \"Rent\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(62, \"button\", 30);\n        i0.ɵɵtext(63, \"Sell\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(64, \"div\", 31)(65, \"div\", 32);\n        i0.ɵɵelement(66, \"i\", 33);\n        i0.ɵɵelementStart(67, \"select\", 34)(68, \"option\");\n        i0.ɵɵtext(69, \"Select Location\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(70, \"option\");\n        i0.ɵɵtext(71, \"Cairo\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(72, \"option\");\n        i0.ɵɵtext(73, \"Alexandria\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(74, \"option\");\n        i0.ɵɵtext(75, \"Giza\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(76, \"div\", 32);\n        i0.ɵɵelement(77, \"i\", 35);\n        i0.ɵɵelementStart(78, \"select\", 34)(79, \"option\");\n        i0.ɵɵtext(80, \"Property Type\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(81, \"option\");\n        i0.ɵɵtext(82, \"Apartment\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(83, \"option\");\n        i0.ɵɵtext(84, \"Villa\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(85, \"option\");\n        i0.ɵɵtext(86, \"Office\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(87, \"div\", 32);\n        i0.ɵɵelement(88, \"i\", 36);\n        i0.ɵɵelementStart(89, \"select\", 34)(90, \"option\");\n        i0.ɵɵtext(91, \"Price Range\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(92, \"option\");\n        i0.ɵɵtext(93, \"Under 1M\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(94, \"option\");\n        i0.ɵɵtext(95, \"1M - 5M\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(96, \"option\");\n        i0.ɵɵtext(97, \"5M+\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(98, \"button\", 37);\n        i0.ɵɵelement(99, \"i\", 38);\n        i0.ɵɵtext(100, \" Search \");\n        i0.ɵɵelementEnd()()()()()()()();\n        i0.ɵɵelementStart(101, \"section\", 39)(102, \"div\", 21)(103, \"div\", 40)(104, \"div\", 41)(105, \"h2\", 42);\n        i0.ɵɵtext(106, \"Why Choose Easy Deal?\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(107, \"p\", 43);\n        i0.ɵɵtext(108, \"Your trusted partner in real estate\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(109, \"div\", 44)(110, \"div\", 45)(111, \"div\", 46)(112, \"div\", 47);\n        i0.ɵɵelement(113, \"i\", 48);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(114, \"h4\", 49);\n        i0.ɵɵtext(115, \"Easy Search\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(116, \"p\", 50);\n        i0.ɵɵtext(117, \" Find properties quickly with our advanced search filters and smart recommendations. \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(118, \"div\", 45)(119, \"div\", 46)(120, \"div\", 47);\n        i0.ɵɵelement(121, \"i\", 51);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(122, \"h4\", 49);\n        i0.ɵɵtext(123, \"Verified Properties\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(124, \"p\", 50);\n        i0.ɵɵtext(125, \" All properties are verified and checked by our professional team for your safety. \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(126, \"div\", 45)(127, \"div\", 46)(128, \"div\", 47);\n        i0.ɵɵelement(129, \"i\", 52);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(130, \"h4\", 49);\n        i0.ɵɵtext(131, \"24/7 Support\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(132, \"p\", 50);\n        i0.ɵɵtext(133, \" Our dedicated support team is available around the clock to assist you. \");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(134, \"section\", 53)(135, \"div\", 21)(136, \"div\", 40)(137, \"div\", 41)(138, \"h2\", 42);\n        i0.ɵɵtext(139, \"Featured Properties\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(140, \"p\", 43);\n        i0.ɵɵtext(141, \"Discover our handpicked selection of premium properties\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(142, \"div\", 44)(143, \"div\", 45)(144, \"div\", 54)(145, \"div\", 55);\n        i0.ɵɵelement(146, \"img\", 56);\n        i0.ɵɵelementStart(147, \"div\", 57)(148, \"span\", 58);\n        i0.ɵɵtext(149, \"Featured\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(150, \"div\", 59)(151, \"h5\", 60);\n        i0.ɵɵtext(152, \"Modern Apartment in New Cairo\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(153, \"p\", 61);\n        i0.ɵɵelement(154, \"i\", 62);\n        i0.ɵɵtext(155, \" New Cairo, Cairo \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(156, \"div\", 63)(157, \"span\", 64);\n        i0.ɵɵelement(158, \"i\", 65);\n        i0.ɵɵtext(159, \" 3 Beds \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(160, \"span\", 64);\n        i0.ɵɵelement(161, \"i\", 66);\n        i0.ɵɵtext(162, \" 2 Baths \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(163, \"span\", 64);\n        i0.ɵɵelement(164, \"i\", 67);\n        i0.ɵɵtext(165, \" 150 m\\u00B2 \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(166, \"div\", 68)(167, \"span\", 69);\n        i0.ɵɵtext(168, \"2,500,000\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(169, \"span\", 70);\n        i0.ɵɵtext(170, \"EGP\");\n        i0.ɵɵelementEnd()()()()()()()();\n        i0.ɵɵelementStart(171, \"footer\", 71)(172, \"div\", 21)(173, \"div\", 72)(174, \"div\", 73)(175, \"div\", 74);\n        i0.ɵɵelement(176, \"img\", 75);\n        i0.ɵɵelementStart(177, \"p\", 76);\n        i0.ɵɵtext(178, \" Your trusted partner in finding the perfect property. We make real estate simple and accessible for everyone. \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(179, \"div\", 77)(180, \"h5\", 78);\n        i0.ɵɵtext(181, \"Quick Links\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(182, \"ul\", 79)(183, \"li\")(184, \"a\", 80);\n        i0.ɵɵtext(185, \"Home\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(186, \"li\")(187, \"a\", 80);\n        i0.ɵɵtext(188, \"Properties\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(189, \"li\")(190, \"a\", 80);\n        i0.ɵɵtext(191, \"About Us\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(192, \"li\")(193, \"a\", 80);\n        i0.ɵɵtext(194, \"Contact\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(195, \"div\", 81)(196, \"h5\", 78);\n        i0.ɵɵtext(197, \"Contact Info\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(198, \"div\", 82)(199, \"p\");\n        i0.ɵɵelement(200, \"i\", 83);\n        i0.ɵɵtext(201, \" +20 ************\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(202, \"p\");\n        i0.ɵɵelement(203, \"i\", 84);\n        i0.ɵɵtext(204, \" <EMAIL>\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(205, \"p\");\n        i0.ɵɵelement(206, \"i\", 85);\n        i0.ɵɵtext(207, \" Cairo, Egypt\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(208, \"div\", 81)(209, \"h5\", 78);\n        i0.ɵɵtext(210, \"Follow Us\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(211, \"div\", 86)(212, \"a\", 87);\n        i0.ɵɵelement(213, \"i\", 88);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(214, \"a\", 87);\n        i0.ɵɵelement(215, \"i\", 89);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(216, \"a\", 87);\n        i0.ɵɵelement(217, \"i\", 90);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(218, \"a\", 87);\n        i0.ɵɵelement(219, \"i\", 91);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelement(220, \"hr\", 92);\n        i0.ɵɵelementStart(221, \"div\", 72)(222, \"div\", 93)(223, \"p\", 94);\n        i0.ɵɵtext(224, \" \\u00A9 2024 Easy Deal. All rights reserved. \");\n        i0.ɵɵelementEnd()()()()();\n      }\n    },\n    dependencies: [CommonModule, RouterModule, i1.RouterLink],\n    styles: [\".home-header[_ngcontent-%COMP%] {\\n  background: white;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  position: sticky;\\n  top: 0;\\n  z-index: 1000;\\n}\\n.home-header[_ngcontent-%COMP%]   .header-logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .header-logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.home-header[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  gap: 2rem;\\n}\\n.home-header[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  text-decoration: none;\\n  font-weight: 600;\\n  font-size: 1rem;\\n  padding: 0.5rem 0;\\n  position: relative;\\n  transition: color 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: var(--bs-primary);\\n}\\n.home-header[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background: var(--bs-primary);\\n  transition: width 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n.home-header[_ngcontent-%COMP%]   .mobile-menu-toggle[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border: none;\\n  background: transparent;\\n  color: #2c3e50;\\n}\\n.home-header[_ngcontent-%COMP%]   .mobile-menu-toggle[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  color: var(--bs-primary);\\n}\\n.home-header[_ngcontent-%COMP%]   .mobile-nav[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e9ecef;\\n}\\n.home-header[_ngcontent-%COMP%]   .mobile-nav[_ngcontent-%COMP%]   .nav-list-mobile[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n.home-header[_ngcontent-%COMP%]   .mobile-nav[_ngcontent-%COMP%]   .nav-list-mobile[_ngcontent-%COMP%]   .nav-item-mobile[_ngcontent-%COMP%]   .nav-link-mobile[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2c3e50;\\n  text-decoration: none;\\n  font-weight: 600;\\n  padding: 1rem 0;\\n  border-bottom: 1px solid #f8f9fa;\\n  transition: color 0.3s ease;\\n}\\n.home-header[_ngcontent-%COMP%]   .mobile-nav[_ngcontent-%COMP%]   .nav-list-mobile[_ngcontent-%COMP%]   .nav-item-mobile[_ngcontent-%COMP%]   .nav-link-mobile[_ngcontent-%COMP%]:hover {\\n  color: var(--bs-primary);\\n}\\n\\n.hero-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  min-height: 100vh;\\n  overflow: hidden;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);\\n  min-height: 100vh;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .hero-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(30, 60, 114, 0.8);\\n  z-index: 1;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-background[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%] {\\n  color: white;\\n  padding: 2rem 0;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n  font-size: 3.5rem;\\n  font-weight: 700;\\n  line-height: 1.2;\\n  margin-bottom: 1.5rem;\\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%]   .hero-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  margin-bottom: 2rem;\\n  opacity: 0.9;\\n  line-height: 1.6;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-tabs[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 1.5rem;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  background: #f8f9fa;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-tabs[_ngcontent-%COMP%]   .search-tab[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0.75rem 1rem;\\n  border: none;\\n  background: transparent;\\n  color: #6c757d;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-tabs[_ngcontent-%COMP%]   .search-tab.active[_ngcontent-%COMP%] {\\n  background: var(--bs-primary);\\n  color: white;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-tabs[_ngcontent-%COMP%]   .search-tab[_ngcontent-%COMP%]:hover:not(.active) {\\n  background: #e9ecef;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr 1fr auto;\\n  gap: 1rem;\\n  align-items: end;\\n}\\n@media (max-width: 768px) {\\n  .hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 1rem;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: #6c757d;\\n  z-index: 3;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%] {\\n  padding-left: 3rem;\\n  border: 2px solid #e9ecef;\\n  border-radius: 10px;\\n  height: 50px;\\n  font-weight: 500;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-input-group[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]:focus {\\n  border-color: var(--bs-primary);\\n  box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-search-form[_ngcontent-%COMP%]   .search-inputs[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%] {\\n  height: 50px;\\n  padding: 0 2rem;\\n  border-radius: 10px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  white-space: nowrap;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "HomeComponent", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "i1", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule],\r\n  templateUrl: './home.component.html',\r\n  styleUrl: './home.component.scss'\r\n})\r\nexport class HomeComponent implements OnInit {\r\n\r\n}\r\n", "<!-- Home Page Header -->\r\n<header class=\"home-header\">\r\n  <div class=\"container-fluid\">\r\n    <div class=\"d-flex align-items-center justify-content-between py-3\">\r\n      <!-- Logo -->\r\n      <div class=\"header-logo\">\r\n        <a routerLink=\"/dashboard\" class=\"d-flex align-items-center\">\r\n          <img alt=\"Easy Deal Logo\" src=\"./assets/media/easydeallogos/home-logo.png\" class=\"h-40px\" />\r\n        </a>\r\n      </div>\r\n\r\n      <!-- Navigation Menu -->\r\n      <nav class=\"header-nav d-none d-lg-flex\">\r\n        <ul class=\"nav-list d-flex align-items-center mb-0\">\r\n          <li class=\"nav-item\">\r\n            <a href=\"#\" class=\"nav-link\">Home</a>\r\n          </li>\r\n          <li class=\"nav-item\">\r\n            <a href=\"#\" class=\"nav-link\">Properties</a>\r\n          </li>\r\n          <li class=\"nav-item\">\r\n            <a href=\"#\" class=\"nav-link\">New Projects</a>\r\n          </li>\r\n          <li class=\"nav-item\">\r\n            <a href=\"#\" class=\"nav-link\">About Us</a>\r\n          </li>\r\n          <li class=\"nav-item\">\r\n            <a href=\"#\" class=\"nav-link\">Contact</a>\r\n          </li>\r\n        </ul>\r\n      </nav>\r\n\r\n      <!-- Mobile Menu Toggle -->\r\n      <div class=\"mobile-menu-toggle d-lg-none\">\r\n        <button class=\"btn btn-icon\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#mobileNav\">\r\n          <i class=\"fas fa-bars fs-2\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Mobile Navigation -->\r\n    <div class=\"collapse\" id=\"mobileNav\">\r\n      <nav class=\"mobile-nav py-3\">\r\n        <ul class=\"nav-list-mobile\">\r\n          <li class=\"nav-item-mobile\">\r\n            <a href=\"#\" class=\"nav-link-mobile\">Home</a>\r\n          </li>\r\n          <li class=\"nav-item-mobile\">\r\n            <a href=\"#\" class=\"nav-link-mobile\">Properties</a>\r\n          </li>\r\n          <li class=\"nav-item-mobile\">\r\n            <a href=\"#\" class=\"nav-link-mobile\">New Projects</a>\r\n          </li>\r\n          <li class=\"nav-item-mobile\">\r\n            <a href=\"#\" class=\"nav-link-mobile\">About Us</a>\r\n          </li>\r\n          <li class=\"nav-item-mobile\">\r\n            <a href=\"#\" class=\"nav-link-mobile\">Contact</a>\r\n          </li>\r\n        </ul>\r\n      </nav>\r\n    </div>\r\n  </div>\r\n</header>\r\n\r\n<!-- Hero Section -->\r\n<section class=\"hero-section\">\r\n  <div class=\"hero-background\">\r\n    <div class=\"hero-overlay\"></div>\r\n    <div class=\"container\">\r\n      <div class=\"row min-vh-100 align-items-center\">\r\n        <div class=\"col-lg-6\">\r\n          <div class=\"hero-content\">\r\n            <h1 class=\"hero-title\">Find Your Dream Property</h1>\r\n            <p class=\"hero-subtitle\">\r\n              Discover the perfect home or investment opportunity with Easy Deal.\r\n              Browse thousands of properties and connect with trusted real estate professionals.\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-lg-6\">\r\n          <div class=\"hero-search-form\">\r\n            <div class=\"search-tabs\">\r\n              <button class=\"search-tab active\" type=\"button\">Buy</button>\r\n              <button class=\"search-tab\" type=\"button\">Rent</button>\r\n              <button class=\"search-tab\" type=\"button\">Sell</button>\r\n            </div>\r\n            <div class=\"search-inputs\">\r\n              <div class=\"search-input-group\">\r\n                <i class=\"fas fa-map-marker-alt\"></i>\r\n                <select class=\"form-select\">\r\n                  <option>Select Location</option>\r\n                  <option>Cairo</option>\r\n                  <option>Alexandria</option>\r\n                  <option>Giza</option>\r\n                </select>\r\n              </div>\r\n              <div class=\"search-input-group\">\r\n                <i class=\"fas fa-home\"></i>\r\n                <select class=\"form-select\">\r\n                  <option>Property Type</option>\r\n                  <option>Apartment</option>\r\n                  <option>Villa</option>\r\n                  <option>Office</option>\r\n                </select>\r\n              </div>\r\n              <div class=\"search-input-group\">\r\n                <i class=\"fas fa-dollar-sign\"></i>\r\n                <select class=\"form-select\">\r\n                  <option>Price Range</option>\r\n                  <option>Under 1M</option>\r\n                  <option>1M - 5M</option>\r\n                  <option>5M+</option>\r\n                </select>\r\n              </div>\r\n              <button class=\"btn btn-primary search-btn\">\r\n                <i class=\"fas fa-search\"></i>\r\n                Search\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Features Section -->\r\n<section class=\"features-section py-5\">\r\n  <div class=\"container\">\r\n    <div class=\"row text-center mb-5\">\r\n      <div class=\"col-12\">\r\n        <h2 class=\"section-title\">Why Choose Easy Deal?</h2>\r\n        <p class=\"section-subtitle\">Your trusted partner in real estate</p>\r\n      </div>\r\n    </div>\r\n    <div class=\"row g-4\">\r\n      <div class=\"col-lg-4 col-md-6\">\r\n        <div class=\"feature-card\">\r\n          <div class=\"feature-icon\">\r\n            <i class=\"fas fa-search text-primary\"></i>\r\n          </div>\r\n          <h4 class=\"feature-title\">Easy Search</h4>\r\n          <p class=\"feature-description\">\r\n            Find properties quickly with our advanced search filters and smart recommendations.\r\n          </p>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-lg-4 col-md-6\">\r\n        <div class=\"feature-card\">\r\n          <div class=\"feature-icon\">\r\n            <i class=\"fas fa-shield-alt text-primary\"></i>\r\n          </div>\r\n          <h4 class=\"feature-title\">Verified Properties</h4>\r\n          <p class=\"feature-description\">\r\n            All properties are verified and checked by our professional team for your safety.\r\n          </p>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-lg-4 col-md-6\">\r\n        <div class=\"feature-card\">\r\n          <div class=\"feature-icon\">\r\n            <i class=\"fas fa-headset text-primary\"></i>\r\n          </div>\r\n          <h4 class=\"feature-title\">24/7 Support</h4>\r\n          <p class=\"feature-description\">\r\n            Our dedicated support team is available around the clock to assist you.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Featured Properties Section -->\r\n<section class=\"featured-properties-section py-5 bg-light\">\r\n  <div class=\"container\">\r\n    <div class=\"row text-center mb-5\">\r\n      <div class=\"col-12\">\r\n        <h2 class=\"section-title\">Featured Properties</h2>\r\n        <p class=\"section-subtitle\">Discover our handpicked selection of premium properties</p>\r\n      </div>\r\n    </div>\r\n    <div class=\"row g-4\">\r\n      <div class=\"col-lg-4 col-md-6\">\r\n        <div class=\"property-card\">\r\n          <div class=\"property-image\">\r\n            <img\r\n              src=\"./assets/media/easydeallogos/empty-living-room-with-blue-sofa-plants-table-empty-white-wall-background-3d-rendering (1) 1.png\"\r\n              alt=\"Property\" class=\"w-100\">\r\n            <div class=\"property-badges\">\r\n              <span class=\"badge bg-success\">Featured</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"property-content\">\r\n            <h5 class=\"property-title\">Modern Apartment in New Cairo</h5>\r\n            <p class=\"property-location\">\r\n              <i class=\"fas fa-map-marker-alt me-1\"></i>\r\n              New Cairo, Cairo\r\n            </p>\r\n            <div class=\"property-details\">\r\n              <span class=\"property-detail\">\r\n                <i class=\"fas fa-bed\"></i>\r\n                3 Beds\r\n              </span>\r\n              <span class=\"property-detail\">\r\n                <i class=\"fas fa-bath\"></i>\r\n                2 Baths\r\n              </span>\r\n              <span class=\"property-detail\">\r\n                <i class=\"fas fa-ruler-combined\"></i>\r\n                150 m²\r\n              </span>\r\n            </div>\r\n            <div class=\"property-price\">\r\n              <span class=\"price\">2,500,000</span>\r\n              <span class=\"currency\">EGP</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- Add more property cards as needed -->\r\n    </div>\r\n  </div>\r\n</section>\r\n\r\n<!-- Footer -->\r\n<footer class=\"footer-section bg-dark text-white py-5\">\r\n  <div class=\"container\">\r\n    <div class=\"row\">\r\n      <div class=\"col-lg-4 col-md-6 mb-4\">\r\n        <div class=\"footer-brand\">\r\n          <img src=\"./assets/media/easydeallogos/home-logo.png\" alt=\"Easy Deal\" class=\"h-40px mb-3\">\r\n          <p class=\"footer-description\">\r\n            Your trusted partner in finding the perfect property. We make real estate simple and accessible for\r\n            everyone.\r\n          </p>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-lg-2 col-md-6 mb-4\">\r\n        <h5 class=\"footer-title\">Quick Links</h5>\r\n        <ul class=\"footer-links\">\r\n          <li><a href=\"#\">Home</a></li>\r\n          <li><a href=\"#\">Properties</a></li>\r\n          <li><a href=\"#\">About Us</a></li>\r\n          <li><a href=\"#\">Contact</a></li>\r\n        </ul>\r\n      </div>\r\n      <div class=\"col-lg-3 col-md-6 mb-4\">\r\n        <h5 class=\"footer-title\">Contact Info</h5>\r\n        <div class=\"footer-contact\">\r\n          <p><i class=\"fas fa-phone me-2\"></i> +20 ************</p>\r\n          <p><i class=\"fas fa-envelope me-2\"></i> info&#64;easydeal.com</p>\r\n          <p><i class=\"fas fa-map-marker-alt me-2\"></i> Cairo, Egypt</p>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-lg-3 col-md-6 mb-4\">\r\n        <h5 class=\"footer-title\">Follow Us</h5>\r\n        <div class=\"footer-social\">\r\n          <a href=\"#\" class=\"social-link\"><i class=\"fab fa-facebook-f\"></i></a>\r\n          <a href=\"#\" class=\"social-link\"><i class=\"fab fa-twitter\"></i></a>\r\n          <a href=\"#\" class=\"social-link\"><i class=\"fab fa-instagram\"></i></a>\r\n          <a href=\"#\" class=\"social-link\"><i class=\"fab fa-linkedin-in\"></i></a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <hr class=\"my-4\">\r\n    <div class=\"row\">\r\n      <div class=\"col-12 text-center\">\r\n        <p class=\"footer-copyright mb-0\">\r\n          © 2024 Easy Deal. All rights reserved.\r\n        </p>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</footer>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;AAS9C,OAAM,MAAOC,aAAa;;qCAAbA,aAAa;EAAA;;UAAbA,aAAa;IAAAC,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCLlBP,EALR,CAAAS,cAAA,gBAA4B,aACG,aACyC,aAEzC,WACsC;QAC3DT,EAAA,CAAAU,SAAA,aAA4F;QAEhGV,EADE,CAAAW,YAAA,EAAI,EACA;QAMAX,EAHN,CAAAS,cAAA,aAAyC,YACa,YAC7B,WACU;QAAAT,EAAA,CAAAY,MAAA,YAAI;QACnCZ,EADmC,CAAAW,YAAA,EAAI,EAClC;QAEHX,EADF,CAAAS,cAAA,aAAqB,YACU;QAAAT,EAAA,CAAAY,MAAA,kBAAU;QACzCZ,EADyC,CAAAW,YAAA,EAAI,EACxC;QAEHX,EADF,CAAAS,cAAA,aAAqB,YACU;QAAAT,EAAA,CAAAY,MAAA,oBAAY;QAC3CZ,EAD2C,CAAAW,YAAA,EAAI,EAC1C;QAEHX,EADF,CAAAS,cAAA,aAAqB,YACU;QAAAT,EAAA,CAAAY,MAAA,gBAAQ;QACvCZ,EADuC,CAAAW,YAAA,EAAI,EACtC;QAEHX,EADF,CAAAS,cAAA,aAAqB,YACU;QAAAT,EAAA,CAAAY,MAAA,eAAO;QAG1CZ,EAH0C,CAAAW,YAAA,EAAI,EACrC,EACF,EACD;QAIJX,EADF,CAAAS,cAAA,eAA0C,kBACyD;QAC/FT,EAAA,CAAAU,SAAA,aAAgC;QAGtCV,EAFI,CAAAW,YAAA,EAAS,EACL,EACF;QAOEX,EAJR,CAAAS,cAAA,eAAqC,eACN,cACC,cACE,aACU;QAAAT,EAAA,CAAAY,MAAA,YAAI;QAC1CZ,EAD0C,CAAAW,YAAA,EAAI,EACzC;QAEHX,EADF,CAAAS,cAAA,cAA4B,aACU;QAAAT,EAAA,CAAAY,MAAA,kBAAU;QAChDZ,EADgD,CAAAW,YAAA,EAAI,EAC/C;QAEHX,EADF,CAAAS,cAAA,cAA4B,aACU;QAAAT,EAAA,CAAAY,MAAA,oBAAY;QAClDZ,EADkD,CAAAW,YAAA,EAAI,EACjD;QAEHX,EADF,CAAAS,cAAA,cAA4B,aACU;QAAAT,EAAA,CAAAY,MAAA,gBAAQ;QAC9CZ,EAD8C,CAAAW,YAAA,EAAI,EAC7C;QAEHX,EADF,CAAAS,cAAA,cAA4B,aACU;QAAAT,EAAA,CAAAY,MAAA,eAAO;QAMvDZ,EANuD,CAAAW,YAAA,EAAI,EAC5C,EACF,EACD,EACF,EACF,EACC;QAIPX,EADF,CAAAS,cAAA,mBAA8B,eACC;QAC3BT,EAAA,CAAAU,SAAA,eAAgC;QAKxBV,EAJR,CAAAS,cAAA,eAAuB,eAC0B,eACvB,eACM,cACD;QAAAT,EAAA,CAAAY,MAAA,gCAAwB;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QACpDX,EAAA,CAAAS,cAAA,aAAyB;QACvBT,EAAA,CAAAY,MAAA,gKAEF;QAEJZ,EAFI,CAAAW,YAAA,EAAI,EACA,EACF;QAIAX,EAHN,CAAAS,cAAA,eAAsB,eACU,eACH,kBACyB;QAAAT,EAAA,CAAAY,MAAA,WAAG;QAAAZ,EAAA,CAAAW,YAAA,EAAS;QAC5DX,EAAA,CAAAS,cAAA,kBAAyC;QAAAT,EAAA,CAAAY,MAAA,YAAI;QAAAZ,EAAA,CAAAW,YAAA,EAAS;QACtDX,EAAA,CAAAS,cAAA,kBAAyC;QAAAT,EAAA,CAAAY,MAAA,YAAI;QAC/CZ,EAD+C,CAAAW,YAAA,EAAS,EAClD;QAEJX,EADF,CAAAS,cAAA,eAA2B,eACO;QAC9BT,EAAA,CAAAU,SAAA,aAAqC;QAEnCV,EADF,CAAAS,cAAA,kBAA4B,cAClB;QAAAT,EAAA,CAAAY,MAAA,uBAAe;QAAAZ,EAAA,CAAAW,YAAA,EAAS;QAChCX,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAY,MAAA,aAAK;QAAAZ,EAAA,CAAAW,YAAA,EAAS;QACtBX,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAY,MAAA,kBAAU;QAAAZ,EAAA,CAAAW,YAAA,EAAS;QAC3BX,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAY,MAAA,YAAI;QAEhBZ,EAFgB,CAAAW,YAAA,EAAS,EACd,EACL;QACNX,EAAA,CAAAS,cAAA,eAAgC;QAC9BT,EAAA,CAAAU,SAAA,aAA2B;QAEzBV,EADF,CAAAS,cAAA,kBAA4B,cAClB;QAAAT,EAAA,CAAAY,MAAA,qBAAa;QAAAZ,EAAA,CAAAW,YAAA,EAAS;QAC9BX,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAY,MAAA,iBAAS;QAAAZ,EAAA,CAAAW,YAAA,EAAS;QAC1BX,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAY,MAAA,aAAK;QAAAZ,EAAA,CAAAW,YAAA,EAAS;QACtBX,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAY,MAAA,cAAM;QAElBZ,EAFkB,CAAAW,YAAA,EAAS,EAChB,EACL;QACNX,EAAA,CAAAS,cAAA,eAAgC;QAC9BT,EAAA,CAAAU,SAAA,aAAkC;QAEhCV,EADF,CAAAS,cAAA,kBAA4B,cAClB;QAAAT,EAAA,CAAAY,MAAA,mBAAW;QAAAZ,EAAA,CAAAW,YAAA,EAAS;QAC5BX,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAY,MAAA,gBAAQ;QAAAZ,EAAA,CAAAW,YAAA,EAAS;QACzBX,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAY,MAAA,eAAO;QAAAZ,EAAA,CAAAW,YAAA,EAAS;QACxBX,EAAA,CAAAS,cAAA,cAAQ;QAAAT,EAAA,CAAAY,MAAA,WAAG;QAEfZ,EAFe,CAAAW,YAAA,EAAS,EACb,EACL;QACNX,EAAA,CAAAS,cAAA,kBAA2C;QACzCT,EAAA,CAAAU,SAAA,aAA6B;QAC7BV,EAAA,CAAAY,MAAA,iBACF;QAOdZ,EAPc,CAAAW,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF,EACF,EACE;QAOFX,EAJR,CAAAS,cAAA,oBAAuC,gBACd,gBACa,gBACZ,eACQ;QAAAT,EAAA,CAAAY,MAAA,8BAAqB;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QACpDX,EAAA,CAAAS,cAAA,cAA4B;QAAAT,EAAA,CAAAY,MAAA,4CAAmC;QAEnEZ,EAFmE,CAAAW,YAAA,EAAI,EAC/D,EACF;QAIAX,EAHN,CAAAS,cAAA,gBAAqB,gBACY,gBACH,gBACE;QACxBT,EAAA,CAAAU,SAAA,cAA0C;QAC5CV,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAS,cAAA,eAA0B;QAAAT,EAAA,CAAAY,MAAA,oBAAW;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAC1CX,EAAA,CAAAS,cAAA,cAA+B;QAC7BT,EAAA,CAAAY,MAAA,8FACF;QAEJZ,EAFI,CAAAW,YAAA,EAAI,EACA,EACF;QAGFX,EAFJ,CAAAS,cAAA,gBAA+B,gBACH,gBACE;QACxBT,EAAA,CAAAU,SAAA,cAA8C;QAChDV,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAS,cAAA,eAA0B;QAAAT,EAAA,CAAAY,MAAA,4BAAmB;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAClDX,EAAA,CAAAS,cAAA,cAA+B;QAC7BT,EAAA,CAAAY,MAAA,4FACF;QAEJZ,EAFI,CAAAW,YAAA,EAAI,EACA,EACF;QAGFX,EAFJ,CAAAS,cAAA,gBAA+B,gBACH,gBACE;QACxBT,EAAA,CAAAU,SAAA,cAA2C;QAC7CV,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAS,cAAA,eAA0B;QAAAT,EAAA,CAAAY,MAAA,qBAAY;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAC3CX,EAAA,CAAAS,cAAA,cAA+B;QAC7BT,EAAA,CAAAY,MAAA,kFACF;QAKVZ,EALU,CAAAW,YAAA,EAAI,EACA,EACF,EACF,EACF,EACE;QAOFX,EAJR,CAAAS,cAAA,oBAA2D,gBAClC,gBACa,gBACZ,eACQ;QAAAT,EAAA,CAAAY,MAAA,4BAAmB;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAClDX,EAAA,CAAAS,cAAA,cAA4B;QAAAT,EAAA,CAAAY,MAAA,gEAAuD;QAEvFZ,EAFuF,CAAAW,YAAA,EAAI,EACnF,EACF;QAIAX,EAHN,CAAAS,cAAA,gBAAqB,gBACY,gBACF,gBACG;QAC1BT,EAAA,CAAAU,SAAA,gBAE+B;QAE7BV,EADF,CAAAS,cAAA,gBAA6B,iBACI;QAAAT,EAAA,CAAAY,MAAA,iBAAQ;QAE3CZ,EAF2C,CAAAW,YAAA,EAAO,EAC1C,EACF;QAEJX,EADF,CAAAS,cAAA,gBAA8B,eACD;QAAAT,EAAA,CAAAY,MAAA,sCAA6B;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAC7DX,EAAA,CAAAS,cAAA,cAA6B;QAC3BT,EAAA,CAAAU,SAAA,cAA0C;QAC1CV,EAAA,CAAAY,MAAA,2BACF;QAAAZ,EAAA,CAAAW,YAAA,EAAI;QAEFX,EADF,CAAAS,cAAA,gBAA8B,iBACE;QAC5BT,EAAA,CAAAU,SAAA,cAA0B;QAC1BV,EAAA,CAAAY,MAAA,iBACF;QAAAZ,EAAA,CAAAW,YAAA,EAAO;QACPX,EAAA,CAAAS,cAAA,iBAA8B;QAC5BT,EAAA,CAAAU,SAAA,cAA2B;QAC3BV,EAAA,CAAAY,MAAA,kBACF;QAAAZ,EAAA,CAAAW,YAAA,EAAO;QACPX,EAAA,CAAAS,cAAA,iBAA8B;QAC5BT,EAAA,CAAAU,SAAA,cAAqC;QACrCV,EAAA,CAAAY,MAAA,sBACF;QACFZ,EADE,CAAAW,YAAA,EAAO,EACH;QAEJX,EADF,CAAAS,cAAA,gBAA4B,iBACN;QAAAT,EAAA,CAAAY,MAAA,kBAAS;QAAAZ,EAAA,CAAAW,YAAA,EAAO;QACpCX,EAAA,CAAAS,cAAA,iBAAuB;QAAAT,EAAA,CAAAY,MAAA,YAAG;QAQxCZ,EARwC,CAAAW,YAAA,EAAO,EAC7B,EACF,EACF,EACF,EAEF,EACF,EACE;QAOFX,EAJR,CAAAS,cAAA,mBAAuD,gBAC9B,gBACJ,gBACqB,gBACR;QACxBT,EAAA,CAAAU,SAAA,gBAA0F;QAC1FV,EAAA,CAAAS,cAAA,cAA8B;QAC5BT,EAAA,CAAAY,MAAA,wHAEF;QAEJZ,EAFI,CAAAW,YAAA,EAAI,EACA,EACF;QAEJX,EADF,CAAAS,cAAA,gBAAoC,eACT;QAAAT,EAAA,CAAAY,MAAA,oBAAW;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAEnCX,EADN,CAAAS,cAAA,eAAyB,WACnB,cAAY;QAAAT,EAAA,CAAAY,MAAA,aAAI;QAAIZ,EAAJ,CAAAW,YAAA,EAAI,EAAK;QACzBX,EAAJ,CAAAS,cAAA,WAAI,cAAY;QAAAT,EAAA,CAAAY,MAAA,mBAAU;QAAIZ,EAAJ,CAAAW,YAAA,EAAI,EAAK;QAC/BX,EAAJ,CAAAS,cAAA,WAAI,cAAY;QAAAT,EAAA,CAAAY,MAAA,iBAAQ;QAAIZ,EAAJ,CAAAW,YAAA,EAAI,EAAK;QAC7BX,EAAJ,CAAAS,cAAA,WAAI,cAAY;QAAAT,EAAA,CAAAY,MAAA,gBAAO;QAE3BZ,EAF2B,CAAAW,YAAA,EAAI,EAAK,EAC7B,EACD;QAEJX,EADF,CAAAS,cAAA,gBAAoC,eACT;QAAAT,EAAA,CAAAY,MAAA,qBAAY;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAExCX,EADF,CAAAS,cAAA,gBAA4B,UACvB;QAAAT,EAAA,CAAAU,SAAA,cAAiC;QAACV,EAAA,CAAAY,MAAA,0BAAgB;QAAAZ,EAAA,CAAAW,YAAA,EAAI;QACzDX,EAAA,CAAAS,cAAA,UAAG;QAAAT,EAAA,CAAAU,SAAA,cAAoC;QAACV,EAAA,CAAAY,MAAA,2BAAqB;QAAAZ,EAAA,CAAAW,YAAA,EAAI;QACjEX,EAAA,CAAAS,cAAA,UAAG;QAAAT,EAAA,CAAAU,SAAA,cAA0C;QAACV,EAAA,CAAAY,MAAA,sBAAY;QAE9DZ,EAF8D,CAAAW,YAAA,EAAI,EAC1D,EACF;QAEJX,EADF,CAAAS,cAAA,gBAAoC,eACT;QAAAT,EAAA,CAAAY,MAAA,kBAAS;QAAAZ,EAAA,CAAAW,YAAA,EAAK;QAErCX,EADF,CAAAS,cAAA,gBAA2B,cACO;QAAAT,EAAA,CAAAU,SAAA,cAAiC;QAAAV,EAAA,CAAAW,YAAA,EAAI;QACrEX,EAAA,CAAAS,cAAA,cAAgC;QAAAT,EAAA,CAAAU,SAAA,cAA8B;QAAAV,EAAA,CAAAW,YAAA,EAAI;QAClEX,EAAA,CAAAS,cAAA,cAAgC;QAAAT,EAAA,CAAAU,SAAA,cAAgC;QAAAV,EAAA,CAAAW,YAAA,EAAI;QACpEX,EAAA,CAAAS,cAAA,cAAgC;QAAAT,EAAA,CAAAU,SAAA,cAAkC;QAGxEV,EAHwE,CAAAW,YAAA,EAAI,EAClE,EACF,EACF;QACNX,EAAA,CAAAU,SAAA,eAAiB;QAGbV,EAFJ,CAAAS,cAAA,gBAAiB,gBACiB,cACG;QAC/BT,EAAA,CAAAY,MAAA,sDACF;QAIRZ,EAJQ,CAAAW,YAAA,EAAI,EACA,EACF,EACF,EACC;;;mBD5QGjB,YAAY,EAAEC,YAAY,EAAAkB,EAAA,CAAAC,UAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}