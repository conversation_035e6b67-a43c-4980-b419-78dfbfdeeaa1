{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/request.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../../../broker/shared/broker-title/broker-title.component\";\nimport * as i5 from \"../../../../_metronic/shared/keenicon/keenicon.component\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"../assignto-brokers-action-menu/assignto-brokers-action-menu.component\";\nfunction AssignToBrokersComponent_tr_45_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const broker_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", broker_r2.accountType, \" \");\n  }\n}\nfunction AssignToBrokersComponent_tr_45_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const broker_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", broker_r2.accountType, \" \");\n  }\n}\nfunction AssignToBrokersComponent_tr_45_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const broker_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", broker_r2.accountType, \" \");\n  }\n}\nfunction AssignToBrokersComponent_tr_45_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const broker_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", broker_r2.accountType, \" \");\n  }\n}\nfunction AssignToBrokersComponent_tr_45_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const broker_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", broker_r2.type, \" \");\n  }\n}\nfunction AssignToBrokersComponent_tr_45_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const broker_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", broker_r2.type, \" \");\n  }\n}\nfunction AssignToBrokersComponent_tr_45_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const area_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", area_r4.nameEn, \" | \", area_r4.nameAr, \"\");\n  }\n}\nfunction AssignToBrokersComponent_tr_45_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const specialization_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(specialization_r5.specialization);\n  }\n}\nfunction AssignToBrokersComponent_tr_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 28)(2, \"div\", 19)(3, \"input\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function AssignToBrokersComponent_tr_45_Template_input_ngModelChange_3_listener($event) {\n      const broker_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(broker_r2.selected, $event) || (broker_r2.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function AssignToBrokersComponent_tr_45_Template_input_change_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.updateSelectAllStatus());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"div\", 30)(6, \"div\", 31);\n    i0.ɵɵelement(7, \"img\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 33)(9, \"a\", 34);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtemplate(12, AssignToBrokersComponent_tr_45_span_12_Template, 2, 1, \"span\", 35)(13, AssignToBrokersComponent_tr_45_span_13_Template, 2, 1, \"span\", 36)(14, AssignToBrokersComponent_tr_45_span_14_Template, 2, 1, \"span\", 37)(15, AssignToBrokersComponent_tr_45_span_15_Template, 2, 1, \"span\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtemplate(17, AssignToBrokersComponent_tr_45_span_17_Template, 2, 1, \"span\", 39)(18, AssignToBrokersComponent_tr_45_span_18_Template, 2, 1, \"span\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\");\n    i0.ɵɵtemplate(20, AssignToBrokersComponent_tr_45_div_20_Template, 3, 2, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtemplate(22, AssignToBrokersComponent_tr_45_div_22_Template, 3, 1, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 42)(24, \"button\", 43);\n    i0.ɵɵelement(25, \"i\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"app-assignto-brokers-action-menu\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const broker_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", broker_r2.selected);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", broker_r2.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", broker_r2.fullName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", broker_r2.accountType === \"Golden Account\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", broker_r2.accountType === \"Silver Account\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", broker_r2.accountType === \"Bronze Account\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", broker_r2.accountType === \"Free\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", broker_r2.type === \"Real Estate Brokage Company\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", broker_r2.type === \"Independent\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", broker_r2.areas);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", broker_r2.specializations);\n  }\n}\nexport class AssignToBrokersComponent {\n  cd;\n  requestService;\n  route;\n  requestId;\n  AssignBroker = [];\n  isLoading = false;\n  trackById(index, broker) {\n    return broker.id;\n  }\n  selectAll = false;\n  selectAllBrokers() {\n    this.AssignBroker.forEach(broker => {\n      broker.selected = this.selectAll;\n    });\n    this.printSelectedIds();\n  }\n  updateSelectAllStatus() {\n    this.selectAll = this.AssignBroker.every(broker => broker.selected);\n    this.printSelectedIds();\n  }\n  printSelectedIds() {\n    const selectedIds = this.AssignBroker.filter(broker => broker.selected).map(broker => broker.id);\n    console.log('Selected IDs:', selectedIds);\n  }\n  get selectedCount() {\n    return this.AssignBroker.filter(broker => broker.selected).length;\n  }\n  constructor(cd, requestService, route) {\n    this.cd = cd;\n    this.requestService = requestService;\n    this.route = route;\n  }\n  ngOnInit() {\n    // Get requestId from route parameters\n    this.route.paramMap.subscribe(params => {\n      this.requestId = Number(params.get('id'));\n      if (this.requestId) {\n        this.loadRequestHistory();\n      } else {\n        console.error('No requestId provided in route parameters');\n      }\n    });\n  }\n  loadRequestHistory() {\n    this.isLoading = true;\n    this.requestService.getRecommendedBrokers(this.requestId).subscribe({\n      next: response => {\n        this.AssignBroker = response?.data || [];\n        this.isLoading = false;\n        this.cd.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading broker history:', error);\n        this.isLoading = false;\n      }\n    });\n  }\n  assignSelectedBrokers() {\n    const selectedBrokerIds = this.AssignBroker.filter(broker => broker.selected).map(broker => broker.id);\n    this.requestService.assignBrokersToRequest(this.requestId, selectedBrokerIds).subscribe({\n      next: res => {\n        console.log('Assigned successfully:', res);\n      },\n      error: err => {\n        console.error('Assignment error:', err);\n      }\n    });\n  }\n  static ɵfac = function AssignToBrokersComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AssignToBrokersComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.RequestService), i0.ɵɵdirectiveInject(i2.ActivatedRoute));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AssignToBrokersComponent,\n    selectors: [[\"app-assign-to-brokers\"]],\n    decls: 50,\n    vars: 5,\n    consts: [[1, \"mb-5\", \"mt-0\"], [1, \"card\", \"mb-5\", \"mb-xl-5\"], [1, \"card-body\", \"pt-3\", \"pb-0\"], [1, \"d-flex\", \"flex-wrap\", \"flex-sm-nowrap\", \"mb-1\"], [1, \"flex-grow-1\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\", \"flex-wrap\", \"mb-2\"], [1, \"d-flex\", \"my-4\"], [1, \"text-dark-blue\", \"fs-2\", \"fw-bolder\", \"me-1\"], [1, \"d-flex\", \"my-4\", \"mt-0\"], [\"data-kt-search-element\", \"form\", \"autocomplete\", \"off\", 1, \"w-300px\", \"position-relative\", \"mb-3\"], [\"name\", \"magnifier\", 1, \"fs-2\", \"fs-lg-1\", \"text-gray-500\", \"position-absolute\", \"top-50\", \"translate-middle-y\", \"ms-3\"], [\"type\", \"text\", \"name\", \"search\", \"value\", \"\", \"placeholder\", \"Search...\", \"data-kt-search-element\", \"input\", 1, \"form-control\", \"form-control-flush\", \"ps-10\", \"bg-light\", \"border\", \"rounded-pill\"], [1, \"btn\", \"btn-sm\", \"btn-light-dark-blue\", \"me-3\", \"cursor-pointer\"], [1, \"fa-solid\", \"fa-filter\"], [1, \"fa-solid\", \"fa-arrow-down-wide-short\"], [1, \"table-responsive\"], [1, \"table\", \"table-row-bordered\", \"table-row-gray-100\", \"align-middle\", \"gs-0\", \"gy-3\", \"mt-5\"], [1, \"fw-bold\", \"bg-light-dark-blue\", \"text-dark-blue\", \"me-1\", \"ms-1\"], [1, \"w-25px\", \"ps-4\", \"rounded-start\"], [1, \"form-check\", \"form-check-sm\", \"form-check-custom\", \"form-check-solid\"], [\"type\", \"checkbox\", \"value\", \"1\", \"data-kt-check\", \"true\", \"data-kt-check-target\", \".widget-13-check\", 1, \"form-check-input\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"min-w-150px\"], [1, \"fa-solid\", \"fa-arrow-down\", \"text-dark-blue\", \"ms-1\"], [1, \"min-w-100px\"], [1, \"min-w-100px\", \"text-end\", \"rounded-end\", \"pe-4\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"colspan\", \"7\", 1, \"text-center\"], [\"routerLink\", \"/requests/sent\", 1, \"btn\", \"btn-light-dark-blue\", \"mt-4\", \"fw-bolder\", 3, \"click\", \"disabled\"], [1, \"ps-4\"], [\"type\", \"checkbox\", \"value\", \"1\", 1, \"form-check-input\", \"widget-13-check\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"d-flex\", \"align-items-center\"], [1, \"symbol\", \"symbol-45px\", \"me-5\"], [\"alt\", \"\", 1, \"rounded-circle\", 3, \"src\"], [1, \"d-flex\", \"justify-content-start\", \"flex-column\"], [1, \"text-gray-900\", \"fw-bold\", \"text-hover-dark-blue\", \"fs-5\"], [\"class\", \"badge badge-light-warning fs-5\", 4, \"ngIf\"], [\"class\", \"badge badge-light-primary fs-5\", 4, \"ngIf\"], [\"class\", \"badge badge-light-danger fs-5\", 4, \"ngIf\"], [\"class\", \"badge text-dark bg-light fs-5\", 4, \"ngIf\"], [\"class\", \"badge badge-light-info fs-5\", 4, \"ngIf\"], [\"class\", \"badge badge-light-success fs-5\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"text-end\", \"pe-4\"], [\"type\", \"button\", \"data-kt-menu-trigger\", \"click\", \"data-kt-menu-placement\", \"bottom-end\", \"data-kt-menu-flip\", \"top-end\", 1, \"btn\", \"btn-sm\", \"btn-icon\", \"btn-color-primary\", \"btn-active-light-primary\"], [1, \"fa-solid\", \"fa-ellipsis-vertical\"], [1, \"badge\", \"badge-light-warning\", \"fs-5\"], [1, \"badge\", \"badge-light-primary\", \"fs-5\"], [1, \"badge\", \"badge-light-danger\", \"fs-5\"], [1, \"badge\", \"text-dark\", \"bg-light\", \"fs-5\"], [1, \"badge\", \"badge-light-info\", \"fs-5\"], [1, \"badge\", \"badge-light-success\", \"fs-5\"], [1, \"badge\", \"badge-dark-blue\", \"fs-7\", \"m-1\"]],\n    template: function AssignToBrokersComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"app-broker-title\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 1)(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"div\", 6)(8, \"h1\", 7);\n        i0.ɵɵtext(9, \"Requests\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"form\", 9);\n        i0.ɵɵelement(12, \"app-keenicon\", 10)(13, \"input\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 6)(15, \"a\", 12);\n        i0.ɵɵelement(16, \"i\", 13);\n        i0.ɵɵtext(17, \" Filter \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"a\", 12);\n        i0.ɵɵelement(19, \"i\", 14);\n        i0.ɵɵtext(20, \" Sort \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(21, \"div\", 15)(22, \"table\", 16)(23, \"thead\")(24, \"tr\", 17)(25, \"th\", 18)(26, \"div\", 19)(27, \"input\", 20);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function AssignToBrokersComponent_Template_input_ngModelChange_27_listener($event) {\n          i0.ɵɵtwoWayBindingSet(ctx.selectAll, $event) || (ctx.selectAll = $event);\n          return $event;\n        });\n        i0.ɵɵlistener(\"change\", function AssignToBrokersComponent_Template_input_change_27_listener() {\n          return ctx.selectAllBrokers();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(28, \"th\", 21);\n        i0.ɵɵtext(29, \" Broker name \");\n        i0.ɵɵelement(30, \"i\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"th\", 21);\n        i0.ɵɵtext(32, \" Account type \");\n        i0.ɵɵelement(33, \"i\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"th\", 23);\n        i0.ɵɵtext(35, \"Type\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"th\", 23);\n        i0.ɵɵtext(37, \" Area \");\n        i0.ɵɵelement(38, \"i\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"th\", 21);\n        i0.ɵɵtext(40, \" Specialization \");\n        i0.ɵɵelement(41, \"i\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"th\", 24);\n        i0.ɵɵtext(43, \"Actions\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(44, \"tbody\");\n        i0.ɵɵtemplate(45, AssignToBrokersComponent_tr_45_Template, 27, 11, \"tr\", 25);\n        i0.ɵɵelementStart(46, \"tr\")(47, \"td\", 26)(48, \"button\", 27);\n        i0.ɵɵlistener(\"click\", function AssignToBrokersComponent_Template_button_click_48_listener() {\n          return ctx.assignSelectedBrokers();\n        });\n        i0.ɵɵtext(49);\n        i0.ɵɵelementEnd()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(27);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectAll);\n        i0.ɵɵadvance(18);\n        i0.ɵɵproperty(\"ngForOf\", ctx.AssignBroker)(\"ngForTrackBy\", ctx.trackById);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"disabled\", ctx.selectedCount === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" Assign to broker (\", ctx.selectedCount, \") \");\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgIf, i2.RouterLink, i4.BrokerTitleComponent, i5.KeeniconComponent, i6.ɵNgNoValidate, i6.CheckboxControlValueAccessor, i6.NgControlStatus, i6.NgControlStatusGroup, i6.NgModel, i6.NgForm, i7.AssigntoBrokersActionMenuComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "broker_r2", "accountType", "type", "ɵɵtextInterpolate2", "area_r4", "nameEn", "nameAr", "ɵɵtextInterpolate", "specialization_r5", "specialization", "ɵɵtwoWayListener", "AssignToBrokersComponent_tr_45_Template_input_ngModelChange_3_listener", "$event", "ɵɵrestoreView", "_r1", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵlistener", "AssignToBrokersComponent_tr_45_Template_input_change_3_listener", "ctx_r2", "ɵɵnextContext", "updateSelectAllStatus", "ɵɵelement", "ɵɵtemplate", "AssignToBrokersComponent_tr_45_span_12_Template", "AssignToBrokersComponent_tr_45_span_13_Template", "AssignToBrokersComponent_tr_45_span_14_Template", "AssignToBrokersComponent_tr_45_span_15_Template", "AssignToBrokersComponent_tr_45_span_17_Template", "AssignToBrokersComponent_tr_45_span_18_Template", "AssignToBrokersComponent_tr_45_div_20_Template", "AssignToBrokersComponent_tr_45_div_22_Template", "ɵɵtwoWayProperty", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "fullName", "areas", "specializations", "AssignToBrokersComponent", "cd", "requestService", "route", "requestId", "AssignB<PERSON>r", "isLoading", "trackById", "index", "broker", "id", "selectAll", "selectAllBrokers", "for<PERSON>ach", "printSelectedIds", "every", "selectedIds", "filter", "map", "console", "log", "selectedCount", "length", "constructor", "ngOnInit", "paramMap", "subscribe", "params", "Number", "get", "loadRequestHistory", "error", "getRecommendedBrokers", "next", "response", "data", "detectChanges", "assignSelectedBrokers", "selectedBrokerIds", "assignBrokersToRequest", "res", "err", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "RequestService", "i2", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "AssignToBrokersComponent_Template", "rf", "ctx", "AssignToBrokersComponent_Template_input_ngModelChange_27_listener", "AssignToBrokersComponent_Template_input_change_27_listener", "AssignToBrokersComponent_tr_45_Template", "AssignToBrokersComponent_Template_button_click_48_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\requests\\components\\assign-to-brokers\\assign-to-brokers.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\requests\\components\\assign-to-brokers\\assign-to-brokers.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { RequestService } from '../../services/request.service';\r\n\r\n@Component({\r\n  selector: 'app-assign-to-brokers',\r\n  templateUrl: './assign-to-brokers.component.html',\r\n  styleUrl: './assign-to-brokers.component.scss',\r\n})\r\nexport class AssignToBrokersComponent implements OnInit {\r\n  requestId: number;\r\n  AssignBroker: any[] = [];\r\n  isLoading = false;\r\n\r\n  trackById(index: number, broker: any): number {\r\n    return broker.id;\r\n  }\r\n\r\n  selectAll = false;\r\n\r\n  selectAllBrokers() {\r\n    this.AssignBroker.forEach((broker) => {\r\n      broker.selected = this.selectAll;\r\n    });\r\n    this.printSelectedIds();\r\n  }\r\n\r\n  updateSelectAllStatus() {\r\n    this.selectAll = this.AssignBroker.every((broker) => broker.selected);\r\n    this.printSelectedIds();\r\n  }\r\n\r\n  printSelectedIds() {\r\n    const selectedIds = this.AssignBroker.filter(\r\n      (broker) => broker.selected\r\n    ).map((broker) => broker.id);\r\n\r\n    console.log('Selected IDs:', selectedIds);\r\n  }\r\n\r\n  get selectedCount(): number {\r\n    return this.AssignBroker.filter((broker) => broker.selected).length;\r\n  }\r\n\r\n  constructor(\r\n    protected cd: ChangeDetectorRef,\r\n    protected requestService: RequestService,\r\n    private route: ActivatedRoute\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Get requestId from route parameters\r\n    this.route.paramMap.subscribe((params) => {\r\n      this.requestId = Number(params.get('id'));\r\n      if (this.requestId) {\r\n        this.loadRequestHistory();\r\n      } else {\r\n        console.error('No requestId provided in route parameters');\r\n      }\r\n    });\r\n  }\r\n\r\n  loadRequestHistory(): void {\r\n    this.isLoading = true;\r\n\r\n    this.requestService.getRecommendedBrokers(this.requestId).subscribe({\r\n      next: (response: any) => {\r\n        this.AssignBroker = response?.data || [];\r\n        this.isLoading = false;\r\n        this.cd.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading broker history:', error);\r\n        this.isLoading = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  assignSelectedBrokers(): void {\r\n    const selectedBrokerIds = this.AssignBroker.filter(\r\n      (broker) => broker.selected\r\n    ).map((broker) => broker.id);\r\n\r\n    this.requestService\r\n      .assignBrokersToRequest(this.requestId, selectedBrokerIds)\r\n      .subscribe({\r\n        next: (res) => {\r\n          console.log('Assigned successfully:', res);\r\n        },\r\n        error: (err) => {\r\n          console.error('Assignment error:', err);\r\n        },\r\n      });\r\n  }\r\n}\r\n", "<div class=\"mb-5 mt-0\">\r\n  <app-broker-title></app-broker-title>\r\n</div>\r\n\r\n<div class=\"card mb-5 mb-xl-5\">\r\n  <div class=\"card-body pt-3 pb-0\">\r\n    <div class=\"d-flex flex-wrap flex-sm-nowrap mb-1\">\r\n      <div class=\"flex-grow-1\">\r\n        <div class=\"d-flex justify-content-between align-items-start flex-wrap mb-2\">\r\n          <div class=\"d-flex my-4\">\r\n            <h1 class=\"text-dark-blue fs-2 fw-bolder me-1\">Requests</h1>\r\n          </div>\r\n\r\n          <div class=\"d-flex my-4 mt-0\">\r\n            <form data-kt-search-element=\"form\" class=\"w-300px position-relative mb-3\" autocomplete=\"off\">\r\n              <app-keenicon name=\"magnifier\"\r\n                class=\"fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y ms-3\"></app-keenicon>\r\n              <input type=\"text\" class=\"form-control form-control-flush ps-10 bg-light border rounded-pill\"\r\n                name=\"search\" value=\"\" placeholder=\"Search...\" data-kt-search-element=\"input\" />\r\n            </form>\r\n          </div>\r\n\r\n          <div class=\"d-flex my-4\">\r\n            <a class=\"btn btn-sm btn-light-dark-blue me-3 cursor-pointer\">\r\n              <i class=\"fa-solid fa-filter\"></i> Filter\r\n            </a>\r\n            <a class=\"btn btn-sm btn-light-dark-blue me-3 cursor-pointer\">\r\n              <i class=\"fa-solid fa-arrow-down-wide-short\"></i> Sort\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive\">\r\n      <table class=\"table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5\">\r\n        <thead>\r\n          <tr class=\"fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1\">\r\n            <th class=\"w-25px ps-4 rounded-start\">\r\n              <div class=\"form-check form-check-sm form-check-custom form-check-solid\">\r\n                <input class=\"form-check-input\" type=\"checkbox\" value=\"1\" data-kt-check=\"true\"\r\n                  data-kt-check-target=\".widget-13-check\" [(ngModel)]=\"selectAll\" (change)=\"selectAllBrokers()\" />\r\n              </div>\r\n            </th>\r\n            <th class=\"min-w-150px\">\r\n              Broker name\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-150px\">\r\n              Account type\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-100px\">Type</th>\r\n            <th class=\"min-w-100px\">\r\n              Area <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-150px\">\r\n              Specialization\r\n              <i class=\"fa-solid fa-arrow-down text-dark-blue ms-1\"></i>\r\n            </th>\r\n            <th class=\"min-w-100px text-end rounded-end pe-4\">Actions</th>\r\n          </tr>\r\n        </thead>\r\n\r\n        <tbody>\r\n          <tr *ngFor=\"let broker of AssignBroker; trackBy: trackById\">\r\n            <td class=\"ps-4\">\r\n              <div class=\"form-check form-check-sm form-check-custom form-check-solid\">\r\n                <input class=\"form-check-input widget-13-check\" type=\"checkbox\" value=\"1\" [(ngModel)]=\"broker.selected\"\r\n                  (change)=\"updateSelectAllStatus()\" />\r\n              </div>\r\n            </td>\r\n\r\n            <td>\r\n              <div class=\"d-flex align-items-center\">\r\n                <div class=\"symbol symbol-45px me-5\">\r\n                  <img [src]=\"broker.image\" alt=\"\" class=\"rounded-circle\" />\r\n                </div>\r\n                <div class=\"d-flex justify-content-start flex-column\">\r\n                  <a class=\"text-gray-900 fw-bold text-hover-dark-blue fs-5\">\r\n                    {{ broker.fullName }}\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </td>\r\n\r\n            <td>\r\n              <span *ngIf=\"broker.accountType === 'Golden Account'\" class=\"badge badge-light-warning fs-5\">\r\n                {{ broker.accountType }}\r\n              </span>\r\n              <span *ngIf=\"broker.accountType === 'Silver Account'\" class=\"badge badge-light-primary fs-5\">\r\n                {{ broker.accountType }}\r\n              </span>\r\n              <span *ngIf=\"broker.accountType === 'Bronze Account'\" class=\"badge badge-light-danger fs-5\">\r\n                {{ broker.accountType }}\r\n              </span>\r\n              <span *ngIf=\"broker.accountType === 'Free'\" class=\"badge text-dark bg-light fs-5\">\r\n                {{ broker.accountType }}\r\n              </span>\r\n            </td>\r\n\r\n            <td>\r\n              <span *ngIf=\"broker.type === 'Real Estate Brokage Company'\" class=\"badge badge-light-info fs-5\">\r\n                {{ broker.type }}\r\n              </span>\r\n              <span *ngIf=\"broker.type === 'Independent'\" class=\"badge badge-light-success fs-5\">\r\n                {{ broker.type }}\r\n              </span>\r\n            </td>\r\n\r\n            <td>\r\n              <div *ngFor=\"let area of broker.areas;\">\r\n                <span class=\"badge badge-dark-blue fs-7 m-1\">{{ area.nameEn }} | {{ area.nameAr }}</span>\r\n              </div>\r\n            </td>\r\n\r\n            <td>\r\n              <div *ngFor=\"let specialization of broker.specializations;\">\r\n                <span class=\"badge badge-dark-blue fs-7 m-1\">{{ specialization.specialization }}</span>\r\n              </div>\r\n            </td>\r\n\r\n            <td class=\"text-end pe-4\">\r\n              <button type=\"button\" class=\"btn btn-sm btn-icon btn-color-primary btn-active-light-primary\"\r\n                data-kt-menu-trigger=\"click\" data-kt-menu-placement=\"bottom-end\" data-kt-menu-flip=\"top-end\">\r\n                <i class=\"fa-solid fa-ellipsis-vertical\"></i>\r\n              </button>\r\n              <app-assignto-brokers-action-menu></app-assignto-brokers-action-menu>\r\n            </td>\r\n          </tr>\r\n\r\n          <tr>\r\n            <td colspan=\"7\" class=\"text-center\">\r\n              <!-- <button class=\"btn btn-light-dark-blue mt-4 fw-bolder\">\r\n                Assign to broker ({{ selectedCount }})\r\n              </button> -->\r\n              <button class=\"btn btn-light-dark-blue mt-4 fw-bolder\" (click)=\"assignSelectedBrokers()\"\r\n                [disabled]=\"selectedCount === 0\" routerLink=\"/requests/sent\">\r\n                Assign to broker ({{ selectedCount }})\r\n              </button>\r\n\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": ";;;;;;;;;;ICuFcA,EAAA,CAAAC,cAAA,eAA6F;IAC3FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,SAAA,CAAAC,WAAA,MACF;;;;;IACAP,EAAA,CAAAC,cAAA,eAA6F;IAC3FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,SAAA,CAAAC,WAAA,MACF;;;;;IACAP,EAAA,CAAAC,cAAA,eAA4F;IAC1FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,SAAA,CAAAC,WAAA,MACF;;;;;IACAP,EAAA,CAAAC,cAAA,eAAkF;IAChFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,SAAA,CAAAC,WAAA,MACF;;;;;IAIAP,EAAA,CAAAC,cAAA,eAAgG;IAC9FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,SAAA,CAAAE,IAAA,MACF;;;;;IACAR,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,SAAA,CAAAE,IAAA,MACF;;;;;IAKER,EADF,CAAAC,cAAA,UAAwC,eACO;IAAAD,EAAA,CAAAE,MAAA,GAAqC;IACpFF,EADoF,CAAAG,YAAA,EAAO,EACrF;;;;IADyCH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAS,kBAAA,KAAAC,OAAA,CAAAC,MAAA,SAAAD,OAAA,CAAAE,MAAA,KAAqC;;;;;IAMlFZ,EADF,CAAAC,cAAA,UAA4D,eACb;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IAClFF,EADkF,CAAAG,YAAA,EAAO,EACnF;;;;IADyCH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAa,iBAAA,CAAAC,iBAAA,CAAAC,cAAA,CAAmC;;;;;;IAlDhFf,EAHN,CAAAC,cAAA,SAA4D,aACzC,cAC0D,gBAEhC;IADmCD,EAAA,CAAAgB,gBAAA,2BAAAC,uEAAAC,MAAA;MAAA,MAAAZ,SAAA,GAAAN,EAAA,CAAAmB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAArB,EAAA,CAAAsB,kBAAA,CAAAhB,SAAA,CAAAiB,QAAA,EAAAL,MAAA,MAAAZ,SAAA,CAAAiB,QAAA,GAAAL,MAAA;MAAA,OAAAlB,EAAA,CAAAwB,WAAA,CAAAN,MAAA;IAAA,EAA6B;IACrGlB,EAAA,CAAAyB,UAAA,oBAAAC,gEAAA;MAAA1B,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAAwB,WAAA,CAAUG,MAAA,CAAAE,qBAAA,EAAuB;IAAA,EAAC;IAExC7B,EAHI,CAAAG,YAAA,EACuC,EACnC,EACH;IAIDH,EAFJ,CAAAC,cAAA,SAAI,cACqC,cACA;IACnCD,EAAA,CAAA8B,SAAA,cAA0D;IAC5D9B,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAsD,YACO;IACzDD,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAI,EACA,EACF,EACH;IAELH,EAAA,CAAAC,cAAA,UAAI;IAUFD,EATA,CAAA+B,UAAA,KAAAC,+CAAA,mBAA6F,KAAAC,+CAAA,mBAGA,KAAAC,+CAAA,mBAGD,KAAAC,+CAAA,mBAGV;IAGpFnC,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,UAAI;IAIFD,EAHA,CAAA+B,UAAA,KAAAK,+CAAA,mBAAgG,KAAAC,+CAAA,mBAGb;IAGrFrC,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAA+B,UAAA,KAAAO,8CAAA,kBAAwC;IAG1CtC,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAA+B,UAAA,KAAAQ,8CAAA,kBAA4D;IAG9DvC,EAAA,CAAAG,YAAA,EAAK;IAGHH,EADF,CAAAC,cAAA,cAA0B,kBAEuE;IAC7FD,EAAA,CAAA8B,SAAA,aAA6C;IAC/C9B,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAA8B,SAAA,wCAAqE;IAEzE9B,EADE,CAAAG,YAAA,EAAK,EACF;;;;IA7D2EH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAwC,gBAAA,YAAAlC,SAAA,CAAAiB,QAAA,CAA6B;IAQhGvB,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAyC,UAAA,QAAAnC,SAAA,CAAAoC,KAAA,EAAA1C,EAAA,CAAA2C,aAAA,CAAoB;IAIvB3C,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,SAAA,CAAAsC,QAAA,MACF;IAMG5C,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAyC,UAAA,SAAAnC,SAAA,CAAAC,WAAA,sBAA6C;IAG7CP,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAyC,UAAA,SAAAnC,SAAA,CAAAC,WAAA,sBAA6C;IAG7CP,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAyC,UAAA,SAAAnC,SAAA,CAAAC,WAAA,sBAA6C;IAG7CP,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAyC,UAAA,SAAAnC,SAAA,CAAAC,WAAA,YAAmC;IAMnCP,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAyC,UAAA,SAAAnC,SAAA,CAAAE,IAAA,mCAAmD;IAGnDR,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAyC,UAAA,SAAAnC,SAAA,CAAAE,IAAA,mBAAmC;IAMpBR,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAyC,UAAA,YAAAnC,SAAA,CAAAuC,KAAA,CAAgB;IAMN7C,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAyC,UAAA,YAAAnC,SAAA,CAAAwC,eAAA,CAA0B;;;AD5GxE,OAAM,MAAOC,wBAAwB;EAoCvBC,EAAA;EACAC,cAAA;EACFC,KAAA;EArCVC,SAAS;EACTC,YAAY,GAAU,EAAE;EACxBC,SAAS,GAAG,KAAK;EAEjBC,SAASA,CAACC,KAAa,EAAEC,MAAW;IAClC,OAAOA,MAAM,CAACC,EAAE;EAClB;EAEAC,SAAS,GAAG,KAAK;EAEjBC,gBAAgBA,CAAA;IACd,IAAI,CAACP,YAAY,CAACQ,OAAO,CAAEJ,MAAM,IAAI;MACnCA,MAAM,CAACjC,QAAQ,GAAG,IAAI,CAACmC,SAAS;IAClC,CAAC,CAAC;IACF,IAAI,CAACG,gBAAgB,EAAE;EACzB;EAEAhC,qBAAqBA,CAAA;IACnB,IAAI,CAAC6B,SAAS,GAAG,IAAI,CAACN,YAAY,CAACU,KAAK,CAAEN,MAAM,IAAKA,MAAM,CAACjC,QAAQ,CAAC;IACrE,IAAI,CAACsC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,MAAME,WAAW,GAAG,IAAI,CAACX,YAAY,CAACY,MAAM,CACzCR,MAAM,IAAKA,MAAM,CAACjC,QAAQ,CAC5B,CAAC0C,GAAG,CAAET,MAAM,IAAKA,MAAM,CAACC,EAAE,CAAC;IAE5BS,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEJ,WAAW,CAAC;EAC3C;EAEA,IAAIK,aAAaA,CAAA;IACf,OAAO,IAAI,CAAChB,YAAY,CAACY,MAAM,CAAER,MAAM,IAAKA,MAAM,CAACjC,QAAQ,CAAC,CAAC8C,MAAM;EACrE;EAEAC,YACYtB,EAAqB,EACrBC,cAA8B,EAChCC,KAAqB;IAFnB,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IAChB,KAAAC,KAAK,GAALA,KAAK;EACZ;EAEHqB,QAAQA,CAAA;IACN;IACA,IAAI,CAACrB,KAAK,CAACsB,QAAQ,CAACC,SAAS,CAAEC,MAAM,IAAI;MACvC,IAAI,CAACvB,SAAS,GAAGwB,MAAM,CAACD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC,CAAC;MACzC,IAAI,IAAI,CAACzB,SAAS,EAAE;QAClB,IAAI,CAAC0B,kBAAkB,EAAE;MAC3B,CAAC,MAAM;QACLX,OAAO,CAACY,KAAK,CAAC,2CAA2C,CAAC;MAC5D;IACF,CAAC,CAAC;EACJ;EAEAD,kBAAkBA,CAAA;IAChB,IAAI,CAACxB,SAAS,GAAG,IAAI;IAErB,IAAI,CAACJ,cAAc,CAAC8B,qBAAqB,CAAC,IAAI,CAAC5B,SAAS,CAAC,CAACsB,SAAS,CAAC;MAClEO,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAC7B,YAAY,GAAG6B,QAAQ,EAAEC,IAAI,IAAI,EAAE;QACxC,IAAI,CAAC7B,SAAS,GAAG,KAAK;QACtB,IAAI,CAACL,EAAE,CAACmC,aAAa,EAAE;MACzB,CAAC;MACDL,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACzB,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA+B,qBAAqBA,CAAA;IACnB,MAAMC,iBAAiB,GAAG,IAAI,CAACjC,YAAY,CAACY,MAAM,CAC/CR,MAAM,IAAKA,MAAM,CAACjC,QAAQ,CAC5B,CAAC0C,GAAG,CAAET,MAAM,IAAKA,MAAM,CAACC,EAAE,CAAC;IAE5B,IAAI,CAACR,cAAc,CAChBqC,sBAAsB,CAAC,IAAI,CAACnC,SAAS,EAAEkC,iBAAiB,CAAC,CACzDZ,SAAS,CAAC;MACTO,IAAI,EAAGO,GAAG,IAAI;QACZrB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoB,GAAG,CAAC;MAC5C,CAAC;MACDT,KAAK,EAAGU,GAAG,IAAI;QACbtB,OAAO,CAACY,KAAK,CAAC,mBAAmB,EAAEU,GAAG,CAAC;MACzC;KACD,CAAC;EACN;;qCApFWzC,wBAAwB,EAAA/C,EAAA,CAAAyF,iBAAA,CAAAzF,EAAA,CAAA0F,iBAAA,GAAA1F,EAAA,CAAAyF,iBAAA,CAAAE,EAAA,CAAAC,cAAA,GAAA5F,EAAA,CAAAyF,iBAAA,CAAAI,EAAA,CAAAC,cAAA;EAAA;;UAAxB/C,wBAAwB;IAAAgD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTrCrG,EAAA,CAAAC,cAAA,aAAuB;QACrBD,EAAA,CAAA8B,SAAA,uBAAqC;QACvC9B,EAAA,CAAAG,YAAA,EAAM;QAQMH,EANZ,CAAAC,cAAA,aAA+B,aACI,aACmB,aACvB,aACsD,aAClD,YACwB;QAAAD,EAAA,CAAAE,MAAA,eAAQ;QACzDF,EADyD,CAAAG,YAAA,EAAK,EACxD;QAGJH,EADF,CAAAC,cAAA,cAA8B,eACkE;QAG5FD,EAFA,CAAA8B,SAAA,wBACqG,iBAEnB;QAEtF9B,EADE,CAAAG,YAAA,EAAO,EACH;QAGJH,EADF,CAAAC,cAAA,cAAyB,aACuC;QAC5DD,EAAA,CAAA8B,SAAA,aAAkC;QAAC9B,EAAA,CAAAE,MAAA,gBACrC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACJH,EAAA,CAAAC,cAAA,aAA8D;QAC5DD,EAAA,CAAA8B,SAAA,aAAiD;QAAC9B,EAAA,CAAAE,MAAA,cACpD;QAIRF,EAJQ,CAAAG,YAAA,EAAI,EACA,EACF,EACF,EACF;QAQMH,EANZ,CAAAC,cAAA,eAA8B,iBAC2D,aAC9E,cAC2D,cACxB,eACqC,iBAE2B;QAAxDD,EAAA,CAAAgB,gBAAA,2BAAAuF,kEAAArF,MAAA;UAAAlB,EAAA,CAAAsB,kBAAA,CAAAgF,GAAA,CAAA5C,SAAA,EAAAxC,MAAA,MAAAoF,GAAA,CAAA5C,SAAA,GAAAxC,MAAA;UAAA,OAAAA,MAAA;QAAA,EAAuB;QAAClB,EAAA,CAAAyB,UAAA,oBAAA+E,2DAAA;UAAA,OAAUF,GAAA,CAAA3C,gBAAA,EAAkB;QAAA,EAAC;QAEnG3D,EAHI,CAAAG,YAAA,EACkG,EAC9F,EACH;QACLH,EAAA,CAAAC,cAAA,cAAwB;QACtBD,EAAA,CAAAE,MAAA,qBACA;QAAAF,EAAA,CAAA8B,SAAA,aAA0D;QAC5D9B,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,cAAwB;QACtBD,EAAA,CAAAE,MAAA,sBACA;QAAAF,EAAA,CAAA8B,SAAA,aAA0D;QAC5D9B,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,cAAwB;QAAAD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACjCH,EAAA,CAAAC,cAAA,cAAwB;QACtBD,EAAA,CAAAE,MAAA,cAAK;QAAAF,EAAA,CAAA8B,SAAA,aAA0D;QACjE9B,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,cAAwB;QACtBD,EAAA,CAAAE,MAAA,wBACA;QAAAF,EAAA,CAAA8B,SAAA,aAA0D;QAC5D9B,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,cAAkD;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAE7DF,EAF6D,CAAAG,YAAA,EAAK,EAC3D,EACC;QAERH,EAAA,CAAAC,cAAA,aAAO;QACLD,EAAA,CAAA+B,UAAA,KAAA0E,uCAAA,mBAA4D;QAuExDzG,EALJ,CAAAC,cAAA,UAAI,cACkC,kBAK6B;QADRD,EAAA,CAAAyB,UAAA,mBAAAiF,2DAAA;UAAA,OAASJ,GAAA,CAAAlB,qBAAA,EAAuB;QAAA,EAAC;QAEtFpF,EAAA,CAAAE,MAAA,IACF;QAQdF,EARc,CAAAG,YAAA,EAAS,EAEN,EACF,EACC,EACF,EACJ,EACF,EACF;;;QA1GoDH,EAAA,CAAAI,SAAA,IAAuB;QAAvBJ,EAAA,CAAAwC,gBAAA,YAAA8D,GAAA,CAAA5C,SAAA,CAAuB;QAwBhD1D,EAAA,CAAAI,SAAA,IAAiB;QAAAJ,EAAjB,CAAAyC,UAAA,YAAA6D,GAAA,CAAAlD,YAAA,CAAiB,iBAAAkD,GAAA,CAAAhD,SAAA,CAAkB;QAwEpDtD,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAyC,UAAA,aAAA6D,GAAA,CAAAlC,aAAA,OAAgC;QAChCpE,EAAA,CAAAI,SAAA,EACF;QADEJ,EAAA,CAAAK,kBAAA,wBAAAiG,GAAA,CAAAlC,aAAA,OACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}