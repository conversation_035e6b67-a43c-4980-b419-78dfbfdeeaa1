import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { RequestService } from '../../../services/request.service';
import Swal from 'sweetalert2';
import { ActivatedRoute } from '@angular/router';
import { Modal } from 'bootstrap';
import { Page } from 'src/app/models/page.model';
import { environment } from 'src/environments/environment';
import * as bootstrap from 'bootstrap';


@Component({
  selector: 'app-request-replies',
  templateUrl: './request-replies.component.html',
  styleUrl: './request-replies.component.scss'
})
export class RequestRepliesComponent implements OnInit {
  selectedBrokerId: any;
  requestId : any ;
  user : any ;

  page: Page = new Page();
  replies : any;

  dealClicked: boolean = false;
  orderBy: string = 'id';
  orderDir: string = 'desc';

  constructor(
    protected cd: ChangeDetectorRef,
    protected requestService: RequestService,
    private route :ActivatedRoute
  ) {}

  ngOnInit() {
    let id = this.route.parent?.snapshot.paramMap.get('id') ?? null;
    this.requestId = id ? Number(id) : null;

    const userJson = localStorage.getItem('currentUser');
    this.user = userJson ? JSON.parse(userJson) : null;

    this.page.pageNumber = 0;
    this.page.size = environment.TABLE_LIMIT;

    this.getReplies();
  }


  getReplies()
  {
    this.requestService.getReplies(this.requestId, this.page).subscribe(
      (response:any) => {
        console.log(response.data);
        this.replies = response.data.data || response.data;
        this.page.totalElements = response.data.total || response.count || 0;
        this.page.count = Math.ceil(this.page.totalElements / this.page.size);
        this.cd.markForCheck();
      },
      (error: any) => {
        console.log(error);
        this.cd.markForCheck();
        Swal.fire('Failed to load data. please try again later.', '', 'error');
      }
    )
  }

  updateRequestStatus(requestId: number, userId: number, status: string, brokerId : number) {
    const payload = {
      userId,
      status,
    };

    this.selectedBrokerId = brokerId;

    this.requestService.updateRequestStatus(requestId, payload).subscribe({
      next: (response) => {
        this.dealClicked = true;
        this.cd.markForCheck();

        const modalElement = document.getElementById('ratingModal');
          if (modalElement) {
            const ratingModal = new bootstrap.Modal(modalElement);
            ratingModal.show();
          }
        },
      error: (error) => {
        console.error('Error updating status:', error);
      },
    });
  }

  formatAccessories(accessories: string[]): string {
    return accessories
      .map(item => item.replace(/_/g, ' '))
      .join(', ');
  }

  selectedUnitPlanImage: string | null = null;

  showUnitPlanModal(imgPath: string) {
      this.selectedUnitPlanImage = imgPath;

      const modalElement = document.getElementById('viewUnitPlanModal');
      if (modalElement) {
        const modal = new Modal(modalElement);
        modal.show();
      }
  }

  onPageChange(newPageNumber: number)
  {
    this.page.pageNumber = newPageNumber;
    this.getReplies();
  }

   sortData(column: string) {
     if (this.orderBy === column) {
      this.orderDir = this.orderDir === 'asc' ? 'desc' : 'asc';
    } else {
      this.orderBy = column;
      this.orderDir = 'asc';
    }

     this.page.orderBy = this.orderBy;
    this.page.orderDir = this.orderDir;
    this.page.pageNumber = 0;
    this.getReplies();
  }

   getSortArrow(column: string): string {
    if (this.orderBy !== column) {
      return '';
    }
    return this.orderDir === 'asc' ? '↑' : '↓';
  }

}
