{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nlet HomeComponent = class HomeComponent {};\nHomeComponent = __decorate([Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  templateUrl: './home.component.html',\n  styleUrl: './home.component.scss'\n})], HomeComponent);\nexport { HomeComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "RouterModule", "HomeComponent", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrl"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\home\\home.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule],\r\n  templateUrl: './home.component.html',\r\n  styleUrl: './home.component.scss'\r\n})\r\nexport class HomeComponent implements OnInit {\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AASvC,IAAMC,aAAa,GAAnB,MAAMA,aAAa,GAEzB;AAFYA,aAAa,GAAAC,UAAA,EAPzBJ,SAAS,CAAC;EACTK,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACN,YAAY,EAAEC,YAAY,CAAC;EACrCM,WAAW,EAAE,uBAAuB;EACpCC,QAAQ,EAAE;CACX,CAAC,C,EACWN,aAAa,CAEzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}