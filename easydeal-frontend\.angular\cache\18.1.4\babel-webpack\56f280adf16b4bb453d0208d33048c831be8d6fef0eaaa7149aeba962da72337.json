{"ast": null, "code": "import { ChatComponentComponent } from './shared/chat-component/chat-component.component';\nconst Routing = [{\n  path: 'dashboard',\n  redirectTo: '/broker/dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'client',\n  loadChildren: () => import('./user/client/client.module').then(m => m.ClientModule)\n}, {\n  path: 'profile',\n  loadChildren: () => import('./shared/profile/profile-routing.module').then(m => m.ProfileRoutingModule)\n}, {\n  path: 'super-admin',\n  loadChildren: () => import('../pages/super-admin/super-admin.module').then(m => m.SuperAdminModule)\n}, {\n  path: 'broker',\n  loadChildren: () => import('./broker/broker.module').then(m => m.BrokerModule)\n}, {\n  path: 'requests',\n  loadChildren: () => import('./requests/requests.module').then(m => m.RequestsModule)\n}, {\n  path: 'developer',\n  loadChildren: () => import('./developer/developer.module').then(m => m.DeveloperModule)\n}, {\n  path: 'chat',\n  component: ChatComponentComponent\n}, {\n  path: 'rating',\n  loadChildren: () => import('./shared/rating/rating.module').then(m => m.RatingModule)\n}, {\n  path: 'builder',\n  loadChildren: () => import('./builder/builder.module').then(m => m.BuilderModule)\n}, {\n  path: 'crafted/pages/profile',\n  loadChildren: () => import('../modules/profile/profile.module').then(m => m.ProfileModule)\n  // data: { layout: 'light-sidebar' },\n}, {\n  path: 'crafted/account',\n  loadChildren: () => import('../modules/account/account.module').then(m => m.AccountModule)\n  // data: { layout: 'dark-header' },\n}, {\n  path: 'crafted/pages/wizards',\n  loadChildren: () => import('../modules/wizards/wizards.module').then(m => m.WizardsModule)\n  // data: { layout: 'light-header' },\n}, {\n  path: 'crafted/widgets',\n  loadChildren: () => import('../modules/widgets-examples/widgets-examples.module').then(m => m.WidgetsExamplesModule)\n  // data: { layout: 'light-header' },\n}, {\n  path: 'apps/chat',\n  loadChildren: () => import('../modules/apps/chat/chat.module').then(m => m.ChatModule)\n  // data: { layout: 'light-sidebar' },\n}, {\n  path: 'apps/users',\n  loadChildren: () => import('./user/user.module').then(m => m.UserModule)\n}, {\n  path: 'apps/roles',\n  loadChildren: () => import('./role/role.module').then(m => m.RoleModule)\n}, {\n  path: 'apps/permissions',\n  loadChildren: () => import('./permission/permission.module').then(m => m.PermissionModule)\n}, {\n  path: '',\n  redirectTo: '/dashboard',\n  pathMatch: 'full'\n}, {\n  path: '**',\n  redirectTo: 'error/404'\n}];\nexport { Routing };", "map": {"version": 3, "names": ["ChatComponentComponent", "Routing", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "ClientModule", "ProfileRoutingModule", "SuperAdminModule", "BrokerModule", "RequestsModule", "DeveloperModule", "component", "RatingModule", "BuilderModule", "ProfileModule", "AccountModule", "WizardsModule", "WidgetsExamplesModule", "ChatModule", "UserModule", "RoleModule", "PermissionModule"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\New folder\\easydeal-frontend\\src\\app\\pages\\routing.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\nimport { ChatComponentComponent } from './shared/chat-component/chat-component.component';\r\n\r\nconst Routing: Routes = [\r\n  {\r\n    path: 'dashboard',\r\n    redirectTo: '/broker/dashboard',\r\n    pathMatch: 'full',\r\n  },\r\n  {\r\n    path: 'client',\r\n    loadChildren: () =>\r\n      import('./user/client/client.module').then((m) => m.ClientModule),\r\n  },\r\n\r\n  {\r\n    path: 'profile',\r\n    loadChildren: () =>\r\n      import('./shared/profile/profile-routing.module').then(\r\n        (m) => m.ProfileRoutingModule\r\n      ),\r\n  },\r\n    {\r\n    path: 'super-admin',\r\n    loadChildren: () =>\r\n      import('../pages/super-admin/super-admin.module').then(\r\n        (m) => m.SuperAdminModule\r\n      ),\r\n  },\r\n\r\n  {\r\n    path: 'broker',\r\n    loadChildren: () =>\r\n      import('./broker/broker.module').then((m) => m.BrokerModule),\r\n  },\r\n  {\r\n    path: 'requests',\r\n    loadChildren: () =>\r\n      import('./requests/requests.module').then((m) => m.RequestsModule),\r\n  },\r\n  {\r\n    path: 'developer',\r\n    loadChildren: () =>\r\n      import('./developer/developer.module').then((m) => m.DeveloperModule),\r\n  },\r\n  {\r\n    path: 'chat',\r\n    component: ChatComponentComponent,\r\n  },\r\n  {\r\n    path: 'rating',\r\n    loadChildren: () =>\r\n      import('./shared/rating/rating.module').then((m) => m.RatingModule),\r\n  },\r\n  {\r\n    path: 'builder',\r\n    loadChildren: () =>\r\n      import('./builder/builder.module').then((m) => m.BuilderModule),\r\n  },\r\n  {\r\n    path: 'crafted/pages/profile',\r\n    loadChildren: () =>\r\n      import('../modules/profile/profile.module').then((m) => m.ProfileModule),\r\n    // data: { layout: 'light-sidebar' },\r\n  },\r\n  {\r\n    path: 'crafted/account',\r\n    loadChildren: () =>\r\n      import('../modules/account/account.module').then((m) => m.AccountModule),\r\n    // data: { layout: 'dark-header' },\r\n  },\r\n  {\r\n    path: 'crafted/pages/wizards',\r\n    loadChildren: () =>\r\n      import('../modules/wizards/wizards.module').then((m) => m.WizardsModule),\r\n    // data: { layout: 'light-header' },\r\n  },\r\n  {\r\n    path: 'crafted/widgets',\r\n    loadChildren: () =>\r\n      import('../modules/widgets-examples/widgets-examples.module').then(\r\n        (m) => m.WidgetsExamplesModule\r\n      ),\r\n    // data: { layout: 'light-header' },\r\n  },\r\n  {\r\n    path: 'apps/chat',\r\n    loadChildren: () =>\r\n      import('../modules/apps/chat/chat.module').then((m) => m.ChatModule),\r\n    // data: { layout: 'light-sidebar' },\r\n  },\r\n  {\r\n    path: 'apps/users',\r\n    loadChildren: () => import('./user/user.module').then((m) => m.UserModule),\r\n  },\r\n  {\r\n    path: 'apps/roles',\r\n    loadChildren: () => import('./role/role.module').then((m) => m.RoleModule),\r\n  },\r\n  {\r\n    path: 'apps/permissions',\r\n    loadChildren: () =>\r\n      import('./permission/permission.module').then((m) => m.PermissionModule),\r\n  },\r\n  {\r\n    path: '',\r\n    redirectTo: '/dashboard',\r\n    pathMatch: 'full',\r\n  },\r\n  {\r\n    path: '**',\r\n    redirectTo: 'error/404',\r\n  },\r\n];\r\n\r\nexport { Routing };\r\n"], "mappings": "AACA,SAASA,sBAAsB,QAAQ,kDAAkD;AAEzF,MAAMC,OAAO,GAAW,CACtB;EACEC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE,mBAAmB;EAC/BC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,QAAQ;EACdG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,YAAY;CACnE,EAED;EACEN,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,yCAAyC,CAAC,CAACC,IAAI,CACnDC,CAAC,IAAKA,CAAC,CAACE,oBAAoB;CAElC,EACC;EACAP,IAAI,EAAE,aAAa;EACnBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,yCAAyC,CAAC,CAACC,IAAI,CACnDC,CAAC,IAAKA,CAAC,CAACG,gBAAgB;CAE9B,EAED;EACER,IAAI,EAAE,QAAQ;EACdG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACI,YAAY;CAC9D,EACD;EACET,IAAI,EAAE,UAAU;EAChBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACK,cAAc;CACpE,EACD;EACEV,IAAI,EAAE,WAAW;EACjBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACM,eAAe;CACvE,EACD;EACEX,IAAI,EAAE,MAAM;EACZY,SAAS,EAAEd;CACZ,EACD;EACEE,IAAI,EAAE,QAAQ;EACdG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,+BAA+B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACQ,YAAY;CACrE,EACD;EACEb,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACS,aAAa;CACjE,EACD;EACEd,IAAI,EAAE,uBAAuB;EAC7BG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACU,aAAa;EACzE;CACD,EACD;EACEf,IAAI,EAAE,iBAAiB;EACvBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACW,aAAa;EACzE;CACD,EACD;EACEhB,IAAI,EAAE,uBAAuB;EAC7BG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACY,aAAa;EACzE;CACD,EACD;EACEjB,IAAI,EAAE,iBAAiB;EACvBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,qDAAqD,CAAC,CAACC,IAAI,CAC/DC,CAAC,IAAKA,CAAC,CAACa,qBAAqB;EAElC;CACD,EACD;EACElB,IAAI,EAAE,WAAW;EACjBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACc,UAAU;EACrE;CACD,EACD;EACEnB,IAAI,EAAE,YAAY;EAClBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACe,UAAU;CAC1E,EACD;EACEpB,IAAI,EAAE,YAAY;EAClBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACgB,UAAU;CAC1E,EACD;EACErB,IAAI,EAAE,kBAAkB;EACxBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACiB,gBAAgB;CAC1E,EACD;EACEtB,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF;AAED,SAASF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}